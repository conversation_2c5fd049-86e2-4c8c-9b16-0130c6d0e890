/*
 * ========================================
 * 文件名: AssemblyInfo.cs
 * 功能描述: 程序集信息配置文件
 * ========================================
 *
 * 主要功能:
 * 1. 定义程序集的基本信息和元数据
 * 2. 配置程序集的版本信息
 * 3. 设置程序集的可见性和COM互操作性
 * 4. 提供程序集的描述和版权信息
 * 5. 配置程序集的GUID标识符
 *
 * 程序集属性说明:
 *
 * AssemblyDescription:
 * - 程序集的功能描述
 * - 当前设置为"Word文档批量自动排版工具"
 * - 在文件属性中显示的程序描述
 *
 * ComVisible:
 * - 控制程序集对COM组件的可见性
 * - 设置为false，表示程序集不向COM公开
 * - 提高安全性和性能
 *
 * Guid:
 * - 程序集的唯一标识符
 * - 用于COM互操作和程序集识别
 * - 当前使用示例GUID格式
 *
 * AssemblyCopyright:
 * - 程序集的版权信息
 * - 当前设置为"Copyright © 阿里云 2023"
 * - 在文件属性中显示的版权声明
 *
 * AssemblyTrademark:
 * - 程序集的商标信息
 * - 当前为空字符串
 * - 可用于显示商标声明
 *
 * AssemblyCulture:
 * - 程序集的区域性信息
 * - 当前为空字符串，表示中性区域性
 * - 用于本地化和国际化支持
 *
 * 配置说明:
 * - 此文件包含程序集级别的属性配置
 * - 这些属性会编译到程序集的元数据中
 * - 可通过反射或文件属性查看这些信息
 * - 版本信息通过其他机制管理（如项目文件）
 *
 * 使用场景:
 * - 程序集的身份识别和描述
 * - COM互操作性控制
 * - 版权和商标信息展示
 * - 程序集的安全性配置
 *
 * 注意事项:
 * - GUID应该是唯一的，当前使用的是示例格式
 * - COM可见性设置影响程序集的安全性
 * - 版权信息应根据实际情况更新
 * - 区域性设置影响本地化行为
 */

using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;


[assembly: AssemblyDescription("Word文档批量自动排版工具")]

[assembly: ComVisible(false)]

// 修改: 使用正确的 GUID 格式
[assembly: Guid("12345678-1234-1234-1234-123456789abc")]

// 修改: 使用 Aspose.Words 命名空间下的 AssemblyVersion 和 AssemblyFileVersion
[assembly: AssemblyCopyright("Copyright © 阿里云 2023")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
