using System;
using System.Drawing;
using System.Text.Json;
using AsposeWordFormatter.Models;

namespace AsposeWordFormatter
{
    /// <summary>
    /// 测试颜色序列化和反序列化的程序
    /// </summary>
    public class TestColorSerialization
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== 页面设置颜色序列化测试 ===");
            
            // 创建一个PageSetupFixed实例并设置颜色
            var pageSetup = new PageSetupFixed();
            
            // 设置分隔线颜色为红色
            pageSetup.SeparatorLineColor = Color.Red;
            Console.WriteLine($"原始分隔线颜色: {pageSetup.SeparatorLineColor.Name} (R:{pageSetup.SeparatorLineColor.R}, G:{pageSetup.SeparatorLineColor.G}, B:{pageSetup.SeparatorLineColor.B})");
            
            // 设置背景颜色为蓝色
            pageSetup.BackgroundColor = Color.Blue;
            Console.WriteLine($"原始背景颜色: {pageSetup.BackgroundColor.Name} (R:{pageSetup.BackgroundColor.R}, G:{pageSetup.BackgroundColor.G}, B:{pageSetup.BackgroundColor.B})");
            
            // 设置边框颜色为绿色
            pageSetup.BorderColor = Color.Green;
            Console.WriteLine($"原始边框颜色: {pageSetup.BorderColor.Name} (R:{pageSetup.BorderColor.R}, G:{pageSetup.BorderColor.G}, B:{pageSetup.BorderColor.B})");
            
            // 设置网格线颜色为黄色
            pageSetup.GridLinesColor = Color.Yellow;
            Console.WriteLine($"原始网格线颜色: {pageSetup.GridLinesColor.Name} (R:{pageSetup.GridLinesColor.R}, G:{pageSetup.GridLinesColor.G}, B:{pageSetup.GridLinesColor.B})");
            
            Console.WriteLine("\n=== 序列化测试 ===");
            
            // 序列化选项
            var jsonOptions = new JsonSerializerOptions 
            {
                WriteIndented = true,
                AllowTrailingCommas = true,
                ReadCommentHandling = JsonCommentHandling.Skip
            };
            
            // 序列化为JSON
            string json = JsonSerializer.Serialize(pageSetup, jsonOptions);
            Console.WriteLine("序列化后的JSON（部分）:");
            
            // 只显示颜色相关的部分
            var lines = json.Split('\n');
            bool showLine = false;
            foreach (var line in lines)
            {
                if (line.Contains("SeparatorLineColorHex") || 
                    line.Contains("BackgroundColorHex") || 
                    line.Contains("BorderColorHex") || 
                    line.Contains("GridLinesColorHex"))
                {
                    Console.WriteLine(line);
                    showLine = true;
                }
                else if (showLine && line.Trim() == "")
                {
                    showLine = false;
                }
            }
            
            Console.WriteLine("\n=== 反序列化测试 ===");
            
            // 反序列化
            var deserializedPageSetup = JsonSerializer.Deserialize<PageSetupFixed>(json, jsonOptions);
            
            if (deserializedPageSetup != null)
            {
                Console.WriteLine($"反序列化后分隔线颜色: {deserializedPageSetup.SeparatorLineColor.Name} (R:{deserializedPageSetup.SeparatorLineColor.R}, G:{deserializedPageSetup.SeparatorLineColor.G}, B:{deserializedPageSetup.SeparatorLineColor.B})");
                Console.WriteLine($"反序列化后背景颜色: {deserializedPageSetup.BackgroundColor.Name} (R:{deserializedPageSetup.BackgroundColor.R}, G:{deserializedPageSetup.BackgroundColor.G}, B:{deserializedPageSetup.BackgroundColor.B})");
                Console.WriteLine($"反序列化后边框颜色: {deserializedPageSetup.BorderColor.Name} (R:{deserializedPageSetup.BorderColor.R}, G:{deserializedPageSetup.BorderColor.G}, B:{deserializedPageSetup.BorderColor.B})");
                Console.WriteLine($"反序列化后网格线颜色: {deserializedPageSetup.GridLinesColor.Name} (R:{deserializedPageSetup.GridLinesColor.R}, G:{deserializedPageSetup.GridLinesColor.G}, B:{deserializedPageSetup.GridLinesColor.B})");
                
                Console.WriteLine("\n=== 验证结果 ===");
                
                bool separatorColorMatch = pageSetup.SeparatorLineColor.ToArgb() == deserializedPageSetup.SeparatorLineColor.ToArgb();
                bool backgroundColorMatch = pageSetup.BackgroundColor.ToArgb() == deserializedPageSetup.BackgroundColor.ToArgb();
                bool borderColorMatch = pageSetup.BorderColor.ToArgb() == deserializedPageSetup.BorderColor.ToArgb();
                bool gridColorMatch = pageSetup.GridLinesColor.ToArgb() == deserializedPageSetup.GridLinesColor.ToArgb();
                
                Console.WriteLine($"分隔线颜色匹配: {(separatorColorMatch ? "✓" : "✗")}");
                Console.WriteLine($"背景颜色匹配: {(backgroundColorMatch ? "✓" : "✗")}");
                Console.WriteLine($"边框颜色匹配: {(borderColorMatch ? "✓" : "✗")}");
                Console.WriteLine($"网格线颜色匹配: {(gridColorMatch ? "✓" : "✗")}");
                
                if (separatorColorMatch && backgroundColorMatch && borderColorMatch && gridColorMatch)
                {
                    Console.WriteLine("\n🎉 所有颜色序列化和反序列化测试通过！");
                }
                else
                {
                    Console.WriteLine("\n❌ 颜色序列化和反序列化测试失败！");
                }
            }
            else
            {
                Console.WriteLine("❌ 反序列化失败！");
            }
            
            Console.WriteLine("\n=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
