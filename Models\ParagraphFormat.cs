/*
 * ========================================
 * 文件名: ParagraphFormat.cs
 * 功能描述: 段落格式数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义段落格式的基本配置选项
 * 2. 提供段落格式到Aspose.Words的转换方法
 * 3. 支持段落对齐、缩进、间距等基础设置
 * 4. 包含特殊缩进类型的处理逻辑
 * 5. 提供段落格式的应用方法
 *
 * 核心属性:
 * - Alignment: 段落对齐方式（左对齐、居中、右对齐、两端对齐）
 * - OutlineLevel: 大纲级别（正文文本、标题级别1-9）
 * - Bidi: 双向文本支持（从右到左的文本）
 * - FirstLineIndent: 首行缩进值
 * - RightIndent: 右缩进值
 * - LeftIndent: 左缩进值
 * - SpaceBefore: 段前间距
 * - SpaceAfter: 段后间距
 * - LineSpacingRule: 行距规则（单倍、1.5倍、双倍、固定值等）
 * - LineSpacing: 行距数值
 * - SpecialIndent: 特殊缩进类型（无、首行缩进、悬挂缩进）
 * - SpecialIndentValue: 特殊缩进的数值
 *
 * 特殊缩进类型:
 * - None: 无特殊缩进
 * - FirstLine: 首行缩进（首行向右缩进）
 * - Hanging: 悬挂缩进（首行不缩进，其他行向右缩进）
 *
 * 核心方法:
 * - ApplyTo(): 将格式设置应用到Aspose.Words段落对象
 *
 * 应用逻辑:
 * - 直接映射基本属性到Aspose.Words格式
 * - 特殊处理缩进类型的转换
 * - 包含参数验证和异常处理
 *
 * 注意事项:
 * - 支持Aspose.Words的所有基本段落格式选项
 * - 包含完整的参数验证
 * - 实现了简洁的格式应用逻辑
 * - 提供了枚举类型的定义
 */

using Aspose.Words;
using AW = Aspose.Words;

namespace AsposeWordFormatter
{
    public class ParagraphFormat
    {
        public ParagraphAlignment Alignment { get; set; } = ParagraphAlignment.Left;
        public OutlineLevel OutlineLevel { get; set; } = OutlineLevel.BodyText;
        public bool Bidi { get; set; } = false;
        public double FirstLineIndent { get; set; } = 0;
        public double RightIndent { get; set; } = 0;
        public double LeftIndent { get; set; } = 0;
        public double SpaceBefore { get; set; } = 0;
        public double SpaceAfter { get; set; } = 0;
        public LineSpacingRule LineSpacingRule { get; set; } = LineSpacingRule.Multiple;
        public double LineSpacing { get; set; } = 1.5;
        public SpecialIndent SpecialIndent { get; set; } = SpecialIndent.None;
        public double SpecialIndentValue { get; set; } = 2;

        public void ApplyTo(AW.Paragraph paragraph)
        {
            if (paragraph == null)
                throw new ArgumentNullException(nameof(paragraph));

            var format = paragraph.ParagraphFormat;
            format.Alignment = (AW.ParagraphAlignment)Alignment;
            format.OutlineLevel = OutlineLevel;
            format.Bidi = Bidi;
            format.FirstLineIndent = FirstLineIndent;
            format.RightIndent = RightIndent;
            format.LeftIndent = LeftIndent;
            format.SpaceBefore = SpaceBefore;
            format.SpaceAfter = SpaceAfter;
            format.LineSpacingRule = LineSpacingRule;
            format.LineSpacing = LineSpacing;

            // 应用特殊缩进
            switch (SpecialIndent)
            {
                case SpecialIndent.FirstLine:
                    format.FirstLineIndent = SpecialIndentValue;
                    break;
                case SpecialIndent.Hanging:
                    format.FirstLineIndent = -SpecialIndentValue;
                    break;
            }
        }
    }

    public enum SpecialIndent
    {
        None,
        FirstLine,
        Hanging
    }
}