/*
 * ========================================
 * 文件名: HeaderFooterForm.cs
 * 功能描述: 页眉页脚设置配置窗体
 * ========================================
 *
 * 主要功能:
 * 1. 页眉内容设置（文本、图片、页码）
 * 2. 页脚内容设置（文本、图片、页码）
 * 3. 页眉页脚字体格式设置
 * 4. 页眉页脚对齐方式配置
 * 5. 页眉页脚背景颜色设置
 * 6. 页码格式和样式配置
 * 7. 页眉页脚的启用/禁用控制
 *
 * 界面结构:
 * - 页眉设置标签页：页眉内容、格式、页码设置
 * - 页脚设置标签页：页脚内容、格式、页码设置
 * - 通用选项区域：启用开关、首页不同等选项
 *
 * 页眉功能:
 * - 文本内容设置（多行文本支持）
 * - 图片内容设置（图片选择和预览）
 * - 页码设置（格式、前缀、后缀、页数显示）
 * - 字体设置（字体、字号、颜色、样式）
 * - 对齐方式（左对齐、居中、右对齐）
 * - 背景颜色设置
 *
 * 页脚功能:
 * - 文本内容设置（多行文本支持）
 * - 图片内容设置（图片选择和预览）
 * - 页码设置（格式、前缀、后缀、页数显示）
 * - 字体设置（字体、字号、颜色、样式）
 * - 对齐方式（左对齐、居中、右对齐）
 * - 背景颜色设置
 *
 * 页码功能:
 * - 页码格式（阿拉伯数字、罗马数字等）
 * - 页码前缀和后缀文本
 * - 包含总页数选项（如"第1页 共10页"）
 * - 页码字体独立设置
 * - 页码对齐方式
 * - 页码背景颜色
 *
 * 通用选项:
 * - 启用页眉设置开关
 * - 启用页脚设置开关
 * - 删除现有页眉选项
 * - 删除现有页脚选项
 * - 首页使用不同页眉页脚
 *
 * 数据管理:
 * - 与HeaderFooterSettings模型集成
 * - 支持所有设置的实时预览
 * - 图片文件的选择和验证
 * - 颜色选择和管理
 *
 * 注意事项:
 * - 支持复杂的页眉页脚布局
 * - 包含丰富的格式化选项
 * - 实现了完整的数据绑定
 * - 支持图片预览和文件验证
 */

using System;
using System.Drawing;
using System.Windows.Forms;
using AsposeWordFormatter.Models;
using AW = Aspose.Words;

namespace AsposeWordFormatter
{
    public partial class HeaderFooterForm : Form
    {
        public HeaderFooterSettings HeaderFooterSettings { get; private set; }

        private TabControl mainTabControl = null!;
        private TabPage headerTabPage = null!;
        private TabPage footerTabPage = null!;
        private RadioButton headerTextRadio = null!;
        private RadioButton headerImageRadio = null!;
        private TextBox headerTextBox = null!;
        private Button headerImageButton = null!;
        private PictureBox headerImagePreview = null!;
        private ComboBox headerAlignmentCombo = null!;
        private RadioButton footerTextRadio = null!;
        private RadioButton footerImageRadio = null!;
        private TextBox footerTextBox = null!;
        private Button footerImageButton = null!;
        private PictureBox footerImagePreview = null!;
        private ComboBox footerAlignmentCombo = null!;

        // 页眉字体设置控件
        private ComboBox headerFontNameCombo = null!;
        private NumericUpDown headerFontSizeNumeric = null!;
        private CheckBox headerBoldCheckBox = null!;
        private CheckBox headerItalicCheckBox = null!;
        private Button headerFontColorButton = null!;
        private Panel headerFontColorPanel = null!;

        // 页脚字体设置控件
        private ComboBox footerFontNameCombo = null!;
        private NumericUpDown footerFontSizeNumeric = null!;
        private CheckBox footerBoldCheckBox = null!;
        private CheckBox footerItalicCheckBox = null!;
        private Button footerFontColorButton = null!;
        private Panel footerFontColorPanel = null!;

        private CheckBox removeHeaderCheckBox = null!;
        private CheckBox removeFooterCheckBox = null!;
        private CheckBox enableHeaderCheckBox = null!;   // 新增：启用页眉设置选项
        private CheckBox enableFooterCheckBox = null!;   // 新增：启用页脚设置选项
        private CheckBox differentFirstPageCheckBox = null!; // 新增：首页使用不同页眉页脚选项
        private Button okButton = null!;
        private Button cancelButton = null!;

        // 新增页码区域控件
        private RadioButton headerPageNumberRadio = null!;
        private ComboBox headerPageNumberFormatCombo = null!;
        private CheckBox headerIncludePageCountCheckBox = null!;
        private TextBox headerPageNumberPrefixTextBox = null!;
        private TextBox headerPageNumberSuffixTextBox = null!;

        // 新增页码区域控件
        private RadioButton footerPageNumberRadio = null!;
        private ComboBox footerPageNumberFormatCombo = null!;
        private CheckBox footerIncludePageCountCheckBox = null!;
        private TextBox footerPageNumberPrefixTextBox = null!;
        private TextBox footerPageNumberSuffixTextBox = null!;

        // 新增背景色控件
        private Panel headerBgColorPanel = null!;
        private Panel footerBgColorPanel = null!;

        // 新增文本对齐方式控件
        private Panel headerTextAlignPanel = null!;
        private FlowLayoutPanel headerTextAlignLayout = null!;
        private ComboBox headerTextAlignCombo = null!;

        // 新增页码对齐方式控件
        private ComboBox headerPageNumAlignCombo = null!;

        // 新增文本对齐方式控件
        private Panel footerTextAlignPanel = null!;
        private FlowLayoutPanel footerTextAlignLayout = null!;
        private ComboBox footerTextAlignCombo = null!;

        // 新增页码对齐方式控件
        private ComboBox footerPageNumAlignCombo = null!;

        // 新增页码字体设置控件
        private ComboBox headerPageNumFontCombo = null!;
        private NumericUpDown headerPageNumSizeNumeric = null!;
        private Button headerPageNumColorButton = null!;
        private Panel headerPageNumColorPanel = null!;
        private Button headerPageNumBgButton = null!;
        private Panel headerPageNumBgPanel = null!;
        private CheckBox headerPageNumBoldCheck = null!;
        private CheckBox headerPageNumItalicCheck = null!;

        // 新增页脚页码字体设置控件
        private ComboBox footerPageNumFontCombo = null!;
        private NumericUpDown footerPageNumSizeNumeric = null!;
        private Button footerPageNumColorButton = null!;
        private Panel footerPageNumColorPanel = null!;
        private Button footerPageNumBgButton = null!;
        private Panel footerPageNumBgPanel = null!;
        private CheckBox footerPageNumBoldCheck = null!;
        private CheckBox footerPageNumItalicCheck = null!;

        public HeaderFooterForm(HeaderFooterSettings settings)
        {
            HeaderFooterSettings = settings ?? new HeaderFooterSettings();
            InitializeComponent();
            LoadSettings();
        }

        private void InitializeComponent()
        {
            this.Text = "页眉页脚设置";
            this.Size = new Size(700, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowIcon = false;
            this.ShowInTaskbar = false;

            // 创建主体布局
            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 1,
                RowCount = 3
            };

            // 创建选项卡控件
            mainTabControl = new TabControl
            {
                Dock = DockStyle.Fill
            };

            // 创建页眉和页脚选项卡
            headerTabPage = new TabPage("页眉设置");
            footerTabPage = new TabPage("页脚设置");

            // 创建页眉和页脚的内容
            var headerPanel = CreateHeaderPanel();
            var footerPanel = CreateFooterPanel();

            // 直接添加到选项卡，确保滚动事件能正确处理
            headerTabPage.AutoScroll = true;
            headerTabPage.Controls.Add(headerPanel);

            footerTabPage.AutoScroll = true;
            footerTabPage.Controls.Add(footerPanel);

            // 添加选项卡到控件
            mainTabControl.Controls.Add(headerTabPage);
            mainTabControl.Controls.Add(footerTabPage);

            // 创建通用选项组
            var optionsGroup = CreateOptionsGroup();

            // 创建按钮面板
            var buttonPanel = CreateButtonPanel();

            // 添加控件到主布局
            mainLayout.Controls.Add(mainTabControl, 0, 0);
            mainLayout.Controls.Add(optionsGroup, 0, 1);
            mainLayout.Controls.Add(buttonPanel, 0, 2);

            // 设置行高
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 75)); // 选项卡控件
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 120)); // 增加通用选项组高度
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 70));  // 增加按钮面板高度从50到70

            this.Controls.Add(mainLayout);
        }

        private Panel CreateHeaderPanel()
        {
            var panel = new Panel
            {
                AutoSize = true,
                Padding = new Padding(5),
                Width = 650,
                Dock = DockStyle.Top
            };

            var layout = new TableLayoutPanel
            {
                AutoSize = true,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(5),
                Width = 630,
                CellBorderStyle = TableLayoutPanelCellBorderStyle.Single
            };

            // 1. 文本内容区域
            var textGroupBox = new GroupBox
            {
                Text = "文本内容",
                AutoSize = false,
                Height = 300, // 增加高度，从250增加到300
                Padding = new Padding(8),
                Dock = DockStyle.Fill,
                Margin = new Padding(0),
                BackColor = SystemColors.Control,
                FlatStyle = FlatStyle.Standard
            };

            var textLayout = new TableLayoutPanel
            {
                AutoSize = false,
                Height = 270, // 增加高度，从220增加到270
                ColumnCount = 1,
                RowCount = 3,
                Margin = new Padding(2),
                Dock = DockStyle.Fill
            };

            // 1. 文本内容选项 - 第一行
            var headerContentGroup = new Panel
            {
                AutoSize = true,
                Dock = DockStyle.Fill
            };

            headerTextRadio = new RadioButton
            {
                Text = "使用文本内容",
                Checked = true,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };
            textLayout.Controls.Add(headerTextRadio, 0, 0);

            // 2. 文本内容 - 第二行
            headerTextBox = new TextBox
            {
                Dock = DockStyle.Fill,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Height = 70,
                Width = 580,
                Margin = new Padding(0, 5, 0, 10)
            };
            textLayout.Controls.Add(headerTextBox, 0, 1);

            // 3. 对齐方式和字体设置 - 第三行
            var formattingPanel = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                AutoSize = false,
                Height = 180, // 增加高度，从150增加到180
                Margin = new Padding(0, 5, 0, 0),
                Dock = DockStyle.Fill
            };

            // 字体设置部分
            var fontGroup = new GroupBox
            {
                Text = "字体设置",
                AutoSize = false,
                Height = 170, // 增加高度，从140增加到170
                Padding = new Padding(3),
                Dock = DockStyle.Fill,
                Margin = new Padding(0)
            };

            var fontLayout = new TableLayoutPanel
            {
                AutoSize = true,
                ColumnCount = 4,
                RowCount = 4,  // 增加到4行，添加对齐方式行
                Padding = new Padding(2),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 第一行：字体和字号
            fontLayout.Controls.Add(new Label { Text = "字体：", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Bottom }, 0, 0);
            headerFontNameCombo = new ComboBox
            {
                Width = 150,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom,
                DrawMode = DrawMode.OwnerDrawFixed
            };
            headerFontNameCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = headerFontNameCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            // 添加系统字体
            foreach (var fontFamily in System.Drawing.FontFamily.Families)
            {
                headerFontNameCombo.Items.Add(fontFamily.Name);
            }
            fontLayout.Controls.Add(headerFontNameCombo, 1, 0);

            fontLayout.Controls.Add(new Label { Text = "字号:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Bottom, Margin = new Padding(10, 0, 0, 0) }, 2, 0);

            headerFontSizeNumeric = new NumericUpDown
            {
                Minimum = 5,
                Maximum = 72,
                Value = 12,
                Width = 60,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom,
                TextAlign = HorizontalAlignment.Center
            };
            fontLayout.Controls.Add(headerFontSizeNumeric, 3, 0);

            // 第二行：字体颜色和背景色
            fontLayout.Controls.Add(new Label { Text = "颜色:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Bottom }, 0, 1);

            var colorLayout = new TableLayoutPanel
            {
                ColumnCount = 3,
                RowCount = 1,
                Margin = new Padding(0),
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            headerFontColorButton = new Button
            {
                Text = "选择",
                Width = 50,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };
            headerFontColorButton.Click += HeaderFontColorButton_Click;

            headerFontColorPanel = new Panel
            {
                BackColor = Color.Black,
                BorderStyle = BorderStyle.FixedSingle,
                Width = 30,
                Height = 20,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            var headerFontColorClear = new Button
            {
                Text = "清除",
                Width = 50,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };
            headerFontColorClear.Click += (s, e) => { headerFontColorPanel.BackColor = Color.Black; };

            colorLayout.Controls.Add(headerFontColorButton, 0, 0);
            colorLayout.Controls.Add(headerFontColorPanel, 1, 0);
            colorLayout.Controls.Add(headerFontColorClear, 2, 0);
            fontLayout.Controls.Add(colorLayout, 1, 1);

            fontLayout.Controls.Add(new Label { Text = "背景:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Bottom, Margin = new Padding(10, 0, 0, 0) }, 2, 1);

            var bgColorLayout = new TableLayoutPanel
            {
                ColumnCount = 3,
                RowCount = 1,
                Margin = new Padding(0),
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            var headerBgColorButton = new Button
            {
                Text = "选择",
                Width = 50,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            headerBgColorPanel = new Panel
            {
                BackColor = Color.Transparent,
                BorderStyle = BorderStyle.FixedSingle,
                Width = 30,
                Height = 20,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            var headerBgColorClear = new Button
            {
                Text = "清除",
                Width = 50,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            headerBgColorButton.Click += (s, e) => {
                using (ColorDialog dialog = new ColorDialog())
                {
                    dialog.Color = headerBgColorPanel.BackColor;
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        headerBgColorPanel.BackColor = dialog.Color;
                        HeaderFooterSettings.HeaderBackgroundColor = dialog.Color;
                    }
                }
            };

            headerBgColorClear.Click += (s, e) => {
                headerBgColorPanel.BackColor = Color.Transparent;
                HeaderFooterSettings.HeaderBackgroundColor = Color.Transparent;
            };

            bgColorLayout.Controls.Add(headerBgColorButton, 0, 0);
            bgColorLayout.Controls.Add(headerBgColorPanel, 1, 0);
            bgColorLayout.Controls.Add(headerBgColorClear, 2, 0);
            fontLayout.Controls.Add(bgColorLayout, 3, 1);

            // 第三行：对齐方式(添加到字体设置内)
            fontLayout.Controls.Add(new Label { Text = "对齐:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Bottom }, 0, 2);

            headerTextAlignCombo = new ComboBox {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom,
                DrawMode = DrawMode.OwnerDrawFixed
            };
            headerTextAlignCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = headerTextAlignCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            headerTextAlignCombo.Items.AddRange(new object[] { "左对齐", "居中对齐", "右对齐" });
            headerTextAlignCombo.SelectedIndex = HeaderFooterSettings.HeaderAlignment == AW.ParagraphAlignment.Left ? 0 :
                                               HeaderFooterSettings.HeaderAlignment == AW.ParagraphAlignment.Right ? 2 : 1;
            headerTextAlignCombo.SelectedIndexChanged += (s, e) => {
                switch (headerTextAlignCombo.SelectedIndex) {
                    case 0:
                        HeaderFooterSettings.HeaderAlignment = AW.ParagraphAlignment.Left;
                        break;
                    case 2:
                        HeaderFooterSettings.HeaderAlignment = AW.ParagraphAlignment.Right;
                        break;
                    default:
                        HeaderFooterSettings.HeaderAlignment = AW.ParagraphAlignment.Center;
                        break;
                }
            };
            fontLayout.Controls.Add(headerTextAlignCombo, 1, 2);

            // 保留headerTextAlignPanel和headerTextAlignLayout变量赋值，但不再使用它们
            headerTextAlignPanel = new Panel();
            headerTextAlignLayout = new FlowLayoutPanel();

            // 第四行：字体样式（粗体、斜体）
            headerBoldCheckBox = new CheckBox
            {
                Text = "粗体",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                Margin = new Padding(0, 5, 0, 0)
            };
            fontLayout.Controls.Add(headerBoldCheckBox, 1, 3);

            headerItalicCheckBox = new CheckBox
            {
                Text = "斜体",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                Margin = new Padding(10, 5, 0, 0)
            };
            fontLayout.Controls.Add(headerItalicCheckBox, 3, 3);

            // 设置列样式
            fontLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));  // 标签
            fontLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));  // 控件1
            fontLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));  // 标签2
            fontLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));  // 控件2

            // 设置行样式 - 均匀分布
            fontLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 字体字号行
            fontLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 颜色行
            fontLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 对齐方式行
            fontLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 粗体斜体行

            fontGroup.Controls.Add(fontLayout);
            formattingPanel.Controls.Add(fontGroup, 0, 0);

            // 设置列宽比例
            formattingPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 添加格式面板到文本区域
            textLayout.Controls.Add(formattingPanel, 0, 2);

            // 设置行高比例
            textLayout.RowStyles.Clear();
            textLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));   // 勾选框
            textLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));   // 文本框
            textLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 180));  // 格式设置 - 使用固定高度而非百分比

            textGroupBox.Controls.Add(textLayout);

            // 2. 新增页码区域
            var pageNumberGroupBox = new GroupBox
            {
                Text = "页码插入",
                AutoSize = true,
                Padding = new Padding(5),
                Dock = DockStyle.Top,
                Margin = new Padding(3), // 调整边距
                BackColor = SystemColors.Control, // 设置背景色
                FlatStyle = FlatStyle.Standard // 使用标准样式
            };

            var pageNumberLayout = new TableLayoutPanel
            {
                AutoSize = true,
                ColumnCount = 2,
                RowCount = 12 // 12行：选项、格式、包含总页数、前缀、后缀、字体格式设置、字体设置、字号设置、颜色设置、背景色设置、字体样式设置
            };

            // 页码选项
            headerPageNumberRadio = new RadioButton
            {
                Text = "使用页码",
                AutoSize = true
            };
            pageNumberLayout.Controls.Add(headerPageNumberRadio, 0, 0);

            // 页码格式
            var formatLabel = new Label
            {
                Text = "页码格式：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(formatLabel, 0, 1);

            headerPageNumberFormatCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(0, 5, 0, 5),
                DrawMode = DrawMode.OwnerDrawFixed
            };
            headerPageNumberFormatCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = headerPageNumberFormatCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            headerPageNumberFormatCombo.Items.AddRange(new object[] {
                "1, 2, 3, ...",
                "I, II, III, ...",
                "i, ii, iii, ...",
                "A, B, C, ...",
                "a, b, c, ..."
            });
            headerPageNumberFormatCombo.SelectedIndex = 0; // 默认数字格式
            pageNumberLayout.Controls.Add(headerPageNumberFormatCombo, 1, 1);

            // 添加页码对齐方式
            var pageNumAlignLabel = new Label
            {
                Text = "对齐方式：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(pageNumAlignLabel, 0, 2);

            headerPageNumAlignCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(0, 5, 0, 5),
                DrawMode = DrawMode.OwnerDrawFixed
            };
            headerPageNumAlignCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = headerPageNumAlignCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            headerPageNumAlignCombo.Items.AddRange(new object[] { "左对齐", "居中对齐", "右对齐" });
            headerPageNumAlignCombo.SelectedIndex = HeaderFooterSettings.HeaderAlignment == AW.ParagraphAlignment.Left ? 0 :
                                                  HeaderFooterSettings.HeaderAlignment == AW.ParagraphAlignment.Right ? 2 : 1;
            headerPageNumAlignCombo.SelectedIndexChanged += (s, e) => {
                switch (headerPageNumAlignCombo.SelectedIndex) {
                    case 0:
                        HeaderFooterSettings.HeaderAlignment = AW.ParagraphAlignment.Left;
                        break;
                    case 2:
                        HeaderFooterSettings.HeaderAlignment = AW.ParagraphAlignment.Right;
                        break;
                    default:
                        HeaderFooterSettings.HeaderAlignment = AW.ParagraphAlignment.Center;
                        break;
                }
            };
            pageNumberLayout.Controls.Add(headerPageNumAlignCombo, 1, 2);

            // 是否包含总页数
            headerIncludePageCountCheckBox = new CheckBox
            {
                Text = "包含总页数",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(headerIncludePageCountCheckBox, 0, 3);
            pageNumberLayout.SetColumnSpan(headerIncludePageCountCheckBox, 2);

            // 页码前缀
            var prefixLabel = new Label
            {
                Text = "页码前缀：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(prefixLabel, 0, 4);

            headerPageNumberPrefixTextBox = new TextBox
            {
                Width = 200,
                Margin = new Padding(0, 5, 0, 5),
                Text = "第",
                TextAlign = HorizontalAlignment.Center
            };
            pageNumberLayout.Controls.Add(headerPageNumberPrefixTextBox, 1, 4);

            // 页码后缀
            var suffixLabel = new Label
            {
                Text = "页码后缀：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(suffixLabel, 0, 5);

            headerPageNumberSuffixTextBox = new TextBox
            {
                Width = 200,
                Margin = new Padding(0, 5, 0, 5),
                Text = "页",
                TextAlign = HorizontalAlignment.Center
            };
            pageNumberLayout.Controls.Add(headerPageNumberSuffixTextBox, 1, 5);

            // 添加字体格式分隔行
            var fontSeparatorLabel = new Label
            {
                Text = "字体格式设置：",
                AutoSize = true,
                Font = new Font(this.Font, FontStyle.Bold),
                Margin = new Padding(0, 15, 0, 5)
            };
            pageNumberLayout.Controls.Add(fontSeparatorLabel, 0, 6);
            pageNumberLayout.SetColumnSpan(fontSeparatorLabel, 2);

            // 添加字体设置
            var pageNumFontLabel = new Label
            {
                Text = "字体：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(pageNumFontLabel, 0, 7);

            headerPageNumFontCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(0, 5, 0, 5),
                DrawMode = DrawMode.OwnerDrawFixed
            };
            headerPageNumFontCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = headerPageNumFontCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            // 添加系统字体
            foreach (var fontFamily in System.Drawing.FontFamily.Families)
            {
                headerPageNumFontCombo.Items.Add(fontFamily.Name);
            }
            // 默认选择与headerFontNameCombo相同的字体
            headerPageNumFontCombo.SelectedIndexChanged += (s, e) => {
                if (headerPageNumFontCombo.SelectedItem != null)
                    headerFontNameCombo.SelectedItem = headerPageNumFontCombo.SelectedItem;
            };
            pageNumberLayout.Controls.Add(headerPageNumFontCombo, 1, 7);

            // 添加字号设置
            var pageNumSizeLabel = new Label
            {
                Text = "字号：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(pageNumSizeLabel, 0, 8);

            headerPageNumSizeNumeric = new NumericUpDown
            {
                Minimum = 5,
                Maximum = 72,
                Value = (decimal)HeaderFooterSettings.FontSize,
                Width = 60,
                Margin = new Padding(0, 5, 0, 5),
                TextAlign = HorizontalAlignment.Center
            };
            headerPageNumSizeNumeric.ValueChanged += (s, e) => {
                headerFontSizeNumeric.Value = headerPageNumSizeNumeric.Value;
            };
            pageNumberLayout.Controls.Add(headerPageNumSizeNumeric, 1, 8);

            // 添加字体颜色设置
            var pageNumColorLabel = new Label
            {
                Text = "颜色：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(pageNumColorLabel, 0, 9);

            headerPageNumColorButton = new Button
            {
                Text = "选择",
                Width = 70
            };
            headerPageNumColorPanel = new Panel
            {
                BackColor = HeaderFooterSettings.FontColor,
                BorderStyle = BorderStyle.FixedSingle,
                Width = 30,
                Height = 20
            };

            var pageNumColorLayout = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            headerPageNumColorButton.Click += (s, e) => {
                using (ColorDialog dialog = new ColorDialog())
                {
                    dialog.Color = headerPageNumColorPanel.BackColor;
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        headerPageNumColorPanel.BackColor = dialog.Color;
                        headerFontColorPanel.BackColor = dialog.Color;
                        HeaderFooterSettings.FontColor = dialog.Color;
                    }
                }
            };

            pageNumColorLayout.Controls.Add(headerPageNumColorButton);
            pageNumColorLayout.Controls.Add(headerPageNumColorPanel);

            // 添加清除按钮
            var headerPageNumColorClear = new Button
            {
                Text = "清除",
                Width = 70,
                Margin = new Padding(5, 0, 0, 0)
            };
            headerPageNumColorClear.Click += (s, e) => {
                headerPageNumColorPanel.BackColor = Color.Transparent;
                headerFontColorPanel.BackColor = Color.Transparent;
                HeaderFooterSettings.FontColor = Color.Black; // 保持字体颜色为黑色，但面板显示为透明
            };
            pageNumColorLayout.Controls.Add(headerPageNumColorClear);

            pageNumberLayout.Controls.Add(pageNumColorLayout, 1, 9);

            // 添加背景色设置
            var pageNumBgLabel = new Label
            {
                Text = "背景色：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(pageNumBgLabel, 0, 10);

            headerPageNumBgButton = new Button
            {
                Text = "选择",
                Width = 70
            };
            headerPageNumBgPanel = new Panel
            {
                BackColor = HeaderFooterSettings.HeaderBackgroundColor ?? Color.Transparent,
                BorderStyle = BorderStyle.FixedSingle,
                Width = 30,
                Height = 20
            };

            var pageNumBgLayout = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            headerPageNumBgButton.Click += (s, e) => {
                using (ColorDialog dialog = new ColorDialog())
                {
                    dialog.Color = headerPageNumBgPanel.BackColor;
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        headerPageNumBgPanel.BackColor = dialog.Color;
                        headerBgColorPanel.BackColor = dialog.Color;
                        HeaderFooterSettings.HeaderBackgroundColor = dialog.Color;
                    }
                }
            };

            pageNumBgLayout.Controls.Add(headerPageNumBgButton);
            pageNumBgLayout.Controls.Add(headerPageNumBgPanel);

            // 添加清除按钮
            var headerPageNumBgClear = new Button
            {
                Text = "清除",
                Width = 70,
                Margin = new Padding(5, 0, 0, 0)
            };
            headerPageNumBgClear.Click += (s, e) => {
                headerPageNumBgPanel.BackColor = Color.Transparent;
                headerBgColorPanel.BackColor = Color.Transparent;
                HeaderFooterSettings.HeaderBackgroundColor = Color.Transparent;
            };
            pageNumBgLayout.Controls.Add(headerPageNumBgClear);

            pageNumberLayout.Controls.Add(pageNumBgLayout, 1, 10);

            // 添加字体样式设置
            var pageNumStylesLayout = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight,
                Margin = new Padding(20, 5, 0, 5)
            };

            headerPageNumBoldCheck = new CheckBox
            {
                Text = "粗体",
                AutoSize = true,
                Checked = HeaderFooterSettings.IsBold
            };
            headerPageNumBoldCheck.CheckedChanged += (s, e) => {
                headerBoldCheckBox.Checked = headerPageNumBoldCheck.Checked;
            };

            headerPageNumItalicCheck = new CheckBox
            {
                Text = "斜体",
                AutoSize = true,
                Checked = HeaderFooterSettings.IsItalic,
                Margin = new Padding(20, 0, 0, 0)
            };
            headerPageNumItalicCheck.CheckedChanged += (s, e) => {
                headerItalicCheckBox.Checked = headerPageNumItalicCheck.Checked;
            };

            pageNumStylesLayout.Controls.Add(headerPageNumBoldCheck);
            pageNumStylesLayout.Controls.Add(headerPageNumItalicCheck);
            pageNumberLayout.Controls.Add(pageNumStylesLayout, 0, 11);
            pageNumberLayout.SetColumnSpan(pageNumStylesLayout, 2);

            pageNumberGroupBox.Controls.Add(pageNumberLayout);

            // 3. 图片内容区域
            var imageGroupBox = new GroupBox
            {
                Text = "图片内容",
                AutoSize = true,
                Padding = new Padding(5),
                Dock = DockStyle.Top,
                Margin = new Padding(3), // 调整边距
                BackColor = SystemColors.Control, // 设置背景色
                FlatStyle = FlatStyle.Standard // 使用标准样式
            };

            var imageLayout = new TableLayoutPanel
            {
                AutoSize = true,
                ColumnCount = 2,
                RowCount = 3,
                Width = 550
            };

            // 图片选项
            headerImageRadio = new RadioButton
            {
                Text = "使用图片内容",
                AutoSize = true
            };
            imageLayout.Controls.Add(headerImageRadio, 0, 0);

            // 图片选择按钮
            headerImageButton = new Button
            {
                Text = "选择图片...",
                Width = 100,
                Height = 30,
                Enabled = false,
                Margin = new Padding(0, 5, 10, 5)
            };
            headerImageButton.Click += HeaderImageButton_Click;
            imageLayout.Controls.Add(headerImageButton, 0, 1);

            // 图片预览
            headerImagePreview = new PictureBox
            {
                SizeMode = PictureBoxSizeMode.Zoom,
                Height = 80,
                Width = 400,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(0, 5, 0, 5)
            };
            imageLayout.Controls.Add(headerImagePreview, 1, 1);

            // 设置对齐方式
            var alignmentLabel = new Label
            {
                Text = "对齐方式：",
                AutoSize = true,
                Margin = new Padding(0, 10, 0, 0)
            };
            imageLayout.Controls.Add(alignmentLabel, 0, 2);

            headerAlignmentCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120,
                Margin = new Padding(0, 10, 0, 0),
                DrawMode = DrawMode.OwnerDrawFixed
            };
            headerAlignmentCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = headerAlignmentCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            headerAlignmentCombo.Items.AddRange(new object[] { "左对齐", "居中对齐", "右对齐" });
            headerAlignmentCombo.SelectedIndex = 1; // 默认居中
            imageLayout.Controls.Add(headerAlignmentCombo, 1, 2);

            // 设置行样式
            imageLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            imageLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            imageLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            // 设置列样式
            imageLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            imageLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            imageGroupBox.Controls.Add(imageLayout);

            // 添加到主布局
            layout.Controls.Add(textGroupBox, 0, 0);
            layout.Controls.Add(pageNumberGroupBox, 0, 1);
            layout.Controls.Add(imageGroupBox, 0, 2);

            // 设置行样式
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            // 绑定事件 - 更新为包含页码选项
            headerTextRadio.Click += (s, e) => {
                headerPageNumberRadio.Checked = false;
                headerImageRadio.Checked = false;
                UpdateHeaderContentState();
            };
            headerPageNumberRadio.Click += (s, e) => {
                headerTextRadio.Checked = false;
                headerImageRadio.Checked = false;
                UpdateHeaderContentState();
            };
            headerImageRadio.Click += (s, e) => {
                headerTextRadio.Checked = false;
                headerPageNumberRadio.Checked = false;
                UpdateHeaderContentState();
            };

            panel.Controls.Add(layout);
            return panel;
        }

        private void UpdateHeaderContentState()
        {
            // 确保单选按钮互斥
            if (headerTextRadio.Checked)
            {
                headerPageNumberRadio.Checked = false;
                headerImageRadio.Checked = false;
            }
            else if (headerPageNumberRadio.Checked)
            {
                headerTextRadio.Checked = false;
                headerImageRadio.Checked = false;
            }
            else if (headerImageRadio.Checked)
            {
                headerTextRadio.Checked = false;
                headerPageNumberRadio.Checked = false;
            }

            // 文本内容状态
            bool textEnabled = headerTextRadio.Checked;
            headerTextBox.Enabled = textEnabled;
            headerFontNameCombo.Enabled = textEnabled || headerPageNumberRadio.Checked;
            headerFontSizeNumeric.Enabled = textEnabled || headerPageNumberRadio.Checked;
            headerBoldCheckBox.Enabled = textEnabled || headerPageNumberRadio.Checked;
            headerItalicCheckBox.Enabled = textEnabled || headerPageNumberRadio.Checked;
            headerFontColorButton.Enabled = textEnabled || headerPageNumberRadio.Checked;

            // 启用/禁用文本对齐
            headerTextAlignPanel.Enabled = textEnabled;

            // 页码内容状态
            bool pageNumberEnabled = headerPageNumberRadio.Checked;
            headerPageNumberFormatCombo.Enabled = pageNumberEnabled;
            headerIncludePageCountCheckBox.Enabled = pageNumberEnabled;
            headerPageNumberPrefixTextBox.Enabled = pageNumberEnabled;
            headerPageNumberSuffixTextBox.Enabled = pageNumberEnabled;
            // 启用/禁用页码对齐
            headerPageNumAlignCombo.Enabled = pageNumberEnabled;

            // 添加页码字体控件的状态更新
            if (headerPageNumFontCombo != null)
                headerPageNumFontCombo.Enabled = pageNumberEnabled;
            if (headerPageNumSizeNumeric != null)
                headerPageNumSizeNumeric.Enabled = pageNumberEnabled;
            if (headerPageNumColorButton != null)
                headerPageNumColorButton.Enabled = pageNumberEnabled;
            if (headerPageNumBgButton != null)
                headerPageNumBgButton.Enabled = pageNumberEnabled;
            if (headerPageNumBoldCheck != null)
                headerPageNumBoldCheck.Enabled = pageNumberEnabled;
            if (headerPageNumItalicCheck != null)
                headerPageNumItalicCheck.Enabled = pageNumberEnabled;

            // 图片内容状态
            bool imageEnabled = headerImageRadio.Checked;
            headerImageButton.Enabled = imageEnabled;
            headerImagePreview.Enabled = imageEnabled;
        }

        private Panel CreateFooterPanel()
        {
            var panel = new Panel
            {
                AutoSize = true,
                Padding = new Padding(5),
                Width = 600,
                Dock = DockStyle.Top
            };

            var layout = new TableLayoutPanel
            {
                AutoSize = true,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(5),
                Width = 580,
                CellBorderStyle = TableLayoutPanelCellBorderStyle.Single
            };

            // 1. 文本内容区域
            var textGroupBox = new GroupBox
            {
                Text = "文本内容",
                AutoSize = false,
                Height = 300, // 增加高度，从250增加到300
                Padding = new Padding(8),
                Dock = DockStyle.Fill,
                Margin = new Padding(0),
                BackColor = SystemColors.Control,
                FlatStyle = FlatStyle.Standard
            };

            var textLayout = new TableLayoutPanel
            {
                AutoSize = false,
                Height = 270, // 增加高度，从220增加到270
                ColumnCount = 1,
                RowCount = 3,
                Margin = new Padding(2),
                Dock = DockStyle.Fill
            };

            // 1. 文本内容选项 - 第一行
            var footerContentGroup = new Panel
            {
                AutoSize = true,
                Dock = DockStyle.Fill
            };

            footerTextRadio = new RadioButton
            {
                Text = "使用文本内容",
                Checked = true,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };
            textLayout.Controls.Add(footerTextRadio, 0, 0);

            // 2. 文本内容 - 第二行
            footerTextBox = new TextBox
            {
                Dock = DockStyle.Fill,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Height = 70,
                Width = 580,
                Margin = new Padding(0, 5, 0, 10)
            };
            textLayout.Controls.Add(footerTextBox, 0, 1);

            // 3. 对齐方式和字体设置 - 第三行
            var formattingPanel = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                AutoSize = false,
                Height = 180, // 增加高度，从150增加到180
                Margin = new Padding(0, 5, 0, 0),
                Dock = DockStyle.Fill
            };

            // 字体设置部分
            var fontGroup = new GroupBox
            {
                Text = "字体设置",
                AutoSize = false,
                Height = 170, // 增加高度，从140增加到170
                Padding = new Padding(3),
                Dock = DockStyle.Fill,
                Margin = new Padding(0)
            };

            var fontLayout = new TableLayoutPanel
            {
                AutoSize = true,
                ColumnCount = 4,
                RowCount = 4,  // 增加到4行，添加对齐方式行
                Padding = new Padding(2),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 第一行：字体和字号
            fontLayout.Controls.Add(new Label { Text = "字体：", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Bottom }, 0, 0);
            footerFontNameCombo = new ComboBox
            {
                Width = 150,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom,
                DrawMode = DrawMode.OwnerDrawFixed
            };
            footerFontNameCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = footerFontNameCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            // 添加系统字体
            foreach (var fontFamily in System.Drawing.FontFamily.Families)
            {
                footerFontNameCombo.Items.Add(fontFamily.Name);
            }
            fontLayout.Controls.Add(footerFontNameCombo, 1, 0);

            fontLayout.Controls.Add(new Label { Text = "字号:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Bottom, Margin = new Padding(10, 0, 0, 0) }, 2, 0);

            footerFontSizeNumeric = new NumericUpDown
            {
                Minimum = 5,
                Maximum = 72,
                Value = 12,
                Width = 60,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom,
                TextAlign = HorizontalAlignment.Center
            };
            fontLayout.Controls.Add(footerFontSizeNumeric, 3, 0);

            // 第二行：字体颜色和背景色
            fontLayout.Controls.Add(new Label { Text = "颜色:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Bottom }, 0, 1);

            var colorLayout = new TableLayoutPanel
            {
                ColumnCount = 3,
                RowCount = 1,
                Margin = new Padding(0),
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            footerFontColorButton = new Button
            {
                Text = "选择",
                Width = 50,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };
            footerFontColorButton.Click += FooterFontColorButton_Click;

            footerFontColorPanel = new Panel
            {
                BackColor = Color.Black,
                BorderStyle = BorderStyle.FixedSingle,
                Width = 30,
                Height = 20,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            var footerFontColorClear = new Button
            {
                Text = "清除",
                Width = 50,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };
            footerFontColorClear.Click += (s, e) => { footerFontColorPanel.BackColor = Color.Black; };

            colorLayout.Controls.Add(footerFontColorButton, 0, 0);
            colorLayout.Controls.Add(footerFontColorPanel, 1, 0);
            colorLayout.Controls.Add(footerFontColorClear, 2, 0);
            fontLayout.Controls.Add(colorLayout, 1, 1);

            fontLayout.Controls.Add(new Label { Text = "背景:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Bottom, Margin = new Padding(10, 0, 0, 0) }, 2, 1);

            var bgColorLayout = new TableLayoutPanel
            {
                ColumnCount = 3,
                RowCount = 1,
                Margin = new Padding(0),
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            var footerBgColorButton = new Button
            {
                Text = "选择",
                Width = 50,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            footerBgColorPanel = new Panel
            {
                BackColor = Color.Transparent,
                BorderStyle = BorderStyle.FixedSingle,
                Width = 30,
                Height = 20,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            var footerBgColorClear = new Button
            {
                Text = "清除",
                Width = 50,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom
            };

            footerBgColorButton.Click += (s, e) => {
                using (ColorDialog dialog = new ColorDialog())
                {
                    dialog.Color = footerBgColorPanel.BackColor;
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        footerBgColorPanel.BackColor = dialog.Color;
                        HeaderFooterSettings.FooterBackgroundColor = dialog.Color;
                    }
                }
            };

            footerBgColorClear.Click += (s, e) => {
                footerBgColorPanel.BackColor = Color.Transparent;
                HeaderFooterSettings.FooterBackgroundColor = Color.Transparent;
            };

            bgColorLayout.Controls.Add(footerBgColorButton, 0, 0);
            bgColorLayout.Controls.Add(footerBgColorPanel, 1, 0);
            bgColorLayout.Controls.Add(footerBgColorClear, 2, 0);
            fontLayout.Controls.Add(bgColorLayout, 3, 1);

            // 第三行：对齐方式(添加到字体设置内)
            fontLayout.Controls.Add(new Label { Text = "对齐:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Bottom }, 0, 2);

            footerTextAlignCombo = new ComboBox {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Anchor = AnchorStyles.Left | AnchorStyles.Bottom,
                DrawMode = DrawMode.OwnerDrawFixed
            };
            footerTextAlignCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = footerTextAlignCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            footerTextAlignCombo.Items.AddRange(new object[] { "左对齐", "居中对齐", "右对齐" });
            footerTextAlignCombo.SelectedIndex = HeaderFooterSettings.FooterAlignment == AW.ParagraphAlignment.Left ? 0 :
                                               HeaderFooterSettings.FooterAlignment == AW.ParagraphAlignment.Right ? 2 : 1;
            footerTextAlignCombo.SelectedIndexChanged += (s, e) => {
                switch (footerTextAlignCombo.SelectedIndex) {
                    case 0:
                        HeaderFooterSettings.FooterAlignment = AW.ParagraphAlignment.Left;
                        break;
                    case 2:
                        HeaderFooterSettings.FooterAlignment = AW.ParagraphAlignment.Right;
                        break;
                    default:
                        HeaderFooterSettings.FooterAlignment = AW.ParagraphAlignment.Center;
                        break;
                }
            };
            fontLayout.Controls.Add(footerTextAlignCombo, 1, 2);

            // 保留footerTextAlignPanel和footerTextAlignLayout变量赋值，但不再使用它们
            footerTextAlignPanel = new Panel();
            footerTextAlignLayout = new FlowLayoutPanel();

            // 第四行：字体样式（粗体、斜体）
            footerBoldCheckBox = new CheckBox
            {
                Text = "粗体",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                Margin = new Padding(0, 5, 0, 0)
            };
            fontLayout.Controls.Add(footerBoldCheckBox, 1, 3);

            footerItalicCheckBox = new CheckBox
            {
                Text = "斜体",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                Margin = new Padding(10, 5, 0, 0)
            };
            fontLayout.Controls.Add(footerItalicCheckBox, 3, 3);

            // 设置列样式
            fontLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));  // 标签
            fontLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));  // 控件1
            fontLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));  // 标签2
            fontLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));  // 控件2

            // 设置行样式 - 均匀分布
            fontLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 字体字号行
            fontLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 颜色行
            fontLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 对齐方式行
            fontLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 粗体斜体行

            fontGroup.Controls.Add(fontLayout);
            formattingPanel.Controls.Add(fontGroup, 0, 0);

            // 设置列宽比例
            formattingPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 添加格式面板到文本区域
            textLayout.Controls.Add(formattingPanel, 0, 2);

            // 设置行高比例
            textLayout.RowStyles.Clear();
            textLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));   // 勾选框
            textLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));   // 文本框
            textLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 180));  // 格式设置 - 使用固定高度而非百分比

            textGroupBox.Controls.Add(textLayout);

            // 2. 新增页码区域
            var pageNumberGroupBox = new GroupBox
            {
                Text = "页码插入",
                AutoSize = true,
                Padding = new Padding(5),
                Dock = DockStyle.Top,
                Margin = new Padding(3), // 调整边距
                BackColor = SystemColors.Control, // 设置背景色
                FlatStyle = FlatStyle.Standard // 使用标准样式
            };

            var pageNumberLayout = new TableLayoutPanel
            {
                AutoSize = true,
                ColumnCount = 2,
                RowCount = 12 // 12行：选项、格式、包含总页数、前缀、后缀、字体格式设置、字体设置、字号设置、颜色设置、背景色设置、字体样式设置
            };

            // 页码选项
            footerPageNumberRadio = new RadioButton
            {
                Text = "使用页码",
                AutoSize = true
            };
            pageNumberLayout.Controls.Add(footerPageNumberRadio, 0, 0);

            // 页码格式
            var formatLabel = new Label
            {
                Text = "页码格式：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(formatLabel, 0, 1);

            footerPageNumberFormatCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(0, 5, 0, 5),
                DrawMode = DrawMode.OwnerDrawFixed
            };
            footerPageNumberFormatCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = footerPageNumberFormatCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            footerPageNumberFormatCombo.Items.AddRange(new object[] {
                "1, 2, 3, ...",
                "I, II, III, ...",
                "i, ii, iii, ...",
                "A, B, C, ...",
                "a, b, c, ..."
            });
            footerPageNumberFormatCombo.SelectedIndex = 0; // 默认数字格式
            pageNumberLayout.Controls.Add(footerPageNumberFormatCombo, 1, 1);

            // 添加页码对齐方式
            var pageNumAlignLabel = new Label
            {
                Text = "对齐方式：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(pageNumAlignLabel, 0, 2);

            footerPageNumAlignCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(0, 5, 0, 5),
                DrawMode = DrawMode.OwnerDrawFixed
            };
            footerPageNumAlignCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = footerPageNumAlignCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            footerPageNumAlignCombo.Items.AddRange(new object[] { "左对齐", "居中对齐", "右对齐" });
            footerPageNumAlignCombo.SelectedIndex = HeaderFooterSettings.FooterAlignment == AW.ParagraphAlignment.Left ? 0 :
                                                  HeaderFooterSettings.FooterAlignment == AW.ParagraphAlignment.Right ? 2 : 1;
            footerPageNumAlignCombo.SelectedIndexChanged += (s, e) => {
                switch (footerPageNumAlignCombo.SelectedIndex) {
                    case 0:
                        HeaderFooterSettings.FooterAlignment = AW.ParagraphAlignment.Left;
                        break;
                    case 2:
                        HeaderFooterSettings.FooterAlignment = AW.ParagraphAlignment.Right;
                        break;
                    default:
                        HeaderFooterSettings.FooterAlignment = AW.ParagraphAlignment.Center;
                        break;
                }
            };
            pageNumberLayout.Controls.Add(footerPageNumAlignCombo, 1, 2);

            // 是否包含总页数
            footerIncludePageCountCheckBox = new CheckBox
            {
                Text = "包含总页数",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(footerIncludePageCountCheckBox, 0, 3);
            pageNumberLayout.SetColumnSpan(footerIncludePageCountCheckBox, 2);

            // 页码前缀
            var prefixLabel = new Label
            {
                Text = "页码前缀：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(prefixLabel, 0, 4);

            footerPageNumberPrefixTextBox = new TextBox
            {
                Width = 200,
                Margin = new Padding(0, 5, 0, 5),
                Text = "第",
                TextAlign = HorizontalAlignment.Center
            };
            pageNumberLayout.Controls.Add(footerPageNumberPrefixTextBox, 1, 4);

            // 页码后缀
            var suffixLabel = new Label
            {
                Text = "页码后缀：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(suffixLabel, 0, 5);

            footerPageNumberSuffixTextBox = new TextBox
            {
                Width = 200,
                Margin = new Padding(0, 5, 0, 5),
                Text = "页",
                TextAlign = HorizontalAlignment.Center
            };
            pageNumberLayout.Controls.Add(footerPageNumberSuffixTextBox, 1, 5);

            // 添加字体格式分隔行
            var footerFontSeparatorLabel = new Label
            {
                Text = "字体格式设置：",
                AutoSize = true,
                Font = new Font(this.Font, FontStyle.Bold),
                Margin = new Padding(0, 15, 0, 5)
            };
            pageNumberLayout.Controls.Add(footerFontSeparatorLabel, 0, 6);
            pageNumberLayout.SetColumnSpan(footerFontSeparatorLabel, 2);

            // 添加字体设置
            var footerPageNumFontLabel = new Label
            {
                Text = "字体：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(footerPageNumFontLabel, 0, 7);

            footerPageNumFontCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(0, 5, 0, 5),
                DrawMode = DrawMode.OwnerDrawFixed
            };
            footerPageNumFontCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = footerPageNumFontCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            // 添加系统字体
            foreach (var fontFamily in System.Drawing.FontFamily.Families)
            {
                footerPageNumFontCombo.Items.Add(fontFamily.Name);
            }
            // 默认选择与footerFontNameCombo相同的字体
            footerPageNumFontCombo.SelectedIndexChanged += (s, e) => {
                if (footerPageNumFontCombo.SelectedItem != null)
                    footerFontNameCombo.SelectedItem = footerPageNumFontCombo.SelectedItem;
            };
            pageNumberLayout.Controls.Add(footerPageNumFontCombo, 1, 7);

            // 添加字号设置
            var footerPageNumSizeLabel = new Label
            {
                Text = "字号：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(footerPageNumSizeLabel, 0, 8);

            footerPageNumSizeNumeric = new NumericUpDown
            {
                Minimum = 5,
                Maximum = 72,
                Value = (decimal)HeaderFooterSettings.FooterFontSize,
                Width = 60,
                Margin = new Padding(0, 5, 0, 5),
                TextAlign = HorizontalAlignment.Center
            };
            footerPageNumSizeNumeric.ValueChanged += (s, e) => {
                footerFontSizeNumeric.Value = footerPageNumSizeNumeric.Value;
            };
            pageNumberLayout.Controls.Add(footerPageNumSizeNumeric, 1, 8);

            // 添加字体颜色设置
            var footerPageNumColorLabel = new Label
            {
                Text = "颜色：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(footerPageNumColorLabel, 0, 9);

            footerPageNumColorButton = new Button
            {
                Text = "选择",
                Width = 70
            };
            footerPageNumColorPanel = new Panel
            {
                BackColor = HeaderFooterSettings.FooterFontColor,
                BorderStyle = BorderStyle.FixedSingle,
                Width = 30,
                Height = 20
            };

            var footerColorLayout = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            footerPageNumColorButton.Click += (s, e) => {
                using (ColorDialog dialog = new ColorDialog())
                {
                    dialog.Color = footerPageNumColorPanel.BackColor;
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        footerPageNumColorPanel.BackColor = dialog.Color;
                        footerFontColorPanel.BackColor = dialog.Color;
                        HeaderFooterSettings.FooterFontColor = dialog.Color;
                    }
                }
            };

            footerColorLayout.Controls.Add(footerPageNumColorButton);
            footerColorLayout.Controls.Add(footerPageNumColorPanel);

            // 添加清除按钮
            var footerPageNumColorClear = new Button
            {
                Text = "清除",
                Width = 70,
                Margin = new Padding(5, 0, 0, 0)
            };
            footerPageNumColorClear.Click += (s, e) => {
                footerPageNumColorPanel.BackColor = Color.Transparent;
                footerFontColorPanel.BackColor = Color.Transparent;
                HeaderFooterSettings.FooterFontColor = Color.Black; // 保持字体颜色为黑色，但面板显示为透明
            };
            footerColorLayout.Controls.Add(footerPageNumColorClear);

            pageNumberLayout.Controls.Add(footerColorLayout, 1, 9);

            // 添加背景色设置
            var footerPageNumBgLabel = new Label
            {
                Text = "背景色：",
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 5)
            };
            pageNumberLayout.Controls.Add(footerPageNumBgLabel, 0, 10);

            footerPageNumBgButton = new Button
            {
                Text = "选择",
                Width = 70
            };
            footerPageNumBgPanel = new Panel
            {
                BackColor = HeaderFooterSettings.FooterBackgroundColor ?? Color.Transparent,
                BorderStyle = BorderStyle.FixedSingle,
                Width = 30,
                Height = 20
            };

            var footerBgLayout = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            footerPageNumBgButton.Click += (s, e) => {
                using (ColorDialog dialog = new ColorDialog())
                {
                    dialog.Color = footerPageNumBgPanel.BackColor;
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        footerPageNumBgPanel.BackColor = dialog.Color;
                        footerBgColorPanel.BackColor = dialog.Color;
                        HeaderFooterSettings.FooterBackgroundColor = dialog.Color;
                    }
                }
            };

            footerBgLayout.Controls.Add(footerPageNumBgButton);
            footerBgLayout.Controls.Add(footerPageNumBgPanel);

            // 添加清除按钮
            var footerPageNumBgClear = new Button
            {
                Text = "清除",
                Width = 70,
                Margin = new Padding(5, 0, 0, 0)
            };
            footerPageNumBgClear.Click += (s, e) => {
                footerPageNumBgPanel.BackColor = Color.Transparent;
                footerBgColorPanel.BackColor = Color.Transparent;
                HeaderFooterSettings.FooterBackgroundColor = Color.Transparent;
            };
            footerBgLayout.Controls.Add(footerPageNumBgClear);

            pageNumberLayout.Controls.Add(footerBgLayout, 1, 10);

            // 添加字体样式设置
            var footerStylesLayout = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight,
                Margin = new Padding(20, 5, 0, 5)
            };

            footerPageNumBoldCheck = new CheckBox
            {
                Text = "粗体",
                AutoSize = true,
                Checked = HeaderFooterSettings.FooterIsBold
            };
            footerPageNumBoldCheck.CheckedChanged += (s, e) => {
                footerBoldCheckBox.Checked = footerPageNumBoldCheck.Checked;
            };

            footerPageNumItalicCheck = new CheckBox
            {
                Text = "斜体",
                AutoSize = true,
                Checked = HeaderFooterSettings.FooterIsItalic,
                Margin = new Padding(20, 0, 0, 0)
            };
            footerPageNumItalicCheck.CheckedChanged += (s, e) => {
                footerItalicCheckBox.Checked = footerPageNumItalicCheck.Checked;
            };

            footerStylesLayout.Controls.Add(footerPageNumBoldCheck);
            footerStylesLayout.Controls.Add(footerPageNumItalicCheck);
            pageNumberLayout.Controls.Add(footerStylesLayout, 0, 11);
            pageNumberLayout.SetColumnSpan(footerStylesLayout, 2);

            pageNumberGroupBox.Controls.Add(pageNumberLayout);

            // 3. 图片内容区域
            var imageGroupBox = new GroupBox
            {
                Text = "图片内容",
                AutoSize = true,
                Padding = new Padding(5),
                Dock = DockStyle.Top,
                Margin = new Padding(3), // 调整边距
                BackColor = SystemColors.Control, // 设置背景色
                FlatStyle = FlatStyle.Standard // 使用标准样式
            };

            var imageLayout = new TableLayoutPanel
            {
                AutoSize = true,
                ColumnCount = 2,
                RowCount = 3,
                Width = 550
            };

            // 图片选项
            footerImageRadio = new RadioButton
            {
                Text = "使用图片内容",
                AutoSize = true
            };
            imageLayout.Controls.Add(footerImageRadio, 0, 0);

            // 图片选择按钮
            footerImageButton = new Button
            {
                Text = "选择图片...",
                Width = 100,
                Height = 30,
                Enabled = false,
                Margin = new Padding(0, 5, 10, 5)
            };
            footerImageButton.Click += FooterImageButton_Click;
            imageLayout.Controls.Add(footerImageButton, 0, 1);

            // 图片预览
            footerImagePreview = new PictureBox
            {
                SizeMode = PictureBoxSizeMode.Zoom,
                Height = 80,
                Width = 400,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(0, 5, 0, 5)
            };
            imageLayout.Controls.Add(footerImagePreview, 1, 1);

            // 设置对齐方式
            var alignmentLabel = new Label
            {
                Text = "对齐方式：",
                AutoSize = true,
                Margin = new Padding(0, 10, 0, 0)
            };
            imageLayout.Controls.Add(alignmentLabel, 0, 2);

            footerAlignmentCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120,
                Margin = new Padding(0, 10, 0, 0),
                DrawMode = DrawMode.OwnerDrawFixed
            };
            footerAlignmentCombo.DrawItem += (s, e) => {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = footerAlignmentCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };
            footerAlignmentCombo.Items.AddRange(new object[] { "左对齐", "居中对齐", "右对齐" });
            footerAlignmentCombo.SelectedIndex = 1; // 默认居中
            imageLayout.Controls.Add(footerAlignmentCombo, 1, 2);

            // 设置行样式
            imageLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            imageLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            imageLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            // 设置列样式
            imageLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            imageLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            imageGroupBox.Controls.Add(imageLayout);

            // 添加到主布局
            layout.Controls.Add(textGroupBox, 0, 0);
            layout.Controls.Add(pageNumberGroupBox, 0, 1);
            layout.Controls.Add(imageGroupBox, 0, 2);

            // 设置行样式
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            // 绑定事件 - 更新为包含页码选项
            footerTextRadio.Click += (s, e) => {
                footerPageNumberRadio.Checked = false;
                footerImageRadio.Checked = false;
                UpdateFooterContentState();
            };
            footerPageNumberRadio.Click += (s, e) => {
                footerTextRadio.Checked = false;
                footerImageRadio.Checked = false;
                UpdateFooterContentState();
            };
            footerImageRadio.Click += (s, e) => {
                footerTextRadio.Checked = false;
                footerPageNumberRadio.Checked = false;
                UpdateFooterContentState();
            };

            panel.Controls.Add(layout);
            return panel;
        }

        private void UpdateFooterContentState()
        {
            // 确保单选按钮互斥
            if (footerTextRadio.Checked)
            {
                footerPageNumberRadio.Checked = false;
                footerImageRadio.Checked = false;
            }
            else if (footerPageNumberRadio.Checked)
            {
                footerTextRadio.Checked = false;
                footerImageRadio.Checked = false;
            }
            else if (footerImageRadio.Checked)
            {
                footerTextRadio.Checked = false;
                footerPageNumberRadio.Checked = false;
            }

            // 文本内容状态
            bool textEnabled = footerTextRadio.Checked;
            footerTextBox.Enabled = textEnabled;
            footerFontNameCombo.Enabled = textEnabled || footerPageNumberRadio.Checked;
            footerFontSizeNumeric.Enabled = textEnabled || footerPageNumberRadio.Checked;
            footerBoldCheckBox.Enabled = textEnabled || footerPageNumberRadio.Checked;
            footerItalicCheckBox.Enabled = textEnabled || footerPageNumberRadio.Checked;
            footerFontColorButton.Enabled = textEnabled || footerPageNumberRadio.Checked;

            // 启用/禁用文本对齐
            footerTextAlignPanel.Enabled = textEnabled;

            // 页码内容状态
            bool pageNumberEnabled = footerPageNumberRadio.Checked;
            footerPageNumberFormatCombo.Enabled = pageNumberEnabled;
            footerIncludePageCountCheckBox.Enabled = pageNumberEnabled;
            footerPageNumberPrefixTextBox.Enabled = pageNumberEnabled;
            footerPageNumberSuffixTextBox.Enabled = pageNumberEnabled;
            // 启用/禁用页码对齐
            footerPageNumAlignCombo.Enabled = pageNumberEnabled;

            // 添加页码字体控件的状态更新
            if (footerPageNumFontCombo != null)
                footerPageNumFontCombo.Enabled = pageNumberEnabled;
            if (footerPageNumSizeNumeric != null)
                footerPageNumSizeNumeric.Enabled = pageNumberEnabled;
            if (footerPageNumColorButton != null)
                footerPageNumColorButton.Enabled = pageNumberEnabled;
            if (footerPageNumBgButton != null)
                footerPageNumBgButton.Enabled = pageNumberEnabled;
            if (footerPageNumBoldCheck != null)
                footerPageNumBoldCheck.Enabled = pageNumberEnabled;
            if (footerPageNumItalicCheck != null)
                footerPageNumItalicCheck.Enabled = pageNumberEnabled;

            // 图片内容状态
            bool imageEnabled = footerImageRadio.Checked;
            footerImageButton.Enabled = imageEnabled;
            footerImagePreview.Enabled = imageEnabled;
        }

        private GroupBox CreateOptionsGroup()
        {
            var group = new GroupBox
            {
                Text = "通用设置",
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                BackColor = SystemColors.Control,
                FlatStyle = FlatStyle.Standard,
                MinimumSize = new Size(0, 118) // 设置最小高度
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(5),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 第一行：清除页眉和清除页脚选项
            var clearOptionsPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                Dock = DockStyle.Top, // 改为顶部对齐
                Padding = new Padding(5),
                AutoSize = true,
                BorderStyle = BorderStyle.None,
                Height = 35 // 设置固定高度
            };

            removeHeaderCheckBox = new CheckBox
            {
                Text = "清除页眉",
                AutoSize = true,
                Margin = new Padding(0, 0, 5, 0)
            };
            removeHeaderCheckBox.CheckedChanged += (s, e) =>
            {
                UpdateControlsState();
            };

            removeFooterCheckBox = new CheckBox
            {
                Text = "清除页脚",
                AutoSize = true,
                Margin = new Padding(30, 0, 0, 0)
            };
            removeFooterCheckBox.CheckedChanged += (s, e) =>
            {
                UpdateControlsState();
            };

            clearOptionsPanel.Controls.Add(removeHeaderCheckBox);
            clearOptionsPanel.Controls.Add(removeFooterCheckBox);
            layout.Controls.Add(clearOptionsPanel, 0, 0);

            // 第二行：启用页眉和启用页脚选项
            var enableOptionsPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                Dock = DockStyle.Top, // 改为顶部对齐
                Padding = new Padding(5),
                AutoSize = true,
                Margin = new Padding(2, 5, 0, 5),
                BorderStyle = BorderStyle.None,
                Height = 35 // 设置固定高度
            };

            enableHeaderCheckBox = new CheckBox
            {
                Text = "启用页眉设置",
                AutoSize = true,
                Margin = new Padding(0, 0, 10, 0)
            };
            enableHeaderCheckBox.CheckedChanged += (s, e) =>
            {
                UpdateControlsState();
            };

            enableFooterCheckBox = new CheckBox
            {
                Text = "启用页脚设置",
                AutoSize = true,
                Margin = new Padding(0, 0, 10, 0)
            };
            enableFooterCheckBox.CheckedChanged += (s, e) =>
            {
                UpdateControlsState();
            };

            // 添加首页不同选项
            differentFirstPageCheckBox = new CheckBox
            {
                Text = "首页使用不同页眉页脚",
                AutoSize = true,
                Checked = HeaderFooterSettings.DifferentFirstPage
            };
            differentFirstPageCheckBox.CheckedChanged += (s, e) =>
            {
                HeaderFooterSettings.DifferentFirstPage = differentFirstPageCheckBox.Checked;
            };

            enableOptionsPanel.Controls.Add(enableHeaderCheckBox);
            enableOptionsPanel.Controls.Add(enableFooterCheckBox);
            enableOptionsPanel.Controls.Add(differentFirstPageCheckBox);
            layout.Controls.Add(enableOptionsPanel, 0, 1);

            // 设置行样式
            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35)); // 第一行固定高度
            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35)); // 第二行固定高度

            group.Controls.Add(layout);
            return group;
        }

        private Panel CreateButtonPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Height = 60, // 增加高度从50到60
                Padding = new Padding(10)
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Right,
                AutoSize = true,
                ColumnCount = 2,
                RowCount = 1,
                Height = 45, // 增加高度
                Margin = new Padding(0, 10, 10, 0) // 增加顶部边距
            };

            // 确定按钮
            okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK,
                Width = 80,
                Height = 32,
                Anchor = AnchorStyles.Right
            };
            okButton.Click += OkButton_Click;

            // 取消按钮
            cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                Width = 80,
                Height = 32,
                Anchor = AnchorStyles.Right
            };

            // 添加到布局 - 交换位置：确定按钮在左边(0)，取消按钮在右边(1)
            layout.Controls.Add(okButton, 0, 0);
            layout.Controls.Add(cancelButton, 1, 0);

            // 设置列样式
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            // 添加列间距
            layout.SetColumnSpan(okButton, 1);
            layout.SetColumnSpan(cancelButton, 1);
            layout.ColumnStyles[0].SizeType = SizeType.AutoSize;
            layout.ColumnStyles[1].SizeType = SizeType.AutoSize;
            layout.Padding = new Padding(0, 0, 0, 0);
            layout.Margin = new Padding(0);
            layout.CellBorderStyle = TableLayoutPanelCellBorderStyle.None;

            // 设置间距 - 现在是确定按钮需要右边距
            okButton.Margin = new Padding(0, 0, 10, 0);

            panel.Controls.Add(layout);
            return panel;
        }

        private void HeaderImageButton_Click(object? sender, EventArgs e)
        {
            using (OpenFileDialog dialog = new OpenFileDialog())
            {
                dialog.Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif";
                dialog.Title = "选择页眉图片";

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        var image = Image.FromFile(dialog.FileName);
                        headerImagePreview.Image = image;
                        HeaderFooterSettings.HeaderImageData = System.IO.File.ReadAllBytes(dialog.FileName);
                        HeaderFooterSettings.HasHeaderImage = true;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"无法加载图片: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void FooterImageButton_Click(object? sender, EventArgs e)
        {
            using (OpenFileDialog dialog = new OpenFileDialog())
            {
                dialog.Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif";
                dialog.Title = "选择页脚图片";

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        var image = Image.FromFile(dialog.FileName);
                        footerImagePreview.Image = image;
                        HeaderFooterSettings.FooterImageData = System.IO.File.ReadAllBytes(dialog.FileName);
                        HeaderFooterSettings.HasFooterImage = true;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"无法加载图片: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void HeaderFontColorButton_Click(object? sender, EventArgs e)
        {
            using (ColorDialog dialog = new ColorDialog())
            {
                dialog.Color = headerFontColorPanel.BackColor;
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    headerFontColorPanel.BackColor = dialog.Color;
                    HeaderFooterSettings.FontColor = dialog.Color;
                }
            }
        }

        private void FooterFontColorButton_Click(object? sender, EventArgs e)
        {
            using (ColorDialog dialog = new ColorDialog())
            {
                dialog.Color = footerFontColorPanel.BackColor;
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    footerFontColorPanel.BackColor = dialog.Color;
                    HeaderFooterSettings.FooterFontColor = dialog.Color;
                }
            }
        }

        private void LoadSettings()
        {
            if (HeaderFooterSettings == null) return;

            // 加载页眉设置
            headerTextRadio.Checked = !HeaderFooterSettings.HasHeaderImage && !HeaderFooterSettings.HasHeaderPageNumber;
            headerPageNumberRadio.Checked = HeaderFooterSettings.HasHeaderPageNumber;
            headerImageRadio.Checked = HeaderFooterSettings.HasHeaderImage;
            headerTextBox.Text = HeaderFooterSettings.HeaderText;

            // 加载页眉页码设置
            if (HeaderFooterSettings.HeaderPageNumberFormat >= 0 &&
                HeaderFooterSettings.HeaderPageNumberFormat < headerPageNumberFormatCombo.Items.Count)
            {
                headerPageNumberFormatCombo.SelectedIndex = HeaderFooterSettings.HeaderPageNumberFormat;
            }
            headerIncludePageCountCheckBox.Checked = HeaderFooterSettings.HeaderIncludePageCount;
            headerPageNumberPrefixTextBox.Text = HeaderFooterSettings.HeaderPageNumberPrefix;
            headerPageNumberSuffixTextBox.Text = HeaderFooterSettings.HeaderPageNumberSuffix;

            // 加载页眉字体设置
            if (headerFontNameCombo.Items.Contains(HeaderFooterSettings.FontName))
                headerFontNameCombo.SelectedItem = HeaderFooterSettings.FontName;
            else if (headerFontNameCombo.Items.Count > 0)
                headerFontNameCombo.SelectedIndex = 0;

            headerFontSizeNumeric.Value = (decimal)HeaderFooterSettings.FontSize;
            headerBoldCheckBox.Checked = HeaderFooterSettings.IsBold;
            headerItalicCheckBox.Checked = HeaderFooterSettings.IsItalic;
            headerFontColorPanel.BackColor = HeaderFooterSettings.FontColor;
            if (HeaderFooterSettings.HeaderBackgroundColor.HasValue &&
                HeaderFooterSettings.HeaderBackgroundColor.Value != Color.Transparent)
            {
                headerBgColorPanel.BackColor = HeaderFooterSettings.HeaderBackgroundColor.Value;
            }
            else
            {
                headerBgColorPanel.BackColor = Color.Transparent;
            }

            // 加载对齐方式
            switch (HeaderFooterSettings.HeaderAlignment)
            {
                case AW.ParagraphAlignment.Left:
                    headerAlignmentCombo.SelectedIndex = 0;
                    break;
                case AW.ParagraphAlignment.Center:
                    headerAlignmentCombo.SelectedIndex = 1;
                    break;
                case AW.ParagraphAlignment.Right:
                    headerAlignmentCombo.SelectedIndex = 2;
                    break;
                default:
                    headerAlignmentCombo.SelectedIndex = 1; // 默认居中
                    break;
            }

            // 加载页眉图片
            if (HeaderFooterSettings.HeaderImageData != null)
            {
                try
                {
                    using (var ms = new System.IO.MemoryStream(HeaderFooterSettings.HeaderImageData))
                    {
                        headerImagePreview.Image = Image.FromStream(ms);
                        headerImagePreview.SizeMode = PictureBoxSizeMode.Zoom;
                    }
                }
                catch
                {
                    headerImagePreview.Image = null;
                }
            }

            // 加载页脚设置
            footerTextRadio.Checked = !HeaderFooterSettings.HasFooterImage && !HeaderFooterSettings.HasFooterPageNumber;
            footerPageNumberRadio.Checked = HeaderFooterSettings.HasFooterPageNumber;
            footerImageRadio.Checked = HeaderFooterSettings.HasFooterImage;
            footerTextBox.Text = HeaderFooterSettings.FooterText;

            // 加载页脚页码设置
            if (HeaderFooterSettings.FooterPageNumberFormat >= 0 &&
                HeaderFooterSettings.FooterPageNumberFormat < footerPageNumberFormatCombo.Items.Count)
            {
                footerPageNumberFormatCombo.SelectedIndex = HeaderFooterSettings.FooterPageNumberFormat;
            }
            footerIncludePageCountCheckBox.Checked = HeaderFooterSettings.FooterIncludePageCount;
            footerPageNumberPrefixTextBox.Text = HeaderFooterSettings.FooterPageNumberPrefix;
            footerPageNumberSuffixTextBox.Text = HeaderFooterSettings.FooterPageNumberSuffix;

            // 加载页脚字体设置
            if (footerFontNameCombo.Items.Contains(HeaderFooterSettings.FooterFontName))
                footerFontNameCombo.SelectedItem = HeaderFooterSettings.FooterFontName;
            else if (footerFontNameCombo.Items.Count > 0)
                footerFontNameCombo.SelectedIndex = 0;

            footerFontSizeNumeric.Value = (decimal)HeaderFooterSettings.FooterFontSize;
            footerBoldCheckBox.Checked = HeaderFooterSettings.FooterIsBold;
            footerItalicCheckBox.Checked = HeaderFooterSettings.FooterIsItalic;
            footerFontColorPanel.BackColor = HeaderFooterSettings.FooterFontColor;
            if (HeaderFooterSettings.FooterBackgroundColor.HasValue &&
                HeaderFooterSettings.FooterBackgroundColor.Value != Color.Transparent)
            {
                footerBgColorPanel.BackColor = HeaderFooterSettings.FooterBackgroundColor.Value;
            }
            else
            {
                footerBgColorPanel.BackColor = Color.Transparent;
            }

            // 加载对齐方式
            switch (HeaderFooterSettings.FooterAlignment)
            {
                case AW.ParagraphAlignment.Left:
                    footerAlignmentCombo.SelectedIndex = 0;
                    break;
                case AW.ParagraphAlignment.Center:
                    footerAlignmentCombo.SelectedIndex = 1;
                    break;
                case AW.ParagraphAlignment.Right:
                    footerAlignmentCombo.SelectedIndex = 2;
                    break;
                default:
                    footerAlignmentCombo.SelectedIndex = 1; // 默认居中
                    break;
            }

            // 加载页脚图片
            if (HeaderFooterSettings.FooterImageData != null)
            {
                try
                {
                    using (var ms = new System.IO.MemoryStream(HeaderFooterSettings.FooterImageData))
                    {
                        footerImagePreview.Image = Image.FromStream(ms);
                        footerImagePreview.SizeMode = PictureBoxSizeMode.Zoom;
                    }
                }
                catch
                {
                    footerImagePreview.Image = null;
                }
            }

            // 加载通用设置
            removeHeaderCheckBox.Checked = HeaderFooterSettings.RemoveHeader;
            removeFooterCheckBox.Checked = HeaderFooterSettings.RemoveFooter;
            enableHeaderCheckBox.Checked = HeaderFooterSettings.EnableHeader;
            enableFooterCheckBox.Checked = HeaderFooterSettings.EnableFooter;

            // 加载首页不同设置
            differentFirstPageCheckBox.Checked = HeaderFooterSettings.DifferentFirstPage;

            // 更新控件状态
            UpdateHeaderContentState();
            UpdateFooterContentState();
            UpdateControlsState();

            // 在加载页眉页码设置部分后添加
            // 加载页眉页码字体设置部分
            if (headerPageNumFontCombo != null && headerFontNameCombo.SelectedItem != null)
                headerPageNumFontCombo.SelectedItem = headerFontNameCombo.SelectedItem;
            if (headerPageNumSizeNumeric != null)
                headerPageNumSizeNumeric.Value = headerFontSizeNumeric.Value;
            if (headerPageNumColorPanel != null)
                headerPageNumColorPanel.BackColor = headerFontColorPanel.BackColor;
            if (headerPageNumBgPanel != null)
                headerPageNumBgPanel.BackColor = HeaderFooterSettings.HeaderBackgroundColor.GetValueOrDefault(Color.Transparent);
            if (headerPageNumBoldCheck != null)
                headerPageNumBoldCheck.Checked = HeaderFooterSettings.IsBold;
            if (headerPageNumItalicCheck != null)
                headerPageNumItalicCheck.Checked = HeaderFooterSettings.IsItalic;

            // 在加载页脚页码设置部分后添加
            // 加载页脚页码字体设置部分
            if (footerPageNumFontCombo != null && footerFontNameCombo.SelectedItem != null)
                footerPageNumFontCombo.SelectedItem = footerFontNameCombo.SelectedItem;
            if (footerPageNumSizeNumeric != null)
                footerPageNumSizeNumeric.Value = footerFontSizeNumeric.Value;
            if (footerPageNumColorPanel != null)
                footerPageNumColorPanel.BackColor = footerFontColorPanel.BackColor;
            if (footerPageNumBgPanel != null)
                footerPageNumBgPanel.BackColor = HeaderFooterSettings.FooterBackgroundColor.GetValueOrDefault(Color.Transparent);
            if (footerPageNumBoldCheck != null)
                footerPageNumBoldCheck.Checked = HeaderFooterSettings.FooterIsBold;
            if (footerPageNumItalicCheck != null)
                footerPageNumItalicCheck.Checked = HeaderFooterSettings.FooterIsItalic;
        }

        private void UpdateControlsState()
        {
            // 处理清除和启用选项的互斥关系
            bool removeHeader = removeHeaderCheckBox.Checked;
            bool removeFooter = removeFooterCheckBox.Checked;
            bool enableHeader = enableHeaderCheckBox.Checked;
            bool enableFooter = enableFooterCheckBox.Checked;

            // 当勾选清除页眉时，启用页眉设置应该不可用
            enableHeaderCheckBox.Enabled = !removeHeader;
            if (removeHeader)
            {
                enableHeaderCheckBox.Checked = false;
            }

            // 当勾选清除页脚时，启用页脚设置应该不可用
            enableFooterCheckBox.Enabled = !removeFooter;
            if (removeFooter)
            {
                enableFooterCheckBox.Checked = false;
            }

            // 当勾选启用页眉设置时，清除页眉选项应该不可用
            removeHeaderCheckBox.Enabled = !enableHeader;
            if (enableHeader)
            {
                removeHeaderCheckBox.Checked = false;
            }

            // 当勾选启用页脚设置时，清除页脚选项应该不可用
            removeFooterCheckBox.Enabled = !enableFooter;
            if (enableFooter)
            {
                removeFooterCheckBox.Checked = false;
            }

            // 控制页眉页脚选项卡的启用/禁用状态
            headerTabPage.Enabled = !removeHeader && enableHeader;
            footerTabPage.Enabled = !removeFooter && enableFooter;
        }

        private void OkButton_Click(object? sender, EventArgs e)
        {
            // 保存通用设置
            HeaderFooterSettings.RemoveHeader = removeHeaderCheckBox.Checked;
            HeaderFooterSettings.RemoveFooter = removeFooterCheckBox.Checked;
            HeaderFooterSettings.EnableHeader = enableHeaderCheckBox.Checked;
            HeaderFooterSettings.EnableFooter = enableFooterCheckBox.Checked;
            // DifferentFirstPage属性已在CheckedChanged事件中设置

            // 确保RemoveHeaderFooter属性与RemoveHeader和RemoveFooter保持一致
            // 只有当同时勾选了"清除页眉"和"清除页脚"时，才设置RemoveHeaderFooter为true
            HeaderFooterSettings.RemoveHeaderFooter = removeHeaderCheckBox.Checked && removeFooterCheckBox.Checked;

            // 保存页眉设置
            HeaderFooterSettings.HasHeader = headerTextRadio.Checked;
            HeaderFooterSettings.HasHeaderPageNumber = headerPageNumberRadio.Checked;
            HeaderFooterSettings.HasHeaderImage = headerImageRadio.Checked;
            HeaderFooterSettings.HeaderText = headerTextBox.Text;

            // 保存页眉页码设置
            HeaderFooterSettings.HeaderPageNumberFormat = headerPageNumberFormatCombo.SelectedIndex;
            HeaderFooterSettings.HeaderIncludePageCount = headerIncludePageCountCheckBox.Checked;
            HeaderFooterSettings.HeaderPageNumberPrefix = headerPageNumberPrefixTextBox.Text;
            HeaderFooterSettings.HeaderPageNumberSuffix = headerPageNumberSuffixTextBox.Text;

            // 保存页眉字体设置
            if (headerFontNameCombo.SelectedItem != null)
                HeaderFooterSettings.FontName = headerFontNameCombo.SelectedItem.ToString() ?? string.Empty;
            HeaderFooterSettings.FontSize = (double)headerFontSizeNumeric.Value;
            HeaderFooterSettings.IsBold = headerBoldCheckBox.Checked;
            HeaderFooterSettings.IsItalic = headerItalicCheckBox.Checked;
            HeaderFooterSettings.FontColor = headerFontColorPanel.BackColor;
            HeaderFooterSettings.HeaderBackgroundColor = headerBgColorPanel.BackColor;

            // 保存页眉页码字体设置
            // 注：使用现有的页眉字体设置，通过上面的绑定事件已经同步
            if (headerPageNumberRadio.Checked)
            {
                // 确保页码使用的字体设置与文本设置保持一致
                if (headerPageNumFontCombo != null && headerPageNumFontCombo.SelectedItem != null)
                    HeaderFooterSettings.FontName = headerPageNumFontCombo.SelectedItem.ToString() ?? string.Empty;
                if (headerPageNumSizeNumeric != null)
                    HeaderFooterSettings.FontSize = (double)headerPageNumSizeNumeric.Value;
                if (headerPageNumBoldCheck != null)
                    HeaderFooterSettings.IsBold = headerPageNumBoldCheck.Checked;
                if (headerPageNumItalicCheck != null)
                    HeaderFooterSettings.IsItalic = headerPageNumItalicCheck.Checked;
                if (headerPageNumColorPanel != null)
                    HeaderFooterSettings.FontColor = headerPageNumColorPanel.BackColor;
                if (headerPageNumBgPanel != null && headerPageNumBgPanel.BackColor != Color.Transparent)
                    HeaderFooterSettings.HeaderBackgroundColor = headerPageNumBgPanel.BackColor;
            }

            // 保存页眉对齐方式
            if (headerAlignmentCombo.SelectedIndex == 0)
                HeaderFooterSettings.HeaderAlignment = AW.ParagraphAlignment.Left;
            else if (headerAlignmentCombo.SelectedIndex == 1)
                HeaderFooterSettings.HeaderAlignment = AW.ParagraphAlignment.Center;
            else if (headerAlignmentCombo.SelectedIndex == 2)
                HeaderFooterSettings.HeaderAlignment = AW.ParagraphAlignment.Right;

            // 保存页脚设置
            HeaderFooterSettings.HasFooter = footerTextRadio.Checked;
            HeaderFooterSettings.HasFooterPageNumber = footerPageNumberRadio.Checked;
            HeaderFooterSettings.HasFooterImage = footerImageRadio.Checked;
            HeaderFooterSettings.FooterText = footerTextBox.Text;

            // 保存页脚页码设置
            HeaderFooterSettings.FooterPageNumberFormat = footerPageNumberFormatCombo.SelectedIndex;
            HeaderFooterSettings.FooterIncludePageCount = footerIncludePageCountCheckBox.Checked;
            HeaderFooterSettings.FooterPageNumberPrefix = footerPageNumberPrefixTextBox.Text;
            HeaderFooterSettings.FooterPageNumberSuffix = footerPageNumberSuffixTextBox.Text;

            // 保存页脚字体设置
            if (footerFontNameCombo.SelectedItem != null)
                HeaderFooterSettings.FooterFontName = footerFontNameCombo.SelectedItem.ToString() ?? string.Empty;
            HeaderFooterSettings.FooterFontSize = (double)footerFontSizeNumeric.Value;
            HeaderFooterSettings.FooterIsBold = footerBoldCheckBox.Checked;
            HeaderFooterSettings.FooterIsItalic = footerItalicCheckBox.Checked;
            HeaderFooterSettings.FooterFontColor = footerFontColorPanel.BackColor;
            HeaderFooterSettings.FooterBackgroundColor = footerBgColorPanel.BackColor;

            // 保存页脚页码字体设置
            // 注：使用现有的页脚字体设置，通过上面的绑定事件已经同步
            if (footerPageNumberRadio.Checked)
            {
                // 确保页码使用的字体设置与文本设置保持一致
                if (footerPageNumFontCombo != null && footerPageNumFontCombo.SelectedItem != null)
                    HeaderFooterSettings.FooterFontName = footerPageNumFontCombo.SelectedItem.ToString() ?? string.Empty;
                if (footerPageNumSizeNumeric != null)
                    HeaderFooterSettings.FooterFontSize = (double)footerPageNumSizeNumeric.Value;
                if (footerPageNumBoldCheck != null)
                    HeaderFooterSettings.FooterIsBold = footerPageNumBoldCheck.Checked;
                if (footerPageNumItalicCheck != null)
                    HeaderFooterSettings.FooterIsItalic = footerPageNumItalicCheck.Checked;
                if (footerPageNumColorPanel != null)
                    HeaderFooterSettings.FooterFontColor = footerPageNumColorPanel.BackColor;
                if (footerPageNumBgPanel != null && footerPageNumBgPanel.BackColor != Color.Transparent)
                    HeaderFooterSettings.FooterBackgroundColor = footerPageNumBgPanel.BackColor;
            }

            // 保存页脚对齐方式
            if (footerAlignmentCombo.SelectedIndex == 0)
                HeaderFooterSettings.FooterAlignment = AW.ParagraphAlignment.Left;
            else if (footerAlignmentCombo.SelectedIndex == 1)
                HeaderFooterSettings.FooterAlignment = AW.ParagraphAlignment.Center;
            else if (footerAlignmentCombo.SelectedIndex == 2)
                HeaderFooterSettings.FooterAlignment = AW.ParagraphAlignment.Right;

            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
}