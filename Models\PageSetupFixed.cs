/*
 * ========================================
 * 文件名: PageSetupFixed.cs
 * 功能描述: 页面设置数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义完整的页面设置配置选项
 * 2. 提供页面设置到Aspose.Words的转换方法
 * 3. 支持所有页面布局和格式设置
 * 4. 包含页面背景、边框、网格等高级设置
 * 5. 提供各功能模块的启用/禁用控制
 *
 * 核心设置分类:
 *
 * 基础页面设置:
 * - 页面边距（上下左右）
 * - 纸张方向（纵向/横向）
 * - 纸张大小（标准/自定义）
 * - 页眉页脚距离
 * - 装订线设置
 *
 * 高级布局设置:
 * - 分栏设置（栏数、间距）
 * - 文本方向和旋转
 * - 页面缩放和适应
 * - 镜像边距和双向文本
 * - 垂直对齐方式
 *
 * 视觉效果设置:
 * - 页面背景颜色和图片
 * - 页面边框样式和颜色
 * - 网格线显示和设置
 * - 艺术边框效果
 *
 * 行号和页码设置:
 * - 行号显示和格式
 * - 页码样式和起始值
 * - 章节和分节设置
 * - 双面打印设置
 *
 * 文档视图设置:
 * - 视图类型（页面布局、大纲等）
 * - 缩放类型和百分比
 * - 格式标记显示
 * - 页面边界显示
 *
 * 数据转换功能:
 * - ApplyTo(): 将设置应用到Aspose.Words.PageSetup
 * - 单位转换（厘米到点）
 * - 枚举值转换
 * - 颜色格式转换
 *
 * 启用控制:
 * - 每个功能都有对应的Enable开关
 * - 支持选择性应用设置
 * - 避免不必要的文档修改
 *
 * 注意事项:
 * - 支持Aspose.Words的所有页面设置选项
 * - 包含版本兼容性处理
 * - 提供完整的错误处理
 * - 支持复杂的页面布局需求
 */

using System;
using AW = Aspose.Words;
using System.Drawing;
using Aspose.Words.Settings;
using System.ComponentModel;

namespace AsposeWordFormatter.Models
{
    // This is a fixed version of the PageSetup class
    public class PageSetupFixed
    {
        // 边距（厘米）
        public double TopMargin { get; set; } = 3.70; // 默认1英寸
        public double BottomMargin { get; set; } = 3.50;
        public double LeftMargin { get; set; } = 2.80; // 默认1.25英寸
        public double RightMargin { get; set; } = 2.60;

        // 纸张方向
        public AW.Orientation Orientation { get; set; } = AW.Orientation.Portrait;

        // 纸张大小（厘米）
        public double PaperWidth { get; set; } = 21.0; // A4宽度
        public double PaperHeight { get; set; } = 29.7; // A4高度
        public AW.PaperSize PaperSize { get; set; } = AW.PaperSize.A4;
        public bool UseCustomPaperSize { get; set; } = false;
        public double CustomWidth { get; set; } = 595.0; // A4 width in points
        public double CustomHeight { get; set; } = 842.0; // A4 height in points

        // 页眉和页脚
        public double HeaderDistance { get; set; } = 1.25; // 默认页眉距顶部1.25厘米
        public double FooterDistance { get; set; } = 1.25; // 默认页脚距底部1.25厘米

        // 分节设置
        public AW.SectionStart SectionStart { get; set; } = AW.SectionStart.Continuous;

        // 装订线设置
        public bool MirrorMargins { get; set; } = false;
        public bool RtlGutter { get; set; } = false; // 装订线位置是否在顶部（默认在左侧）
        public double Gutter { get; set; } = 0; // 装订线宽度（厘米）

        // 文本方向
        public AW.TextOrientation TextOrientation { get; set; } = AW.TextOrientation.Horizontal;

        // 行号设置
        public AW.LineNumberRestartMode LineNumberRestartMode { get; set; } = AW.LineNumberRestartMode.RestartPage;
        public int LineNumberCountBy { get; set; } = 1;
        public double LineNumberDistanceFromText { get; set; } = 0.5; // 厘米
        public int LineStartingNumber { get; set; } = 1;
        public bool LineNumberingIsActive { get; set; } = false; // 是否启用行号
        public int LineNumberingStartValue { get; set; } = 1; // 行号起始值
        public int LineNumberingCountBy { get; set; } = 1; // 行号计数间隔
        public double LineNumberingDistance { get; set; } = 0.5; // 行号与文本的距离（厘米）
        public AW.LineNumberRestartMode LineNumberingRestartMode { get; set; } = AW.LineNumberRestartMode.RestartPage; // 行号重启模式

        // 页码设置
        public AW.NumberStyle PageNumberStyle { get; set; } = AW.NumberStyle.Arabic;
        public bool RestartPageNumbering { get; set; } = false;
        public int PageStartingNumber { get; set; } = 1;

        // 章节设置
        public int HeadingLevelForChapter { get; set; } = 0;
        public AW.ChapterPageSeparator ChapterPageSeparator { get; set; } = AW.ChapterPageSeparator.Hyphen;

        // 双面打印设置
        public int SheetsPerBooklet { get; set; } = 0;

        // 脚注和尾注设置（新增）
        public bool SuppressEndnotes { get; set; } = false; // 隐藏尾注
        public int FirstPageTray { get; set; } = 0; // 第一页纸盒
        public int OtherPagesTray { get; set; } = 0; // 其他页纸盒

        // 脚注选项设置（新增）
        public AW.Notes.FootnotePosition FootnotePosition { get; set; } = AW.Notes.FootnotePosition.BottomOfPage;
        public AW.NumberStyle FootnoteNumberStyle { get; set; } = AW.NumberStyle.Arabic;
        public int FootnoteStartNumber { get; set; } = 1;
        public AW.Notes.FootnoteNumberingRule FootnoteRestartRule { get; set; } = AW.Notes.FootnoteNumberingRule.Continuous;
        public int FootnoteColumns { get; set; } = 0; // 0表示自动

        // 尾注选项设置（新增）
        public AW.Notes.EndnotePosition EndnotePosition { get; set; } = AW.Notes.EndnotePosition.EndOfDocument;
        public AW.NumberStyle EndnoteNumberStyle { get; set; } = AW.NumberStyle.LowercaseRoman;
        public int EndnoteStartNumber { get; set; } = 1;
        public AW.Notes.FootnoteNumberingRule EndnoteRestartRule { get; set; } = AW.Notes.FootnoteNumberingRule.Continuous;

        // 分栏高级设置（新增）
        public bool LineBetween { get; set; } = false; // 分栏间是否有分隔线
        public double SeparatorLineWidth { get; set; } = 0.75; // 分隔线宽度（磅）

        [System.Text.Json.Serialization.JsonIgnore]
        public Color SeparatorLineColor { get; set; } = Color.Black; // 分隔线颜色

        // 分隔线颜色序列化辅助属性
        [Browsable(false)]
        public string SeparatorLineColorHex
        {
            get => SeparatorLineColor != Color.Black ? ColorTranslator.ToHtml(SeparatorLineColor) : string.Empty;
            set
            {
                if (string.IsNullOrEmpty(value))
                    SeparatorLineColor = Color.Black;
                else
                    SeparatorLineColor = ColorTranslator.FromHtml(value);
            }
        }

        // 节设置（新增）
        public AW.SectionStart SectionStartType { get; set; } = AW.SectionStart.NewPage;
        public bool DifferentFirstPageHeaderFooter { get; set; } = false;
        public bool OddAndEvenPagesHeaderFooter { get; set; } = false;

        // 垂直对齐设置（新增）
        public AW.PageVerticalAlignment VerticalAlignment { get; set; } = AW.PageVerticalAlignment.Top;

        // 多页设置（新增）
        public AW.Settings.MultiplePagesType MultiplePages { get; set; } = AW.Settings.MultiplePagesType.Normal;
        public int SheetsPerBookletCustom { get; set; } = 4; // 书籍折页时每册页数

        // 段落格式
        public ParagraphFormat ParagraphFormat { get; set; } = new ParagraphFormat
        {
            Alignment = ParagraphAlignment.Left,
            OutlineLevel = AW.OutlineLevel.BodyText,
            Bidi = false,
            FirstLineIndent = 0,
            RightIndent = 0,
            LeftIndent = 0,
            SpaceBefore = 0,
            SpaceAfter = 0,
            LineSpacingRule = AW.LineSpacingRule.Multiple,
            LineSpacing = 1.5
        };

        // 字体格式
        public FontFormat FontFormat { get; set; } = new FontFormat
        {
            FontName = "宋体",
            FontSize = 12,
            FontColor = Color.Black,
            Bold = false,
            Italic = false
        };

        // 背景设置
        [System.Text.Json.Serialization.JsonIgnore]
        public Color BackgroundColor { get; set; } = Color.Transparent;

        // 背景颜色序列化辅助属性
        [Browsable(false)]
        public string BackgroundColorHex
        {
            get => BackgroundColor != Color.Transparent ? ColorTranslator.ToHtml(BackgroundColor) : string.Empty;
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    BackgroundColor = Color.Transparent;
                }
                else
                {
                    try
                    {
                        var color = ColorTranslator.FromHtml(value);
                        // 防止设置黑色背景，避免文档背景变黑
                        if (color.ToArgb() == Color.Black.ToArgb())
                        {
                            Console.WriteLine("警告：尝试设置黑色背景，已自动调整为透明色以避免文档背景变黑");
                            BackgroundColor = Color.Transparent;
                        }
                        else
                        {
                            BackgroundColor = color;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解析背景颜色时出错: {ex.Message}，使用透明色");
                        BackgroundColor = Color.Transparent;
                    }
                }
            }
        }

        public string? BackgroundImage { get; set; } = null;
        public DisplayMode BackgroundImageDisplayMode { get; set; } = DisplayMode.Tiled;

        // 页面边框设置
        public BorderLineStyle BorderStyle { get; set; } = BorderLineStyle.None;

        [System.Text.Json.Serialization.JsonIgnore]
        public Color BorderColor { get; set; } = Color.Black;

        // 边框颜色序列化辅助属性
        [Browsable(false)]
        public string BorderColorHex
        {
            get => BorderColor != Color.Black ? ColorTranslator.ToHtml(BorderColor) : string.Empty;
            set
            {
                if (string.IsNullOrEmpty(value))
                    BorderColor = Color.Black;
                else
                    BorderColor = ColorTranslator.FromHtml(value);
            }
        }

        public double BorderWidth { get; set; } = 0.5; // 厘米
        public int BorderArtStyle { get; set; } = 0; // 0表示无艺术效果
        public bool ApplyBorderTop { get; set; } = true;
        public bool ApplyBorderLeft { get; set; } = true;
        public bool ApplyBorderBottom { get; set; } = true;
        public bool ApplyBorderRight { get; set; } = true;
        public double BorderDistanceFromTop { get; set; } = 24; // 点
        public double BorderDistanceFromLeft { get; set; } = 24; // 点
        public double BorderDistanceFromBottom { get; set; } = 24; // 点
        public double BorderDistanceFromRight { get; set; } = 24; // 点

        // 边框高级属性（新增）
        public bool BorderAlwaysInFront { get; set; } = false; // 边框总在前面
        public AW.PageBorderAppliesTo BorderAppliesTo { get; set; } = AW.PageBorderAppliesTo.AllPages; // 边框应用范围
        public AW.PageBorderDistanceFrom BorderDistanceFrom { get; set; } = AW.PageBorderDistanceFrom.Text; // 边框距离测量起点
        public bool BorderSurroundsHeader { get; set; } = true; // 边框包围页眉
        public bool BorderSurroundsFooter { get; set; } = true; // 边框包围页脚

        // 页面网格设置
        public bool ShowGridLines { get; set; } = false;

        [System.Text.Json.Serialization.JsonIgnore]
        public Color GridLinesColor { get; set; } = Color.LightGray;

        // 网格线颜色序列化辅助属性
        [Browsable(false)]
        public string GridLinesColorHex
        {
            get => GridLinesColor != Color.LightGray ? ColorTranslator.ToHtml(GridLinesColor) : string.Empty;
            set
            {
                if (string.IsNullOrEmpty(value))
                    GridLinesColor = Color.LightGray;
                else
                    GridLinesColor = ColorTranslator.FromHtml(value);
            }
        }

        public double HorizontalGridSpacing { get; set; } = 1.0; // 厘米
        public double VerticalGridSpacing { get; set; } = 1.0; // 厘米
        public bool SnapToGrid { get; set; } = false;
        public int SnapToGridDivision { get; set; } = 1;

        // 文档网格布局属性（新增）
        public int CharactersPerLine { get; set; } = 0; // 每行字符数，0表示不限制
        public int LinesPerPage { get; set; } = 0; // 每页行数，0表示不限制
        public AW.SectionLayoutMode LayoutMode { get; set; } = AW.SectionLayoutMode.Default; // 布局模式

        // 页面布局增强设置
        // 分栏设置
        public int ColumnCount { get; set; } = 1;
        public double ColumnSpacing { get; set; } = 1.27; // 厘米
        public bool EvenlySpaced { get; set; } = true;

        // 页面大小缩放
        public int ScalePercentage { get; set; } = 100; // 百分比
        public bool ScaleToFitPages { get; set; } = false;
        public int ScaleToPageCount { get; set; } = 1;

        // 页面镜像
        public int Bidi { get; set; } = 0; // 0: 从左到右, 1: 从右到左

        // 文档视图设置
        public AW.Settings.ViewType ViewType { get; set; } = AW.Settings.ViewType.PageLayout;
        public AW.Settings.ZoomType ZoomType { get; set; } = AW.Settings.ZoomType.None;
        public int ZoomPercent { get; set; } = 100;
        public bool ShowFormatMarks { get; set; } = false;
        public bool DoNotDisplayPageBoundaries { get; set; } = false;
        public bool DisplayBackgroundShape { get; set; } = true;

        // 各个功能的启用标志
        public bool EnableMargins { get; set; } = true;
        public bool EnableOrientation { get; set; } = true;
        public bool EnablePaperSize { get; set; } = true;
        public bool EnableGutter { get; set; } = false;
        public bool EnableHeaderFooterDistance { get; set; } = false;
        public bool EnableMarginPresets { get; set; } = false;
        public bool EnableMarginValidation { get; set; } = false;
        public bool EnableBackgroundColor { get; set; } = false;
        public bool EnableBackgroundImage { get; set; } = false;
        public bool EnableBorderStyle { get; set; } = false;
        public bool EnableBorderColor { get; set; } = false;
        public bool EnableBorderWidth { get; set; } = false;
        public bool EnableBorderArt { get; set; } = false;
        public bool EnableGridColor { get; set; } = false;
        public bool EnableGridSpacing { get; set; } = false;
        public bool EnableSnapToGrid { get; set; } = false;
        public bool EnableColumns { get; set; } = false;
        public bool EnableScaling { get; set; } = false;
        public bool EnableTextOrientation { get; set; } = false;
        public bool EnableMirrorMargins { get; set; } = false;
        public bool EnableBidi { get; set; } = false;
        public bool EnableViewType { get; set; } = false;
        public bool EnableZoom { get; set; } = false;
        public bool EnableFormatMarks { get; set; } = false;

        // 新增功能的启用标志
        public bool EnableCharactersPerLine { get; set; } = false;
        public bool EnableLinesPerPage { get; set; } = false;
        public bool EnableLayoutMode { get; set; } = false;
        public bool EnableBorderAdvanced { get; set; } = false;
        public bool EnableFootnoteEndnote { get; set; } = false;
        public bool EnablePrintSettings { get; set; } = false;

        // 脚注尾注选项的启用标志（新增）
        public bool EnableFootnoteOptions { get; set; } = false;
        public bool EnableEndnoteOptions { get; set; } = false;

        // 分栏高级设置的启用标志（新增）
        public bool EnableColumnAdvanced { get; set; } = false;

        // 节设置的启用标志（新增）
        public bool EnableSectionSettings { get; set; } = false;

        // 垂直对齐的启用标志（新增）
        public bool EnableVerticalAlignment { get; set; } = false;

        // 多页设置的启用标志（新增）
        public bool EnableMultiplePages { get; set; } = false;

        // 章节设置的启用标志（新增）
        public bool EnableChapterSettings { get; set; } = false;

        /// <summary>
        /// 边框样式
        /// </summary>
        public enum BorderLineStyle
        {
            None,
            Single,
            Double,
            Thick,
            Dotted,
            Dashed,
            DotDash,
            DotDotDash,
            Triple,
            ThinThickThinSmallGap,
            ThickThinMediumGap,
            ThinThickLargeGap,
            ThinThickThinLargeGap,
            Wave,
            DoubleWave,
            ThickThinSmallGap,
            ThickThinThickSmallGap,
            ThinThickMediumGap,
            ThinThickThinMediumGap
        }

        /// <summary>
        /// 背景图片显示模式
        /// </summary>
        public enum DisplayMode
        {
            Tiled,      // 平铺
            Centered,   // 居中
            Stretched   // 拉伸
        }

        /// <summary>
        /// 将当前页面设置应用到Aspose.Words.PageSetup对象
        /// </summary>
        /// <param name="pageSetup">要应用设置的Aspose.Words.PageSetup对象</param>
        public void ApplyTo(AW.PageSetup pageSetup)
        {
            if (pageSetup == null)
                throw new ArgumentNullException(nameof(pageSetup));

            try
            {
                // 设置边距（转换为点）
                pageSetup.TopMargin = CmToPoints(TopMargin);
                pageSetup.BottomMargin = CmToPoints(BottomMargin);
                pageSetup.LeftMargin = CmToPoints(LeftMargin);
                pageSetup.RightMargin = CmToPoints(RightMargin);

                // 设置纸张方向
                pageSetup.Orientation = Orientation;

                // 设置纸张大小
                pageSetup.PaperSize = PaperSize;
                if (PaperSize == AW.PaperSize.Custom)
                {
                    pageSetup.PageWidth = CmToPoints(PaperWidth);
                    pageSetup.PageHeight = CmToPoints(PaperHeight);
                }

                // 设置页眉和页脚
                pageSetup.HeaderDistance = CmToPoints(HeaderDistance);
                pageSetup.FooterDistance = CmToPoints(FooterDistance);
                pageSetup.DifferentFirstPageHeaderFooter = DifferentFirstPageHeaderFooter;
                pageSetup.OddAndEvenPagesHeaderFooter = OddAndEvenPagesHeaderFooter;

                // 设置分节
                pageSetup.SectionStart = SectionStart;

                // 设置垂直对齐（新增）
                if (EnableVerticalAlignment)
                {
                    pageSetup.VerticalAlignment = VerticalAlignment;
                }

                // 设置装订线
                pageSetup.Gutter = CmToPoints(Gutter);
                pageSetup.RtlGutter = RtlGutter; // 直接设置RtlGutter属性

                // 设置镜像边距/多页设置（新增）
                if (EnableMultiplePages)
                {
                    pageSetup.MultiplePages = MultiplePages;

                    // 如果是书籍折页，设置每册页数
                    if (MultiplePages == AW.Settings.MultiplePagesType.BookFoldPrinting)
                    {
                        pageSetup.SheetsPerBooklet = SheetsPerBookletCustom;
                    }
                }

                // 设置文本方向
                pageSetup.TextOrientation = TextOrientation;

                // 设置行号
                pageSetup.LineNumberRestartMode = LineNumberRestartMode;
                pageSetup.LineNumberCountBy = LineNumberCountBy;
                pageSetup.LineNumberDistanceFromText = CmToPoints(LineNumberDistanceFromText);
                pageSetup.LineStartingNumber = LineStartingNumber;

                // 设置新增的行号属性
                if (LineNumberingIsActive)
                {
                    // 直接设置到 PageSetup 对象
                    pageSetup.LineNumberRestartMode = LineNumberingRestartMode;
                    pageSetup.LineStartingNumber = LineNumberingStartValue;
                    pageSetup.LineNumberCountBy = LineNumberingCountBy;
                    pageSetup.LineNumberDistanceFromText = CmToPoints(LineNumberingDistance);
                }
                else
                {
                    // 设置默认值或禁用行号
                    pageSetup.LineNumberRestartMode = AW.LineNumberRestartMode.RestartPage;
                    pageSetup.LineStartingNumber = 1;
                    pageSetup.LineNumberCountBy = 0; // 0表示禁用行号
                    pageSetup.LineNumberDistanceFromText = CmToPoints(0.5);
                }

                // 设置页码
                pageSetup.PageNumberStyle = PageNumberStyle;
                pageSetup.RestartPageNumbering = RestartPageNumbering;
                pageSetup.PageStartingNumber = PageStartingNumber;

                // 设置章节（新增启用控制）
                if (EnableChapterSettings)
                {
                    pageSetup.HeadingLevelForChapter = HeadingLevelForChapter;
                    pageSetup.ChapterPageSeparator = ChapterPageSeparator;
                }

                // 设置双面打印
                pageSetup.SheetsPerBooklet = SheetsPerBooklet;

                // 设置分栏
                pageSetup.TextColumns.SetCount(ColumnCount);
                pageSetup.TextColumns.Spacing = CmToPoints(ColumnSpacing);
                pageSetup.TextColumns.EvenlySpaced = EvenlySpaced;

                // 设置文档网格布局属性（新增）
                if (EnableCharactersPerLine)
                {
                    pageSetup.CharactersPerLine = CharactersPerLine;
                }

                if (EnableLinesPerPage)
                {
                    pageSetup.LinesPerPage = LinesPerPage;
                }

                if (EnableLayoutMode)
                {
                    pageSetup.LayoutMode = LayoutMode;
                }

                // 设置缩放
                if (ScaleToFitPages)
                {
                    // 当使用适应页数缩放时
                    if (!EnableLayoutMode) // 只有在没有手动设置LayoutMode时才自动设置
                    {
                        pageSetup.LayoutMode = AW.SectionLayoutMode.LineGrid;
                    }
                    if (!EnableCharactersPerLine) // 只有在没有手动设置时才自动计算
                    {
                        pageSetup.CharactersPerLine = 0; // 自动计算
                    }
                    if (!EnableLinesPerPage) // 只有在没有手动设置时才自动计算
                    {
                        pageSetup.LinesPerPage = 0; // 自动计算
                    }
                }
                else
                {
                    // 使用百分比缩放 - 检查API兼容性
                    // 某些版本的Aspose.Words可能没有TextScaling属性
                    try
                    {
                        // 尝试使用反射设置TextScaling
                        var prop = typeof(AW.PageSetup).GetProperty("TextScaling");
                        if (prop != null)
                        {
                            prop.SetValue(pageSetup, ScalePercentage);
                        }
                    }
                    catch
                    {
                        // 如果不支持TextScaling，使用替代方案
                        Console.WriteLine("警告: 当前Aspose.Words版本不支持TextScaling属性。");
                    }
                }

                // 设置文本旋转方向
                switch (TextOrientation)
                {
                    case AW.TextOrientation.Upward:
                        pageSetup.TextOrientation = AW.TextOrientation.Upward;
                        break;
                    case AW.TextOrientation.Downward:
                        pageSetup.TextOrientation = AW.TextOrientation.Downward;
                        break;
                    default:
                        pageSetup.TextOrientation = AW.TextOrientation.Horizontal;
                        break;
                }

                // 设置镜像边距和文本方向
                // 检查API兼容性
                try
                {
                    // 尝试使用反射设置MirrorMargins
                    var prop = typeof(AW.PageSetup).GetProperty("MirrorMargins");
                    if (prop != null)
                    {
                        prop.SetValue(pageSetup, MirrorMargins);
                    }
                    else
                    {
                        // 尝试使用MultiplePages属性作为替代
                        if (MirrorMargins)
                        {
                            pageSetup.MultiplePages = AW.Settings.MultiplePagesType.MirrorMargins;
                        }
                    }
                }
                catch
                {
                    // 如果不支持MirrorMargins，使用替代方案
                    Console.WriteLine("警告: 当前Aspose.Words版本不支持MirrorMargins属性。");
                    // 尝试使用MultiplePages属性作为替代
                    if (MirrorMargins)
                    {
                        pageSetup.MultiplePages = AW.Settings.MultiplePagesType.MirrorMargins;
                    }
                }

                // 设置双向文本（新增启用控制）
                if (EnableBidi)
                {
                    pageSetup.Bidi = Bidi != 0;
                }

                // 设置背景颜色
                if (EnableBackgroundColor && BackgroundColor != Color.Transparent && BackgroundColor != Color.Black)
                {
                    try
                    {
                        // 尝试使用反射设置PageColor属性
                        var prop = typeof(AW.PageSetup).GetProperty("PageColor");
                        if (prop != null)
                        {
                            prop.SetValue(pageSetup, BackgroundColor);
                            Console.WriteLine($"已设置页面背景颜色: {BackgroundColor}");
                        }
                        else
                        {
                            // 如果PageColor属性不存在，记录信息
                            // 背景颜色设置将在WordFormatter.cs中处理
                            Console.WriteLine("当前Aspose.Words版本不支持直接设置页面背景颜色，将在文档处理时尝试其他方法。");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"设置页面背景颜色时出错: {ex.Message}");
                    }
                }
                else if (EnableBackgroundColor && BackgroundColor == Color.Black)
                {
                    Console.WriteLine("警告：检测到黑色背景设置，为避免文档背景变黑已跳过背景色应用");
                }

                // 设置背景图片
                if (EnableBackgroundImage && !string.IsNullOrEmpty(BackgroundImage))
                {
                    try
                    {
                        // 注意：在这里我们无法直接访问Section对象
                        // 因为PageSetup对象没有ParentSection属性
                        // 我们将在WordFormatter.cs中处理背景图片设置

                        // 保存背景图片信息，以便在WordFormatter.cs中使用
                        // 这里不做任何操作，只是保存设置
                        Console.WriteLine("背景图片设置将在WordFormatter.cs中处理。");

                        // 保存显示模式信息，以便在WordFormatter.cs中使用
                        // DisplayMode已经保存在BackgroundImageDisplayMode属性中
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"设置页面背景图片时出错: {ex.Message}");
                    }
                }

                // 设置边框
                if (EnableBorderStyle && BorderStyle != BorderLineStyle.None)
                {
                    try
                    {
                        // 直接设置边框属性
                        // 注意：在这里我们无法直接访问Section对象
                        // 因为PageSetup对象没有ParentSection属性

                        // 设置边框线型
                        AW.LineStyle lineStyle = ConvertToAsposeBorderLineStyle(BorderStyle);

                        // 直接在PageSetup对象上设置边框属性
                        var borders = pageSetup.Borders;

                        // 应用边框设置
                        if (ApplyBorderTop)
                            borders.Top.LineStyle = lineStyle;

                        if (ApplyBorderLeft)
                            borders.Left.LineStyle = lineStyle;

                        if (ApplyBorderBottom)
                            borders.Bottom.LineStyle = lineStyle;

                        if (ApplyBorderRight)
                            borders.Right.LineStyle = lineStyle;

                        // 设置边框颜色
                        if (EnableBorderColor)
                        {
                            Color borderColor = BorderColor;

                            if (ApplyBorderTop)
                                borders.Top.Color = borderColor;

                            if (ApplyBorderLeft)
                                borders.Left.Color = borderColor;

                            if (ApplyBorderBottom)
                                borders.Bottom.Color = borderColor;

                            if (ApplyBorderRight)
                                borders.Right.Color = borderColor;
                        }

                        // 设置边框宽度
                        if (EnableBorderWidth)
                        {
                            double borderWidth = CmToPoints(BorderWidth);

                            if (ApplyBorderTop)
                                borders.Top.LineWidth = borderWidth;

                            if (ApplyBorderLeft)
                                borders.Left.LineWidth = borderWidth;

                            if (ApplyBorderBottom)
                                borders.Bottom.LineWidth = borderWidth;

                            if (ApplyBorderRight)
                                borders.Right.LineWidth = borderWidth;
                        }

                        // 设置边框距离
                        // 注意：在Aspose.Words的某些版本中，BorderCollection可能没有这些属性
                        // 我们将尝试使用反射来设置这些属性
                        try
                        {
                            var distanceFromTopProp = typeof(AW.BorderCollection).GetProperty("DistanceFromTop");
                            if (distanceFromTopProp != null)
                                distanceFromTopProp.SetValue(borders, BorderDistanceFromTop);

                            var distanceFromLeftProp = typeof(AW.BorderCollection).GetProperty("DistanceFromLeft");
                            if (distanceFromLeftProp != null)
                                distanceFromLeftProp.SetValue(borders, BorderDistanceFromLeft);

                            var distanceFromBottomProp = typeof(AW.BorderCollection).GetProperty("DistanceFromBottom");
                            if (distanceFromBottomProp != null)
                                distanceFromBottomProp.SetValue(borders, BorderDistanceFromBottom);

                            var distanceFromRightProp = typeof(AW.BorderCollection).GetProperty("DistanceFromRight");
                            if (distanceFromRightProp != null)
                                distanceFromRightProp.SetValue(borders, BorderDistanceFromRight);
                        }
                        catch
                        {
                            Console.WriteLine("当前Aspose.Words版本不支持设置边框距离属性。");
                        }

                        // 设置边框艺术效果
                        if (EnableBorderArt && BorderArtStyle > 0)
                        {
                            try
                            {
                                // 尝试使用反射设置ArtStyle属性
                                var prop = typeof(AW.BorderCollection).GetProperty("ArtStyle");
                                if (prop != null)
                                {
                                    prop.SetValue(borders, BorderArtStyle);
                                }
                            }
                            catch
                            {
                                Console.WriteLine("当前Aspose.Words版本不支持边框艺术效果。");
                            }
                        }

                        // 设置边框高级属性（新增）
                        if (EnableBorderAdvanced)
                        {
                            pageSetup.BorderAlwaysInFront = BorderAlwaysInFront;
                            pageSetup.BorderAppliesTo = BorderAppliesTo;
                            pageSetup.BorderDistanceFrom = BorderDistanceFrom;
                            pageSetup.BorderSurroundsHeader = BorderSurroundsHeader;
                            pageSetup.BorderSurroundsFooter = BorderSurroundsFooter;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"设置页面边框时出错: {ex.Message}");
                    }
                }

                // 设置脚注和尾注选项（新增）
                if (EnableFootnoteEndnote)
                {
                    pageSetup.SuppressEndnotes = SuppressEndnotes;
                }

                // 设置脚注选项（新增）
                if (EnableFootnoteOptions)
                {
                    pageSetup.FootnoteOptions.Position = FootnotePosition;
                    pageSetup.FootnoteOptions.NumberStyle = FootnoteNumberStyle;
                    pageSetup.FootnoteOptions.StartNumber = FootnoteStartNumber;
                    pageSetup.FootnoteOptions.RestartRule = FootnoteRestartRule;
                    pageSetup.FootnoteOptions.Columns = FootnoteColumns;
                }

                // 设置尾注选项（新增）
                if (EnableEndnoteOptions)
                {
                    pageSetup.EndnoteOptions.Position = EndnotePosition;
                    pageSetup.EndnoteOptions.NumberStyle = EndnoteNumberStyle;
                    pageSetup.EndnoteOptions.StartNumber = EndnoteStartNumber;
                    pageSetup.EndnoteOptions.RestartRule = EndnoteRestartRule;
                }

                // 设置分栏高级选项（新增）
                if (EnableColumnAdvanced)
                {
                    pageSetup.TextColumns.LineBetween = LineBetween;
                    // 注意：Aspose.Words可能不直接支持分隔线宽度和颜色设置
                    // 这些属性可能需要通过其他方式实现
                }

                // 设置节选项（新增）
                if (EnableSectionSettings)
                {
                    pageSetup.SectionStart = SectionStartType;
                    pageSetup.DifferentFirstPageHeaderFooter = DifferentFirstPageHeaderFooter;
                    pageSetup.OddAndEvenPagesHeaderFooter = OddAndEvenPagesHeaderFooter;
                }



                // 设置打印设置（新增）
                if (EnablePrintSettings)
                {
                    pageSetup.FirstPageTray = FirstPageTray;
                    pageSetup.OtherPagesTray = OtherPagesTray;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"应用页面设置时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 从Aspose.Words.PageSetup对象读取设置
        /// </summary>
        /// <param name="pageSetup">要读取的Aspose.Words.PageSetup对象</param>
        public void ReadFrom(AW.PageSetup pageSetup)
        {
            if (pageSetup == null)
                throw new ArgumentNullException(nameof(pageSetup));

            try
            {
                // 读取边距（转换为厘米）
                TopMargin = PointsToCm(pageSetup.TopMargin);
                BottomMargin = PointsToCm(pageSetup.BottomMargin);
                LeftMargin = PointsToCm(pageSetup.LeftMargin);
                RightMargin = PointsToCm(pageSetup.RightMargin);

                // 读取纸张方向
                Orientation = pageSetup.Orientation;

                // 读取纸张大小
                PaperSize = pageSetup.PaperSize;
                PaperWidth = PointsToCm(pageSetup.PageWidth);
                PaperHeight = PointsToCm(pageSetup.PageHeight);

                // 读取页眉和页脚
                HeaderDistance = PointsToCm(pageSetup.HeaderDistance);
                FooterDistance = PointsToCm(pageSetup.FooterDistance);
                DifferentFirstPageHeaderFooter = pageSetup.DifferentFirstPageHeaderFooter;
                OddAndEvenPagesHeaderFooter = pageSetup.OddAndEvenPagesHeaderFooter;

                // 读取分节
                SectionStart = pageSetup.SectionStart;

                // 读取垂直对齐设置（新增）
                VerticalAlignment = pageSetup.VerticalAlignment;

                // 读取装订线
                Gutter = PointsToCm(pageSetup.Gutter);
                RtlGutter = pageSetup.RtlGutter;

                // 读取镜像边距/多页设置（新增）
                MultiplePages = pageSetup.MultiplePages;
                MirrorMargins = (pageSetup.MultiplePages == AW.Settings.MultiplePagesType.MirrorMargins);
                SheetsPerBookletCustom = pageSetup.SheetsPerBooklet;

                // 读取文本方向
                TextOrientation = pageSetup.TextOrientation;

                // 读取行号设置
                LineNumberRestartMode = pageSetup.LineNumberRestartMode;
                LineNumberCountBy = pageSetup.LineNumberCountBy;
                LineNumberDistanceFromText = PointsToCm(pageSetup.LineNumberDistanceFromText);
                LineStartingNumber = pageSetup.LineStartingNumber;

                // 读取新增行号设置
                LineNumberingIsActive = pageSetup.LineNumberCountBy > 0;
                LineNumberingStartValue = pageSetup.LineStartingNumber;
                LineNumberingCountBy = pageSetup.LineNumberCountBy;
                LineNumberingDistance = PointsToCm(pageSetup.LineNumberDistanceFromText);
                LineNumberingRestartMode = pageSetup.LineNumberRestartMode;

                // 读取页码设置
                PageNumberStyle = pageSetup.PageNumberStyle;
                RestartPageNumbering = pageSetup.RestartPageNumbering;
                PageStartingNumber = pageSetup.PageStartingNumber;

                // 读取章节
                HeadingLevelForChapter = pageSetup.HeadingLevelForChapter;
                ChapterPageSeparator = pageSetup.ChapterPageSeparator;

                // 读取双面打印
                SheetsPerBooklet = pageSetup.SheetsPerBooklet;

                // 读取分栏设置
                ColumnCount = pageSetup.TextColumns.Count;
                ColumnSpacing = PointsToCm(pageSetup.TextColumns.Spacing);
                EvenlySpaced = pageSetup.TextColumns.EvenlySpaced;

                // 读取文档网格布局属性（新增）
                CharactersPerLine = pageSetup.CharactersPerLine;
                LinesPerPage = pageSetup.LinesPerPage;
                LayoutMode = pageSetup.LayoutMode;

                // 读取页面缩放设置
                if (pageSetup.LayoutMode == AW.SectionLayoutMode.LineGrid)
                {
                    ScaleToFitPages = true;
                    ScaleToPageCount = 1; // 默认为1页，因为Aspose.Words没有直接提供此属性
                    ScalePercentage = 100; // 默认100%
                }
                else
                {
                    ScaleToFitPages = false;
                    // 尝试使用反射读取TextScaling
                    try
                    {
                        var prop = typeof(AW.PageSetup).GetProperty("TextScaling");
                        if (prop != null)
                        {
                            var value = prop.GetValue(pageSetup);
                            if (value != null)
                                ScalePercentage = (int)value;
                            else
                                ScalePercentage = 100; // Default if null
                        }
                        else
                        {
                            ScalePercentage = 100; // 默认值
                        }
                    }
                    catch
                    {
                        // 如果不支持TextScaling，使用默认值
                        ScalePercentage = 100;
                    }
                }

                // 读取镜像边距和文本方向
                try
                {
                    // 尝试使用反射读取MirrorMargins
                    var prop = typeof(AW.PageSetup).GetProperty("MirrorMargins");
                    if (prop != null)
                    {
                        var value = prop.GetValue(pageSetup);
                        if (value != null)
                            MirrorMargins = (bool)value;
                        else
                            MirrorMargins = false; // Default if null
                    }
                    else
                    {
                        // 尝试使用MultiplePages属性作为替代
                        MirrorMargins = (pageSetup.MultiplePages == MultiplePagesType.MirrorMargins);
                    }
                }
                catch
                {
                    // 如果不支持MirrorMargins，使用替代方案
                    MirrorMargins = (pageSetup.MultiplePages == MultiplePagesType.MirrorMargins);
                }

                Bidi = pageSetup.Bidi ? 1 : 0;

                // 读取边框高级属性（新增）
                BorderAlwaysInFront = pageSetup.BorderAlwaysInFront;
                BorderAppliesTo = pageSetup.BorderAppliesTo;
                BorderDistanceFrom = pageSetup.BorderDistanceFrom;
                BorderSurroundsHeader = pageSetup.BorderSurroundsHeader;
                BorderSurroundsFooter = pageSetup.BorderSurroundsFooter;

                // 读取脚注选项（新增）
                FootnotePosition = pageSetup.FootnoteOptions.Position;
                FootnoteNumberStyle = pageSetup.FootnoteOptions.NumberStyle;
                FootnoteStartNumber = pageSetup.FootnoteOptions.StartNumber;
                FootnoteRestartRule = pageSetup.FootnoteOptions.RestartRule;
                FootnoteColumns = pageSetup.FootnoteOptions.Columns;

                // 读取尾注选项（新增）
                EndnotePosition = pageSetup.EndnoteOptions.Position;
                EndnoteNumberStyle = pageSetup.EndnoteOptions.NumberStyle;
                EndnoteStartNumber = pageSetup.EndnoteOptions.StartNumber;
                EndnoteRestartRule = pageSetup.EndnoteOptions.RestartRule;

                // 读取分栏高级设置（新增）
                LineBetween = pageSetup.TextColumns.LineBetween;

                // 读取节设置（新增）
                SectionStartType = pageSetup.SectionStart;

                // 读取脚注和尾注设置（新增）
                SuppressEndnotes = pageSetup.SuppressEndnotes;

                // 读取打印设置（新增）
                FirstPageTray = pageSetup.FirstPageTray;
                OtherPagesTray = pageSetup.OtherPagesTray;

                // 读取背景颜色
                // 在未来版本中实现
                // BackgroundColor = pageSetup.PageColor;

                // 读取边框样式
                // 在未来版本中实现
                // 注意: Aspose.Words可能需要特定的API来设置边框

                // 从页面设置读取文档视图设置需要在Document级别读取
                // 这需要在拥有Document实例时单独应用
                /*
                if (document != null)
                {
                    var viewOptions = document.ViewOptions;

                    // 读取视图类型
                    ViewType = viewOptions.ViewType;

                    // 读取缩放设置
                    ZoomType = viewOptions.ZoomType;
                    ZoomPercent = viewOptions.ZoomPercent;

                    // 读取显示选项
                    // ShowFormatMarks在Aspose.Words中无法直接获取

                    // 读取页面边界显示选项
                    DoNotDisplayPageBoundaries = viewOptions.DoNotDisplayPageBoundaries;

                    // 读取背景形状显示选项
                    DisplayBackgroundShape = viewOptions.DisplayBackgroundShape;
                }
                */
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取页面设置时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 将厘米转换为磅点
        /// </summary>
        /// <param name="centimeters">厘米值</param>
        /// <returns>磅点值</returns>
        private static double CmToPoints(double centimeters)
        {
            // 1厘米 = 28.3464567点
            return centimeters * 28.3464567;
        }

        /// <summary>
        /// 将磅点转换为厘米
        /// </summary>
        /// <param name="points">磅点值</param>
        /// <returns>厘米值</returns>
        private static double PointsToCm(double points)
        {
            // 1点 = 0.035277778厘米
            return points / 28.3464567;
        }



        /// <summary>
        /// 将自定义边框样式转换为Aspose的边框线样式
        /// </summary>
        private static AW.LineStyle ConvertToAsposeBorderLineStyle(BorderLineStyle style)
        {
            // 简化实现
            switch (style)
            {
                case BorderLineStyle.None:
                    return AW.LineStyle.None;
                case BorderLineStyle.Double:
                    return AW.LineStyle.Double;
                default:
                    return AW.LineStyle.Single;
            }
        }

        /// <summary>
        /// 从Aspose的边框线样式转换为自定义边框样式
        /// </summary>
        private static BorderLineStyle ConvertFromAsposeBorderLineStyle(AW.LineStyle style)
        {
            // 简化实现
            switch (style)
            {
                case AW.LineStyle.None:
                    return BorderLineStyle.None;
                case AW.LineStyle.Double:
                    return BorderLineStyle.Double;
                default:
                    return BorderLineStyle.Single;
            }
        }
    }
}