/*
 * ========================================
 * 文件名: TxtSettings.cs
 * 功能描述: TXT文件处理设置数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义TXT文件的处理配置选项
 * 2. 支持TXT到Word格式的转换设置
 * 3. 提供文本编码检测和处理
 * 4. 包含段落、列表、表格的智能识别
 * 5. 支持文本样式和格式的自动应用
 *
 * 转换设置:
 * - ConvertToWordBeforeProcessing: 是否将TXT转换为Word后再处理
 * - ConvertToFormat: 转换的目标格式（默认DOCX）
 *
 * 编码设置:
 * - EnableEncodingDetection: 是否启用编码自动检测
 * - DefaultEncoding: 默认编码格式（UTF-8）
 *
 * 段落处理:
 * - EnableParagraphProcessing: 是否启用段落处理
 * - TreatConsecutiveBlankLinesAsParagraph: 将连续空行视为段落分隔
 * - BlankLinesThreshold: 连续空行阈值（超过此数量视为段落分隔）
 * - PreserveLineBreaks: 是否保留原始换行符
 *
 * 列表检测:
 * - EnableListDetection: 是否启用列表检测
 * - DetectNumberedLists: 检测编号列表（1. 2. 3.等）
 * - DetectBulletedLists: 检测项目符号列表（• - *等）
 *
 * 表格检测:
 * - EnableTableDetection: 是否启用表格检测
 * - TableDelimiter: 表格分隔符（默认Tab制表符）
 *
 * 标题检测:
 * - EnableHeadingDetection: 是否启用标题检测
 * - MaxHeadingLevel: 最大标题级别（1-9）
 *
 * 文本样式:
 * - EnableTextStyling: 是否启用文本样式
 * - DefaultFontName: 默认字体名称
 * - DefaultFontSize: 默认字体大小
 *
 * 智能识别功能:
 * - 自动识别段落结构
 * - 智能检测列表格式
 * - 表格数据的自动转换
 * - 标题层级的自动识别
 * - 文本样式的自动应用
 *
 * 编码处理:
 * - 支持多种文本编码格式
 * - 自动检测文件编码
 * - 处理编码转换问题
 * - 保持文本内容的完整性
 *
 * 应用场景:
 * - 纯文本文件的格式化处理
 * - TXT到Word的批量转换
 * - 文本内容的结构化处理
 * - 多语言文本文件的处理
 *
 * 注意事项:
 * - 支持复杂的文本结构识别
 * - 包含完整的编码处理机制
 * - 实现了智能的格式转换逻辑
 * - 提供了灵活的处理选项配置
 */

using System;
using System.Collections.Generic;
using Aspose.Words;

namespace AsposeWordFormatter.Models
{
    public class TxtSettings
    {
        // 是否将txt转换为word后再处理
        public bool ConvertToWordBeforeProcessing { get; set; } = true;

        // 编码设置
        public bool EnableEncodingDetection { get; set; } = true;
        public string DefaultEncoding { get; set; } = "UTF-8";

        // 段落处理
        public bool EnableParagraphProcessing { get; set; } = true;
        public bool TreatConsecutiveBlankLinesAsParagraph { get; set; } = true;
        public int BlankLinesThreshold { get; set; } = 2;  // 连续空行超过此数量视为段落分隔
        public bool PreserveLineBreaks { get; set; } = true;  // 是否保留原始换行符

        // 列表处理
        public bool EnableListDetection { get; set; } = true;
        public bool DetectNumberedLists { get; set; } = true;
        public bool DetectBulletedLists { get; set; } = true;

        // 表格处理
        public bool EnableTableDetection { get; set; } = true;
        public string TableDelimiter { get; set; } = "\t";  // 默认使用Tab作为表格分隔符

        // 标题处理
        public bool EnableHeadingDetection { get; set; } = true;
        public int MaxHeadingLevel { get; set; } = 3;

        // 文本样式
        public bool EnableTextStyling { get; set; } = true;
        public string DefaultFontName { get; set; } = "宋体";
        public double DefaultFontSize { get; set; } = 12.0;

        // 文档格式
        public SaveFormat ConvertToFormat { get; set; } = SaveFormat.Docx;
    }
}