<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>annotations</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>AsposeSlidesFormatter</RootNamespace>
    <AssemblyName>SlidesFormatter</AssemblyName>
    <!-- 新增：禁用默认的 Compile 项 -->
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <ApplicationIcon>favicon.ico</ApplicationIcon>

  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="SkiaSharp" Version="3.116.0" />
  </ItemGroup>

  <ItemGroup>
    <!-- 引用本地Aspose.Slides.dll文件 -->
    <Reference Include="Aspose.Slides">
      <HintPath>Aspose.Slides.dll</HintPath>
      <Private>True</Private>
      <SpecificVersion>False</SpecificVersion>
      <!-- 添加禁用XML文档复制的属性 -->
      <CopyLocalSatelliteAssemblies>false</CopyLocalSatelliteAssemblies>
    </Reference>
  </ItemGroup>

  <!-- 添加全局属性，禁用XML文档复制 -->
  <PropertyGroup>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>
    <ProduceReferenceAssembly>false</ProduceReferenceAssembly>
  </PropertyGroup>

  <!-- 防止Aspose.Slides.xml被复制到输出目录 -->
  <ItemGroup>
    <Content Include="Aspose.Slides.xml">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Content>
    <!-- 添加明确的排除项 -->
    <None Remove="Aspose.Slides.xml" />
  </ItemGroup>

  <!-- 使用更强的排除规则 -->
  <Target Name="RemoveAsposeXmlFromOutput" AfterTargets="Build">
    <Delete Files="$(OutputPath)Aspose.Slides.xml" />
  </Target>

  <ItemGroup>
    <!-- 显式列出需要编译的文件 -->
    <Compile Include="Program.cs" />
    <Compile Include="MainForm.cs" />
    <Compile Include="MainForm.Designer.cs" />
    <Compile Include="SlidesFormatter.cs" />
    <Compile Include="Logger.cs" />
    <Compile Include="SettingsManager.cs" />
    <Compile Include="Enums.cs" />

    <!-- 表单文件 -->
    <Compile Include="DeleteContentForm.cs" />
    <Compile Include="HeaderFooterForm.cs" />
    <Compile Include="GlobalParagraphFormatForm.cs" />
    <Compile Include="ParagraphMatchForm.cs" />
    <Compile Include="ParagraphPresetRuleForm.cs" />
    <Compile Include="ContentReplaceForm.cs" />
    <Compile Include="ContentReplaceRuleForm.cs" />
    <Compile Include="FileNameReplaceForm.cs" />
    <Compile Include="FileNameReplaceRuleForm.cs" />
    <Compile Include="DocumentPropertiesForm.cs" />
    <Compile Include="DocumentFormatForm.cs" />
    <Compile Include="PageSetupForm.cs" />
    <Compile Include="SettingsForm.cs" />
    <Compile Include="ScheduleSettingsForm.cs" />
    <Compile Include="PdfSettingsForm.cs" />
    <Compile Include="LogSettingsForm.cs" />
    <Compile Include="IllegalWordsEditForm.cs" />

    <!-- Models 目录下的文件 -->
    <Compile Include="Models\*.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="favicon.ico" />
  </ItemGroup>

  <ItemGroup>
    <None Include="App.config" />
    <EmbeddedResource Include="Aspose.Total.NET.lic" />
    <None Include="Aspose.Slides.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>

