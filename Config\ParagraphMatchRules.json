// 段落匹配规则配置
// 此文件定义了如何根据内容匹配段落并应用特定格式
[
  {
    // 基本信息
    "Name": "标题样式", // 规则名称
    "IsEnabled": true, // 是否启用此规则

    // 匹配条件
    "Pattern": "^第[一二三四五六七八九十]+(章|节|篇).*", // 匹配模式，此处为以"第x章"等开头的文本
    "UseRegex": true, // 是否使用正则表达式匹配
    "CaseSensitive": false, // 是否区分大小写

    // 格式应用范围
    "ApplyScope": 0, // 0=整段应用，1=匹配部分到第一个句号结束，2=匹配部分到换行结束，等

    // 匹配规则列表
    "StartWithPatterns": [], // 以特定文本开始的模式列表
    "ContainsPatterns": [], // 包含特定文本的模式列表
    "EndWithPatterns": [], // 以特定文本结束的模式列表

    // 字体格式
    "StyleName": "标题 1", // Word样式名称
    "FontName": "黑体", // 字体名称
    "FontSize": 16, // 字体大小（磅）
    "Bold": true, // 是否加粗
    "Italic": false, // 是否斜体
    "Underline": 0, // 下划线类型：0=无，1=单下划线，等
    "FontColor": "#000000", // 字体颜色（十六进制RGB）

    // 多语言字体格式
    "ChineseFontName": "黑体", // 中文字体
    "WesternFontName": "Times New Roman", // 西文字体
    "ComplexScriptFontName": "Arial", // 复杂脚本字体
    "ChineseFontStyle": 0, // 中文字体样式：0=常规，1=粗体，2=斜体，3=粗斜体
    "WesternFontStyle": 0, // 西文字体样式
    "ComplexScriptFontStyle": 0, // 复杂脚本字体样式
    "ChineseFontSize": 16, // 中文字体大小
    "WesternFontSize": 16, // 西文字体大小
    "ComplexScriptFontSize": 16, // 复杂脚本字体大小

    // 高亮背景色
    "HighlightColor": null, // 文字高亮背景色，null为不设置

    // 段落格式
    "Alignment": 0, // 对齐方式：0=左对齐，1=居中，2=右对齐，3=两端对齐，4=分散对齐
    "OutlineLevel": 1, // 大纲级别：0=正文，1=一级标题，...9=九级标题
    "Bidi": false, // 双向文本
    "LineSpacing": 1.5, // 行距
    "LineSpacingRule": 5, // 行距规则：0=至少，1=固定，2=1.5倍行距，3=双倍行距，4=多倍行距，5=精确
    "SpaceBefore": 12, // 段前间距（磅）
    "SpaceAfter": 12, // 段后间距（磅）
    "FirstLineIndent": 0, // 首行缩进（磅）
    "LeftIndent": 0, // 左缩进（磅）
    "RightIndent": 0, // 右缩进（磅）
    "SpecialIndent": 0, // 特殊缩进类型：0=无，1=首行缩进，2=悬挂缩进
    "SpecialIndentValue": 2, // 特殊缩进值（字符数）

    // 长度限制
    "MinLength": 0, // 最小长度（字符数）
    "MaxLength": 2147483647, // 最大长度（字符数）

    // 文本方向
    "TextDirection": 0, // 0=从左到右，1=从右到左

    // 格式功能控制开关
    "EnableBasicFormat": true, // 是否启用基本格式
    "EnableBorderShading": true, // 是否启用边框和底纹
    "EnablePagination": true, // 是否启用分页控制
    "EnableTabStops": true, // 是否启用制表位设置
    "EnableAlignment": true, // 是否启用对齐方式
    "EnableOutlineLevel": true, // 是否启用大纲级别
    "EnableTextDirection": true, // 是否启用文本方向
    "EnableIndent": true, // 是否启用缩进设置
    "EnableSpacing": true, // 是否启用间距设置
    "EnableChineseFont": true, // 是否启用中文字体设置
    "EnableWesternFont": true, // 是否启用西文字体设置
    "EnableComplexScriptFont": true, // 是否启用复杂文字字体设置
    "EnableFontColor": true, // 是否启用字体颜色
    "EnableHighlightColor": true, // 是否启用高亮颜色

    // 边框设置
    "HasBorders": false, // 是否有边框
    "BorderType": 1, // 边框类型：1=单线，2=双线，等
    "BorderWidth": 0.5, // 边框宽度（磅）
    "BorderColor": "#000000", // 边框颜色
    "HasTopBorder": true, // 是否有上边框
    "HasBottomBorder": true, // 是否有下边框
    "HasLeftBorder": true, // 是否有左边框
    "HasRightBorder": true, // 是否有右边框

    // 底纹设置
    "HasShading": false, // 是否有底纹
    "ShadingColor": "#D3D3D3", // 底纹颜色（浅灰色，避免黑色）
    "ShadingPattern": 0, // 底纹图案：0=实底纹，1=百分之5，等

    // 分页和段落连接控制
    "KeepWithNext": true, // 是否与下段同页
    "KeepLinesTogether": true, // 是否段中不分页
    "PageBreakBefore": false, // 是否段前分页
    "WidowOrphanControl": true, // 是否控制孤行寡行
    "NoSpaceBetweenParagraphs": false, // 相同样式段落之间是否无间距

    // 制表位设置
    "TabStops": [
      {
        "Position": 36, // 制表位位置（磅）
        "Alignment": 0, // 对齐方式：0=左对齐，1=居中，2=右对齐，3=小数点对齐
        "Leader": 0 // 前导符：0=无，1=点，2=中点，3=连字符，4=下划线
      }
    ],

    // 图片水印设置
    "EnableWatermark": false, // 是否启用文本水印
    "WatermarkText": "机密", // 水印文本
    "WatermarkFontFamily": "Arial", // 水印字体
    "WatermarkFontSize": 36, // 水印字体大小
    "WatermarkColor": "#808080", // 水印颜色
    "WatermarkOpacity": 0.5, // 水印透明度（0-1）
    "WatermarkRotationAngle": 45, // 水印旋转角度

    // 图片水印
    "EnableImageWatermark": false, // 是否启用图片水印
    "WatermarkImagePath": "D:\\Images\\watermark.png", // 水印图片路径
    "PreserveImageWatermarkAspectRatio": true, // 是否保持图片纵横比
    "ImageWatermarkWidth": 150, // 水印图片宽度（磅）
    "ImageWatermarkHeight": 150, // 水印图片高度（磅）
    "ImageWatermarkScaleX": 100, // 水印图片水平缩放比例（%）
    "ImageWatermarkScaleY": 100, // 水印图片垂直缩放比例（%）
    "ImageWatermarkOpacity": 0.5, // 水印图片透明度（0-1）
    "ImageWatermarkColorMode": 0, // 颜色模式：0=彩色，1=灰度，2=黑白
    "ImageWatermarkLayout": 4, // 水印布局：0=无，1=水平，2=垂直，3=对角线，4=反向对角线

    // 水印位置控制
    "EnableWatermarkPosition": false, // 是否启用水印位置设置
    "WatermarkAtPageCenter": true, // 水印是否居中
    "WatermarkHorizontalPosition": 0, // 水印水平位置（磅）
    "WatermarkVerticalPosition": 0, // 水印垂直位置（磅）
    "WatermarkOnAllPages": true, // 是否在所有页面上显示水印
    "WatermarkPageRange": "", // 水印页面范围，如"1,3-5,8"
    "WatermarkBehindContent": false, // 水印是否在内容后面

    // 超链接设置
    "EnableImageHyperlink": false, // 是否启用超链接
    "HyperlinkUrl": "", // 超链接URL
    "HyperlinkToolTip": "", // 超链接提示文本
    "HyperlinkType": 0, // 超链接类型：0=URL，1=书签，2=文件
    "BookmarkName": "", // 书签名称
    "OpenHyperlinkInNewWindow": false // 是否在新窗口中打开超链接
  },
  {
    "Name": "正文样式",
    "IsEnabled": true,
    "Pattern": ".*",
    "UseRegex": true,
    "CaseSensitive": false,
    "FontName": "宋体",
    "FontSize": 12,
    "LineSpacing": 1.5,
    "FirstLineIndent": 28.0,
    "Alignment": 3
  }
]