/*
 * ========================================
 * 文件名: TabStopSettings.cs
 * 功能描述: 制表位设置数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义制表位的配置选项
 * 2. 支持多个制表位的管理
 * 3. 提供制表位到Aspose.Words的转换方法
 * 4. 包含制表位的清除和应用逻辑
 * 5. 支持制表位的批量设置
 *
 * 核心属性:
 * - TabPositions: 制表位列表（TabStop对象集合）
 * - ClearExistingTabs: 是否清除现有制表位
 *
 * 制表位属性:
 * - Position: 制表位位置（以点为单位）
 * - Alignment: 制表位对齐方式（左对齐、居中、右对齐、小数点对齐）
 * - Leader: 制表位前导符（无、点、短划线、下划线等）
 *
 * 构造函数:
 * - TabStopSettings(): 默认构造函数，创建空的制表位列表
 * - TabStopSettings(TabStop): 使用单个制表位初始化
 *
 * 核心方法:
 * - ApplyTo(): 将制表位设置应用到Aspose.Words段落
 *
 * 应用逻辑:
 * - 可选择性清除现有制表位
 * - 批量添加新的制表位设置
 * - 支持多种制表位类型和样式
 *
 * 制表位对齐类型:
 * - Left: 左对齐制表位
 * - Center: 居中对齐制表位
 * - Right: 右对齐制表位
 * - Decimal: 小数点对齐制表位
 * - Bar: 竖线制表位
 *
 * 前导符类型:
 * - None: 无前导符
 * - Dots: 点前导符（....）
 * - Dashes: 短划线前导符（----）
 * - Underline: 下划线前导符（____）
 * - Heavy: 粗线前导符
 * - MiddleDot: 中点前导符
 *
 * 应用场景:
 * - 段落格式中的制表位设置
 * - 表格对齐和格式化
 * - 文档布局的精确控制
 * - 目录和索引的格式化
 *
 * 注意事项:
 * - 支持Aspose.Words的所有制表位选项
 * - 包含完整的参数验证
 * - 实现了灵活的制表位管理
 * - 提供了批量操作的便利性
 */

using System;
using System.Collections.Generic;
using AW = Aspose.Words;

namespace AsposeWordFormatter.Models
{
    public class TabStopSettings
    {
        public List<TabStop> TabPositions { get; set; } = new List<TabStop>();
        public bool ClearExistingTabs { get; set; } = true;

        public TabStopSettings()
        {
        }

        public TabStopSettings(TabStop tabStop)
        {
            if (tabStop != null)
            {
                TabPositions.Add(tabStop);
            }
        }

        public void ApplyTo(AW.Paragraph paragraph)
        {
            if (paragraph == null)
                return;

            if (ClearExistingTabs)
            {
                paragraph.ParagraphFormat.TabStops.Clear();
            }

            foreach (var tabStop in TabPositions)
            {
                paragraph.ParagraphFormat.TabStops.Add(
                    tabStop.Position,
                    tabStop.Alignment,
                    tabStop.Leader);
            }
        }
    }
}