/*
 * ========================================
 * 文件名: MainForm.cs
 * 功能描述: 主窗体界面和核心业务逻辑控制器
 * ========================================
 *
 * 主要功能:
 * 1. 用户界面布局和交互控制
 * 2. 文档处理流程的协调和管理
 * 3. 各功能模块的配置界面调用
 * 4. 实时进度显示和状态更新
 * 5. 定时任务管理和调度
 * 6. 日志显示和用户反馈
 *
 * 核心职责:
 * - 主用户界面的创建和管理
 * - 文件处理流程的启动、停止和监控
 * - 各配置窗体的调用和数据传递
 * - 处理进度和统计信息的实时更新
 * - 用户操作的响应和验证
 * - 定时处理功能的管理
 *
 * 界面组件:
 * - 文件路径选择区域
 * - 功能模块开关和配置按钮
 * - 处理控制按钮（开始、停止、定时等）
 * - 进度显示和统计信息区域
 * - 日志输出窗口
 * - 菜单栏和工具栏
 *
 * 依赖关系:
 * - SlidesFormatter: 核心演示文稿处理引擎
 * - Logger: 日志记录系统
 * - SettingsManager: 配置管理器
 * - 各种Form类: 功能配置窗体
 * - Models命名空间: 数据模型类
 *
 * 注意事项:
 * - 支持多线程处理和取消操作
 * - 实现了拖拽文件夹功能
 * - 包含完整的错误处理和用户提示
 * - 支持配置的导入导出功能
 */

#nullable enable
using System;
using System.IO;
using System.Windows.Forms;
using Aspose.Slides; // 更改为 Aspose.Slides 命名空间
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using System.Threading;
using System.Drawing;
using System.Text.RegularExpressions;
using System.Diagnostics;
using AS = Aspose.Slides;
using AsposeSlidesFormatter;
using AsposeSlidesFormatter.Models;
using System.Reflection;

namespace AsposeSlidesFormatter
{
    public partial class MainForm : Form
    {
        private readonly Settings? settings;
        private readonly Logger? logger;
        private readonly SlidesFormatter? slidesFormatter;
        private bool isProcessing = false;
        private CancellationTokenSource? cancellationTokenSource;

        // 统计信息相关字段
        private Panel? totalFilesLabel;
        private Panel? successFilesLabel;
        private Panel? failedFilesLabel;
        private Panel? retriedFilesLabel;
        private Panel? processingTimeLabel;
        private DateTime processingStartTime = DateTime.Now;  // 初始化为当前时间
        private System.Windows.Forms.Timer? processingTimer;
        private ProgressBar? progressBarControl;
        private Label? progressBarLabel;
        private Label? speedInfoLabel;
        private DateTime lastUpdateTime = DateTime.Now;
        private int lastProcessedCount = 0;

        // 添加新的统计信息持久化字段
        private double currentProcessingSpeed = 0.0;  // 当前处理速度
        private string speedUnit = "文件/分钟";       // 速度单位
        private TimeSpan currentRemainingTime = TimeSpan.Zero;  // 当前预计剩余时间
        private string lastValidEstimatedEndTime = "--";  // 最后有效的预计结束时间

        // 添加定时处理相关字段
        private System.Windows.Forms.Timer? scheduleTimer;
        private Label? nextRunTimeLabel;
        private bool isScheduleEnabled = false;

        // 操作按钮
        private Button? startButton;
        private Button? stopButton;
        private Button? scheduleButton;
        private Settings? currentSettings;  // 添加可修改的设置字段

        // 菜单栏相关
        private MenuStrip? menuStrip;
        private ToolStripMenuItem? fileMenu;
        private ToolStripMenuItem? operationMenu;
        private ToolStripMenuItem? settingsMenu;
        private ToolStripMenuItem? helpMenu;

        // 在类的字段部分添加新字段
        private Panel? expectedEndTimeLabel;
        private int _totalFiles = 0;

        public MainForm()
        {
            try
            {
                // 1. 获取Logger单例并配置日志级别
                logger = Logger.Instance;
                logger?.SetLogLevelEnabled(LogLevel.Debug, true); // 启用Debug级别日志
                logger?.Log("开始初始化主窗体...");

                // 2. 加载应用程序配置
                currentSettings = SettingsManager.LoadSettings();
                settings = currentSettings;  // 保持兼容性
                logger?.Log("应用程序配置加载完成");

                // 3. 初始化UI组件（必须在创建业务组件之前）
                InitializeComponent();
                logger?.Log("UI组件初始化完成");

                // 4. 创建业务组件
                if (settings != null && logger != null)
                {
                    slidesFormatter = new SlidesFormatter(settings, logger, settings.SourceDirectory ?? "", settings.OutputDirectory ?? "");
                }
                cancellationTokenSource = new CancellationTokenSource();
                logger?.Log("业务组件创建完成");

                // 5. 订阅事件
                SubscribeToEvents();
                logger?.Log("事件订阅完成");

                // 6. 初始化UI功能
                InitializeUserInterface();
                logger?.Log("用户界面初始化完成");

                // 7. 记录启动完成日志
                LogStartupComplete();

                logger?.Log("主窗体初始化完成");
            }
            catch (Exception ex)
            {
                // 初始化失败的应急处理
                string errorMessage = "主窗体初始化失败";

                try
                {
                    if (logger != null)
                    {
                        logger.LogError(errorMessage, ex);
                    }
                }
                catch
                {
                    // 如果连日志都无法记录，输出到控制台
                    Console.WriteLine($"{errorMessage}: {ex.Message}");
                }

                MessageBox.Show($"{errorMessage}: {ex.Message}\n\n程序可能无法正常工作。",
                    "初始化错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 尝试基本的初始化，确保程序能够启动
                try
                {
                    if (logger == null) logger = Logger.Instance;
                    if (currentSettings == null) currentSettings = new Settings();
                    if (settings == null) settings = currentSettings;
                    InitializeComponent();
                }
                catch
                {
                    // 如果连基本初始化都失败，程序将无法启动
                }
            }
        }

        /// <summary>
        /// 订阅所有必要的事件
        /// </summary>
        private void SubscribeToEvents()
        {
            if (slidesFormatter != null)
            {
                slidesFormatter.ProgressChanged += SlidesFormatter_ProgressChanged;
                slidesFormatter.StatusChanged += SlidesFormatter_StatusChanged;
                slidesFormatter.ErrorOccurred += SlidesFormatter_ErrorOccurred;
            }

            if (logger != null)
            {
                logger.LogMessage += Logger_LogMessage;
            }
        }

        /// <summary>
        /// 初始化用户界面功能
        /// </summary>
        private void InitializeUserInterface()
        {
            // 设置拖拽功能
            SetupDragDrop();

            // 设置初始按钮状态
            isProcessing = false;

            // 初始化布局
            InitializeLayout();

            // 初始化定时器
            InitializeScheduleTimer();

            // 检查定时功能状态并更新UI
            UpdateScheduleStatus();
        }

        /// <summary>
        /// 记录启动完成日志
        /// </summary>
        private void LogStartupComplete()
        {
            string versionInfo = "PPT演示文稿批量处理工具 v1.0";
            logger?.Log("==================================================");
            logger?.Log(versionInfo);
            logger?.Log("==================================================");
            logger?.Log($"主窗体启动完成 - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            logger?.Log($"来源目录: {settings?.SourceDirectory ?? "未设置"}");
            logger?.Log($"输出目录: {settings?.OutputDirectory ?? "未设置"}");
            logger?.Log("系统准备就绪，等待用户操作");
        }

        private void InitializeScheduleTimer()
        {
            // 创建定时器，每秒检查一次
            scheduleTimer = new System.Windows.Forms.Timer();
            scheduleTimer.Interval = 1000; // 1秒
            scheduleTimer.Tick += ScheduleTimer_Tick;
            scheduleTimer.Start();

            isScheduleEnabled = settings?.ScheduleSettings?.Enabled ?? false;
        }

        private void ScheduleTimer_Tick(object? sender, EventArgs e)
        {
            if (isScheduleEnabled && settings?.ScheduleSettings != null)
            {
                // 更新下次运行时间显示
                UpdateNextRunTimeDisplay();

                // 检查是否应该开始处理
                if (!isProcessing && settings.ScheduleSettings.ShouldRunNow())
                {
                    logger?.Log("定时任务触发，开始处理文件...");
                    StartProcessing();

                    // 记录此次运行
                    settings.ScheduleSettings.SaveRunRecord();

                    // 保存设置，确保记录了当前运行次数
                    if (currentSettings != null)
                    {
                        SettingsManager.SaveSettings(currentSettings);
                    }

                    // 如果是一次性任务或者达到了运行次数限制，使用过期时间，可能需要更新UI状态
                    if (settings.ScheduleSettings.ScheduleMode == 0 ||
                        (settings.ScheduleSettings.UseLimitedRuns &&
                         settings.ScheduleSettings.MaxRunCount > 0 &&
                         settings.ScheduleSettings.RunCount >= settings.ScheduleSettings.MaxRunCount) ||
                        (settings.ScheduleSettings.UseExpirationTime &&
                         DateTime.Now >= settings.ScheduleSettings.ExpirationTime))
                    {
                        UpdateScheduleStatus();
                    }
                }
            }
        }

        private void UpdateNextRunTimeDisplay()
        {
            if (nextRunTimeLabel != null && settings?.ScheduleSettings != null && settings.ScheduleSettings.Enabled)
            {
                DateTime nextRunTime = settings.ScheduleSettings.CalculateNextRunTime();

                // 如果返回的是最小日期值，表示没有下一次运行
                if (nextRunTime == DateTime.MinValue)
                {
                    if (settings.ScheduleSettings.ScheduleMode == 0)
                    {
                        nextRunTimeLabel.Text = "一次性任务已完成";
                    }
                    else if (settings.ScheduleSettings.UseLimitedRuns &&
                             settings.ScheduleSettings.RunCount >= settings.ScheduleSettings.MaxRunCount)
                    {
                        nextRunTimeLabel.Text = $"任务已完成 (已运行 {settings.ScheduleSettings.RunCount} 次)";
                    }
                    else if (settings.ScheduleSettings.UseExpirationTime &&
                             DateTime.Now >= settings.ScheduleSettings.ExpirationTime)
                    {
                        nextRunTimeLabel.Text = $"任务已过期 (截止于 {settings.ScheduleSettings.ExpirationTime:yyyy-MM-dd HH:mm:ss})";
                    }
                    else
                    {
                        nextRunTimeLabel.Text = "没有计划的下次运行";
                    }
                }
                else
                {
                    TimeSpan remaining = nextRunTime - DateTime.Now;

                    if (remaining.TotalSeconds > 0)
                    {
                        string remainingText = string.Format("{0:D2}:{1:D2}:{2:D2}",
                            (int)remaining.TotalHours,
                            remaining.Minutes,
                            remaining.Seconds);

                        // 显示模式信息
                        string modeInfo = "";
                        switch (settings.ScheduleSettings.ScheduleMode)
                        {
                            case 0:
                                modeInfo = "一次性";
                                break;
                            case 1:
                                modeInfo = "定时";
                                break;
                            case 2:
                                modeInfo = "间隔";
                                break;
                        }

                        // 显示限制信息
                        string limitInfo = "";
                        if (settings.ScheduleSettings.UseLimitedRuns)
                        {
                            limitInfo = $" (已运行 {settings.ScheduleSettings.RunCount}/{settings.ScheduleSettings.MaxRunCount} 次)";
                        }
                        else if (settings.ScheduleSettings.UseExpirationTime)
                        {
                            limitInfo = $" (过期时间 {settings.ScheduleSettings.ExpirationTime:yyyy-MM-dd HH:mm})";
                        }

                        nextRunTimeLabel.Text = $"下次运行: {nextRunTime:yyyy-MM-dd HH:mm:ss} ({modeInfo}, 剩余 {remainingText}){limitInfo}";
                    }
                    else
                    {
                        nextRunTimeLabel.Text = "定时任务准备执行...";
                    }
                }
            }
            else if (nextRunTimeLabel != null)
            {
                nextRunTimeLabel.Text = "定时处理未启用";
            }

            // 如果统计面板已初始化，直接更新speedInfoLabel显示定时状态
            if (speedInfoLabel != null)
            {
                // 获取当前显示的信息
                string currentText = speedInfoLabel.Text;

                // 如果包含定时信息，先移除它
                int scheduleStartIndex = currentText.LastIndexOf(" | 下次运行:");
                if (scheduleStartIndex > 0)
                {
                    currentText = currentText.Substring(0, scheduleStartIndex);
                }
                else
                {
                    // 检查其他定时状态前缀
                    string[] statusPrefixes = new[] { " | 一次性任务已完成", " | 任务已完成", " | 任务已过期", " | 没有计划的下次运行", " | 定时任务准备执行..." };
                    foreach (var prefix in statusPrefixes)
                    {
                        int index = currentText.LastIndexOf(prefix);
                        if (index > 0)
                        {
                            currentText = currentText.Substring(0, index);
                            break;
                        }
                    }
                }

                // 添加最新的定时状态信息
                string scheduleInfo = GetScheduleInfo();
                speedInfoLabel.Text = currentText + scheduleInfo;
            }
        }

        private void UpdateScheduleStatus()
        {
            isScheduleEnabled = settings?.ScheduleSettings?.Enabled ?? false;

            // 使用UpdateButtonState方法统一更新所有按钮状态
            UpdateButtonState();

            // 更新下次运行时间显示
            UpdateNextRunTimeDisplay();
        }

        private void SetupDragDrop()
        {
            // 设置来源目录文本框的拖拽功能
            if (sourceFolderTextBox != null)
            {
                sourceFolderTextBox.AllowDrop = true;
                sourceFolderTextBox.Refresh();
                if (sourceFolderTextBox.Parent != null)
                    sourceFolderTextBox.Parent.AllowDrop = true;
                sourceFolderTextBox.DragEnter += SourceFolderTextBox_DragEnter;
                sourceFolderTextBox.DragDrop += SourceFolderTextBox_DragDrop;
            }

            // 设置输出目录文本框的拖拽功能
            if (targetFolderTextBox != null)
            {
                targetFolderTextBox.AllowDrop = true;
                targetFolderTextBox.Refresh();
                if (targetFolderTextBox.Parent != null)
                    targetFolderTextBox.Parent.AllowDrop = true;
                targetFolderTextBox.DragEnter += TargetFolderTextBox_DragEnter;
                targetFolderTextBox.DragDrop += TargetFolderTextBox_DragDrop;
            }
        }

        private void SourceFolderTextBox_DragEnter(object? sender, DragEventArgs e)
        {
            if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
            {
                if (e.Data.GetData(DataFormats.FileDrop) is string[] files && files.Length == 1 && Directory.Exists(files[0]))
                {
                    e.Effect = DragDropEffects.Copy;
                }
                else
                {
                    e.Effect = DragDropEffects.None;
                }
            }
            else
            {
                e.Effect = DragDropEffects.None;
            }
        }

        private void SourceFolderTextBox_DragDrop(object? sender, DragEventArgs e)
        {
            if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
            {
                if (e.Data.GetData(DataFormats.FileDrop) is string[] files && files.Length == 1 && Directory.Exists(files[0]))
                {
                    if (sourceFolderTextBox != null)
                    {
                        sourceFolderTextBox.Text = files[0];
                        if (settings != null) settings.SourceDirectory = files[0];
                        if (currentSettings != null) currentSettings.SourceDirectory = files[0];
                    }
                }
            }
        }

        private void TargetFolderTextBox_DragEnter(object? sender, DragEventArgs e)
        {
            if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
            {
                if (e.Data.GetData(DataFormats.FileDrop) is string[] files && files.Length == 1 && Directory.Exists(files[0]))
                {
                    e.Effect = DragDropEffects.Copy;
                }
                else
                {
                    e.Effect = DragDropEffects.None;
                }
            }
            else
            {
                e.Effect = DragDropEffects.None;
            }
        }

        private void TargetFolderTextBox_DragDrop(object? sender, DragEventArgs e)
        {
            if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
            {
                if (e.Data.GetData(DataFormats.FileDrop) is string[] files && files.Length == 1 && Directory.Exists(files[0]))
                {
                    if (targetFolderTextBox != null)
                    {
                        targetFolderTextBox.Text = files[0];
                        if (settings != null) settings.OutputDirectory = files[0];
                        if (currentSettings != null) currentSettings.OutputDirectory = files[0];
                    }
                }
            }
        }

        private void InitializeLayout()
        {
            // 设置窗体属性
            this.Text = "PPT演示文稿批量处理工具";
            this.Size = new System.Drawing.Size(1000, 800);  // 增加窗体高度以容纳新区域
            this.StartPosition = FormStartPosition.CenterScreen;

            // 创建菜单栏并添加到窗体
            CreateMenuStrip();
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);

            // 创建主布局
            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4,
                Padding = new Padding(10)
            };

            // 添加路径区域
            mainLayout.Controls.Add(CreatePathPanel(), 0, 0);

            // 添加功能按钮区域（包含了复选框开关）
            mainLayout.Controls.Add(CreateButtonPanel(), 0, 1);

            // 添加操作按钮和日志区域
            mainLayout.Controls.Add(CreateOperationPanel(), 0, 2);

            // 添加统计信息面板
            mainLayout.Controls.Add(CreateStatisticsPanel(), 0, 3);

            // 设置行高比例
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 180));  // 路径面板高度增加到180
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 190));  // 按钮区域高度
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 200));  // 操作按钮和日志区域高度
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 100));  // 统计信息面板高度

            // 创建容器面板，用于放置主布局（在菜单栏下方）
            Panel contentPanel = new Panel
            {
                Dock = DockStyle.Fill
            };
            contentPanel.Controls.Add(mainLayout);

            // 清除并重新添加控件，确保菜单栏在顶部，内容在下方
            this.Controls.Clear();
            this.Controls.Add(contentPanel);
            this.Controls.Add(menuStrip);

            // 重新设置按钮文本为中文
            if (startButton != null)
            {
                startButton.Text = "开始处理";
            }
            if (stopButton != null)
            {
                stopButton.Text = "停止处理";
            }

            // 添加按钮事件处理程序
            if (startButton != null)
            {
                startButton.Click += StartButton_Click;
            }
            if (stopButton != null)
            {
                stopButton.Click += StopButton_Click;
            }
        }

        private void CreateMenuStrip()
        {
            // 创建菜单栏
            menuStrip = new MenuStrip();
            menuStrip.Dock = DockStyle.Top;

            // 创建"文件"菜单
            fileMenu = new ToolStripMenuItem("文件(&F)");
            var openSourceFolderMenuItem = new ToolStripMenuItem("打开来源目录(&O)", null, OpenSourceFolder_Click);
            var openTargetFolderMenuItem = new ToolStripMenuItem("打开输出目录(&T)", null, OpenTargetFolder_Click);
            var exitMenuItem = new ToolStripMenuItem("退出(&X)", null, Exit_Click);
            fileMenu.DropDownItems.AddRange(new ToolStripItem[] {
                openSourceFolderMenuItem,
                openTargetFolderMenuItem,
                new ToolStripSeparator(),
                exitMenuItem
            });

            // 创建"操作"菜单
            operationMenu = new ToolStripMenuItem("操作(&O)");
            var startMenuItem = new ToolStripMenuItem("开始处理(&S)", null, StartMenuItem_Click);
            var stopMenuItem = new ToolStripMenuItem("停止处理(&P)", null, StopMenuItem_Click);
            var refreshMenuItem = new ToolStripMenuItem("刷新文件列表(&R)", null, RefreshMenuItem_Click);
            operationMenu.DropDownItems.AddRange(new ToolStripItem[] {
                startMenuItem,
                stopMenuItem,
                new ToolStripSeparator(),
                refreshMenuItem
            });

            // 创建"设置"菜单
            settingsMenu = new ToolStripMenuItem("设置(&S)");
            var pageSetupMenuItem = new ToolStripMenuItem("页面设置(&P)", null, PageSetup_Click);
            var docFormatMenuItem = new ToolStripMenuItem("文档格式(&D)", null, DocumentFormat_Click);
            var scheduleSettingsMenuItem = new ToolStripMenuItem("定时任务设置(&T)", null, ScheduleSettings_Click);
            var pdfSettingsMenuItem = new ToolStripMenuItem("PDF设置(&P)", null, PdfSettings_Click);
            settingsMenu.DropDownItems.AddRange(new ToolStripItem[] {
                pageSetupMenuItem,
                docFormatMenuItem,
                scheduleSettingsMenuItem,
                pdfSettingsMenuItem
            });

            // 创建"帮助"菜单
            helpMenu = new ToolStripMenuItem("帮助(&H)");
            var aboutMenuItem = new ToolStripMenuItem("关于(&A)", null, About_Click);
            helpMenu.DropDownItems.AddRange(new ToolStripItem[] {
                aboutMenuItem
            });

            // 将菜单项添加到菜单栏
            menuStrip.Items.AddRange(new ToolStripItem[] {
                fileMenu,
                operationMenu,
                settingsMenu,
                helpMenu
            });
        }

        // 菜单项的事件处理方法
        private void OpenSourceFolder_Click(object? sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(settings?.SourceDirectory) && Directory.Exists(settings.SourceDirectory))
            {
                try
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = "explorer.exe",
                        Arguments = settings.SourceDirectory,
                        UseShellExecute = true
                    });
                }
                catch (Exception ex)
                {
                    logger?.Log($"打开来源目录失败: {ex.Message}", LogLevel.Error);
                }
            }
            else
            {
                logger?.Log("来源目录不存在", LogLevel.Warning);
            }
        }

        private void OpenTargetFolder_Click(object? sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(settings?.OutputDirectory) && Directory.Exists(settings.OutputDirectory))
            {
                try
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = "explorer.exe",
                        Arguments = settings.OutputDirectory,
                        UseShellExecute = true
                    });
                }
                catch (Exception ex)
                {
                    logger?.Log($"打开输出目录失败: {ex.Message}", LogLevel.Error);
                }
            }
            else
            {
                logger?.Log("输出目录不存在", LogLevel.Warning);
            }
        }

        private void Exit_Click(object? sender, EventArgs e)
        {
            this.Close();
        }

        private void StartMenuItem_Click(object? sender, EventArgs e)
        {
            StartButton_Click(sender, e);
        }

        private void StopMenuItem_Click(object? sender, EventArgs e)
        {
            StopButton_Click(sender, e);
        }

        private void RefreshMenuItem_Click(object? sender, EventArgs e)
        {
            RefreshFileList();
        }

        private void PageSetup_Click(object? sender, EventArgs e)
        {
            if (currentSettings != null)
            {
                var pageSetupForm = new PageSetupForm(currentSettings);
                if (pageSetupForm.ShowDialog() == DialogResult.OK)
                {
                    // 更新设置并保存
                    SettingsManager.SaveSettings(currentSettings);
                    logger?.Log("页面设置已更新");
                }
            }
        }

        private void DocumentFormat_Click(object? sender, EventArgs e)
        {
            DocumentFormatButton_Click(sender, e);
        }

        private void ScheduleSettings_Click(object? sender, EventArgs e)
        {
            if (currentSettings != null)
            {
                // 确保ScheduleSettings不为null
                if (currentSettings.ScheduleSettings == null)
                {
                    currentSettings.ScheduleSettings = new ScheduleSettings();
                }

                var scheduleForm = new ScheduleSettingsForm(currentSettings.ScheduleSettings);
                if (scheduleForm.ShowDialog() == DialogResult.OK)
                {
                    // 更新定时设置
                    currentSettings.ScheduleSettings = scheduleForm.ScheduleSettings;
                    SettingsManager.SaveSettings(currentSettings);
                    isScheduleEnabled = currentSettings.ScheduleSettings.Enabled;
                    UpdateScheduleStatus();
                    logger?.Log("定时任务设置已更新");
                }
            }
        }

        private void PdfSettings_Click(object? sender, EventArgs e)
        {
            if (currentSettings != null)
            {
                var pdfForm = new PdfSettingsForm(currentSettings.PdfSettings ?? new PdfSettings());
                if (pdfForm.ShowDialog() == DialogResult.OK)
                {
                    // 更新PDF设置
                    currentSettings.PdfSettings = pdfForm.PdfSettings;
                    SettingsManager.SaveSettings(currentSettings);
                    logger?.Log("PDF转换设置已更新");
                }
            }
        }

        private void About_Click(object? sender, EventArgs e)
        {
            string version = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0";
            string aboutMessage = $"Word文档格式化工具 v{version}\n" +
                                 "本工具用于批量处理和格式化Word文档\n" +
                                 "**********  WWW.YZWK.COM \n" +
                                 "版权所有 © 2023 All Rights Reserved";
            MessageBox.Show(aboutMessage, "关于", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AppendLog(string message)
        {
            if (logTextBox == null)
                return;

            // 确保不超过最大行数（保留最新的日志）
            const int maxLines = 1000;
            var lines = logTextBox.Lines;
            if (lines.Length > maxLines)
            {
                var newLines = new string[maxLines];
                Array.Copy(lines, lines.Length - maxLines, newLines, 0, maxLines);
                logTextBox.Lines = newLines;
            }

            // 添加新日志消息
            logTextBox.AppendText(message + Environment.NewLine);
            logTextBox.SelectionStart = logTextBox.Text.Length;
            logTextBox.ScrollToCaret();
        }

        private Panel CreatePathPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,  // 从5列改为3列
                RowCount = 4,     // 恢复为4行
                Padding = new Padding(12),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 设置列宽
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));      // 标签列
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));      // 文本框和复选框列
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));      // 按钮列

            // 设置行高
            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 36));  // 来源目录行增加高度
            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 36));  // 输出目录行增加高度
            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 36));  // 冲突处理行增加高度
            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 36));  // 文档格式按钮行

            // 来源目录行
            var sourceLabel = new Label { Text = "来源目录:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Right };

            // 创建来源目录行的容器面板
            var sourcePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                Margin = new Padding(0)
            };
            sourcePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100)); // 文本框
            sourcePanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));     // 复选框

            // 创建源文件夹文本框，以防为null
            if (sourceFolderTextBox == null)
            {
                sourceFolderTextBox = new TextBox();
            }

            sourceFolderTextBox.Dock = DockStyle.Fill;
            sourceFolderTextBox.Text = settings?.SourceDirectory ?? "";
            sourceFolderTextBox.Margin = new Padding(0, 0, 5, 0);
            sourceFolderTextBox.TextAlign = HorizontalAlignment.Left;

            // 包含子目录复选框
            var includeSubdirsCheck = new CheckBox
            {
                Text = "包含子目录",
                Checked = settings?.IncludeSubdirectories ?? false,
                AutoSize = true,
                Anchor = AnchorStyles.Right
            };
            includeSubdirsCheck.CheckedChanged += (object? s, EventArgs e) =>
            {
                if (settings != null) settings.IncludeSubdirectories = includeSubdirsCheck.Checked;
                if (currentSettings != null) currentSettings.IncludeSubdirectories = includeSubdirsCheck.Checked;
            };

            sourcePanel.Controls.Add(sourceFolderTextBox, 0, 0);
            sourcePanel.Controls.Add(includeSubdirsCheck, 1, 0);

            var sourceButton = new Button {
                Text = "浏览...",
                Dock = DockStyle.Fill,
                Margin = new Padding(5, 0, 0, 0)
            };
            sourceButton.Click += (object? s, EventArgs e) =>
            {
                using (var dialog = new FolderBrowserDialog())
                {
                    if (dialog.ShowDialog() == DialogResult.OK && sourceFolderTextBox != null)
                    {
                        sourceFolderTextBox.Text = dialog.SelectedPath;
                        if (settings != null) settings.SourceDirectory = dialog.SelectedPath;
                        if (currentSettings != null) currentSettings.SourceDirectory = dialog.SelectedPath;
                    }
                }
            };

            // 输出目录行
            var outputLabel = new Label { Text = "输出目录:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Right };

            // 创建输出目录行的容器面板
            var targetPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                Margin = new Padding(0)
            };
            targetPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100)); // 文本框
            targetPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));     // 复选框

            // 创建目标文件夹文本框，以防为null
            if (targetFolderTextBox == null)
            {
                targetFolderTextBox = new TextBox();
            }

            targetFolderTextBox.Dock = DockStyle.Fill;
            targetFolderTextBox.Text = settings?.OutputDirectory ?? "";
            targetFolderTextBox.Margin = new Padding(0, 0, 5, 0);
            targetFolderTextBox.TextAlign = HorizontalAlignment.Left;

            // 保持目录结构复选框
            var keepStructureCheck = new CheckBox
            {
                Text = "保持目录结构",
                Checked = settings?.KeepDirectoryStructure ?? false,
                AutoSize = true,
                Anchor = AnchorStyles.Right
            };
            keepStructureCheck.CheckedChanged += (object? s, EventArgs e) =>
            {
                if (settings != null) settings.KeepDirectoryStructure = keepStructureCheck.Checked;
                if (currentSettings != null) currentSettings.KeepDirectoryStructure = keepStructureCheck.Checked;
            };

            targetPanel.Controls.Add(targetFolderTextBox, 0, 0);
            targetPanel.Controls.Add(keepStructureCheck, 1, 0);

            var outputButton = new Button {
                Text = "浏览...",
                Dock = DockStyle.Fill,
                Margin = new Padding(5, 0, 0, 0)
            };
            outputButton.Click += (object? s, EventArgs e) =>
            {
                using (var dialog = new FolderBrowserDialog())
                {
                    if (dialog.ShowDialog() == DialogResult.OK && targetFolderTextBox != null)
                    {
                        targetFolderTextBox.Text = dialog.SelectedPath;
                        if (settings != null) settings.OutputDirectory = dialog.SelectedPath;
                        if (currentSettings != null) currentSettings.OutputDirectory = dialog.SelectedPath;
                    }
                }
            };

            // 冲突处理选项
            var conflictLabel = new Label { Text = "冲突处理:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft };

            // 创建冲突处理行的容器面板，修改为单行水平排列
            var conflictPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = false,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 创建冲突处理下拉框
            var conflictCombo = new ComboBox
            {
                Items = { "覆盖", "跳过", "重命名" },
                SelectedItem = settings?.ConflictHandling ?? "覆盖",
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 90,
                Height = 25,
                Margin = new Padding(0, 5, 10, 0),
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                DrawMode = DrawMode.OwnerDrawFixed
            };
            conflictCombo.SelectedIndexChanged += (object? s, EventArgs e) =>
            {
                if (settings != null) settings.ConflictHandling = conflictCombo.SelectedItem?.ToString() ?? string.Empty;
                if (currentSettings != null) currentSettings.ConflictHandling = conflictCombo.SelectedItem?.ToString() ?? string.Empty;
            };
            conflictCombo.DrawItem += ComboBox_DrawItem;

            // 直接添加到面板，不再使用嵌套面板
            conflictPanel.Controls.Add(conflictCombo);

            // 添加文件处理方式单选按钮组
            var fileOperationPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = false,
                AutoSize = true,
                Padding = new Padding(10, 5, 0, 0),
                Margin = new Padding(0)
            };

            var copyRadio = new RadioButton
            {
                Text = "复制",
                Checked = !(settings?.MoveFiles ?? false),
                AutoSize = true,
                Margin = new Padding(0, 3, 10, 0)
            };
            copyRadio.CheckedChanged += (object? s, EventArgs e) =>
            {
                if (copyRadio.Checked)
                {
                    if (settings != null) settings.MoveFiles = false;
                    if (currentSettings != null) currentSettings.MoveFiles = false;
                }
            };

            var moveRadio = new RadioButton
            {
                Text = "移动",
                Checked = settings?.MoveFiles ?? false,
                AutoSize = true,
                Margin = new Padding(0, 3, 10, 0)
            };
            moveRadio.CheckedChanged += (object? s, EventArgs e) =>
            {
                if (moveRadio.Checked)
                {
                    if (settings != null) settings.MoveFiles = true;
                    if (currentSettings != null) currentSettings.MoveFiles = true;
                }
            };

            fileOperationPanel.Controls.Add(copyRadio);
            fileOperationPanel.Controls.Add(moveRadio);

            // 添加文件操作面板到主面板
            conflictPanel.Controls.Add(fileOperationPanel);

            // 创建各种线程设置控件
            var processOriginalCheck = new CheckBox
            {
                Text = "直接处理源文件",
                Checked = settings?.ProcessOriginalFiles ?? false,
                AutoSize = true,
                Margin = new Padding(0, 5, 10, 0),
                Anchor = AnchorStyles.Left | AnchorStyles.Top
            };
            processOriginalCheck.CheckedChanged += (object? s, EventArgs e) =>
            {
                if (settings != null) settings.ProcessOriginalFiles = processOriginalCheck.Checked;
                if (currentSettings != null) currentSettings.ProcessOriginalFiles = processOriginalCheck.Checked;

                // 更新UI状态
                bool processOriginalEnabled = processOriginalCheck.Checked;
                if (copyRadio != null) copyRadio.Enabled = !processOriginalEnabled;
                if (moveRadio != null) moveRadio.Enabled = !processOriginalEnabled;
                if (targetFolderTextBox != null) targetFolderTextBox.Enabled = !processOriginalEnabled;
                if (outputButton != null) outputButton.Enabled = !processOriginalEnabled;
                if (keepStructureCheck != null) keepStructureCheck.Enabled = !processOriginalEnabled;
            };

            var threadCountLabel = new Label { Text = "线程数:", AutoSize = true, Margin = new Padding(0, 6, 5, 0) };
            var threadCountNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 32,
                Value = settings?.MaxThreads ?? 1,
                TextAlign = HorizontalAlignment.Center,
                Width = 70,
                // 如果启用了智能线程管理，则禁用线程数输入框
                Enabled = !(settings?.EnableSmartThreading ?? false)
            };
            threadCountNumeric.ValueChanged += (object? s, EventArgs e) =>
            {
                if (settings != null) settings.MaxThreads = (int)threadCountNumeric.Value;
                if (currentSettings != null) currentSettings.MaxThreads = (int)threadCountNumeric.Value;
                logger?.Log($"线程数已更新为: {settings?.MaxThreads ?? (int)threadCountNumeric.Value}");
            };

            // 添加智能线程管理选项
            var smartThreadingCheck = new CheckBox
            {
                Text = "智能线程管理",
                AutoSize = true,
                Checked = settings?.EnableSmartThreading ?? false,
                Margin = new Padding(10, 6, 5, 0)
            };
            smartThreadingCheck.CheckedChanged += (object? s, EventArgs e) =>
            {
                if (settings != null) settings.EnableSmartThreading = smartThreadingCheck.Checked;
                if (currentSettings != null) currentSettings.EnableSmartThreading = smartThreadingCheck.Checked;

                // 当启用智能线程管理时，禁用线程数输入框
                threadCountNumeric.Enabled = !smartThreadingCheck.Checked;

                // 如果启用智能线程管理，显示提示信息
                if (smartThreadingCheck.Checked)
                {
                    // 保存当前值
                    if (threadCountNumeric.Value > 0)
                    {
                        if (settings != null) settings.MaxThreads = (int)threadCountNumeric.Value;
                        if (currentSettings != null) currentSettings.MaxThreads = (int)threadCountNumeric.Value;
                    }

                    // 设置为0表示自动
                    threadCountNumeric.Value = 0;
                    if (settings != null) settings.MaxThreads = 0;
                    if (currentSettings != null) currentSettings.MaxThreads = 0;

                    logger?.Log("智能线程管理已启用，线程数将根据系统负载自动调整");
                }
                else
                {
                    // 如果当前值为0，设置为默认值
                    if (threadCountNumeric.Value == 0)
                    {
                        int defaultThreads = Math.Max(1, Environment.ProcessorCount / 2);
                        threadCountNumeric.Value = defaultThreads;
                        if (settings != null) settings.MaxThreads = defaultThreads;
                        if (currentSettings != null) currentSettings.MaxThreads = defaultThreads;
                    }

                    logger?.Log("智能线程管理已禁用，将使用手动设置的线程数");
                }
            };

            var retryCountLabel = new Label { Text = "重试次数:", AutoSize = true, Margin = new Padding(0, 6, 5, 0) };
            var retryCountNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 10,
                Value = settings?.MaxRetryCount ?? 0,
                TextAlign = HorizontalAlignment.Center,
                Width = 70
            };
            retryCountNumeric.ValueChanged += (object? s, EventArgs e) =>
            {
                if (settings != null) settings.MaxRetryCount = (int)retryCountNumeric.Value;
                if (currentSettings != null) currentSettings.MaxRetryCount = (int)retryCountNumeric.Value;
            };



            // 创建线程设置容器面板
            var threadPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = false,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0, 5, 0, 0) // 添加上边距，与冲突处理下拉框对齐
            };

            // 将线程设置相关控件添加到线程面板（不包括批处理大小）
            threadPanel.Controls.Add(processOriginalCheck);
            threadPanel.Controls.Add(threadCountLabel);
            threadPanel.Controls.Add(threadCountNumeric);
            threadPanel.Controls.Add(smartThreadingCheck);
            threadPanel.Controls.Add(retryCountLabel);
            threadPanel.Controls.Add(retryCountNumeric);



            // 添加线程设置面板到主面板
            conflictPanel.Controls.Add(threadPanel);



            // 添加控件到布局
            if (sourceLabel != null) layout.Controls.Add(sourceLabel, 0, 0);
            if (sourcePanel != null) layout.Controls.Add(sourcePanel, 1, 0);
            if (sourceButton != null) layout.Controls.Add(sourceButton, 2, 0);

            if (outputLabel != null) layout.Controls.Add(outputLabel, 0, 1);
            if (targetPanel != null) layout.Controls.Add(targetPanel, 1, 1);
            if (outputButton != null) layout.Controls.Add(outputButton, 2, 1);

            if (conflictLabel != null) layout.Controls.Add(conflictLabel, 0, 2);
            if (conflictPanel != null) layout.Controls.Add(conflictPanel, 1, 2);

            // 添加"文档格式"标签
            var documentFormatLabel = new Label
            {
                Text = "支持文档：",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft
            };
            layout.Controls.Add(documentFormatLabel, 0, 3);

            // 为文档格式按钮和全选/取消全选按钮创建一个容器
            var buttonsFlowPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = false,
                Margin = new Padding(0)
            };

            // 添加"文档格式"按钮
            var documentFormatButton = new Button
            {
                Text = "文档格式",
                AutoSize = true,
                Anchor = AnchorStyles.Left
            };
            documentFormatButton.Click += DocumentFormatButton_Click;
            buttonsFlowPanel.Controls.Add(documentFormatButton);

            // 添加"全选"按钮
            var selectAllButton = new Button
            {
                Text = "全选",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                Margin = new Padding(5, 0, 0, 0) // 左侧边距增加
            };
            selectAllButton.Click += SelectAllButton_Click;
            buttonsFlowPanel.Controls.Add(selectAllButton);

            // 添加"取消全选"按钮
            var unselectAllButton = new Button
            {
                Text = "取消全选",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                Margin = new Padding(5, 0, 0, 0) // 左侧边距增加
            };
            unselectAllButton.Click += UnselectAllButton_Click;
            buttonsFlowPanel.Controls.Add(unselectAllButton);

            // 创建批处理大小标签
            var batchSizeLabel = new Label {
                Text = "批处理大小:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(20, 6, 5, 0) // 增加左侧边距，调整垂直位置
            };
            buttonsFlowPanel.Controls.Add(batchSizeLabel);

            // 创建批处理大小数值控件
            var batchSizeNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 1000,
                Value = settings?.BatchSize ?? 10,
                TextAlign = HorizontalAlignment.Center,
                Width = 70,
                Margin = new Padding(0, 8, 0, 0)
            };
            batchSizeNumeric.ValueChanged += (object? s, EventArgs e) =>
            {
                // 直接设置批处理大小，无需使用反射
                if (settings != null) settings.BatchSize = (int)batchSizeNumeric.Value;
                if (currentSettings != null) currentSettings.BatchSize = (int)batchSizeNumeric.Value;

                if (batchSizeNumeric.Value == 0)
                {
                    logger?.Log("批处理大小设置为自动，将根据系统内存和线程数自动计算最优批处理大小");
                }
                else
                {
                    logger?.Log($"批处理大小已更新为: {settings?.BatchSize ?? (int)batchSizeNumeric.Value}");
                }
            };
            buttonsFlowPanel.Controls.Add(batchSizeNumeric);

            // 将按钮容器添加到布局
            layout.Controls.Add(buttonsFlowPanel, 1, 3);

            // 根据"直接处理源文件"设置更新控件状态
            bool processOriginalEnabled = settings?.ProcessOriginalFiles ?? false;
            if (copyRadio != null) copyRadio.Enabled = !processOriginalEnabled;
            if (moveRadio != null) moveRadio.Enabled = !processOriginalEnabled;
            if (targetFolderTextBox != null) targetFolderTextBox.Enabled = !processOriginalEnabled;
            if (outputButton != null) outputButton.Enabled = !processOriginalEnabled;
            if (keepStructureCheck != null) keepStructureCheck.Enabled = !processOriginalEnabled;

            panel.Controls.Add(layout);
            return panel;
        }

        private Panel CreateButtonPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 3,
                Padding = new Padding(10), // 增加整体内边距
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 设置列宽相等
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));

            // 设置行高比例，增加行间距
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33F));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33F));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33F));

            // 创建ToolTip组件
            var toolTip = new ToolTip
            {
                AutoPopDelay = 5000,
                InitialDelay = 1000,
                ReshowDelay = 500,
                ShowAlways = true
            };

            // 创建标准按钮样式
            Action<Button> styleButton = (btn) => {
                btn.Dock = DockStyle.Fill;
                btn.Margin = new Padding(5); // 增加按钮边距
                btn.FlatStyle = FlatStyle.System;
                btn.Font = new System.Drawing.Font(this.Font.FontFamily, 9F);
            };

            // 创建标准复选框样式
            Action<CheckBox> styleCheckBox = (cb) => {
                cb.AutoSize = true;
                cb.Margin = new Padding(5); // 增加复选框边距
                cb.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
                cb.Cursor = Cursors.Hand;
                cb.FlatStyle = FlatStyle.System;
                cb.Anchor = AnchorStyles.Left;
            };

            // 创建复合控件容器函数
            Func<string, bool, EventHandler, EventHandler, TableLayoutPanel> createButtonWithCheckBox =
                (string buttonText, bool isChecked, EventHandler checkChangedHandler, EventHandler buttonClickHandler) => {
                    var container = new TableLayoutPanel
                    {
                        Dock = DockStyle.Fill,
                        ColumnCount = 2,
                        RowCount = 1,
                        Margin = new Padding(3), // 增加容器边距
                        CellBorderStyle = TableLayoutPanelCellBorderStyle.None
                    };

                    // 设置列宽比例 - 调整复选框列宽，给予更多空间
                    container.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 45)); // 复选框列宽增加
                    container.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100)); // 按钮列宽

                    // 创建复选框 - 调整样式
                    var checkBox = new CheckBox {
                        Checked = isChecked,
                        Text = "",
                        AutoSize = false, // 禁用自动大小
                        Width = 45, // 增加宽度
                        Height = 45, // 增加高度
                        Margin = new Padding(12, 5, 0, 0), // 调整边距垂直居中
                        FlatStyle = FlatStyle.Standard, // 标准样式，便于显示更大的复选框
                        UseVisualStyleBackColor = false, // 禁用视觉样式，使用自定义样式
                        BackColor = SystemColors.Control, // 使用系统背景色
                        Appearance = Appearance.Normal // 确保是正常的复选框外观
                    };

                    // 增加复选框边距，让勾选区域更加突出
                    checkBox.Padding = new Padding(3);
                    checkBox.CheckedChanged += checkChangedHandler;

                    // 创建按钮
                    var button = new Button {
                        Text = buttonText,
                        Dock = DockStyle.Fill,
                        Margin = new Padding(8, 4, 4, 4), // 增加按钮边距
                        FlatStyle = FlatStyle.System,
                        Font = new System.Drawing.Font(this.Font.FontFamily, 9F)
                    };
                    button.Click += buttonClickHandler;

                    // 添加到容器
                    container.Controls.Add(checkBox, 0, 0);
                    container.Controls.Add(button, 1, 0);

                    return container;
                };

            // 页面设置
            var pageSetupContainer = createButtonWithCheckBox(
                "页面设置",
                settings?.EnablePageSetup ?? false,
                (s, e) => {
                    if (s is CheckBox checkbox)
                    {
                        if (settings != null) settings.EnablePageSetup = checkbox.Checked;
                        if (currentSettings != null) currentSettings.EnablePageSetup = checkbox.Checked;
                    }
                },
                (s, e) => {
                    logger?.Log("打开页面设置窗口");
                    if (currentSettings != null)
                    {
                        using var form = new PageSetupForm(currentSettings);
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            logger?.Log("已更新页面设置");
                            // 记录页面设置的更改到日志
                            logger?.Log("应用页面设置完成");
                            logger?.Log($"页边距设置: 上={currentSettings.PageSetup?.TopMargin.ToString() ?? "0"}, 下={currentSettings.PageSetup?.BottomMargin.ToString() ?? "0"}, 左={currentSettings.PageSetup?.LeftMargin.ToString() ?? "0"}, 右={currentSettings.PageSetup?.RightMargin.ToString() ?? "0"}");
                            logger?.Log($"纸张大小: {(currentSettings.PageSetup?.PaperSize != null ? currentSettings.PageSetup.PaperSize.ToString() : "未设置")}, 方向: {(currentSettings.PageSetup?.Orientation != null ? currentSettings.PageSetup.Orientation.ToString() : "未设置")}");

                            // 保存更改后的设置到配置文件中
                            try
                            {
                                logger?.Log("正在保存页面设置到配置文件...");
                                SettingsManager.SaveSettings(currentSettings);
                                logger?.Log("已成功保存页面设置到配置文件。");
                            }
                            catch (Exception ex)
                            {
                                logger?.LogError("保存页面设置时出错", ex);
                                MessageBox.Show($"保存设置时出错: {ex.Message}", "保存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            );
            toolTip.SetToolTip(pageSetupContainer.Controls[0], "启用或禁用页面设置功能");
            if (pageSetupContainer.Controls.Count > 1 && pageSetupContainer.Controls[1] is Button setupButton)
            {
                settingsButton = setupButton; // 保存引用
            }

            // 删除内容设置
            var deleteContentContainer = createButtonWithCheckBox(
                "删除内容设置",
                settings?.EnableDeleteContent ?? false,
                (s, e) => {
                    if (s is CheckBox checkbox)
                    {
                        if (settings != null) settings.EnableDeleteContent = checkbox.Checked;
                        if (currentSettings != null) currentSettings.EnableDeleteContent = checkbox.Checked;
                    }
                },
                (s, e) => {
                    logger?.Log("打开删除内容设置窗口");
                    if (currentSettings != null)
                    {
                        if (currentSettings.DeleteSettings == null)
                        {
                            currentSettings.DeleteSettings = new DeleteSettings();
                        }

                        using var form = new DeleteContentForm(currentSettings.DeleteSettings);
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            logger?.Log("删除内容设置已更新");

                            // 保存更改后的设置到配置文件中
                            try
                            {
                                logger?.Log("正在保存删除内容设置到配置文件...");
                                SettingsManager.SaveSettings(currentSettings);
                                logger?.Log("已成功保存删除内容设置到配置文件。");
                            }
                            catch (Exception ex)
                            {
                                logger?.LogError("保存删除内容设置时出错", ex);
                                MessageBox.Show($"保存设置时出错: {ex.Message}", "保存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            );
            toolTip.SetToolTip(deleteContentContainer.Controls[0], "启用或禁用删除内容设置功能");

            // 内容替换规则
            var contentReplaceContainer = createButtonWithCheckBox(
                "内容替换规则",
                settings?.EnableContentReplace ?? false,
                (s, e) => {
                    if (s is CheckBox checkbox)
                    {
                        if (settings != null) settings.EnableContentReplace = checkbox.Checked;
                        if (currentSettings != null) currentSettings.EnableContentReplace = checkbox.Checked;
                    }
                },
                (s, e) => {
                    logger?.Log("打开内容替换规则窗口");
                    if (currentSettings != null)
                    {
                        using var form = new ContentReplaceForm(currentSettings.ContentReplaceRules);
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            currentSettings.ContentReplaceRules = form.Rules;
                            if (settings != null) settings.ContentReplaceRules = form.Rules;
                            logger?.Log("内容替换规则已更新");

                            // 保存更改后的设置到配置文件中
                            try
                            {
                                logger?.Log("正在保存内容替换规则到配置文件...");
                                SettingsManager.SaveSettings(currentSettings);
                                logger?.Log("已成功保存内容替换规则到配置文件。");
                            }
                            catch (Exception ex)
                            {
                                logger?.LogError("保存内容替换规则时出错", ex);
                                MessageBox.Show($"保存设置时出错: {ex.Message}", "保存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            );
            toolTip.SetToolTip(contentReplaceContainer.Controls[0], "启用或禁用内容替换规则功能");

            // 全局段落格式
            var globalParaFormatContainer = createButtonWithCheckBox(
                "全局段落格式",
                settings?.EnableGlobalParagraphFormat ?? false,
                (s, e) => {
                    if (s is CheckBox checkbox)
                    {
                        if (settings != null) settings.EnableGlobalParagraphFormat = checkbox.Checked;
                        if (currentSettings != null) currentSettings.EnableGlobalParagraphFormat = checkbox.Checked;
                    }
                },
                (s, e) => {
                    logger?.Log("打开全局段落格式窗口");
                    if (currentSettings != null)
                    {
                        // 确保GlobalParagraphFormat不为null，如果为null则使用默认设置
                        if (currentSettings.GlobalParagraphFormat == null)
                        {
                            // 创建一个具有完整默认值的ParagraphMatchRule
                            currentSettings.GlobalParagraphFormat = new AsposeSlidesFormatter.Models.ParagraphMatchRule
                            {
                            HyperlinkUrl = string.Empty,
                            HyperlinkToolTip = string.Empty,
                            BookmarkName = string.Empty,
                            // 设置默认颜色值
                            FontColor = Color.Black,
                            HighlightColor = null,
                            BorderColor = Color.Black,
                            ShadingColor = Color.LightGray,
                            WatermarkColor = Color.FromArgb(128, Color.Gray),
                            WatermarkBorderColor = Color.Black,
                            TableBorderColor = Color.Black,
                            TableGridlineColor = Color.LightGray,
                            TableShadingColor = Color.LightGray,
                            CellBorderColor = Color.Black,
                            CellShadingColor = Color.LightGray,
                            ImageBorderColor = Color.Black,
                            // 设置默认功能启用状态
                            EnableBasicFormat = true,
                            EnableFontFormat = true,
                            EnableBorderShading = false,
                            EnablePagination = false,
                            EnableTabStops = false,
                            EnableChineseFont = true,
                            EnableWesternFont = true,
                            EnableComplexScriptFont = true,
                            EnableFontColor = true,
                            EnableHighlightColor = true,
                            // 设置应用范围默认值
                            ApplyToMainDocument = true,
                            ApplyToHeader = false,
                            ApplyToFooter = false,
                            ApplyToTextBox = true,
                            ApplyToFootnote = false,
                            ApplyToEndnote = false,
                                ApplyToComment = false
                            };
                        }

                        using var form = new GlobalParagraphFormatForm(currentSettings.GlobalParagraphFormat);
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            currentSettings.GlobalParagraphFormat = form.FormatRule;
                            if (settings != null) settings.GlobalParagraphFormat = form.FormatRule;
                            logger?.Log("全局段落格式已更新");

                            // 保存更改后的设置到配置文件中
                            try
                            {
                                logger?.Log("正在保存全局段落格式到配置文件...");
                                SettingsManager.SaveSettings(currentSettings);
                                logger?.Log("已成功保存全局段落格式到配置文件。");
                            }
                            catch (Exception ex)
                            {
                                logger?.LogError("保存全局段落格式时出错", ex);
                                MessageBox.Show($"保存设置时出错: {ex.Message}", "保存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            );
            toolTip.SetToolTip(globalParaFormatContainer.Controls[0], "启用或禁用全局段落格式功能");

            // 段落匹配规则
            var paragraphMatchContainer = createButtonWithCheckBox(
                "段落匹配规则",
                settings?.EnableParagraphMatch ?? false,
                (s, e) => {
                    if (s is CheckBox checkbox)
                    {
                        if (settings != null) settings.EnableParagraphMatch = checkbox.Checked;
                        if (currentSettings != null) currentSettings.EnableParagraphMatch = checkbox.Checked;
                    }
                },
                (s, e) => {
                    logger?.Log("打开段落匹配规则窗口");
                    if (currentSettings != null)
                    {
                        using var form = new ParagraphMatchForm(currentSettings.ParagraphMatchRules);
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            currentSettings.ParagraphMatchRules = form.Rules;
                            if (settings != null) settings.ParagraphMatchRules = form.Rules;
                            logger?.Log("段落匹配规则已更新");

                            // 保存更改后的设置到配置文件中
                            try
                            {
                                logger?.Log("正在保存段落匹配规则到配置文件...");
                                SettingsManager.SaveSettings(currentSettings);
                                logger?.Log("已成功保存段落匹配规则到配置文件。");
                            }
                            catch (Exception ex)
                            {
                                logger?.LogError("保存段落匹配规则时出错", ex);
                                MessageBox.Show($"保存设置时出错: {ex.Message}", "保存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            );
            toolTip.SetToolTip(paragraphMatchContainer.Controls[0], "启用或禁用段落匹配规则功能");

            // 页眉页脚设置
            var headerFooterContainer = createButtonWithCheckBox(
                "页眉页脚设置",
                settings?.EnableHeaderFooter ?? false,
                (s, e) => {
                    if (s is CheckBox checkbox)
                    {
                        if (settings != null) settings.EnableHeaderFooter = checkbox.Checked;
                        if (currentSettings != null) currentSettings.EnableHeaderFooter = checkbox.Checked;
                    }
                },
                (s, e) => {
                    logger?.Log("打开页眉页脚设置窗口");
                    if (currentSettings != null)
                    {
                        if (currentSettings.HeaderFooterSettings == null)
                        {
                            currentSettings.HeaderFooterSettings = new HeaderFooterSettings();
                        }

                        using var form = new HeaderFooterForm(currentSettings.HeaderFooterSettings);
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            currentSettings.HeaderFooterSettings = form.HeaderFooterSettings;
                            if (settings != null) settings.HeaderFooterSettings = form.HeaderFooterSettings;
                            logger?.Log("页眉页脚设置已更新");

                            // 保存更改后的设置到配置文件中
                            try
                            {
                                logger?.Log("正在保存页眉页脚设置到配置文件...");
                                SettingsManager.SaveSettings(currentSettings);
                                logger?.Log("已成功保存页眉页脚设置到配置文件。");
                            }
                            catch (Exception ex)
                            {
                                logger?.LogError("保存页眉页脚设置时出错", ex);
                                MessageBox.Show($"保存设置时出错: {ex.Message}", "保存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            );
            toolTip.SetToolTip(headerFooterContainer.Controls[0], "启用或禁用页眉页脚设置功能");

            // 文档属性
            var docPropsContainer = createButtonWithCheckBox(
                "文档属性",
                settings?.EnableDocumentProperties ?? false,
                (s, e) => {
                    if (s is CheckBox checkbox)
                    {
                        if (settings != null) settings.EnableDocumentProperties = checkbox.Checked;
                        if (currentSettings != null) currentSettings.EnableDocumentProperties = checkbox.Checked;
                    }
                },
                (s, e) => {
                    logger?.Log("打开文档属性窗口");
                    if (currentSettings != null)
                    {
                        using var form = new DocumentPropertiesForm(currentSettings.DocumentProperties);
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            currentSettings.DocumentProperties = form.DocumentProperties;
                            if (settings != null) settings.DocumentProperties = form.DocumentProperties;
                            logger?.Log("文档属性已更新");

                            // 保存更改后的设置到配置文件中
                            try
                            {
                                logger?.Log("正在保存文档属性到配置文件...");
                                SettingsManager.SaveSettings(currentSettings);
                                logger?.Log("已成功保存文档属性到配置文件。");
                            }
                            catch (Exception ex)
                            {
                                logger?.LogError("保存文档属性时出错", ex);
                                MessageBox.Show($"保存设置时出错: {ex.Message}", "保存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            );
            toolTip.SetToolTip(docPropsContainer.Controls[0], "启用或禁用文档属性功能");

            // 文件名替换
            var fileNameRulesContainer = createButtonWithCheckBox(
                "文件名替换",
                settings?.EnableFileNameReplace ?? false,
                (s, e) => {
                    if (s is CheckBox checkbox)
                    {
                        if (settings != null) settings.EnableFileNameReplace = checkbox.Checked;
                        if (currentSettings != null) currentSettings.EnableFileNameReplace = checkbox.Checked;
                    }
                },
                (s, e) => {
                    logger?.Log("打开文件名替换窗口");
                    if (currentSettings != null)
                    {
                        // 如果没有规则，添加一个示例规则
                        if (currentSettings.FileNameReplaceRules.Count == 0)
                        {
                            currentSettings.FileNameReplaceRules.Add(new FileNameReplaceRule
                            {
                                IsEnabled = true,
                                OldValue = "示例_",
                                NewValue = "",
                                IsRegex = false,
                                IsCaseSensitive = false,
                                IncludeExtension = false
                            });
                            logger?.Log("添加了示例文件名替换规则");
                        }

                        using var form = new FileNameReplaceForm(currentSettings.FileNameReplaceRules);
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            currentSettings.FileNameReplaceRules = form.Rules;
                            if (settings != null) settings.FileNameReplaceRules = form.Rules;
                            logger?.Log("文件名替换规则已更新");

                            // 保存更改后的设置到配置文件中
                            try
                            {
                                logger?.Log("正在保存文件名替换规则到配置文件...");
                                SettingsManager.SaveSettings(currentSettings);
                                logger?.Log("已成功保存文件名替换规则到配置文件。");
                            }
                            catch (Exception ex)
                            {
                                logger?.LogError("保存文件名替换规则时出错", ex);
                                MessageBox.Show($"保存设置时出错: {ex.Message}", "保存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            );
            toolTip.SetToolTip(fileNameRulesContainer.Controls[0], "启用或禁用文件名替换功能");

            // Word转PDF
            var word2PdfContainer = createButtonWithCheckBox(
                "Word转PDF",
                settings?.EnableWordToPdf ?? false,
                (s, e) => {
                    if (s is CheckBox checkbox)
                    {
                        if (settings != null) settings.EnableWordToPdf = checkbox.Checked;
                        if (currentSettings != null) currentSettings.EnableWordToPdf = checkbox.Checked;
                    }
                },
                (s, e) => {
                    logger?.Log("打开Word转PDF设置");
                    if (currentSettings != null)
                    {
                        // 检查是否启用了Word转PDF功能
                        if (!(settings?.EnableWordToPdf ?? false))
                        {
                            MessageBox.Show("Word转PDF功能已关闭，请启用该功能后再试。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return;
                        }

                        // 显示PDF设置窗口
                        using (var form = new PdfSettingsForm(currentSettings.PdfSettings ?? new PdfSettings()))
                        {
                            if (form.ShowDialog() == DialogResult.OK)
                            {
                                logger?.Log("PDF转换设置已更新");

                                try
                                {
                                    SettingsManager.SaveSettings(currentSettings);
                                    logger?.Log("PDF设置已保存到配置文件");
                                }
                                catch (Exception ex)
                                {
                                    logger?.LogError("保存PDF设置到配置文件时出错", ex);
                                    MessageBox.Show($"保存设置时出错: {ex.Message}", "保存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                        }
                    }
                }
            );
            toolTip.SetToolTip(word2PdfContainer.Controls[0], "启用或禁用Word转PDF功能");

            // 添加PDF设置按钮
            var pdfSettingsButton = new Button
            {
                Text = "PDF设置",
                AutoSize = true,
                Dock = DockStyle.Bottom,
                Margin = new Padding(0, 5, 0, 0),
                Padding = new Padding(10, 3, 10, 3)
            };

            pdfSettingsButton.Click += (s, e) =>
            {
                if (currentSettings != null)
                {
                    using (var form = new PdfSettingsForm(currentSettings.PdfSettings ?? new PdfSettings()))
                    {
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            logger?.Log("PDF转换设置已更新");

                            try
                            {
                                SettingsManager.SaveSettings(currentSettings);
                                logger?.Log("PDF设置已保存到配置文件");
                            }
                            catch (Exception ex)
                            {
                                logger?.LogError("保存PDF设置到配置文件时出错", ex);
                                MessageBox.Show($"保存设置时出错: {ex.Message}", "保存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            };

            word2PdfContainer.Controls.Add(pdfSettingsButton);
            toolTip.SetToolTip(pdfSettingsButton, "配置PDF转换的详细设置");

            // 添加控件到布局
            layout.Controls.Add(pageSetupContainer, 0, 0);           // 页面设置
            layout.Controls.Add(deleteContentContainer, 1, 0);       // 删除内容设置
            layout.Controls.Add(contentReplaceContainer, 2, 0);      // 内容替换规则

            layout.Controls.Add(globalParaFormatContainer, 0, 1);    // 全局段落格式
            layout.Controls.Add(paragraphMatchContainer, 1, 1);      // 段落匹配规则
            layout.Controls.Add(headerFooterContainer, 2, 1);        // 页眉页脚设置

            layout.Controls.Add(docPropsContainer, 0, 2);            // 文档属性
            layout.Controls.Add(fileNameRulesContainer, 1, 2);       // 文件名替换
            layout.Controls.Add(word2PdfContainer, 2, 2);            // Word转PDF

            panel.Controls.Add(layout);
            return panel;
        }

        // 重置统计信息
        private void ResetStatistics()
        {
            // 重置总文件数
            if (totalFilesLabel?.Tag is Label totalLabel)
                totalLabel.Text = "0";

            // 重置成功处理数及百分比
            if (successFilesLabel?.Tag is Label successLabel)
                successLabel.Text = "0 (0.0%)";

            // 重置处理失败数及百分比
            if (failedFilesLabel?.Tag is Label failedLabel)
                failedLabel.Text = "0 (0.0%)";

            // 重置重试次数及百分比
            if (retriedFilesLabel?.Tag is Label retriedLabel)
                retriedLabel.Text = "0 (0.0%)";

            // 开始时间重置为当前时间
            processingStartTime = DateTime.Now;
            if (processingTimeLabel?.Tag is Label timeLabel)
                timeLabel.Text = processingStartTime.ToString("yyyy-MM-dd HH:mm:ss");

            // 重置预计结束时间 - 只在初始化时设置为"--"
            if (expectedEndTimeLabel?.Tag is Label endTimeLabel)
            {
                // 检查是否是初始化阶段
                if (string.IsNullOrEmpty(endTimeLabel.Text) || endTimeLabel.Text == "--")
                    endTimeLabel.Text = "--";
                // 否则保留现有值
            }

            // 重置进度条
            if (progressBarControl != null)
                progressBarControl.Value = 0;

            if (progressBarLabel != null)
                progressBarLabel.Text = "0.0%";

            // 重置统计信息持久化字段
            currentProcessingSpeed = 0.0;
            speedUnit = "文件/分钟";
            currentRemainingTime = TimeSpan.Zero;
            lastValidEstimatedEndTime = "--";

            // 重置速度和时间信息
            if (speedInfoLabel != null)
            {
                // 获取定时状态信息（如果有）
                string scheduleInfo = GetScheduleInfo();

                speedInfoLabel.Text = $"处理速度: 0.0 {speedUnit} | 处理耗时: 00:00:00 | 预计剩余时间: --:--:--{scheduleInfo}";
            }

            // 重置计算处理速度和剩余时间使用的变量
            lastUpdateTime = DateTime.Now;
            lastProcessedCount = 0;
        }

        // 获取定时状态信息的辅助方法
        private string GetScheduleInfo()
        {
            if (nextRunTimeLabel != null && !string.IsNullOrEmpty(nextRunTimeLabel.Text) &&
                nextRunTimeLabel.Text != "定时处理未启用")
            {
                return $" | {nextRunTimeLabel.Text}";
            }
            return "";
        }

        // 更新统计信息的方法
        private void UpdateStatistics(int total, int success, int failed, int retried)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => UpdateStatistics(total, success, failed, retried)));
                return;
            }

            var now = DateTime.Now;
            var timeSpan = now - lastUpdateTime;
            var processedCount = success + failed;
            var newProcessedCount = processedCount - lastProcessedCount;

            // 计算处理速度（文件/分钟）
            double speed = timeSpan.TotalMinutes > 0 ? newProcessedCount / timeSpan.TotalMinutes : 0;

            // 更新持久化的处理速度
            if (speed > 0 || currentProcessingSpeed == 0)
            {
                currentProcessingSpeed = speed;
                speedUnit = "文件/分钟";
            }

            // 计算预计剩余时间
            TimeSpan remainingTime = TimeSpan.Zero;
            DateTime estimatedEndTime = DateTime.Now;

            if (speed > 0)
            {
                var remainingFiles = total - processedCount;
                double remainingMinutes = remainingFiles / speed;
                remainingTime = TimeSpan.FromMinutes(remainingMinutes);

                // 更新持久化的预计剩余时间
                currentRemainingTime = remainingTime;

                // 计算预计结束时间
                estimatedEndTime = DateTime.Now.Add(remainingTime);
            }

            // 更新预计结束时间
            if (expectedEndTimeLabel?.Tag is Label endTimeLabel)
            {
                if (speed > 0 && processedCount > 0 && processedCount < total)
                {
                    // 保存有效的预计结束时间
                    string newEstimatedEndTime = estimatedEndTime.ToString("yyyy-MM-dd HH:mm:ss");
                    lastValidEstimatedEndTime = newEstimatedEndTime;
                    endTimeLabel.Text = newEstimatedEndTime;
                }
                else if (!string.IsNullOrEmpty(lastValidEstimatedEndTime) && lastValidEstimatedEndTime != "--")
                {
                    // 使用最后有效的预计结束时间
                    endTimeLabel.Text = lastValidEstimatedEndTime;
                }
            }

            // 计算处理耗时
            TimeSpan elapsed = DateTime.Now - processingStartTime;

            // 更新速度和时间信息
            if (speedInfoLabel != null)
            {
                // 获取定时状态信息
                string scheduleInfo = GetScheduleInfo();

                speedInfoLabel.Text = $"处理速度: {currentProcessingSpeed:F1} {speedUnit} | 处理耗时: {elapsed.Hours:00}:{elapsed.Minutes:00}:{elapsed.Seconds:00} | 预计剩余时间: {currentRemainingTime:hh\\:mm\\:ss}{scheduleInfo}";
            }

            // 更新总文件数
            if (totalFilesLabel?.Tag is Label totalLabel)
                totalLabel.Text = total.ToString();

            // 更新成功文件数及百分比
            if (successFilesLabel?.Tag is Label successLabel)
            {
                double successRate = total > 0 ? (double)success / total * 100 : 0;
                successLabel.Text = $"{success} ({successRate:F1}%)";
            }

            // 更新失败文件数及百分比
            if (failedFilesLabel?.Tag is Label failedLabel)
            {
                double failedRate = total > 0 ? (double)failed / total * 100 : 0;
                failedLabel.Text = $"{failed} ({failedRate:F1}%)";
            }

            // 更新重试次数及百分比
            if (retriedFilesLabel?.Tag is Label retriedLabel)
            {
                double retriedRate = total > 0 ? (double)retried / total * 100 : 0;
                retriedLabel.Text = $"{retried} ({retriedRate:F1}%)";
            }

            // 更新开始时间
            if (processingTimeLabel?.Tag is Label timeLabel)
            {
                // 保持开始时间不变
                if (string.IsNullOrEmpty(timeLabel.Text) || !timeLabel.Text.Contains("-"))
                {
                    timeLabel.Text = processingStartTime.ToString("yyyy-MM-dd HH:mm:ss");
                }
            }

            // 更新进度条
            if (progressBarControl != null && progressBarLabel != null)
            {
                double progress = total > 0 ? (double)processedCount / total * 100 : 0;
                progressBarControl.Value = Math.Min((int)progress, 100);
                progressBarLabel.Text = $"{progress:F1}%";
            }

            // 更新最后处理时间和计数
            lastUpdateTime = now;
            lastProcessedCount = processedCount;
        }

        // 更新处理时间的方法
        private void UpdateProcessingTime()
        {
            // 计算处理耗时 - 无论是否正在处理，都计算并显示
            TimeSpan elapsed = DateTime.Now - processingStartTime;

            // 检查是否已经完成或停止处理
            if (isProcessing && slidesFormatter != null &&
                (slidesFormatter.CompletedFiles + slidesFormatter.FailedFiles >= slidesFormatter.TotalFiles))
            {
                // 如果所有文件都已处理完成(成功+失败)，停止计时器
                isProcessing = false;
                processingTimer?.Stop();

                // 更新按钮状态
                UpdateButtonState();

                // 记录最终状态
                if (slidesFormatter != null)
                {
                    logger?.Log($"处理已结束。共 {slidesFormatter.TotalFiles} 个文件，成功 {slidesFormatter.CompletedFiles} 个，" +
                              $"失败 {slidesFormatter.FailedFiles} 个，重试 {slidesFormatter.RetriedFiles} 个");
                }

                // 更新最终的统计信息，但不重置
                if (speedInfoLabel != null)
                {
                    // 获取定时状态信息
                    string scheduleInfo = GetScheduleInfo();

                    // 使用持久化的处理速度和预计剩余时间，更新处理耗时
                    speedInfoLabel.Text = $"处理速度: {currentProcessingSpeed:F1} {speedUnit} | 处理耗时: {elapsed.Hours:00}:{elapsed.Minutes:00}:{elapsed.Seconds:00} | 预计剩余时间: {currentRemainingTime:hh\\:mm\\:ss}{scheduleInfo}";
                }
            }
            else if (speedInfoLabel != null) // 无论是否正在处理，都更新显示
            {
                // 获取定时状态信息
                string scheduleInfo = GetScheduleInfo();

                // 使用持久化的处理速度和预计剩余时间，只更新处理耗时
                speedInfoLabel.Text = $"处理速度: {currentProcessingSpeed:F1} {speedUnit} | 处理耗时: {elapsed.Hours:00}:{elapsed.Minutes:00}:{elapsed.Seconds:00} | 预计剩余时间: {currentRemainingTime:hh\\:mm\\:ss}{scheduleInfo}";
            }
        }

        private void SlidesFormatter_ProgressChanged(object? sender, AsposeSlidesFormatter.ProgressEventArgs e)
        {
            if (InvokeRequired)
            {
                BeginInvoke(() => SlidesFormatter_ProgressChanged(sender, e));
                return;
            }

            // 更新统计信息
            UpdateStatistics(e.TotalFiles, e.CompletedFiles, e.FailedFiles, e.RetriedFiles);
        }

        private void SlidesFormatter_StatusChanged(object? sender, string status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => SlidesFormatter_StatusChanged(sender, status)));
                return;
            }

            // 如果处理完成或被停止，更新按钮状态
            if (status.StartsWith("处理完成") || status.StartsWith("处理已取消") ||
                status.StartsWith("处理已停止") || status.Contains("发生错误") ||
                status.Contains("没有找到需要处理的文件"))
            {
                isProcessing = false;

                // 更新所有按钮状态
                UpdateButtonState();

                // 停止计时器，但不重置统计信息
                processingTimer?.Stop();

                // 更新最终的统计信息，但保持显示
                if (sender is SlidesFormatter formatter)
                {
                    // 更新统计信息，但保持处理速度和预计剩余时间的显示
                    int total = formatter.TotalFiles;
                    int success = formatter.CompletedFiles;
                    int failed = formatter.FailedFiles;
                    int retried = formatter.RetriedFiles;

                    // 更新总文件数
                    if (totalFilesLabel?.Tag is Label totalLabel)
                        totalLabel.Text = total.ToString();

                    // 更新成功文件数及百分比
                    if (successFilesLabel?.Tag is Label successLabel)
                    {
                        double successRate = total > 0 ? (double)success / total * 100 : 0;
                        successLabel.Text = $"{success} ({successRate:F1}%)";
                    }

                    // 更新失败文件数及百分比
                    if (failedFilesLabel?.Tag is Label failedLabel)
                    {
                        double failedRate = total > 0 ? (double)failed / total * 100 : 0;
                        failedLabel.Text = $"{failed} ({failedRate:F1}%)";
                    }

                    // 更新重试次数及百分比
                    if (retriedFilesLabel?.Tag is Label retriedLabel)
                    {
                        double retriedRate = total > 0 ? (double)retried / total * 100 : 0;
                        retriedLabel.Text = $"{retried} ({retriedRate:F1}%)";
                    }

                    // 更新进度条
                    if (progressBarControl != null && progressBarLabel != null)
                    {
                        double progress = total > 0 ? (double)(success + failed) / total * 100 : 0;
                        progressBarControl.Value = Math.Min((int)progress, 100);
                        progressBarLabel.Text = $"{progress:F1}%";
                    }

                    // 确保预计结束时间显示最后有效的值
                    if (expectedEndTimeLabel?.Tag is Label endTimeLabel && !string.IsNullOrEmpty(lastValidEstimatedEndTime) && lastValidEstimatedEndTime != "--")
                    {
                        endTimeLabel.Text = lastValidEstimatedEndTime;
                    }

                    // 更新处理时间 - 这会更新speedInfoLabel，保持显示最后的统计数据
                    UpdateProcessingTime();
                }
            }
        }

        private void SlidesFormatter_ErrorOccurred(object? sender, ErrorEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => SlidesFormatter_ErrorOccurred(sender, e)));
                return;
            }

            string filePath = e.FilePath ?? "未知文件";
            string errorMessage = e.Exception?.Message ?? "未知错误";

            // 所有错误只记录到日志，不再显示弹窗
            logger?.LogError($"处理文件出错: {filePath}, 错误信息: {errorMessage}", e.Exception ?? new Exception(errorMessage));

            // 更新UI中的错误统计信息（如果需要）
            if (sender is SlidesFormatter formatter)
            {
                UpdateStatistics(formatter.TotalFiles, formatter.CompletedFiles, formatter.FailedFiles, formatter.RetriedFiles);
            }
        }

        private void Logger_LogMessage(object? sender, string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => Logger_LogMessage(sender, message)));
                return;
            }

            // 将日志添加到文本框
            AppendLog(message);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            try
            {
                if (currentSettings != null)
                {
                    // 确保TxtSettings的值是正确的
                    if (currentSettings.TxtSettings != null)
                    {
                        // 从配置文件重新加载TxtSettings，确保不会覆盖用户的设置
                        var loadedSettings = SettingsManager.LoadSettings();
                        if (loadedSettings.TxtSettings != null)
                        {
                            // 使用已保存的TxtSettings替换当前的TxtSettings
                            currentSettings.TxtSettings = loadedSettings.TxtSettings;
                            logger?.Log($"关闭窗口时重新加载TXT设置: ConvertToWordBeforeProcessing = {currentSettings.TxtSettings.ConvertToWordBeforeProcessing}");
                        }
                    }

                    // 保存当前设置
                    SettingsManager.SaveSettings(currentSettings);
                    logger?.Log("关闭窗口时保存设置完成");
                }
            }
            catch (Exception ex)
            {
                logger?.LogError("关闭窗口时保存设置出错", ex);
            }

            // 停止所有处理
            if (isProcessing && slidesFormatter != null)
            {
                slidesFormatter.StopProcessing();
                isProcessing = false;
            }

            // 停止所有定时器
            processingTimer?.Stop();
            processingTimer?.Dispose();

            scheduleTimer?.Stop();
            scheduleTimer?.Dispose();

            // 取消挂起的任务
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();

            // 确保SlidesFormatter释放资源
            slidesFormatter?.Dispose();

            logger?.Log("应用程序正在关闭");
        }

        // 添加按钮状态更新方法
        private void UpdateButtonState()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(UpdateButtonState));
                return;
            }

            // 根据当前处理状态启用/禁用按钮
            if (startButton != null)
            {
                startButton.Enabled = !isProcessing && !isScheduleEnabled; // 处理中或定时启用时禁用开始按钮
            }

            if (stopButton != null)
            {
                stopButton.Enabled = isProcessing || isScheduleEnabled; // 处理中或定时启用时启用停止按钮
            }

            if (scheduleButton != null)
            {
                scheduleButton.Enabled = !isProcessing; // 处理中时禁用定时按钮
            }
        }

        // 查找按钮辅助方法
        private Button? FindButton(string text)
        {
            foreach (Control control in this.Controls)
            {
                if (control is TableLayoutPanel mainLayout)
                {
                    foreach (Control panelControl in mainLayout.Controls)
                    {
                        if (panelControl is Panel operationPanel)
                        {
                            foreach (Control layoutControl in operationPanel.Controls)
                            {
                                if (layoutControl is TableLayoutPanel operationLayout)
                                {
                                    foreach (Control buttonsPanelControl in operationLayout.Controls)
                                    {
                                        if (buttonsPanelControl is TableLayoutPanel buttonsPanel)
                                        {
                                            foreach (Control buttonControl in buttonsPanel.Controls)
                                            {
                                                if (buttonControl is Button button && button.Text == text)
                                                {
                                                    return button;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return null;
        }

        // 创建操作按钮和日志区域的面板
        private Panel CreateOperationPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 1,
                Padding = new Padding(10),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 创建按钮面板，使用TableLayoutPanel来实现三行三列的布局
            var buttonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 3,
                Padding = new Padding(5),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 设置列宽均分
            for (int i = 0; i < 3; i++)
            {
                buttonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            }

            // 设置行高均分
            for (int i = 0; i < 3; i++)
            {
                buttonsPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33F));
            }

            // 统一按钮样式的方法
            Action<Button> styleButton = (btn) =>
            {
                btn.Dock = DockStyle.Fill;
                btn.Margin = new Padding(8);
                btn.FlatStyle = FlatStyle.System;
                btn.Font = new System.Drawing.Font(btn.Font.FontFamily, 9F, System.Drawing.FontStyle.Regular);
                btn.BackColor = SystemColors.Control;
                btn.UseVisualStyleBackColor = true;
                btn.FlatAppearance.BorderColor = Color.FromArgb(200, 200, 200);
                btn.FlatAppearance.BorderSize = 1;
            };

            // 设置按钮的大小和外观
            startButton = new Button { Text = "开始处理" };
            styleButton(startButton);
            startButton.Click += StartButton_Click;

            stopButton = new Button { Text = "停止处理", Enabled = false };
            styleButton(stopButton);
            stopButton.Click += StopButton_Click;

            // 创建定时处理按钮
            scheduleButton = new Button { Text = "定时处理" };
            styleButton(scheduleButton);
            scheduleButton.Click += ScheduleButton_Click;

            // 创建日志设置按钮
            var logSettingsButton = new Button { Text = "日志设置" };
            styleButton(logSettingsButton);
            logSettingsButton.Click += LogSettingsButton_Click;

            // 创建清空日志按钮
            var clearLogButton = new Button { Text = "清空日志" };
            styleButton(clearLogButton);
            clearLogButton.Click += (object? s, EventArgs e) => logger?.ClearLog();

            // 创建导出配置按钮
            var exportConfigButton = new Button { Text = "导出配置" };
            styleButton(exportConfigButton);
            exportConfigButton.Click += (object? s, EventArgs e) => {
                using (SaveFileDialog dialog = new SaveFileDialog())
                {
                    dialog.Filter = "ZIP文件|*.zip";
                    dialog.Title = "导出配置";
                    dialog.FileName = "AsposeSlidesFormatter_Config.zip";

                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        try
                        {
                            // 创建临时目录
                            string tempDir = Path.Combine(Path.GetTempPath(), "AsposeSlidesFormatter_Config_" + Guid.NewGuid().ToString());
                            Directory.CreateDirectory(tempDir);

                            try
                            {
                                // 复制所有配置文件到临时目录
                                string configDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
                                if (Directory.Exists(configDir))
                                {
                                    foreach (string file in Directory.GetFiles(configDir, "*.json"))
                                    {
                                        string destFile = Path.Combine(tempDir, Path.GetFileName(file));
                                        File.Copy(file, destFile);
                                    }
                                }

                                // 创建ZIP文件
                                System.IO.Compression.ZipFile.CreateFromDirectory(tempDir, dialog.FileName);
                                MessageBox.Show("配置导出成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                            finally
                            {
                                // 清理临时目录
                                if (Directory.Exists(tempDir))
                                {
                                    Directory.Delete(tempDir, true);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"导出配置失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            };

            // 创建导入配置按钮
            var importConfigButton = new Button { Text = "导入配置" };
            styleButton(importConfigButton);
            importConfigButton.Click += (object? s, EventArgs e) => {
                using (OpenFileDialog dialog = new OpenFileDialog())
                {
                    dialog.Filter = "ZIP文件|*.zip|JSON文件|*.json";
                    dialog.Title = "导入配置";

                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        try
                        {
                            if (Path.GetExtension(dialog.FileName).ToLower() == ".zip")
                            {
                                // 创建临时目录
                                string tempDir = Path.Combine(Path.GetTempPath(), "AsposeSlidesFormatter_Config_" + Guid.NewGuid().ToString());
                                Directory.CreateDirectory(tempDir);

                                try
                                {
                                    // 解压ZIP文件
                                    System.IO.Compression.ZipFile.ExtractToDirectory(dialog.FileName, tempDir);

                                    // 获取配置目录
                                    string configDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
                                    Directory.CreateDirectory(configDir);

                                    // 复制所有JSON文件到配置目录
                                    foreach (string file in Directory.GetFiles(tempDir, "*.json"))
                                    {
                                        string destFile = Path.Combine(configDir, Path.GetFileName(file));
                                        File.Copy(file, destFile, true);
                                    }

                                    // 重新加载配置
                                    currentSettings = SettingsManager.LoadSettings();
                                    wordFormatter?.UpdateSettings(currentSettings);
                                    MessageBox.Show("配置导入成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                                finally
                                {
                                    // 清理临时目录
                                    if (Directory.Exists(tempDir))
                                    {
                                        Directory.Delete(tempDir, true);
                                    }
                                }
                            }
                            else
                            {
                                // 兼容旧版本的单文件配置
                                string configJson = File.ReadAllText(dialog.FileName);
                                var importedSettings = System.Text.Json.JsonSerializer.Deserialize<Settings>(configJson);
                                if (importedSettings != null)
                                {
                                    currentSettings = importedSettings;
                                    // 更新SlidesFormatter的设置
                                    // slidesFormatter?.UpdateSettings(currentSettings); // 暂时注释，需要实现UpdateSettings方法
                                    // 保存到拆分的配置文件
                                    SettingsManager.SaveSettings(currentSettings);
                                    MessageBox.Show("配置导入成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                                else
                                {
                                    MessageBox.Show("导入的配置文件格式有误！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"导入配置失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            };

            // 创建打开来源目录按钮
            var openSourceButton = new Button { Text = "打开来源目录" };
            styleButton(openSourceButton);
            openSourceButton.Click += (object? s, EventArgs e) => {
                try
                {
                    if (!string.IsNullOrEmpty(settings?.SourceDirectory) && Directory.Exists(settings.SourceDirectory))
                    {
                        System.Diagnostics.Process.Start("explorer.exe", settings.SourceDirectory);
                    }
                    else
                    {
                        MessageBox.Show("来源目录不存在", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    logger?.LogError("打开来源目录时发生错误", ex);
                    MessageBox.Show($"打开来源目录时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            // 创建打开输出目录按钮
            var openTargetButton = new Button { Text = "打开输出目录" };
            styleButton(openTargetButton);
            openTargetButton.Click += (object? s, EventArgs e) => {
                try
                {
                    if (!string.IsNullOrEmpty(settings?.OutputDirectory) && Directory.Exists(settings.OutputDirectory))
                    {
                        System.Diagnostics.Process.Start("explorer.exe", settings.OutputDirectory);
                    }
                    else
                    {
                        MessageBox.Show("输出目录不存在", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    logger?.LogError("打开输出目录时发生错误", ex);
                    MessageBox.Show($"打开输出目录时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            // 第一行添加按钮
            buttonsPanel.Controls.Add(startButton, 0, 0);
            buttonsPanel.Controls.Add(scheduleButton, 1, 0);
            buttonsPanel.Controls.Add(stopButton, 2, 0);

            // 第二行添加按钮
            buttonsPanel.Controls.Add(logSettingsButton, 0, 1);
            buttonsPanel.Controls.Add(clearLogButton, 1, 1);
            buttonsPanel.Controls.Add(exportConfigButton, 2, 1);

            // 第三行添加按钮
            buttonsPanel.Controls.Add(importConfigButton, 0, 2);
            buttonsPanel.Controls.Add(openSourceButton, 1, 2);
            buttonsPanel.Controls.Add(openTargetButton, 2, 2);

            // 添加按钮面板到布局
            layout.Controls.Add(buttonsPanel, 0, 0);

            panel.Controls.Add(layout);
            return panel;
        }

        // 添加定时处理按钮点击事件
        private void ScheduleButton_Click(object? sender, EventArgs e)
        {
            try
            {
                // 打开定时处理设置窗口
                if (currentSettings?.ScheduleSettings == null && currentSettings != null)
                {
                    currentSettings.ScheduleSettings = new ScheduleSettings();
                }

                if (currentSettings?.ScheduleSettings != null)
                {
                    using (var scheduleForm = new ScheduleSettingsForm(currentSettings.ScheduleSettings))
                    {
                        if (scheduleForm.ShowDialog() == DialogResult.OK)
                        {
                            // 配置已保存到 settings 对象中
                            SettingsManager.SaveSettings(currentSettings);

                            // 更新定时处理状态
                            isScheduleEnabled = currentSettings.ScheduleSettings.Enabled;

                            // 更新UI状态
                            UpdateScheduleStatus();

                            // 添加日志
                            if (isScheduleEnabled)
                            {
                                DateTime nextRunTime = currentSettings.ScheduleSettings.CalculateNextRunTime();
                                logger?.Log($"定时处理已启用，下次运行时间: {nextRunTime:yyyy-MM-dd HH:mm:ss}");
                            }
                            else
                            {
                                logger?.Log("定时处理已禁用");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger?.LogError("配置定时处理时发生错误", ex);
                MessageBox.Show($"配置定时处理时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 日志设置按钮点击事件
        private void LogSettingsButton_Click(object? sender, EventArgs e)
        {
            try
            {
                // 确保日志设置不为null
                if (currentSettings?.LogSettings == null && currentSettings != null)
                {
                    currentSettings.LogSettings = new LogSettings();
                }

                if (currentSettings?.LogSettings != null)
                {
                    using (var logSettingsForm = new LogSettingsForm(currentSettings.LogSettings))
                    {
                        if (logSettingsForm.ShowDialog() == DialogResult.OK)
                        {
                            // 配置已保存到 settings 对象中
                            SettingsManager.SaveSettings(currentSettings);

                            // 记录日志设置更改
                            logger?.Log("日志设置已更新并保存");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger?.LogError("配置日志设置时发生错误", ex);
                MessageBox.Show($"配置日志设置时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 开始处理按钮点击事件
        private void StartButton_Click(object? sender, EventArgs e)
        {
            // 已经在处理中，不再重复操作
            if (isProcessing) return;

            try
            {
                // 验证路径
                string sourceDir = settings?.SourceDirectory ?? "";
                string outputDir = settings?.OutputDirectory ?? "";

                bool pathsValid = true;

                // 检查源目录存在
                if (string.IsNullOrEmpty(sourceDir) || !Directory.Exists(sourceDir))
                {
                    MessageBox.Show("源文件目录不存在或未设置", "源目录无效", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    pathsValid = false;
                }

                // 检查输出目录
                if (string.IsNullOrEmpty(outputDir))
                {
                    MessageBox.Show("输出目录未设置", "输出目录无效", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    pathsValid = false;
                }

                // 创建输出目录(如果不存在)
                if (pathsValid && !Directory.Exists(outputDir))
                {
                    try
                    {
                        Directory.CreateDirectory(outputDir);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"无法创建输出目录：{ex.Message}", "目录创建失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        pathsValid = false;
                    }
                }

                // 路径有效，继续处理
                if (pathsValid)
                {
                    // 判断是否需要进行PDF转换
                    if (settings?.EnableWordToPdf == true)
                    {
                        logger?.Log("开始PPT演示文稿的批量格式化和PDF转换...");
                        // 先进行PPT格式化处理，然后再进行PDF转换
                        StartProcessing();

                        // 添加延迟等待PPT处理完成后再开始PDF转换
                        var timer = new System.Windows.Forms.Timer
                        {
                            Interval = 500 // 每500ms检查一次
                        };

                        timer.Tick += async (s, args) =>
                        {
                            // 检查PPT处理是否完成
                            if (!isProcessing)
                            {
                                timer.Stop();
                                timer.Dispose();

                                // PPT处理完成后，开始PDF转换
                                logger?.Log("PPT演示文稿格式化完成，开始PDF转换...");
                                await Task.Run(() => ProcessPptToPdf());
                            }
                        };

                        timer.Start();
                    }
                    else
                    {
                        logger?.Log("开始PPT演示文稿的批量格式化...");
                        StartProcessing();
                    }
                }
            }
            catch (Exception ex)
            {
                logger?.LogError("启动处理时出错", ex);
                MessageBox.Show($"启动处理发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task ProcessPptToPdf()
        {
            try
            {
                // 更新处理状态
                isProcessing = true;

                // 更新按钮状态
                UpdateButtonState();

                // 重置统计计数器
                ResetStatistics();

                // 记录开始时间
                processingStartTime = DateTime.Now;
                if (processingTimer != null)
                    processingTimer.Start();

                // 获取所有PPT文件
                var sourceFolder = settings?.SourceDirectory ?? "";
                var outputFolder = settings?.OutputDirectory ?? "";
                logger?.Log($"开始PDF转换，源目录: {sourceFolder}，输出目录: {outputFolder}");

                // 创建新的取消令牌源
                cancellationTokenSource?.Dispose();
                cancellationTokenSource = new CancellationTokenSource();
                var cancellationToken = cancellationTokenSource.Token;

                // 在后台线程中获取文件列表
                var pptFiles = await Task.Run(() => GetPresentationFilesFromDirectory(sourceFolder, settings?.IncludeSubdirectories ?? false), cancellationToken);

                if (pptFiles.Count == 0)
                {
                    logger?.Log("未找到PPT文件");
                    MessageBox.Show("在指定目录中未找到PPT文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 复位状态
                    isProcessing = false;
                    UpdateButtonState();
                    processingTimer?.Stop();
                    return;
                }

                // 更新统计信息
                _totalFiles = pptFiles.Count;
                UpdateStatistics(_totalFiles, 0, 0, 0);
                logger?.Log($"找到 {_totalFiles} 个PPT文件待转换为PDF");

                // 启动UI更新定时器
                var uiUpdateTimer = new System.Windows.Forms.Timer
                {
                    Interval = 200,
                    Enabled = true
                };

                uiUpdateTimer.Tick += (s, e) =>
                {
                    // 更新处理状态和进度显示
                    if (slidesFormatter != null)
                    {
                        UpdateStatistics(
                            slidesFormatter.TotalFiles,
                            slidesFormatter.CompletedFiles,
                            slidesFormatter.FailedFiles,
                            slidesFormatter.RetriedFiles
                        );
                    }

                    // 更新进度条
                    if (progressBarControl != null && slidesFormatter != null && slidesFormatter.TotalFiles > 0)
                    {
                        var totalFiles = slidesFormatter.TotalFiles; // 在null检查后获取值
                        int completedFiles = slidesFormatter.CompletedFiles;
                        int percentage = (int)(completedFiles * 100.0 / totalFiles);
                        progressBarControl.Value = Math.Min(percentage, 100);

                        if (progressBarLabel != null)
                            progressBarLabel.Text = $"{percentage}%";

                        // 检查是否所有文件都已处理完成
                        if (slidesFormatter?.TotalFiles > 0 && completedFiles >= (slidesFormatter?.TotalFiles ?? 0) && isProcessing)
                        {
                            // 所有文件处理完成，强制更新状态
                            isProcessing = false;
                            UpdateButtonState();
                            processingTimer?.Stop();
                            logger?.Log("所有文件已处理完成");
                        }
                    }

                    // 更新每秒处理速度
                    if (speedInfoLabel != null && slidesFormatter != null)
                    {
                        var now = DateTime.Now;
                        var elapsed = (now - lastUpdateTime).TotalSeconds;

                        if (elapsed >= 2.0) // 至少2秒更新一次速度统计
                        {
                            var processed = (slidesFormatter?.CompletedFiles ?? 0) - lastProcessedCount;
                            double filesPerSecond = processed / elapsed;

                            // 更新持久化的处理速度
                            if (filesPerSecond > 0 && slidesFormatter != null)
                            {
                                currentProcessingSpeed = filesPerSecond;
                                speedUnit = "文件/秒";

                                // 计算预计剩余时间
                                int total = slidesFormatter?.TotalFiles ?? 0;
                                int processedCount = (slidesFormatter?.CompletedFiles ?? 0) + (slidesFormatter?.FailedFiles ?? 0);
                                var remainingFiles = total - processedCount;

                                if (filesPerSecond > 0 && remainingFiles > 0)
                                {
                                    double remainingSeconds = remainingFiles / filesPerSecond;
                                    currentRemainingTime = TimeSpan.FromSeconds(remainingSeconds);
                                }

                                // 获取定时状态信息
                                string scheduleInfo = GetScheduleInfo();

                                // 计算处理耗时
                                TimeSpan elapsed2 = DateTime.Now - processingStartTime;

                                // 更新完整的统计信息
                                speedInfoLabel.Text = $"处理速度: {currentProcessingSpeed:F2} {speedUnit} | 处理耗时: {elapsed2.Hours:00}:{elapsed2.Minutes:00}:{elapsed2.Seconds:00} | 预计剩余时间: {currentRemainingTime:hh\\:mm\\:ss}{scheduleInfo}";
                            }

                            // 更新上次统计时间和计数
                            lastUpdateTime = now;
                            lastProcessedCount = slidesFormatter?.CompletedFiles ?? 0;
                        }
                    }

                    // 更新处理时间
                    UpdateProcessingTime();

                    // 如果处理完成，停止定时器，但保持统计信息显示
                    if (!isProcessing)
                    {
                        // 更新最后一次的统计信息
                        UpdateProcessingTime();

                        // 停止定时器
                        uiUpdateTimer.Stop();
                        uiUpdateTimer.Dispose();
                    }
                };

                // 确定最优线程数
                int optimalThreads;

                if (settings?.EnableSmartThreading == true)
                {
                    // 智能线程管理：使用SlidesFormatter中的方法获取CPU使用率
                    double cpuUsage = 50; // 默认值
                    try
                    {
                        using (var cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total", true))
                        {
                            cpuCounter.NextValue();
                            Thread.Sleep(500);
                            cpuUsage = cpuCounter.NextValue();
                        }
                        logger?.Log($"当前CPU使用率: {cpuUsage:F1}%");
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError("获取CPU使用率时出错", ex);
                    }

                    if (cpuUsage > 80)
                    {
                        // CPU负载高，减少线程数
                        optimalThreads = Math.Max(1, Environment.ProcessorCount / 2);
                    }
                    else if (cpuUsage > 60)
                    {
                        // CPU负载中等，使用适中的线程数
                        optimalThreads = Math.Max(1, Environment.ProcessorCount * 3 / 4);
                    }
                    else
                    {
                        // CPU负载低，可以使用更多线程
                        optimalThreads = Environment.ProcessorCount;
                    }

                    // 如果用户设置了最大线程数，则不超过该值
                    if (settings?.MaxThreads > 0)
                    {
                        optimalThreads = Math.Min(optimalThreads, settings.MaxThreads);
                    }

                    logger?.Log($"PDF转换使用智能线程管理，根据系统负载选择线程数: {optimalThreads}");
                }
                else if (settings?.MaxThreads <= 0)
                {
                    // 用户设置为自动，使用处理器核心数作为线程数
                    optimalThreads = Environment.ProcessorCount;
                    logger?.Log($"PDF转换自动选择线程数: {optimalThreads} (基于处理器核心数)");
                }
                else
                {
                    // 使用用户设置的线程数
                    optimalThreads = settings?.MaxThreads ?? Environment.ProcessorCount;
                    logger?.Log($"PDF转换使用用户设置的线程数: {optimalThreads}");
                }

                // 创建并行处理选项
                var parallelOptions = new ParallelOptions
                {
                    MaxDegreeOfParallelism = optimalThreads,
                    CancellationToken = cancellationToken
                };

                // 处理计数器
                int processedCount = 0;
                int successCount = 0;
                int failedCount = 0;
                object lockObj = new object();

                // 使用后台任务处理文件
                await Task.Run(async () =>
                {
                    try
                    {
                        // 根据设置选择并行或串行处理
                        if (settings?.MaxThreads > 1)
                        {
                            // 并行处理文件
                            await Parallel.ForEachAsync(pptFiles, parallelOptions, async (pptFile, ct) =>
                            {
                                bool success = await ProcessSinglePptToPdfAsync(pptFile, outputFolder, ct);

                                lock (lockObj)
                                {
                                    processedCount++;
                                    if (success)
                                        successCount++;
                                    else
                                        failedCount++;

                                    // 更新进度
                                    int percentage = (int)(processedCount * 100.0 / _totalFiles);

                                    // 调用UI线程更新
                                    if (InvokeRequired)
                                    {
                                        BeginInvoke(() =>
                                        {
                                            UpdateStatistics(_totalFiles, successCount, failedCount, 0);

                                            if (progressBarControl != null)
                                            {
                                                progressBarControl.Value = Math.Min(percentage, 100);

                                                if (progressBarLabel != null)
                                                    progressBarLabel.Text = $"{percentage}%";
                                            }
                                        });
                                    }
                                    else
                                    {
                                        UpdateStatistics(_totalFiles, successCount, failedCount, 0);

                                        if (progressBarControl != null)
                                        {
                                            progressBarControl.Value = Math.Min(percentage, 100);

                                            if (progressBarLabel != null)
                                                progressBarLabel.Text = $"{percentage}%";
                                        }
                                    }
                                }
                            });
                        }
                        else
                        {
                            // 串行处理文件
                            foreach (var pptFile in pptFiles)
                            {
                                if (cancellationToken.IsCancellationRequested)
                                    break;

                                bool success = await ProcessSinglePptToPdfAsync(pptFile, outputFolder, cancellationToken);

                                processedCount++;
                                if (success)
                                    successCount++;
                                else
                                    failedCount++;

                                // 更新进度
                                int percentage = (int)(processedCount * 100.0 / _totalFiles);

                                // 调用UI线程更新
                                if (InvokeRequired)
                                {
                                    BeginInvoke(() =>
                                    {
                                        UpdateStatistics(_totalFiles, successCount, failedCount, 0);

                                        if (progressBarControl != null)
                                        {
                                            progressBarControl.Value = Math.Min(percentage, 100);

                                            if (progressBarLabel != null)
                                                progressBarLabel.Text = $"{percentage}%";
                                        }
                                    });
                                }
                                else
                                {
                                    UpdateStatistics(_totalFiles, successCount, failedCount, 0);

                                    if (progressBarControl != null)
                                    {
                                        progressBarControl.Value = Math.Min(percentage, 100);

                                        if (progressBarLabel != null)
                                            progressBarLabel.Text = $"{percentage}%";
                                    }
                                }
                            }
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        logger?.Log("PDF转换已取消");
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError($"PDF批量转换过程中发生错误: {ex.Message}", ex);
                    }
                    finally
                    {
                        // 更新最终结果
                        if (InvokeRequired)
                        {
                            BeginInvoke(() =>
                            {
                                // 更新统计信息，但保持处理速度和预计剩余时间的显示
                                int total = _totalFiles;
                                int success = successCount;
                                int failed = failedCount;

                                // 更新总文件数
                                if (totalFilesLabel?.Tag is Label totalLabel)
                                    totalLabel.Text = total.ToString();

                                // 更新成功文件数及百分比
                                if (successFilesLabel?.Tag is Label successLabel)
                                {
                                    double successRate = total > 0 ? (double)success / total * 100 : 0;
                                    successLabel.Text = $"{success} ({successRate:F1}%)";
                                }

                                // 更新失败文件数及百分比
                                if (failedFilesLabel?.Tag is Label failedLabel)
                                {
                                    double failedRate = total > 0 ? (double)failed / total * 100 : 0;
                                    failedLabel.Text = $"{failed} ({failedRate:F1}%)";
                                }

                                // 更新进度条
                                if (progressBarControl != null && progressBarLabel != null)
                                {
                                    double progress = total > 0 ? (double)(success + failed) / total * 100 : 0;
                                    progressBarControl.Value = Math.Min((int)progress, 100);
                                    progressBarLabel.Text = $"{progress:F1}%";
                                }

                                // 确保预计结束时间显示最后有效的值
                                if (expectedEndTimeLabel?.Tag is Label endTimeLabel && !string.IsNullOrEmpty(lastValidEstimatedEndTime) && lastValidEstimatedEndTime != "--")
                                {
                                    endTimeLabel.Text = lastValidEstimatedEndTime;
                                }

                                isProcessing = false;
                                UpdateButtonState();
                                processingTimer?.Stop();

                                // 更新处理时间 - 这会更新speedInfoLabel，保持显示最后的统计数据
                                UpdateProcessingTime();

                                // 处理完成提示
                                logger?.Log($"PDF转换完成: 共 {_totalFiles} 个文件, 成功 {successCount} 个, 失败 {failedCount} 个");
                            });
                        }
                        else
                        {
                            // 更新统计信息，但保持处理速度和预计剩余时间的显示
                            int total = _totalFiles;
                            int success = successCount;
                            int failed = failedCount;

                            // 更新总文件数
                            if (totalFilesLabel?.Tag is Label totalLabel)
                                totalLabel.Text = total.ToString();

                            // 更新成功文件数及百分比
                            if (successFilesLabel?.Tag is Label successLabel)
                            {
                                double successRate = total > 0 ? (double)success / total * 100 : 0;
                                successLabel.Text = $"{success} ({successRate:F1}%)";
                            }

                            // 更新失败文件数及百分比
                            if (failedFilesLabel?.Tag is Label failedLabel)
                            {
                                double failedRate = total > 0 ? (double)failed / total * 100 : 0;
                                failedLabel.Text = $"{failed} ({failedRate:F1}%)";
                            }

                            // 更新进度条
                            if (progressBarControl != null && progressBarLabel != null)
                            {
                                double progress = total > 0 ? (double)(success + failed) / total * 100 : 0;
                                progressBarControl.Value = Math.Min((int)progress, 100);
                                progressBarLabel.Text = $"{progress:F1}%";
                            }

                            // 确保预计结束时间显示最后有效的值
                            if (expectedEndTimeLabel?.Tag is Label endTimeLabel && !string.IsNullOrEmpty(lastValidEstimatedEndTime) && lastValidEstimatedEndTime != "--")
                            {
                                endTimeLabel.Text = lastValidEstimatedEndTime;
                            }

                            isProcessing = false;
                            UpdateButtonState();
                            processingTimer?.Stop();

                            // 更新处理时间 - 这会更新speedInfoLabel，保持显示最后的统计数据
                            UpdateProcessingTime();

                            // 处理完成提示
                            logger?.Log($"PDF转换完成: 共 {_totalFiles} 个文件, 成功 {successCount} 个, 失败 {failedCount} 个");
                        }
                    }
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                logger?.LogError($"PDF转换过程中发生错误: {ex.Message}", ex);
                MessageBox.Show($"PDF转换过程中发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 恢复状态
                isProcessing = false;
                UpdateButtonState();
                processingTimer?.Stop();
            }
        }

        private async Task<bool> ProcessSinglePptToPdfAsync(string pptFilePath, string outputFolder, CancellationToken cancellationToken)
        {
            try
            {
                var fileName = Path.GetFileName(pptFilePath);
                logger?.Log($"正在转换: {fileName}");

                // 使用Aspose.Slides直接转换PDF
                using (var presentation = new Presentation(pptFilePath))
                {
                    string outputFileName = Path.GetFileNameWithoutExtension(pptFilePath) + ".pdf";
                    string outputFile = Path.Combine(outputFolder, outputFileName);

                    // 确保输出目录存在
                    Directory.CreateDirectory(outputFolder);

                    presentation.Save(outputFile, Aspose.Slides.Export.SaveFormat.Pdf);
                    logger?.Log($"转换成功: {fileName} => {Path.GetFileName(outputFile)}");
                    return true;
                }
            }
            catch (OperationCanceledException)
            {
                logger?.Log($"转换已取消: {Path.GetFileName(pptFilePath)}");
                throw; // 重新抛出取消异常
            }
            catch (Exception ex)
            {
                logger?.LogError($"转换文件 {pptFilePath} 失败: {ex.Message}", ex);
                return false;
            }
        }

        private List<string> GetPresentationFilesFromDirectory(string directory, bool includeSubdirectories)
        {
            var files = new List<string>();
            try
            {
                // 支持的演示文稿文件扩展名
                var supportedExtensions = new[] { ".ppt", ".pptx", ".pptm", ".pot", ".potx", ".potm", ".pps", ".ppsx", ".ppsm", ".odp" };

                var searchOption = includeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;

                foreach (var extension in supportedExtensions)
                {
                    try
                    {
                        var extensionFiles = Directory.GetFiles(directory, $"*{extension}", searchOption);
                        files.AddRange(extensionFiles);
                    }
                    catch (Exception ex)
                    {
                        logger?.LogWarning($"搜索 {extension} 文件时出错: {ex.Message}");
                    }
                }

                // 过滤掉临时文件
                files = files.Where(f => !IsOfficeTemporaryFile(f)).ToList();

                logger?.Log($"找到 {files.Count} 个演示文稿文件");
                return files;
            }
            catch (Exception ex)
            {
                logger?.LogError($"搜索演示文稿文件时出错: {ex.Message}", ex);
                return files;
            }
        }

        private bool IsOfficeTemporaryFile(string filePath)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);

                // Office临时文件通常以~$开头
                if (fileName.StartsWith("~$"))
                    return true;

                // PowerPoint临时文件模式
                if (fileName.StartsWith("ppt") && fileName.Contains("tmp"))
                    return true;

                return false;
            }
            catch
            {
                return false;
            }
        }

        // 停止处理按钮点击事件
        private void StopButton_Click(object? sender, EventArgs e)
        {
            try
            {
                // 停止文件处理
                if (isProcessing)
                {
                    logger?.Log("正在停止文件处理...");
                    slidesFormatter?.StopProcessing();

                    // 直接更新处理状态为非处理中
                    isProcessing = false;

                    // 更新按钮状态
                    UpdateButtonState();

                    // 停止处理时间计时器，但保持统计信息显示
                    processingTimer?.Stop();

                    // 确保预计结束时间显示最后有效的值
                    if (expectedEndTimeLabel?.Tag is Label endTimeLabel && !string.IsNullOrEmpty(lastValidEstimatedEndTime) && lastValidEstimatedEndTime != "--")
                    {
                        endTimeLabel.Text = lastValidEstimatedEndTime;
                    }

                    // 更新最后一次的统计信息
                    UpdateProcessingTime();
                }

                // 停止定时处理
                if (isScheduleEnabled)
                {
                    logger?.Log("正在停止定时处理...");

                    // 禁用定时处理
                    if (settings?.ScheduleSettings != null)
                    {
                        settings.ScheduleSettings.Enabled = false;
                        isScheduleEnabled = false;

                        // 保存设置
                        if (currentSettings != null)
                        {
                            SettingsManager.SaveSettings(currentSettings);
                        }

                        // 更新UI状态
                        UpdateScheduleStatus();

                        logger?.Log("定时处理已停止");
                    }
                }
            }
            catch (Exception ex)
            {
                logger?.LogError("停止处理过程时发生错误", ex);
                MessageBox.Show($"停止处理过程时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private Panel CreateStatisticsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3, // 三行：统计信息、进度条、处理速度和时间
                Padding = new Padding(5)
            };

            // 设置行高
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 60)); // 统计信息占60%
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 20)); // 进度条占20%
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 20)); // 处理速度和时间占20%

            // 第一行：创建统计信息表格
            var statsTable = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,  // 三列：每列一个统计项
                RowCount = 2,     // 两行：标签和值
                Padding = new Padding(0),
                Margin = new Padding(0),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 设置列宽
            statsTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            statsTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            statsTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));

            // 设置行高
            statsTable.RowStyles.Add(new RowStyle(SizeType.Percent, 50));
            statsTable.RowStyles.Add(new RowStyle(SizeType.Percent, 50));

            // 创建统计标签 - 第一行
            var fileCountLabel = new Label
            {
                Text = "总文件数:",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Bold),
                Padding = new Padding(10, 0, 0, 0) // 添加左侧内边距
            };

            var successCountLabel = new Label
            {
                Text = "成功处理:",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Bold),
                Padding = new Padding(10, 0, 0, 0) // 添加左侧内边距
            };

            var failedCountLabel = new Label
            {
                Text = "处理失败:",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Bold),
                Padding = new Padding(10, 0, 0, 0) // 添加左侧内边距
            };

            // 创建统计标签 - 第二行
            var retriedCountLabel = new Label
            {
                Text = "重试次数:",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Bold),
                Padding = new Padding(10, 0, 0, 0) // 添加左侧内边距
            };

            var timeLabel = new Label
            {
                Text = "开始时间:",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Bold),
                Padding = new Padding(10, 0, 0, 0) // 添加左侧内边距
            };

            // 创建定时状态的控件（但不添加到界面布局中）
            var scheduleLabel = new Label
            {
                Text = "定时状态:",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Bold),
                Padding = new Padding(10, 0, 0, 0) // 添加左侧内边距
            };

            // 创建右侧值显示面板
            totalFilesLabel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.None
            };
            var totalFilesValueLabel = new Label
            {
                Text = "0",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                AutoSize = false,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Regular),
                Padding = new Padding(10, 0, 0, 0) // 与标题标签相同的左侧内边距
            };
            totalFilesLabel.Controls.Add(totalFilesValueLabel);
            totalFilesLabel.Tag = totalFilesValueLabel; // 保存标签引用

            successFilesLabel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.None
            };
            var successFilesValueLabel = new Label
            {
                Text = "0 (0.0%)",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                AutoSize = false,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Regular),
                ForeColor = Color.Green,
                Padding = new Padding(10, 0, 0, 0) // 与标题标签相同的左侧内边距
            };
            successFilesLabel.Controls.Add(successFilesValueLabel);
            successFilesLabel.Tag = successFilesValueLabel; // 保存标签引用

            failedFilesLabel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.None
            };
            var failedFilesValueLabel = new Label
            {
                Text = "0 (0.0%)",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                AutoSize = false,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Regular),
                ForeColor = Color.Red,
                Padding = new Padding(10, 0, 0, 0) // 与标题标签相同的左侧内边距
            };
            failedFilesLabel.Controls.Add(failedFilesValueLabel);
            failedFilesLabel.Tag = failedFilesValueLabel; // 保存标签引用

            retriedFilesLabel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.None
            };
            var retriedFilesValueLabel = new Label
            {
                Text = "0 (0%)",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                AutoSize = false,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Regular),
                ForeColor = Color.DarkOrange,
                Padding = new Padding(10, 0, 0, 0) // 与标题标签相同的左侧内边距
            };
            retriedFilesLabel.Controls.Add(retriedFilesValueLabel);
            retriedFilesLabel.Tag = retriedFilesValueLabel; // 保存标签引用

            processingTimeLabel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.None
            };
            var processingTimeValueLabel = new Label
            {
                Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                AutoSize = false,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Regular),
                Padding = new Padding(10, 0, 0, 0) // 与标题标签相同的左侧内边距
            };
            processingTimeLabel.Controls.Add(processingTimeValueLabel);
            processingTimeLabel.Tag = processingTimeValueLabel; // 保存标签引用

            // 创建下次运行时间显示面板（但不添加到界面布局中）
            var schedulePanelContainer = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.None
            };
            nextRunTimeLabel = new Label
            {
                Text = "定时处理未启用",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                AutoSize = false,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Regular),
                ForeColor = Color.Gray,
                Padding = new Padding(10, 0, 0, 0) // 与标题标签相同的左侧内边距
            };
            schedulePanelContainer.Controls.Add(nextRunTimeLabel);

            // 添加标签和值到布局
            statsTable.Controls.Add(fileCountLabel, 0, 0);
            statsTable.Controls.Add(totalFilesLabel, 0, 1);
            statsTable.Controls.Add(successCountLabel, 1, 0);
            statsTable.Controls.Add(successFilesLabel, 1, 1);
            statsTable.Controls.Add(failedCountLabel, 2, 0);
            statsTable.Controls.Add(failedFilesLabel, 2, 1);

            // 创建第二个表格，用于显示更多统计信息
            var infoTable = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,  // 三列：重试次数、处理时间、预计结束时间
                RowCount = 2,     // 两行：标签和值
                Padding = new Padding(0),
                Margin = new Padding(0),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 设置列宽
            infoTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            infoTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            infoTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.34F));

            // 设置行高
            infoTable.RowStyles.Add(new RowStyle(SizeType.Percent, 50));
            infoTable.RowStyles.Add(new RowStyle(SizeType.Percent, 50));

            // 创建预计结束时间标签
            var endTimeLabel = new Label
            {
                Text = "预计结束时间:",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Bold),
                Padding = new Padding(10, 0, 0, 0) // 添加左侧内边距
            };

            // 创建预计结束时间值控件
            expectedEndTimeLabel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.None
            };
            var expectedEndTimeValueLabel = new Label
            {
                Text = "--",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                AutoSize = false,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Regular),
                Padding = new Padding(10, 0, 0, 0) // 与标题标签相同的左侧内边距
            };
            expectedEndTimeLabel.Controls.Add(expectedEndTimeValueLabel);
            expectedEndTimeLabel.Tag = expectedEndTimeValueLabel; // 保存标签引用

            // 添加标签和值到第二个表格
            infoTable.Controls.Add(retriedCountLabel, 0, 0);
            infoTable.Controls.Add(retriedFilesLabel, 0, 1);
            infoTable.Controls.Add(timeLabel, 1, 0);
            infoTable.Controls.Add(processingTimeLabel, 1, 1);
            infoTable.Controls.Add(endTimeLabel, 2, 0);
            infoTable.Controls.Add(expectedEndTimeLabel, 2, 1);

            // 创建一个包含两个统计表格的面板
            var statsPanelContainer = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(0),
                Margin = new Padding(0),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 整齐的行高分配
            statsPanelContainer.RowStyles.Clear();
            statsPanelContainer.RowStyles.Add(new RowStyle(SizeType.Percent, 50));
            statsPanelContainer.RowStyles.Add(new RowStyle(SizeType.Percent, 50));

            // 修改statsTable和infoTable为统一的边距和样式
            statsTable.Margin = new Padding(0);
            statsTable.Padding = new Padding(0);
            statsTable.CellBorderStyle = TableLayoutPanelCellBorderStyle.None;

            infoTable.Margin = new Padding(0);
            infoTable.Padding = new Padding(0);
            infoTable.CellBorderStyle = TableLayoutPanelCellBorderStyle.None;

            statsPanelContainer.Controls.Add(statsTable, 0, 0);
            statsPanelContainer.Controls.Add(infoTable, 0, 1);

            // 第二行：创建进度条
            var progressPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(5, 0, 5, 0)
            };

            progressBarControl = new ProgressBar
            {
                Dock = DockStyle.Fill,
                Minimum = 0,
                Maximum = 100,
                Value = 0,
                Style = ProgressBarStyle.Continuous
            };

            progressBarLabel = new Label
            {
                Text = "0%",
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                AutoSize = false
            };

            progressPanel.Controls.Add(progressBarControl);
            progressPanel.Controls.Add(progressBarLabel);

            // 第三行：处理速度和时间信息
            var infoPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(5, 0, 5, 0)
            };

            speedInfoLabel = new Label
            {
                Text = "处理速度: 0 文件/分钟 | 处理耗时: 00:00:00 | 预计剩余时间: --:--:--",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                AutoSize = false,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9, System.Drawing.FontStyle.Regular)
            };

            infoPanel.Controls.Add(speedInfoLabel);

            // 添加三个面板到主布局
            layout.Controls.Add(statsPanelContainer, 0, 0);
            layout.Controls.Add(progressPanel, 0, 1);
            layout.Controls.Add(infoPanel, 0, 2);

            panel.Controls.Add(layout);

            // 创建处理时间更新定时器
            processingTimer = new System.Windows.Forms.Timer
            {
                Interval = 1000 // 每秒更新一次
            };
            processingTimer.Tick += (s, e) => {
                UpdateProcessingTime();

                // 更新处理进度
                if (isProcessing && slidesFormatter != null)
                {
                    int total = slidesFormatter.TotalFiles;
                    int completed = slidesFormatter.CompletedFiles;
                    int failed = slidesFormatter.FailedFiles;
                    int retried = slidesFormatter.RetriedFiles;

                    UpdateStatistics(total, completed, failed, retried);
                }
            };

            return panel;
        }

        private void DocumentFormatButton_Click(object? sender, EventArgs e)
        {
            using (var formatForm = new DocumentFormatForm())
            {
                if (formatForm.ShowDialog() == DialogResult.OK)
                {
                    // 刷新文件列表以反映新的格式设置
                    RefreshFileList();
                }
            }
        }

        private void RefreshFileList()
        {
            // 获取当前文件列表
            var files = GetFilesFromDirectory(settings?.SourceDirectory ?? "", settings?.IncludeSubdirectories ?? false);

            // 过滤掉未启用的格式
            files = files.Where(f =>
            {
                string ext = Path.GetExtension(f).ToUpper().TrimStart('.');
                return SettingsManager.IsFormatEnabled(ext);
            }).ToList();

            // 更新文件列表显示
            UpdateFileList(files);
        }

        private List<string> GetFilesFromDirectory(string directory, bool includeSubdirectories)
        {
            try
            {
                if (string.IsNullOrEmpty(directory) || !Directory.Exists(directory))
                {
                    return new List<string>();
                }

                var searchOption = includeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var allFiles = Directory.GetFiles(directory, "*.*", searchOption);

                // 过滤掉以~$开头的临时文件
                var filteredFiles = allFiles.Where(f => !Path.GetFileName(f).StartsWith("~$")).ToList();

                // 记录被过滤的临时文件数量
                int tempFilesCount = allFiles.Length - filteredFiles.Count;
                if (tempFilesCount > 0)
                {
                    logger?.Log($"从目录 {directory} 中找到 {allFiles.Length} 个文件，过滤掉 {tempFilesCount} 个临时文件，剩余 {filteredFiles.Count} 个文件");
                }

                return filteredFiles;
            }
            catch (Exception ex)
            {
                logger?.LogError($"获取目录 {directory} 中的文件时出错: {ex.Message}", ex);
                return new List<string>();
            }
        }

        private void UpdateFileList(List<string> files)
        {
            // 更新文件列表显示
            logger?.Log($"找到 {files.Count} 个文件");
            // 这里可以添加更新UI的代码，如果需要的话
        }

        // 提取开始处理的逻辑到单独的方法，便于定时调用
        private async void StartProcessing()
        {
            try
            {
                // 先重置统计信息，确保所有计数器和显示都被重置
                ResetStatistics();

                // 开始处理
                isProcessing = true;

                // 更新所有按钮状态
                UpdateButtonState();

                // 更新界面状态
                logger?.Log("正在处理文件...");
                processingStartTime = DateTime.Now;
                if (processingTimer != null)
                    processingTimer.Start();

                // 开始文件处理
                string sourceFolder = settings?.SourceDirectory ?? "";
                string outputFolder = settings?.OutputDirectory ?? "";

                try
                {
                    // 确保在wordFormatter中重置统计数据
                    logger?.Log($"开始处理文件，源目录: {sourceFolder}, 输出目录: {outputFolder}");

                    // 创建新的CancellationTokenSource
                    cancellationTokenSource?.Dispose();
                    cancellationTokenSource = new CancellationTokenSource();

                    // 启动处理 - 获取文件总数在后台线程执行
                    if (slidesFormatter != null)
                    {
                        await Task.Run(() =>
                        {
                            slidesFormatter.StartProcessing(sourceFolder, outputFolder);
                        });
                    }

                    // 等待获取文件总数并立即更新统计信息
                    if (slidesFormatter != null)
                    {
                        int totalFiles = slidesFormatter.TotalFiles;
                        logger?.Log($"找到 {totalFiles} 个文件等待处理");
                        UpdateStatistics(totalFiles, 0, 0, 0);
                    }

                    // 启动UI更新定时器 - 每200毫秒更新一次UI，保持UI响应性
                    var uiUpdateTimer = new System.Windows.Forms.Timer
                    {
                        Interval = 200,
                        Enabled = true
                    };

                    uiUpdateTimer.Tick += (s, e) =>
                    {
                        // 更新处理状态和进度显示
                        if (slidesFormatter != null)
                        {
                            UpdateStatistics(
                                slidesFormatter.TotalFiles,
                                slidesFormatter.CompletedFiles,
                                slidesFormatter.FailedFiles,
                                slidesFormatter.RetriedFiles
                            );
                        }

                        // 更新进度条
                        if (progressBarControl != null && slidesFormatter != null && slidesFormatter.TotalFiles > 0)
                        {
                            var totalFiles = slidesFormatter.TotalFiles; // 在null检查后获取值
                            int completedFiles = slidesFormatter.CompletedFiles;
                            int percentage = (int)(completedFiles * 100.0 / totalFiles);
                            progressBarControl.Value = Math.Min(percentage, 100);

                            if (progressBarLabel != null)
                                progressBarLabel.Text = $"{percentage}%";

                            // 检查是否所有文件都已处理完成
                            if (slidesFormatter?.TotalFiles > 0 && completedFiles >= (slidesFormatter?.TotalFiles ?? 0) && isProcessing)
                            {
                                // 所有文件处理完成，强制更新状态
                                isProcessing = false;
                                UpdateButtonState();
                                processingTimer?.Stop();
                                logger?.Log("所有文件已处理完成");
                            }
                        }

                        // 更新每秒处理速度
                        if (speedInfoLabel != null)
                        {
                            var now = DateTime.Now;
                            var elapsed = (now - lastUpdateTime).TotalSeconds;

                            if (elapsed >= 2.0) // 至少2秒更新一次速度统计
                            {
                                var processed = (wordFormatter?.CompletedFiles ?? 0) - lastProcessedCount;
                                double filesPerSecond = processed / elapsed;

                                // 更新持久化的处理速度
                                if (filesPerSecond > 0)
                                {
                                    currentProcessingSpeed = filesPerSecond;
                                    speedUnit = "文件/秒";

                                    // 计算预计剩余时间
                                    int total = wordFormatter?.TotalFiles ?? 0;
                                    int processedCount = (wordFormatter?.CompletedFiles ?? 0) + (wordFormatter?.FailedFiles ?? 0);
                                    var remainingFiles = total - processedCount;

                                    if (filesPerSecond > 0 && remainingFiles > 0)
                                    {
                                        double remainingSeconds = remainingFiles / filesPerSecond;
                                        currentRemainingTime = TimeSpan.FromSeconds(remainingSeconds);
                                    }

                                    // 获取定时状态信息
                                    string scheduleInfo = GetScheduleInfo();

                                    // 计算处理耗时
                                    TimeSpan elapsed2 = DateTime.Now - processingStartTime;

                                    // 更新完整的统计信息
                                    speedInfoLabel.Text = $"处理速度: {currentProcessingSpeed:F2} {speedUnit} | 处理耗时: {elapsed2.Hours:00}:{elapsed2.Minutes:00}:{elapsed2.Seconds:00} | 预计剩余时间: {currentRemainingTime:hh\\:mm\\:ss}{scheduleInfo}";
                                }

                                // 更新上次统计时间和计数
                                lastUpdateTime = now;
                                lastProcessedCount = slidesFormatter?.CompletedFiles ?? 0;
                            }
                        }

                        // 更新处理时间
                        UpdateProcessingTime();

                        // 如果处理完成，停止定时器，但保持统计信息显示
                        if (!isProcessing)
                        {
                            // 更新最后一次的统计信息
                            UpdateProcessingTime();

                            // 停止定时器
                            uiUpdateTimer.Stop();
                            uiUpdateTimer.Dispose();
                        }
                    };
                }
                catch (Exception ex)
                {
                    logger?.LogError("开始处理时出错", ex);
                    MessageBox.Show($"开始处理时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                    // 出错时重置处理状态
                    isProcessing = false;
                    UpdateButtonState();
                    processingTimer?.Stop();
                }
            }
            catch (Exception ex)
            {
                logger?.LogError("执行开始操作时出错", ex);
                MessageBox.Show($"执行开始操作时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 出错时重置处理状态
                isProcessing = false;
                UpdateButtonState();
                processingTimer?.Stop();
            }
        }

        private void SelectAllButton_Click(object? sender, EventArgs e)
        {
            try
            {
                // 设置所有功能启用标志为true
                if (settings != null)
                {
                    settings.EnablePageSetup = true;
                    settings.EnableDeleteContent = true;
                    settings.EnableContentReplace = true;
                    settings.EnableGlobalParagraphFormat = true;
                    settings.EnableParagraphMatch = true;
                    settings.EnableHeaderFooter = true;
                    settings.EnableDocumentProperties = true;
                    settings.EnableFileNameReplace = true;
                    settings.EnableWordToPdf = true;
                }

                // 同样更新currentSettings
                if (currentSettings != null)
                {
                    currentSettings.EnablePageSetup = true;
                    currentSettings.EnableDeleteContent = true;
                    currentSettings.EnableContentReplace = true;
                    currentSettings.EnableGlobalParagraphFormat = true;
                    currentSettings.EnableParagraphMatch = true;
                    currentSettings.EnableHeaderFooter = true;
                    currentSettings.EnableDocumentProperties = true;
                    currentSettings.EnableFileNameReplace = true;
                    currentSettings.EnableWordToPdf = true;
                }

                // 记录日志
                logger?.Log("已全选所有功能");

                // 更新UI中的复选框状态
                UpdateCheckboxStates();

                // 保存设置到文件
                if (currentSettings != null)
                {
                    SettingsManager.SaveSettings(currentSettings);
                }

                // 移除消息弹窗
            }
            catch (Exception ex)
            {
                MessageBox.Show($"全选操作出错: {ex.Message}\n请检查日志文件获取详细信息", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                logger?.LogError("全选操作出错", ex);
            }
        }

        private void UnselectAllButton_Click(object? sender, EventArgs e)
        {
            try
            {
                // 设置所有功能启用标志为false
                if (settings != null)
                {
                    settings.EnablePageSetup = false;
                    settings.EnableDeleteContent = false;
                    settings.EnableContentReplace = false;
                    settings.EnableGlobalParagraphFormat = false;
                    settings.EnableParagraphMatch = false;
                    settings.EnableHeaderFooter = false;
                    settings.EnableDocumentProperties = false;
                    settings.EnableFileNameReplace = false;
                    settings.EnableWordToPdf = false;
                }

                // 同样更新currentSettings
                if (currentSettings != null)
                {
                    currentSettings.EnablePageSetup = false;
                    currentSettings.EnableDeleteContent = false;
                    currentSettings.EnableContentReplace = false;
                    currentSettings.EnableGlobalParagraphFormat = false;
                    currentSettings.EnableParagraphMatch = false;
                    currentSettings.EnableHeaderFooter = false;
                    currentSettings.EnableDocumentProperties = false;
                    currentSettings.EnableFileNameReplace = false;
                    currentSettings.EnableWordToPdf = false;
                }

                // 记录日志
                logger?.Log("已取消全选所有功能");

                // 更新UI中的复选框状态
                UpdateCheckboxStates();

                // 保存设置到文件
                if (currentSettings != null)
                {
                    SettingsManager.SaveSettings(currentSettings);
                }

                // 移除成功消息弹窗
            }
            catch (Exception ex)
            {
                MessageBox.Show($"取消全选操作出错: {ex.Message}\n请检查日志文件获取详细信息", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                logger?.LogError("取消全选操作出错", ex);
            }
        }

        private void UpdateCheckboxStates()
        {
            try
            {
                // Find the content panel (the main panel that contains everything)
                var contentPanel = Controls.OfType<Panel>().FirstOrDefault();
                if (contentPanel == null)
                {
                    logger?.LogError("无法找到内容面板", new Exception("未找到内容面板控件"));
                    return;
                }

                // Find the main layout in the content panel
                var mainLayout = contentPanel.Controls.OfType<TableLayoutPanel>().FirstOrDefault();
                if (mainLayout == null)
                {
                    logger?.LogError("无法找到主布局", new Exception("未找到主布局控件"));
                    return;
                }

                // Find the button panel (second row, index 1)
                if (mainLayout.Controls.Count <= 1 || !(mainLayout.Controls[1] is Panel buttonPanel))
                {
                    logger?.LogError("无法找到按钮面板", new Exception("主布局中未找到按钮面板"));
                    return;
                }

                // Find the layout within the button panel
                var layout = buttonPanel.Controls.OfType<TableLayoutPanel>().FirstOrDefault();
                if (layout == null)
                {
                    logger?.LogError("无法找到布局面板", new Exception("未找到布局面板控件"));
                    return;
                }

                if (settings != null)
                {
                    logger?.Log($"当前功能状态: 页面设置={settings.EnablePageSetup}, 删除内容={settings.EnableDeleteContent}, 内容替换={settings.EnableContentReplace}, 全局段落格式={settings.EnableGlobalParagraphFormat}, 段落匹配={settings.EnableParagraphMatch}, 页眉页脚={settings.EnableHeaderFooter}, 文档属性={settings.EnableDocumentProperties}, 文件名替换={settings.EnableFileNameReplace}, Word转PDF={settings.EnableWordToPdf}");
                }

                int foundControls = 0;
                foreach (Control container in layout.Controls)
                {
                    if (container is TableLayoutPanel tlp && tlp.Controls.Count > 0 && tlp.Controls[0] is CheckBox checkBox)
                    {
                        // 根据按钮文本确定应该设置什么状态
                        if (tlp.Controls.Count > 1 && tlp.Controls[1] is Button button)
                        {
                            if (settings != null)
                            {
                                switch (button.Text)
                                {
                                    case "页面设置":
                                        checkBox.Checked = settings.EnablePageSetup;
                                        logger?.Log($"更新复选框: 页面设置={checkBox.Checked}");
                                        foundControls++;
                                        break;
                                    case "删除内容设置":
                                        checkBox.Checked = settings.EnableDeleteContent;
                                        logger?.Log($"更新复选框: 删除内容设置={checkBox.Checked}");
                                        foundControls++;
                                        break;
                                    case "内容替换规则":
                                        checkBox.Checked = settings.EnableContentReplace;
                                        logger?.Log($"更新复选框: 内容替换规则={checkBox.Checked}");
                                        foundControls++;
                                        break;
                                    case "全局段落格式":
                                        checkBox.Checked = settings.EnableGlobalParagraphFormat;
                                        logger?.Log($"更新复选框: 全局段落格式={checkBox.Checked}");
                                        foundControls++;
                                        break;
                                    case "段落匹配规则":
                                        checkBox.Checked = settings.EnableParagraphMatch;
                                        logger?.Log($"更新复选框: 段落匹配规则={checkBox.Checked}");
                                        foundControls++;
                                        break;
                                    case "页眉页脚设置":
                                        checkBox.Checked = settings.EnableHeaderFooter;
                                        logger?.Log($"更新复选框: 页眉页脚设置={checkBox.Checked}");
                                        foundControls++;
                                        break;
                                    case "文档属性":
                                        checkBox.Checked = settings.EnableDocumentProperties;
                                        logger?.Log($"更新复选框: 文档属性={checkBox.Checked}");
                                        foundControls++;
                                        break;
                                    case "文件名替换":
                                        checkBox.Checked = settings.EnableFileNameReplace;
                                        logger?.Log($"更新复选框: 文件名替换={checkBox.Checked}");
                                        foundControls++;
                                        break;
                                    case "Word转PDF":
                                        checkBox.Checked = settings.EnableWordToPdf;
                                        logger?.Log($"更新复选框: Word转PDF={checkBox.Checked}");
                                        foundControls++;
                                        break;
                                }
                            }
                        }
                    }
                }

                logger?.Log($"找到并更新了 {foundControls} 个功能复选框");

                // 保存设置到配置文件
                try
                {
                    if (currentSettings != null)
                    {
                        SettingsManager.SaveSettings(currentSettings);
                        logger?.Log("设置已保存到配置文件");
                    }
                }
                catch (Exception ex)
                {
                    logger?.LogError("保存设置时出错", ex);
                }
            }
            catch (Exception ex)
            {
                logger?.LogError("更新复选框状态时出错", ex);
                MessageBox.Show($"更新复选框状态时出错: {ex.Message}\n请检查日志文件获取详细信息", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // ComboBox居中绘制事件处理程序
        private void ComboBox_DrawItem(object? sender, DrawItemEventArgs e)
        {
            if (sender is not ComboBox comboBox || e.Index < 0 || e.Index >= comboBox.Items.Count) return;

            e.DrawBackground();

            string text = comboBox.Items[e.Index]?.ToString() ?? "";

            // 使用控件字体或默认字体
            System.Drawing.Font font = e.Font ?? comboBox.Font ?? SystemFonts.DefaultFont;

            // 计算文本居中位置
            var textSize = e.Graphics.MeasureString(text, font);
            var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
            var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;

            // 绘制文本
            using (var brush = new SolidBrush(e.ForeColor))
            {
                e.Graphics.DrawString(text, font, brush, x, y);
            }

            e.DrawFocusRectangle();
        }


    }
}

