/*
 * ========================================
 * 文件名: Settings.cs
 * 功能描述: 主配置数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义应用程序的所有配置选项
 * 2. 提供配置数据的结构化存储
 * 3. 支持JSON序列化和反序列化
 * 4. 包含所有功能模块的配置引用
 *
 * 配置分类:
 *
 * 基础设置:
 * - 源目录和输出目录配置
 * - 文件处理选项（子目录、目录结构等）
 * - 冲突处理和备份设置
 * - 多线程和批处理配置
 *
 * 功能模块开关:
 * - EnablePageSetup: 页面设置功能
 * - EnableDeleteContent: 内容删除功能
 * - EnableContentReplace: 内容替换功能
 * - EnableGlobalParagraphFormat: 全局段落格式
 * - EnableParagraphMatch: 段落匹配规则
 * - EnableHeaderFooter: 页眉页脚设置
 * - EnableDocumentProperties: 文档属性设置
 * - EnableFileNameReplace: 文件名替换
 * - EnableWordToPdf: PDF转换功能
 *
 * 子配置对象:
 * - PageSetup: 页面设置配置
 * - DeleteSettings: 内容删除配置
 * - HeaderFooterSettings: 页眉页脚配置
 * - DocumentProperties: 文档属性配置
 * - PdfSettings: PDF转换配置
 * - TxtSettings: TXT文件处理配置
 * - ScheduleSettings: 定时任务配置
 * - GlobalParagraphFormat: 全局段落格式
 *
 * 规则集合:
 * - ParagraphMatchRules: 段落匹配规则列表
 * - FileNameReplaceRules: 文件名替换规则列表
 * - ContentReplaceRules: 内容替换规则列表
 *
 * 注意事项:
 * - 所有属性都支持JSON序列化
 * - 包含完整的默认值设置
 * - 支持配置的分模块管理
 * - 实现了配置的向后兼容性
 */

using System;
using System.Collections.Generic;
using Aspose.Words;
using AsposeWordFormatter.Models;
using AW = Aspose.Words;

namespace AsposeWordFormatter.Models
{
    /// <summary>
    /// 日志设置类
    /// </summary>
    public class LogSettings
    {
        /// <summary>
        /// 日志总开关
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// Debug级别日志开关
        /// </summary>
        public bool EnableDebugLog { get; set; } = false;

        /// <summary>
        /// Info级别日志开关
        /// </summary>
        public bool EnableInfoLog { get; set; } = true;

        /// <summary>
        /// Warning级别日志开关
        /// </summary>
        public bool EnableWarningLog { get; set; } = true;

        /// <summary>
        /// Error级别日志开关
        /// </summary>
        public bool EnableErrorLog { get; set; } = true;

        /// <summary>
        /// Fatal级别日志开关
        /// </summary>
        public bool EnableFatalLog { get; set; } = true;

        /// <summary>
        /// 是否使用分离的日志文件（按日志级别分别保存）
        /// </summary>
        public bool UseSeparateLogFiles { get; set; } = true;

        /// <summary>
        /// 是否保留总日志文件（含所有启用级别的日志）
        /// </summary>
        public bool KeepAllLogFile { get; set; } = true;
    }

    /// <summary>
    /// 重试设置类
    /// </summary>
    public class RetrySettings
    {
        /// <summary>
        /// 最大重试次数 - 处理文件失败时的重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 基础延迟时间（毫秒）- 第一次重试前的等待时间
        /// </summary>
        public int BaseDelayMs { get; set; } = 1000;

        /// <summary>
        /// 延迟时间乘数（用于指数退避）- 每次重试后延迟时间的增长倍数
        /// </summary>
        public float DelayMultiplier { get; set; } = 2.0f;

        /// <summary>
        /// 最大延迟时间（毫秒）- 重试间隔的上限
        /// </summary>
        public int MaxDelayMs { get; set; } = 10000;

        /// <summary>
        /// 文件锁定检测的最大尝试次数 - 检查文件是否被锁定的尝试次数
        /// </summary>
        public int FileLockCheckMaxAttempts { get; set; } = 20;

        /// <summary>
        /// 文件锁定检测的延迟时间（毫秒）- 检查文件锁定状态的间隔时间
        /// </summary>
        public int FileLockCheckDelayMs { get; set; } = 500;

        /// <summary>
        /// 文件被占用时的特殊重试次数 - 当文件被其他进程占用时使用的重试次数
        /// </summary>
        public int FileLockRetryCount { get; set; } = 5;

        /// <summary>
        /// 文件被占用时的基础延迟时间（毫秒）- 文件被占用时第一次重试前的等待时间
        /// </summary>
        public int FileLockBaseDelayMs { get; set; } = 2000;

        /// <summary>
        /// 是否启用智能内存管理 - 根据内存使用情况自动触发垃圾回收
        /// </summary>
        public bool EnableSmartMemoryManagement { get; set; } = true;

        /// <summary>
        /// 内存使用阈值（MB）- 超过此阈值时触发垃圾回收
        /// </summary>
        public int MemoryThresholdMB { get; set; } = 500;
    }
    public class Settings
    {
        // 路径设置
        public string SourceDirectory { get; set; } = string.Empty;
        public string OutputDirectory { get; set; } = string.Empty;
        public bool IncludeSubdirectories { get; set; } = true;
        public bool KeepDirectoryStructure { get; set; } = true;
        public string ConflictHandling { get; set; } = "覆盖"; // 覆盖、跳过、重命名
        public bool ProcessOriginalFiles { get; set; } = false; // 是否直接处理原文件
        public bool MoveFiles { get; set; } = false; // 是否移动文件（false为复制）
        /// <summary>
        /// 最大线程数 - 控制并行处理的线程数量
        /// 设置为0表示自动选择最优线程数（基于CPU核心数）
        /// </summary>
        public int MaxThreads { get; set; } = 1; // 最大线程数，默认为1线程

        /// <summary>
        /// 最大重试次数 - 处理文件失败时的重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3; // 最大重试次数，默认为3次

        /// <summary>
        /// 批处理大小 - 每批处理的文件数量，用于优化内存使用
        /// 设置为0表示自动选择最优批处理大小（基于线程数和系统内存）
        /// </summary>
        public int BatchSize { get; set; } = 50; // 每批处理文件数，默认为50个

        // 功能开关
        public bool EnablePageSetup { get; set; } = true;
        public bool EnableDeleteContent { get; set; } = true;
        public bool EnableContentReplace { get; set; } = false;
        public bool EnableGlobalParagraphFormat { get; set; } = true;
        public bool EnableParagraphMatch { get; set; } = false;
        public bool EnableHeaderFooter { get; set; } = true;
        public bool EnableDocumentProperties { get; set; } = true;
        public bool EnableFileNameReplace { get; set; } = false;
        public bool EnableWordToPdf { get; set; } = false;

        // 添加定时处理设置
        public ScheduleSettings? ScheduleSettings { get; set; } = new ScheduleSettings();

        // 日志设置
        public LogSettings? LogSettings { get; set; } = new LogSettings();

        // 页面设置
        public PageSetupFixed? PageSetup { get; set; } = new PageSetupFixed();

        // 页眉页脚设置
        public HeaderFooterSettings? HeaderFooterSettings { get; set; } = new HeaderFooterSettings();

        // 文档属性
        public DocumentProperties? DocumentProperties { get; set; } = new DocumentProperties();

        // 段落匹配规则
        public List<ParagraphMatchRule> ParagraphMatchRules { get; set; } = new List<ParagraphMatchRule>();

        // 文件名替换规则
        public List<FileNameReplaceRule> FileNameReplaceRules { get; set; } = new List<FileNameReplaceRule>();

        // 文件内容替换规则
        public List<ContentReplaceRule> ContentReplaceRules { get; set; } = new List<ContentReplaceRule>();
        public bool ReplaceManualLineBreaks { get; set; } = false;

        // 全文设置
        public ParagraphMatchRule? GlobalParagraphFormat { get; set; } = new ParagraphMatchRule()
        {
            HyperlinkUrl = string.Empty,
            HyperlinkToolTip = string.Empty,
            BookmarkName = string.Empty
        };

        // 文件处理相关
        public bool BackupOriginalFiles { get; set; } = true;
        public string BackupDirectory { get; set; } = "backup";

        // 添加删除设置属性
        public DeleteSettings? DeleteSettings { get; set; } = new DeleteSettings();

        // TXT处理设置
        public TxtSettings? TxtSettings { get; set; } = new TxtSettings();

        // 文档处理相关
        public bool RemoveEmptyParagraphs { get; set; } = true;
        public bool NormalizeWhitespace { get; set; } = true;
        public bool RemoveMultipleSpaces { get; set; } = true;
        public bool RemoveHiddenContent { get; set; } = false;
        public bool RemoveComments { get; set; } = false;
        public bool RemoveHeadersFooters { get; set; } = false;

        // 段落格式化相关
        public bool ApplyDefaultParagraphFormat { get; set; } = true;
        public double DefaultLineSpacing { get; set; } = 1.5;
        public double DefaultFirstLineIndent { get; set; } = 28.0;
        public double DefaultLeftIndent { get; set; } = 0.0;
        public double DefaultRightIndent { get; set; } = 0.0;
        public double DefaultSpaceBefore { get; set; } = 0.0;
        public double DefaultSpaceAfter { get; set; } = 0.0;

        // 字体格式化相关
        public bool ApplyDefaultFontFormat { get; set; } = true;
        public string DefaultFontName { get; set; } = "宋体";
        public double DefaultFontSize { get; set; } = 12.0;
        public bool PreserveFontStyles { get; set; } = true;

        // 导出选项
        public bool OptimizeFileSize { get; set; } = true;
        public bool UpdateFields { get; set; } = true;
        public bool UpdateTOC { get; set; } = true;
        public SaveFormat SaveFormat { get; set; } = SaveFormat.Docx;

        // PDF转换设置
        public PdfSettings? PdfSettings { get; set; }

        /// <summary>
        /// 重试设置
        /// </summary>
        public RetrySettings RetrySettings { get; set; } = new RetrySettings();

        /// <summary>
        /// 是否启用智能线程管理 - 根据系统负载动态调整线程数
        /// </summary>
        public bool EnableSmartThreading { get; set; } = false; // 智能线程管理，默认关闭
    }

    public class ContentReplaceRule
    {
        public string Find { get; set; } = string.Empty;
        public string Replace { get; set; } = string.Empty;
        public bool CaseSensitive { get; set; } = false;
        public bool UseRegex { get; set; } = false;
        public bool IsEnabled { get; set; } = true;
        public bool FindWholeWordsOnly { get; set; } = false; // 是否只匹配整个单词
        // 添加范围限制属性
        public bool SearchInMainText { get; set; } = true;      // 在正文中查找替换
        public bool SearchInHeaders { get; set; } = true;       // 在页眉中查找替换
        public bool SearchInFooters { get; set; } = true;       // 在页脚中查找替换
        public bool SearchInTextBoxes { get; set; } = true;     // 在文本框中查找替换
        public bool SearchInFootnotes { get; set; } = true;     // 在脚注中查找替换
        public bool SearchInComments { get; set; } = false;     // 在批注中查找替换
    }

    public class PdfSettings
    {
        // 图像压缩设置
        public bool EnableImageCompression { get; set; } = true;
        public AW.Saving.PdfImageCompression ImageCompression { get; set; } = AW.Saving.PdfImageCompression.Auto;
        public int JpegQuality { get; set; } = 75;

        // 文档元数据设置
        public bool EnableDocumentMetadata { get; set; } = false;
        public string Author { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Keywords { get; set; } = string.Empty;

        // 数字签名设置
        public bool EnableDigitalSignature { get; set; } = false;
        public string CertificatePath { get; set; } = string.Empty;
        public string CertificatePassword { get; set; } = string.Empty;

        // 图像优化设置
        public bool EnableImageOptimization { get; set; } = true;
        public bool DownsampleImages { get; set; } = true;

        // 分辨率设置
        public bool EnableResolutionSettings { get; set; } = true;
        public int ColorImageResolution { get; set; } = 220;
        public int GrayImageResolution { get; set; } = 220;
        public int MonoImageResolution { get; set; } = 300;

        // 文本压缩设置
        public bool EnableTextCompression { get; set; } = true;
        public AW.Saving.PdfTextCompression TextCompression { get; set; } = AW.Saving.PdfTextCompression.Flate;

        // 文档结构设置
        public bool EnableDocumentStructure { get; set; } = true;
        public bool ExportDocumentStructure { get; set; } = true;

        // 字体设置
        public bool EnableFontSettings { get; set; } = true;
        public bool EmbedFullFonts { get; set; } = false;
        public AW.Saving.PdfFontEmbeddingMode FontEmbeddingMode { get; set; } = AW.Saving.PdfFontEmbeddingMode.EmbedAll;

        // 超链接设置
        public bool EnableHyperlinkSettings { get; set; } = true;
        public bool CreateNoteHyperlinks { get; set; } = true;
        public bool OpenHyperlinksInNewWindow { get; set; } = false;

        // PDF兼容性设置
        public bool EnableComplianceSettings { get; set; } = true;
        public AW.Saving.PdfCompliance Compliance { get; set; } = AW.Saving.PdfCompliance.Pdf17;

        // 渲染设置
        public bool EnableRenderingSettings { get; set; } = true;
        public AW.Saving.DmlEffectsRenderingMode DmlEffectsRenderingMode { get; set; } = AW.Saving.DmlEffectsRenderingMode.Fine;
        public bool AdditionalTextPositioning { get; set; } = false;

        // 其他设置
        public bool EnableOtherSettings { get; set; } = true;
        public bool DisplayDocTitle { get; set; } = true;
        public AW.Saving.PdfPageMode PageMode { get; set; } = AW.Saving.PdfPageMode.UseOutlines;
        public AW.Saving.PdfPageLayout PageLayout { get; set; } = AW.Saving.PdfPageLayout.OneColumn;

        // 安全和加密设置
        public bool EnableSecuritySettings { get; set; } = false;
        public string UserPassword { get; set; } = string.Empty;
        public string OwnerPassword { get; set; } = string.Empty;
        public bool AllowPrinting { get; set; } = true;
        public bool AllowModifyContents { get; set; } = false;
        public bool AllowCopyContent { get; set; } = true;
        public bool AllowModifyAnnotations { get; set; } = false;
        public bool AllowFillInForms { get; set; } = true;
        public bool AllowAccessibleReaders { get; set; } = true;
        public bool AllowAssembly { get; set; } = false;

        // 数字签名设置
        public bool EnableDigitalSignatureAdvanced { get; set; } = false;
        public string SignatureReason { get; set; } = string.Empty;
        public string SignatureLocation { get; set; } = string.Empty;
        public string SignatureContactInfo { get; set; } = string.Empty;
        public AW.Saving.PdfDigitalSignatureHashAlgorithm HashAlgorithm { get; set; } = AW.Saving.PdfDigitalSignatureHashAlgorithm.Sha256;

        // 书签和大纲设置
        public bool EnableBookmarkSettings { get; set; } = false;
        public int HeadingsOutlineLevels { get; set; } = 9;
        public int ExpandedOutlineLevels { get; set; } = 2;
        public bool CreateMissingOutlineLevels { get; set; } = true;
        public bool CreateOutlinesForHeadingsInTables { get; set; } = false;
        public int DefaultBookmarksOutlineLevel { get; set; } = 0;
        public AW.Saving.HeaderFooterBookmarksExportMode HeaderFooterBookmarksExportMode { get; set; } = AW.Saving.HeaderFooterBookmarksExportMode.All;

        // 高级图像设置
        public bool EnableAdvancedImageSettings { get; set; } = false;
        public bool InterpolateImages { get; set; } = true;
        public bool PreblendImages { get; set; } = true;
        public AW.Saving.PdfImageColorSpaceExportMode ImageColorSpaceExportMode { get; set; } = AW.Saving.PdfImageColorSpaceExportMode.Auto;

        // 表单字段设置
        public bool EnableFormFieldSettings { get; set; } = false;
        public bool PreserveFormFields { get; set; } = true;
        public bool UseSdtTagAsFormFieldName { get; set; } = false;
        public bool RenderChoiceFormFieldBorder { get; set; } = true;

        // 页面显示设置
        public bool EnablePageDisplaySettings { get; set; } = false;
        public AW.Saving.PdfZoomBehavior ZoomBehavior { get; set; } = AW.Saving.PdfZoomBehavior.None;
        public int ZoomFactor { get; set; } = 100;
        public bool UseBookFoldPrintingSettings { get; set; } = false;

        // 元数据和自定义属性设置
        public bool EnableMetadataSettings { get; set; } = false;
        public AW.Saving.PdfCustomPropertiesExport CustomPropertiesExport { get; set; } = AW.Saving.PdfCustomPropertiesExport.Standard;
        public bool ExportLanguageToSpanTag { get; set; } = false;
        public bool ExportParagraphGraphicsToArtifact { get; set; } = false;

        // 字体高级设置
        public bool EnableAdvancedFontSettings { get; set; } = false;
        public bool UseCoreFonts { get; set; } = false;

        // 创建保存选项
        public AW.Saving.PdfSaveOptions CreateSaveOptions()
        {
            var options = new AW.Saving.PdfSaveOptions();

            // 启用内存优化，减少大文档处理时的内存占用
            options.MemoryOptimization = true;

            // 图像压缩设置
            if (EnableImageCompression)
            {
                options.ImageCompression = this.ImageCompression;
                options.JpegQuality = this.JpegQuality;

                // 添加更多图像压缩控制
                if (this.ImageCompression == AW.Saving.PdfImageCompression.Auto)
                {
                    // 自动模式下，可以设置更多参数来控制压缩行为
                    options.PreserveFormFields = true; // 保留表单字段中的图像质量
                }
            }

            // 图像优化设置
            if (EnableImageOptimization)
            {
                // 配置图像优化选项
                options.DownsampleOptions.DownsampleImages = this.DownsampleImages;

                if (this.DownsampleImages)
                {
                    // 设置默认降采样参数
                    options.DownsampleOptions.Resolution = 150;
                    options.DownsampleOptions.ResolutionThreshold = 225;
                }
            }

            // 分辨率设置
            if (EnableResolutionSettings)
            {
                // 启用图像降采样
                options.DownsampleOptions.DownsampleImages = true;

                // 设置彩色图像分辨率
                options.DownsampleOptions.Resolution = ColorImageResolution;

                // 设置分辨率阈值 - 在Aspose.Words中，这通常用于控制何时应用降采样
                // 当图像分辨率超过此阈值时才会降采样
                options.DownsampleOptions.ResolutionThreshold = GrayImageResolution;

                // 注意：Aspose.Words的最新版本可能提供更多控制不同图像类型分辨率的选项
                // 如果有可用的API，可以在此处添加单色图像分辨率设置

                // 优化图像处理
                options.OptimizeOutput = true;
            }

            // 文本压缩设置
            if (EnableTextCompression)
            {
                options.TextCompression = this.TextCompression;
            }

            // 文档结构设置
            if (EnableDocumentStructure)
            {
                options.ExportDocumentStructure = this.ExportDocumentStructure;

                // 如果导出文档结构，确保设置适当的标记选项
                if (this.ExportDocumentStructure)
                {
                    // 启用标记以支持辅助功能
                    options.ExportDocumentStructure = true;
                }
            }

            // 字体设置
            if (EnableFontSettings)
            {
                options.EmbedFullFonts = this.EmbedFullFonts;
                options.FontEmbeddingMode = this.FontEmbeddingMode;

                // 添加字体替换机制，处理系统中不可用的字体
                // 注意：PdfSaveOptions不直接支持FontSettings属性，需要通过全局设置
                var fontSettings = new AW.Fonts.FontSettings();
                fontSettings.SubstitutionSettings.DefaultFontSubstitution.DefaultFontName = "SimSun"; // 使用宋体作为默认替代字体
                fontSettings.SubstitutionSettings.FontInfoSubstitution.Enabled = true;

                // 将字体设置应用到全局 - 不能直接赋值给DefaultInstance，因为它是只读的
                // 使用SetFontsFolders方法来配置字体设置
                AW.Fonts.FontSettings.DefaultInstance.SetFontsFolders(
                    new string[] { Environment.GetFolderPath(Environment.SpecialFolder.Fonts) },
                    true);
            }

            // 超链接设置
            if (EnableHyperlinkSettings)
            {
                options.CreateNoteHyperlinks = this.CreateNoteHyperlinks;
                options.OpenHyperlinksInNewWindow = this.OpenHyperlinksInNewWindow;
            }

            // PDF兼容性设置
            if (EnableComplianceSettings)
            {
                options.Compliance = this.Compliance;

                // 根据选择的兼容性级别设置相应的选项
                switch (this.Compliance)
                {
                    case AW.Saving.PdfCompliance.PdfA1a:
                    case AW.Saving.PdfCompliance.PdfA1b:
                        // PDF/A 需要嵌入所有字体
                        options.FontEmbeddingMode = AW.Saving.PdfFontEmbeddingMode.EmbedAll;
                        break;
                }
            }

            // 渲染设置
            if (EnableRenderingSettings)
            {
                options.DmlEffectsRenderingMode = this.DmlEffectsRenderingMode;
                options.AdditionalTextPositioning = this.AdditionalTextPositioning;

                // 添加更多渲染控制选项
                if (this.DmlEffectsRenderingMode == AW.Saving.DmlEffectsRenderingMode.Fine)
                {
                    // 精细渲染模式下，可以启用更多高质量渲染选项
                    options.UseHighQualityRendering = true;
                }
            }

            // 其他设置
            if (EnableOtherSettings)
            {
                options.DisplayDocTitle = this.DisplayDocTitle;
                options.PageMode = this.PageMode;
                options.PageLayout = this.PageLayout;

                // 启用输出优化，减小文件大小
                options.OptimizeOutput = true;
            }

            // 文档元数据设置
            if (EnableDocumentMetadata)
            {
                // 注意：PdfSaveOptions不直接支持DocumentInfo属性
                // 这些元数据需要在保存文档前应用到文档对象
                // 在WordFormatter.cs中使用这些选项时，需要添加以下代码：
                //
                // if (pdfSettings.EnableDocumentMetadata)
                // {
                //     doc.BuiltInDocumentProperties.Author = pdfSettings.Author;
                //     doc.BuiltInDocumentProperties.Title = pdfSettings.Title;
                //     doc.BuiltInDocumentProperties.Subject = pdfSettings.Subject;
                //     doc.BuiltInDocumentProperties.Keywords = pdfSettings.Keywords;
                //     doc.BuiltInDocumentProperties.Creator = "Aspose.Words Word文档处理工具";
                // }
                //
                // 这样元数据就会被正确地包含在生成的PDF文件中
            }

            // 数字签名设置
            if (EnableDigitalSignature && !string.IsNullOrEmpty(CertificatePath))
            {
                try
                {
                    // 根据Aspose.Words的API创建数字签名
                    // 注意：不同版本的Aspose.Words可能有不同的构造函数参数
                    // 使用Aspose.Words.DigitalSignatures命名空间下的类来创建数字签名
                    // 首先加载证书
                    var certificateHolder = AW.DigitalSignatures.CertificateHolder.Create(
                        CertificatePath,
                        CertificatePassword);

                    // 创建签名选项
                    var signOptions = new AW.DigitalSignatures.SignOptions
                    {
                        Comments = "文档签名",
                        SignTime = DateTime.Now
                    };

                    // 设置数字签名详情 - 使用PdfDigitalSignatureDetails类型
                    // 添加所有必需的参数
                    options.DigitalSignatureDetails = new AW.Saving.PdfDigitalSignatureDetails(
                        certificateHolder,
                        "签名原因", // 添加签名原因参数
                        "中国", // 添加签名位置参数
                        DateTime.Now // 添加签名日期参数
                    );
                }
                catch (Exception ex)
                {
                    // 记录错误但继续保存
                    System.Diagnostics.Debug.WriteLine($"设置数字签名时出错: {ex.Message}");
                }
            }

            // 安全和加密设置
            if (EnableSecuritySettings)
            {
                if (!string.IsNullOrEmpty(UserPassword) || !string.IsNullOrEmpty(OwnerPassword))
                {
                    var permissions = AW.Saving.PdfPermissions.DisallowAll;

                    if (AllowPrinting) permissions |= AW.Saving.PdfPermissions.Printing;
                    if (AllowModifyContents) permissions |= AW.Saving.PdfPermissions.ModifyContents;
                    if (AllowCopyContent) permissions |= AW.Saving.PdfPermissions.ContentCopy;
                    if (AllowModifyAnnotations) permissions |= AW.Saving.PdfPermissions.ModifyAnnotations;
                    if (AllowFillInForms) permissions |= AW.Saving.PdfPermissions.FillIn;
                    if (AllowAccessibleReaders) permissions |= AW.Saving.PdfPermissions.ContentCopyForAccessibility;
                    if (AllowAssembly) permissions |= AW.Saving.PdfPermissions.DocumentAssembly;

                    options.EncryptionDetails = new AW.Saving.PdfEncryptionDetails(
                        UserPassword, OwnerPassword, permissions);
                }
            }

            // 书签和大纲设置
            if (EnableBookmarkSettings)
            {
                options.OutlineOptions.HeadingsOutlineLevels = HeadingsOutlineLevels;
                options.OutlineOptions.ExpandedOutlineLevels = ExpandedOutlineLevels;
                options.OutlineOptions.CreateMissingOutlineLevels = CreateMissingOutlineLevels;
                options.OutlineOptions.CreateOutlinesForHeadingsInTables = CreateOutlinesForHeadingsInTables;
                options.OutlineOptions.DefaultBookmarksOutlineLevel = DefaultBookmarksOutlineLevel;
                options.HeaderFooterBookmarksExportMode = HeaderFooterBookmarksExportMode;
            }

            // 高级图像设置
            if (EnableAdvancedImageSettings)
            {
                options.InterpolateImages = InterpolateImages;
                options.PreblendImages = PreblendImages;
                options.ImageColorSpaceExportMode = ImageColorSpaceExportMode;
            }

            // 表单字段设置
            if (EnableFormFieldSettings)
            {
                options.PreserveFormFields = PreserveFormFields;
                options.UseSdtTagAsFormFieldName = UseSdtTagAsFormFieldName;
                // 注意：RenderChoiceFormFieldBorder可能在某些版本中不可用
                // options.RenderChoiceFormFieldBorder = RenderChoiceFormFieldBorder;
            }

            // 页面显示设置
            if (EnablePageDisplaySettings)
            {
                options.ZoomBehavior = ZoomBehavior;
                options.ZoomFactor = ZoomFactor;
                options.UseBookFoldPrintingSettings = UseBookFoldPrintingSettings;
            }

            // 元数据和自定义属性设置
            if (EnableMetadataSettings)
            {
                options.CustomPropertiesExport = CustomPropertiesExport;
                options.ExportLanguageToSpanTag = ExportLanguageToSpanTag;
                // 注意：ExportParagraphGraphicsToArtifact可能在某些版本中不可用
                // options.ExportParagraphGraphicsToArtifact = ExportParagraphGraphicsToArtifact;
            }

            // 字体高级设置
            if (EnableAdvancedFontSettings)
            {
                options.UseCoreFonts = UseCoreFonts;
            }

            return options;
        }
    }
}