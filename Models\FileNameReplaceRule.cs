/*
 * ========================================
 * 文件名: FileNameReplaceRule.cs
 * 功能描述: 文件名替换规则数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义文件名替换规则的数据结构
 * 2. 提供文件名替换的执行逻辑
 * 3. 支持正则表达式和普通文本替换
 * 4. 包含文件名合法性验证和清理
 * 5. 提供规则验证和错误处理机制
 *
 * 核心属性:
 * - IsEnabled: 规则是否启用
 * - OldValue: 要替换的原始文本或正则表达式
 * - NewValue: 替换后的新文本
 * - IsRegex: 是否使用正则表达式模式
 * - IsCaseSensitive: 是否区分大小写
 * - IncludeExtension: 是否包含文件扩展名
 *
 * 核心方法:
 * - ApplyTo(): 将规则应用到指定文件名
 * - Validate(): 验证规则的有效性
 * - Clone(): 创建规则的深度副本
 * - SanitizeFileName(): 清理文件名中的非法字符
 *
 * 文件名处理特性:
 * - 支持正则表达式和普通文本两种替换模式
 * - 智能处理文件扩展名（可选择包含或排除）
 * - 自动清理Windows系统不允许的文件名字符
 * - 处理Windows保留文件名（CON、PRN、AUX等）
 * - 限制文件名长度以符合系统要求
 *
 * 性能优化:
 * - 正则表达式编译缓存机制
 * - 线程安全的正则表达式处理
 * - 使用StringBuilder优化字符串操作
 * - 二分查找检查保留文件名
 *
 * 错误处理:
 * - 完整的异常捕获和包装
 * - 详细的错误信息提供
 * - 正则表达式语法验证
 * - 超时和性能保护
 *
 * 文件名安全性:
 * - 移除非法字符（<>:"/\|?*等）
 * - 处理保留文件名冲突
 * - 确保文件名不为空或只包含点号
 * - 限制文件名长度防止系统限制
 *
 * 注意事项:
 * - 支持Windows文件系统的所有限制
 * - 包含完整的错误恢复机制
 * - 实现了高性能的字符串处理
 * - 提供了规则的完整验证功能
 */

using System;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;

namespace AsposeSlidesFormatter.Models
{
    public class FileNameReplaceRule
    {
        // 定义Windows不允许用于文件名的字符
        private static readonly char[] InvalidFileNameChars = Path.GetInvalidFileNameChars();
        // 定义Windows不允许作为文件名的保留名称
        private static readonly string[] ReservedFileNames = {
            "CON", "PRN", "AUX", "NUL",
            "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
            "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"
        };

        // 缓存编译后的正则表达式以提高性能
        private Regex? _compiledRegex;
        private string _lastRegexPattern = string.Empty;
        private RegexOptions _lastRegexOptions;
        private readonly object _regexLock = new object();

        public bool IsEnabled { get; set; } = true;
        public string OldValue { get; set; } = string.Empty;
        public string NewValue { get; set; } = string.Empty;
        public bool IsRegex { get; set; }
        public bool IsCaseSensitive { get; set; }
        public bool IncludeExtension { get; set; }

        /// <summary>
        /// 应用文件名替换规则到指定的文件名
        /// </summary>
        /// <param name="fileName">要处理的文件名</param>
        /// <returns>处理后的文件名</returns>
        public string ApplyTo(string fileName)
        {
            if (!IsEnabled || string.IsNullOrEmpty(fileName))
                return fileName;

            try
            {
                string extension = Path.GetExtension(fileName);
                string nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
                string textToReplace = IncludeExtension ? fileName : nameWithoutExtension;
                string result;

                if (IsRegex)
                {
                    try
                    {
                        var options = IsCaseSensitive ?
                            RegexOptions.None :
                            RegexOptions.IgnoreCase;

                        // 使用缓存的正则表达式以提高性能
                        Regex regex = GetCompiledRegex(OldValue, options);
                        result = regex.Replace(textToReplace, NewValue);
                    }
                    catch (ArgumentException ex)
                    {
                        // 捕获正则表达式语法错误，提供更详细的错误信息
                        throw new InvalidOperationException(
                            $"正则表达式语法错误 (模式: '{OldValue}'): {ex.Message}", ex);
                    }
                    catch (RegexMatchTimeoutException ex)
                    {
                        // 捕获正则表达式执行超时
                        throw new InvalidOperationException(
                            $"正则表达式执行超时 (模式: '{OldValue}'): {ex.Message}", ex);
                    }
                    catch (Exception ex)
                    {
                        // 其他正则表达式错误
                        throw new InvalidOperationException(
                            $"正则表达式执行错误 (模式: '{OldValue}'): {ex.Message}", ex);
                    }
                }
                else
                {
                    try
                    {
                        // 使用优化的字符串替换方法
                        result = IsCaseSensitive ?
                            textToReplace.Replace(OldValue, NewValue) :
                            textToReplace.Replace(OldValue, NewValue, StringComparison.OrdinalIgnoreCase);
                    }
                    catch (Exception ex)
                    {
                        // 捕获字符串替换错误，提供更详细的错误信息
                        throw new InvalidOperationException(
                            $"字符串替换错误 (原值: '{OldValue}', 新值: '{NewValue}'): {ex.Message}", ex);
                    }
                }

                // 如果不包含扩展名，需要重新添加扩展名
                if (!IncludeExtension)
                {
                    result = result + extension;
                }

                // 确保文件名不包含非法字符
                return SanitizeFileName(result);
            }
            catch (Exception ex)
            {
                // 包装异常以保留错误上下文
                throw new InvalidOperationException(
                    $"文件名替换规则应用失败 (原值: '{OldValue}', 新值: '{NewValue}'): {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取编译后的正则表达式对象，使用缓存提高性能
        /// </summary>
        /// <param name="pattern">正则表达式模式</param>
        /// <param name="options">正则表达式选项</param>
        /// <returns>编译后的正则表达式对象</returns>
        private Regex GetCompiledRegex(string pattern, RegexOptions options)
        {
            // 使用线程安全的方式处理正则表达式缓存
            lock (_regexLock)
            {
                if (_compiledRegex == null || pattern != _lastRegexPattern || options != _lastRegexOptions)
                {
                    // 添加RegexOptions.Compiled以提高性能
                    _compiledRegex = new Regex(pattern, options | RegexOptions.Compiled);
                    _lastRegexPattern = pattern;
                    _lastRegexOptions = options;
                }
                return _compiledRegex;
            }
        }

        /// <summary>
        /// 移除文件名中的非法字符并确保文件名符合Windows系统要求
        /// </summary>
        /// <param name="fileName">需要处理的文件名</param>
        /// <returns>处理后的合法文件名</returns>
        private string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "Document"; // 空文件名使用默认值

            try
            {
                // 如果是完整路径，分离路径和文件名
                string? directory = Path.GetDirectoryName(fileName);
                string fileNameOnly = Path.GetFileName(fileName);
                string extension = Path.GetExtension(fileNameOnly);
                string nameWithoutExt = Path.GetFileNameWithoutExtension(fileNameOnly);

                // 使用StringBuilder提高性能
                StringBuilder sanitizedName = new StringBuilder(nameWithoutExt.Length);

                // 一次性处理所有字符，避免多次字符串替换操作
                foreach (char c in nameWithoutExt)
                {
                    // 检查是否是非法字符
                    if (Array.IndexOf(InvalidFileNameChars, c) >= 0 || c == '/' || c == '\\')
                    {
                        sanitizedName.Append('_');
                    }
                    else
                    {
                        sanitizedName.Append(c);
                    }
                }

                // 转换为字符串并处理其他限制
                string result = sanitizedName.ToString().Trim('.');

                // 处理特殊情况：文件名不能是空白或只包含点号
                if (string.IsNullOrWhiteSpace(result) || result.Trim('.') == string.Empty)
                {
                    result = "Document";
                }

                // 使用二分查找检查是否是保留文件名（更高效）
                if (Array.BinarySearch(ReservedFileNames, result, StringComparer.OrdinalIgnoreCase) >= 0)
                {
                    result += "_file";
                }

                // 限制文件名长度
                if (result.Length > 200)
                {
                    result = result.Substring(0, 200);
                }

                // 重组文件名和扩展名
                string sanitizedFileName = result + extension;

                // 重组路径（如果有的话）
                return directory != null ? Path.Combine(directory, sanitizedFileName) : sanitizedFileName;
            }
            catch (Exception)
            {
                // 如果处理过程中出现任何错误，返回一个安全的默认文件名
                return "Document" + Path.GetExtension(fileName);
            }
        }

        /// <summary>
        /// 验证规则是否有效
        /// </summary>
        /// <param name="errorMessage">如果规则无效，返回错误信息</param>
        /// <returns>规则是否有效</returns>
        public bool Validate(out string errorMessage)
        {
            errorMessage = string.Empty;

            // 验证原值不能为空
            if (string.IsNullOrEmpty(OldValue))
            {
                errorMessage = "原值不能为空";
                return false;
            }

            // 如果使用正则表达式，验证正则表达式语法
            if (IsRegex)
            {
                try
                {
                    // 尝试编译正则表达式以验证其有效性
                    new Regex(OldValue);
                }
                catch (ArgumentException ex)
                {
                    errorMessage = $"无效的正则表达式: {ex.Message}";
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 创建规则的深度副本
        /// </summary>
        /// <returns>规则的副本</returns>
        public FileNameReplaceRule Clone()
        {
            return new FileNameReplaceRule
            {
                IsEnabled = this.IsEnabled,
                OldValue = this.OldValue,
                NewValue = this.NewValue,
                IsRegex = this.IsRegex,
                IsCaseSensitive = this.IsCaseSensitive,
                IncludeExtension = this.IncludeExtension
            };
        }
    }
}