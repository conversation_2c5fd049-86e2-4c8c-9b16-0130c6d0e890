/*
 * ========================================
 * 文件名: FileNameReplaceForm.cs
 * 功能描述: 文件名替换规则管理窗体
 * ========================================
 *
 * 主要功能:
 * 1. 文件名替换规则的创建、编辑和删除
 * 2. 替换规则的批量管理和操作
 * 3. 规则的优先级排序和调整
 * 4. 文件名替换的预览和验证
 * 5. 规则的导入导出功能
 * 6. 正则表达式和普通文本替换支持
 *
 * 界面结构:
 * - 规则列表区域：显示所有文件名替换规则
 * - 编辑规则区域：单个规则的详细编辑
 * - 批量操作面板：批量启用、禁用、删除等
 * - 规则排序按钮：上移、下移、置顶、置底
 * - 导入导出功能：CSV格式的规则管理
 *
 * 核心特性:
 * - 支持正则表达式和普通文本替换
 * - 区分大小写选项
 * - 文件扩展名包含选项
 * - 规则的启用/禁用控制
 * - 规则执行顺序管理
 * - 批量操作支持
 *
 * 规则属性:
 * - OldValue: 要替换的原始文本或正则表达式
 * - NewValue: 替换后的新文本
 * - IsRegex: 是否使用正则表达式
 * - IsCaseSensitive: 是否区分大小写
 * - IncludeExtension: 是否包含文件扩展名
 * - IsEnabled: 规则是否启用
 *
 * 批量操作:
 * - 批量启用/禁用规则
 * - 批量删除规则
 * - 批量设置属性
 * - 规则的导入导出
 *
 * 文件名处理:
 * - 支持文件名和扩展名的分别处理
 * - 非法字符的自动处理
 * - 文件名长度限制检查
 * - 重复文件名的处理策略
 *
 * 数据管理:
 * - 与FileNameReplaceRule模型集成
 * - 支持规则的实时预览
 * - CSV格式的导入导出
 * - 规则的验证和错误检查
 *
 * 注意事项:
 * - 文件名替换的安全性检查
 * - 正则表达式的语法验证
 * - 文件系统兼容性考虑
 * - 规则执行顺序的重要性
 */

using System;
using System.Windows.Forms;
using System.Drawing;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using AsposeWordFormatter.Models;

namespace AsposeWordFormatter
{
    public partial class FileNameReplaceForm : Form
    {
        private ListView rulesListView = null!;
        private TextBox oldValueTextBox = null!;
        private TextBox newValueTextBox = null!;
        private CheckBox isRegexCheck = null!;
        private CheckBox isCaseSensitiveCheck = null!;
        private CheckBox includeExtensionCheck = null!;
        private CheckBox isEnabledCheck = null!;
        private Button editButton = null!;
        private Button moveUpButton = null!;
        private Button moveDownButton = null!;
        private Button moveToTopButton = null!;
        private Button moveToBottomButton = null!;
        private TableLayoutPanel batchOperationPanel = null!; // 保存批量操作面板的引用

        public List<FileNameReplaceRule> Rules { get; private set; }

        public FileNameReplaceForm(List<FileNameReplaceRule>? rules = null)
        {
            Rules = rules ?? new List<FileNameReplaceRule>();
            InitializeComponent();

            // 确保界面初始化完成后刷新规则列表
            this.Load += (s, e) => {
                RefreshRulesList();
                // 更新日志
                Console.WriteLine($"正在加载文件名替换规则，共 {Rules.Count} 条规则");
            };
        }

        private void InitializeComponent()
        {
            this.Text = "文件名替换规则";
            this.Size = new Size(800, 700); // 增加默认高度
            this.MinimumSize = new Size(800, 700); // 增加最小高度
            this.Padding = new Padding(10);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable; // 允许调整大小
            this.MaximizeBox = true;
            this.MinimizeBox = false;

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(5),
                AutoSize = false
            };

            // 设置行高比例
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 45)); // 规则列表占45%
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 40)); // 编辑区域占40%
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 15)); // 按钮区域占15%

            // 规则列表
            var rulesGroup = new GroupBox { Text = "替换规则", Dock = DockStyle.Fill, Padding = new Padding(10) };

            // 添加搜索和筛选面板
            var searchFilterPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 4,
                RowCount = 1,
                Height = 35,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 设置列宽
            searchFilterPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 50)); // 搜索标签
            searchFilterPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70)); // 搜索框
            searchFilterPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80)); // 筛选标签
            searchFilterPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30)); // 筛选下拉框

            // 添加搜索标签和搜索框
            var searchLabel = new Label { Text = "搜索:", Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft };
            var searchTextBox = new TextBox { Dock = DockStyle.Fill };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            // 添加筛选标签和筛选下拉框
            var filterLabel = new Label { Text = "筛选:", Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft };
            var filterComboBox = new ComboBox { Dock = DockStyle.Fill, DropDownStyle = ComboBoxStyle.DropDownList };
            filterComboBox.Items.AddRange(new object[] {
                "全部规则",
                "仅已启用规则",
                "仅禁用规则",
                "仅正则表达式规则",
                "仅区分大小写规则",
                "仅包含扩展名规则"
            });
            filterComboBox.SelectedIndex = 0;
            filterComboBox.SelectedIndexChanged += FilterComboBox_SelectedIndexChanged;

            // 添加到搜索筛选面板
            searchFilterPanel.Controls.Add(searchLabel, 0, 0);
            searchFilterPanel.Controls.Add(searchTextBox, 1, 0);
            searchFilterPanel.Controls.Add(filterLabel, 2, 0);
            searchFilterPanel.Controls.Add(filterComboBox, 3, 0);

            // 批量操作面板
            batchOperationPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Bottom,
                ColumnCount = 3,
                RowCount = 1,
                Height = 35,
                Margin = new Padding(0, 5, 0, 0)
            };

            // 设置列宽均匀
            for (int i = 0; i < 3; i++)
            {
                batchOperationPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            }

            // 批量操作按钮
            var batchEnableButton = new Button
            {
                Text = "批量启用",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            batchEnableButton.Click += BatchEnableButton_Click;

            var batchDisableButton = new Button
            {
                Text = "批量禁用",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            batchDisableButton.Click += BatchDisableButton_Click;

            var batchDeleteButton = new Button
            {
                Text = "批量删除",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            batchDeleteButton.Click += BatchDeleteButton_Click;

            // 添加按钮到批量操作面板
            batchOperationPanel.Controls.Add(batchEnableButton, 0, 0);
            batchOperationPanel.Controls.Add(batchDisableButton, 1, 0);
            batchOperationPanel.Controls.Add(batchDeleteButton, 2, 0);

            rulesListView = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = true, // 允许多选
                CheckBoxes = true,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                Font = new Font(Font.FontFamily, 9F, FontStyle.Regular) // 设置标准字体
            };

            // 添加 Paint 事件处理器来绘制整体边框
            rulesListView.Paint += (s, e) =>
            {
                if (rulesListView.Items.Count == 0)
                {
                    // 如果没有项目，绘制整个区域的边框
                    using (var pen = new Pen(Color.Gray))
                    {
                        var rect = rulesListView.ClientRectangle;
                        rect.Width -= 1;
                        rect.Height -= 1;
                        e.Graphics.DrawRectangle(pen, rect);
                    }
                }
                else
                {
                    // 计算列表项总高度
                    int totalItemsHeight = rulesListView.Items.Count * rulesListView.GetItemRect(0).Height;

                    // 如果有空白区域，绘制边框延伸到底部
                    if (totalItemsHeight < rulesListView.ClientRectangle.Height)
                    {
                        using (var pen = new Pen(Color.Gray))
                        {
                            // 绘制左边框和右边框延伸部分
                            e.Graphics.DrawLine(pen,
                                0, totalItemsHeight,
                                0, rulesListView.ClientRectangle.Height - 1);
                            e.Graphics.DrawLine(pen,
                                rulesListView.ClientRectangle.Width - 1, totalItemsHeight,
                                rulesListView.ClientRectangle.Width - 1, rulesListView.ClientRectangle.Height - 1);

                            // 绘制底部边框
                            e.Graphics.DrawLine(pen,
                                0, rulesListView.ClientRectangle.Height - 1,
                                rulesListView.ClientRectangle.Width - 1, rulesListView.ClientRectangle.Height - 1);
                        }
                    }
                }
            };

            // 添加列并设置对齐方式
            rulesListView.Columns.AddRange(new[]
            {
                new ColumnHeader { Text = "启用", Width = 50, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "原值", Width = 218, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "新值", Width = 218, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "正则表达式", Width = 80, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "区分大小写", Width = 80, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "包含扩展名", Width = 80, TextAlign = HorizontalAlignment.Center }
            });

            // 修改表头绘制方法
            rulesListView.OwnerDraw = true;

            // 表头绘制事件
            rulesListView.DrawColumnHeader += (s, e) =>
            {
                // 使用系统默认绘制背景
                e.DrawBackground();

                // 定义标题文本
                string headerText = rulesListView.Columns[e.ColumnIndex].Text;

                // 设置文本格式 - 使用中心对齐
                using (StringFormat format = new StringFormat())
                {
                    format.Alignment = StringAlignment.Center;
                    format.LineAlignment = StringAlignment.Center;

                    // 使用黑色和粗体绘制文本，确保可见性
                    using (Font headerFont = new Font(rulesListView.Font.FontFamily, rulesListView.Font.Size, FontStyle.Bold))
                    {
                        // 绘制表头文本
                        using (SolidBrush foreBrush = new SolidBrush(Color.Black))
                        {
                            e.Graphics.DrawString(headerText, headerFont, foreBrush, e.Bounds, format);
                        }
                    }
                }

                // 绘制列分隔线和边框
                using (Pen borderPen = new Pen(Color.LightGray))
                {
                    // 绘制底部水平线
                    e.Graphics.DrawLine(borderPen, e.Bounds.Left, e.Bounds.Bottom - 1, e.Bounds.Right, e.Bounds.Bottom - 1);

                    // 绘制右侧垂直线
                    e.Graphics.DrawLine(borderPen, e.Bounds.Right - 1, e.Bounds.Top, e.Bounds.Right - 1, e.Bounds.Bottom);
                }

                // 不使用默认绘制
                e.DrawDefault = false;
            };

            rulesListView.DrawSubItem += (s, e) =>
            {
                // 布尔值列（第0、3、4、5列）居中对齐
                bool isCenterAligned = e.ColumnIndex == 0 || e.ColumnIndex >= 3;

                StringFormat sf = new StringFormat
                {
                    Alignment = isCenterAligned ? StringAlignment.Center : StringAlignment.Near,
                    LineAlignment = StringAlignment.Center
                };

                // 保存当前的背景色
                Color backColor = e.Item.Selected ? SystemColors.Highlight : e.Item.BackColor;
                Color foreColor = e.Item.Selected ? SystemColors.HighlightText : e.Item.ForeColor;

                // 绘制背景
                using (var brush = new SolidBrush(backColor))
                {
                    e.Graphics.FillRectangle(brush, e.Bounds);
                }

                // 绘制文本（添加一些内边距）
                Rectangle textBounds = e.Bounds;
                textBounds.Inflate(-4, -2); // 文本区域缩进4像素
                using (var brush = new SolidBrush(foreColor))
                {
                    e.Graphics.DrawString(e.SubItem?.Text ?? string.Empty,
                        e.SubItem?.Font ?? rulesListView!.Font,
                        brush,
                        textBounds,
                        sf);
                }

                // 不绘制单元格边框

                e.DrawDefault = false;
            };

            // 添加启用状态变更事件处理
            rulesListView.ItemChecked += (s, e) =>
            {
                if (e.Item.Tag is FileNameReplaceRule rule)
                {
                    rule.IsEnabled = e.Item.Checked;
                }
            };

            // 组合规则组面板
            var rulesGroupLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(0)
            };

            rulesGroupLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35)); // 搜索筛选面板
            rulesGroupLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // 规则列表
            rulesGroupLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35)); // 批量操作面板

            rulesGroupLayout.Controls.Add(searchFilterPanel, 0, 0);
            rulesGroupLayout.Controls.Add(rulesListView, 0, 1);
            rulesGroupLayout.Controls.Add(batchOperationPanel, 0, 2);
            rulesGroup.Controls.Add(rulesGroupLayout);

            // 添加选择变更事件处理
            rulesListView.SelectedIndexChanged += (s, e) => {
                UpdateButtonStates();
                UpdateBatchButtonsState();
                RulesListView_SelectedIndexChanged(s, e);
            };

            // 规则编辑区域
            var editGroup = new GroupBox { Text = "编辑规则", Dock = DockStyle.Fill, Padding = new Padding(10) };
            var editLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 5,
                AutoSize = false,
                Padding = new Padding(5)
            };

            // 设置列宽
            editLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30));
            editLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70));

            // 设置行高
            for (int i = 0; i < 4; i++) // 前4行使用固定高度
            {
                editLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));
            }
            editLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 60)); // 最后一行给更多空间显示说明文字

            // 添加启用复选框
            isEnabledCheck = new CheckBox { Text = "启用规则", AutoSize = true, Checked = true, Margin = new Padding(0, 5, 0, 5) };
            editLayout.Controls.Add(isEnabledCheck, 0, 0);
            editLayout.SetColumnSpan(isEnabledCheck, 2);

            var oldValueLabel = new Label { Text = "原值:", Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft, Margin = new Padding(0, 5, 0, 5) };
            oldValueTextBox = new TextBox { Dock = DockStyle.Fill, Margin = new Padding(0, 5, 0, 5) };
            editLayout.Controls.Add(oldValueLabel, 0, 1);
            editLayout.Controls.Add(oldValueTextBox, 1, 1);

            var newValueLabel = new Label { Text = "新值:", Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft, Margin = new Padding(0, 5, 0, 5) };
            newValueTextBox = new TextBox { Dock = DockStyle.Fill, Margin = new Padding(0, 5, 0, 5) };
            editLayout.Controls.Add(newValueLabel, 0, 2);
            editLayout.Controls.Add(newValueTextBox, 1, 2);

            // 创建一个新的FlowLayoutPanel来容纳三个复选框
            var checkBoxPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = false,
                AutoSize = true,
                Margin = new Padding(0, 5, 0, 5)
            };

            isRegexCheck = new CheckBox { Text = "使用正则表达式", AutoSize = true, Margin = new Padding(0, 0, 10, 0) };
            isCaseSensitiveCheck = new CheckBox { Text = "区分大小写", AutoSize = true, Margin = new Padding(0, 0, 10, 0) };
            includeExtensionCheck = new CheckBox { Text = "包含扩展名", AutoSize = true, Margin = new Padding(0, 0, 0, 0) };

            checkBoxPanel.Controls.AddRange(new Control[] { isRegexCheck, isCaseSensitiveCheck, includeExtensionCheck });
            editLayout.Controls.Add(checkBoxPanel, 0, 3);
            editLayout.SetColumnSpan(checkBoxPanel, 2);

            // 添加导入格式说明
            var importHelpLabel = new Label
            {
                Text = "导入格式说明：CSV文件，每行一条规则\n格式：启用,原值,新值,是否正则表达式,是否区分大小写,是否包含扩展名\n例如: true,old,new,false,true,false",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(Font.FontFamily, 8),
                ForeColor = Color.Gray,
                Margin = new Padding(0, 10, 0, 5)
            };
            editLayout.Controls.Add(importHelpLabel, 0, 4);
            editLayout.SetColumnSpan(importHelpLabel, 2);

            // 按钮区域 - 使用两行布局
            var buttonPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = false,
                Margin = new Padding(0, 10, 0, 0)
            };

            // 设置行高
            buttonPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 50));
            buttonPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 50));

            // 第一行按钮面板（移动相关按钮）
            var moveButtonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 5,
                RowCount = 1,
                AutoSize = false,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 设置列宽均分
            for (int i = 0; i < 5; i++)
            {
                moveButtonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20));
            }

            // 第二行按钮面板（功能按钮）
            var functionButtonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 6,
                RowCount = 1,
                AutoSize = false,
                Margin = new Padding(0, 5, 0, 0)
            };

            // 设置列宽均分
            for (int i = 0; i < 6; i++)
            {
                functionButtonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f / 6));
            }

            // 设置按钮样式
            var buttonStyle = new Padding(5);
            var buttonMargin = new Padding(3);

            // 第一行按钮：移动相关
            moveToTopButton = CreateButton("移至顶部", buttonStyle, buttonMargin);
            moveUpButton = CreateButton("上移", buttonStyle, buttonMargin);
            moveDownButton = CreateButton("下移", buttonStyle, buttonMargin);
            moveToBottomButton = CreateButton("移至底部", buttonStyle, buttonMargin);
            editButton = CreateButton("更新", buttonStyle, buttonMargin);

            // 第二行按钮：功能相关
            var addButton = CreateButton("添加", buttonStyle, buttonMargin);
            var importButton = CreateButton("导入", buttonStyle, buttonMargin);
            var exportButton = CreateButton("导出", buttonStyle, buttonMargin);
            var removeButton = CreateButton("删除", buttonStyle, buttonMargin);
            var okButton = CreateButton("确定", buttonStyle, buttonMargin);
            var cancelButton = CreateButton("取消", buttonStyle, buttonMargin);

            // 添加按钮事件
            moveToTopButton.Click += MoveToTopButton_Click;
            moveUpButton.Click += MoveUpButton_Click;
            moveDownButton.Click += MoveDownButton_Click;
            moveToBottomButton.Click += MoveToBottomButton_Click;
            editButton.Click += EditButton_Click;
            addButton.Click += AddButton_Click;
            importButton.Click += ImportButton_Click;
            exportButton.Click += ExportButton_Click;
            removeButton.Click += (s, e) =>
            {
                if (rulesListView.SelectedItems.Count > 0)
                {
                    var item = rulesListView.SelectedItems[0];
                    if (item.Tag is FileNameReplaceRule rule)
                    {
                        Rules.Remove(rule);
                        RefreshRulesList();
                        ClearEditArea();
                    }
                }
            };
            okButton.Click += (s, e) => DialogResult = DialogResult.OK;
            cancelButton.Click += (s, e) => DialogResult = DialogResult.Cancel;

            // 添加第一行按钮到移动按钮面板
            moveButtonsPanel.Controls.Add(moveToTopButton, 0, 0);
            moveButtonsPanel.Controls.Add(moveUpButton, 1, 0);
            moveButtonsPanel.Controls.Add(moveDownButton, 2, 0);
            moveButtonsPanel.Controls.Add(moveToBottomButton, 3, 0);
            moveButtonsPanel.Controls.Add(editButton, 4, 0);

            // 添加第二行按钮到功能按钮面板
            functionButtonsPanel.Controls.Add(addButton, 0, 0);
            functionButtonsPanel.Controls.Add(importButton, 1, 0);
            functionButtonsPanel.Controls.Add(exportButton, 2, 0);
            functionButtonsPanel.Controls.Add(removeButton, 3, 0);
            functionButtonsPanel.Controls.Add(okButton, 4, 0);
            functionButtonsPanel.Controls.Add(cancelButton, 5, 0);

            // 将两个按钮面板添加到主按钮面板
            buttonPanel.Controls.Add(moveButtonsPanel, 0, 0);
            buttonPanel.Controls.Add(functionButtonsPanel, 0, 1);

            editGroup.Controls.Add(editLayout);

            mainLayout.Controls.Add(rulesGroup, 0, 0);
            mainLayout.Controls.Add(editGroup, 0, 1);
            mainLayout.Controls.Add(buttonPanel, 0, 2);

            Controls.Add(mainLayout);

            // 初始化按钮状态
            UpdateButtonStates();
            RefreshRulesList();
        }

        private Button CreateButton(string text, Padding padding, Padding margin)
        {
            return new Button
            {
                Text = text,
                AutoSize = false,
                Padding = padding,
                Margin = margin,
                Dock = DockStyle.Fill,
                Enabled = true,
                Height = 30  // 设置固定高度
            };
        }

        private void UpdateButtonStates()
        {
            var hasSelection = rulesListView.SelectedItems.Count > 0;
            var selectedIndex = hasSelection ? rulesListView.SelectedIndices[0] : -1;

            editButton.Enabled = hasSelection;
            moveUpButton.Enabled = hasSelection && selectedIndex > 0;
            moveDownButton.Enabled = hasSelection && selectedIndex < rulesListView.Items.Count - 1;
            moveToTopButton.Enabled = hasSelection && selectedIndex > 0;
            moveToBottomButton.Enabled = hasSelection && selectedIndex < rulesListView.Items.Count - 1;
        }

        private void RulesListView_SelectedIndexChanged(object? sender, EventArgs e)
        {
            var hasSelection = rulesListView.SelectedItems.Count > 0;
            var selectedIndex = hasSelection ? rulesListView.SelectedIndices[0] : -1;

            editButton.Enabled = hasSelection;
            moveUpButton.Enabled = hasSelection && selectedIndex > 0;
            moveDownButton.Enabled = hasSelection && selectedIndex < Rules.Count - 1;
            moveToTopButton.Enabled = hasSelection && selectedIndex > 0;
            moveToBottomButton.Enabled = hasSelection && selectedIndex < Rules.Count - 1;

            if (hasSelection)
            {
                var item = rulesListView.SelectedItems[0];
                if (item.Tag is FileNameReplaceRule rule)
                {
                    LoadRuleToEditArea(rule);
                }
            }
            else
            {
                ClearEditArea();
            }
        }

        private void LoadRuleToEditArea(FileNameReplaceRule rule)
        {
            isEnabledCheck.Checked = rule.IsEnabled;
            oldValueTextBox.Text = rule.OldValue;
            newValueTextBox.Text = rule.NewValue;
            isRegexCheck.Checked = rule.IsRegex;
            isCaseSensitiveCheck.Checked = rule.IsCaseSensitive;
            includeExtensionCheck.Checked = rule.IncludeExtension;
        }

        private void ClearEditArea()
        {
            isEnabledCheck.Checked = true;
            oldValueTextBox.Clear();
            newValueTextBox.Clear();
            isRegexCheck.Checked = false;
            isCaseSensitiveCheck.Checked = false;
            includeExtensionCheck.Checked = false;
        }

        private void AddButton_Click(object? sender, EventArgs e)
        {
            var rule = new FileNameReplaceRule
            {
                IsEnabled = isEnabledCheck.Checked,
                OldValue = oldValueTextBox.Text,
                NewValue = newValueTextBox.Text,
                IsRegex = isRegexCheck.Checked,
                IsCaseSensitive = isCaseSensitiveCheck.Checked,
                IncludeExtension = includeExtensionCheck.Checked
            };

            // 使用新添加的验证方法验证规则
            if (!rule.Validate(out string errorMessage))
            {
                MessageBox.Show(errorMessage, "规则验证错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            Rules.Add(rule);
            RefreshRulesList();
            ClearEditArea();
        }

        private void EditButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView.SelectedItems.Count == 0)
            {
                return;
            }

            var selectedIndex = rulesListView.SelectedIndices[0];
            var item = rulesListView.SelectedItems[0];
            if (item.Tag is FileNameReplaceRule rule)
            {
                // 创建一个临时规则进行验证，避免直接修改原规则
                var tempRule = rule.Clone();
                tempRule.IsEnabled = isEnabledCheck.Checked;
                tempRule.OldValue = oldValueTextBox.Text;
                tempRule.NewValue = newValueTextBox.Text;
                tempRule.IsRegex = isRegexCheck.Checked;
                tempRule.IsCaseSensitive = isCaseSensitiveCheck.Checked;
                tempRule.IncludeExtension = includeExtensionCheck.Checked;

                // 验证规则
                if (!tempRule.Validate(out string errorMessage))
                {
                    MessageBox.Show(errorMessage, "规则验证错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 验证通过后，更新原规则
                rule.IsEnabled = tempRule.IsEnabled;
                rule.OldValue = tempRule.OldValue;
                rule.NewValue = tempRule.NewValue;
                rule.IsRegex = tempRule.IsRegex;
                rule.IsCaseSensitive = tempRule.IsCaseSensitive;
                rule.IncludeExtension = tempRule.IncludeExtension;

                RefreshRulesList();
                if (selectedIndex >= 0 && selectedIndex < rulesListView.Items.Count)
                {
                    rulesListView.Items[selectedIndex].Selected = true;
                }
            }
        }

        private void MoveToTopButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView.SelectedItems.Count == 0 || rulesListView.SelectedIndices[0] == 0)
            {
                return;
            }

            var selectedIndex = rulesListView.SelectedIndices[0];
            var rule = Rules[selectedIndex];
            Rules.RemoveAt(selectedIndex);
            Rules.Insert(0, rule);
            RefreshRulesList();
            rulesListView.Items[0].Selected = true;
        }

        private void MoveUpButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView.SelectedItems.Count == 0 || rulesListView.SelectedIndices[0] == 0)
            {
                return;
            }

            var selectedIndex = rulesListView.SelectedIndices[0];
            var rule = Rules[selectedIndex];
            Rules.RemoveAt(selectedIndex);
            Rules.Insert(selectedIndex - 1, rule);
            RefreshRulesList();
            rulesListView.Items[selectedIndex - 1].Selected = true;
        }

        private void MoveDownButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView.SelectedItems.Count == 0 || rulesListView.SelectedIndices[0] == Rules.Count - 1)
            {
                return;
            }

            var selectedIndex = rulesListView.SelectedIndices[0];
            var rule = Rules[selectedIndex];
            Rules.RemoveAt(selectedIndex);
            Rules.Insert(selectedIndex + 1, rule);
            RefreshRulesList();
            rulesListView.Items[selectedIndex + 1].Selected = true;
        }

        private void MoveToBottomButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView.SelectedItems.Count == 0 || rulesListView.SelectedIndices[0] == Rules.Count - 1)
            {
                return;
            }

            var selectedIndex = rulesListView.SelectedIndices[0];
            var rule = Rules[selectedIndex];
            Rules.RemoveAt(selectedIndex);
            Rules.Add(rule);
            RefreshRulesList();
            rulesListView.Items[Rules.Count - 1].Selected = true;
        }

        private void ImportButton_Click(object? sender, EventArgs e)
        {
            try
            {
                using (var openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "文本文件 (*.txt;*.csv)|*.txt;*.csv|所有文件 (*.*)|*.*";
                    openFileDialog.Title = "选择要导入的规则文件";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        var importedRules = ImportRules(openFileDialog.FileName);
                        if (importedRules.Count > 0)
                        {
                            // 添加导入的规则到现有规则列表
                            Rules.AddRange(importedRules);
                            RefreshRulesList();
                            MessageBox.Show($"成功导入 {importedRules.Count} 条规则", "导入成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("没有找到有效的规则", "导入提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (Rules.Count == 0)
                {
                    MessageBox.Show("当前没有规则可导出", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using (var saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Filter = "CSV文件 (*.csv)|*.csv|文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*";
                    saveFileDialog.Title = "导出规则到文件";
                    saveFileDialog.DefaultExt = "csv";
                    saveFileDialog.FileName = "文件名替换规则.csv";

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        ExportRules(saveFileDialog.FileName);
                        MessageBox.Show($"成功导出 {Rules.Count} 条规则到文件", "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportRules(string filePath)
        {
            var lines = new List<string>
            {
                "启用,原值,新值,是否正则表达式,是否区分大小写,是否包含扩展名" // 添加标题行
            };

            lines.AddRange(Rules.Select(r => string.Join(",",
                r.IsEnabled,
                r.OldValue,
                r.NewValue,
                r.IsRegex,
                r.IsCaseSensitive,
                r.IncludeExtension)));

            File.WriteAllLines(filePath, lines, Encoding.UTF8);
        }

        private List<FileNameReplaceRule> ImportRules(string filePath)
        {
            var rules = new List<FileNameReplaceRule>();
            var invalidRules = new List<string>();
            int lineNumber = 0;

            try
            {
                var lines = File.ReadAllLines(filePath, Encoding.UTF8);
                bool isFirstLine = true;

                foreach (var line in lines)
                {
                    lineNumber++;

                    if (isFirstLine) // 跳过标题行
                    {
                        isFirstLine = false;
                        continue;
                    }

                    if (string.IsNullOrWhiteSpace(line))
                        continue;

                    try
                    {
                        var parts = line.Split(',');
                        if (parts.Length >= 6 && bool.TryParse(parts[0], out bool isEnabled))
                        {
                            var rule = new FileNameReplaceRule
                            {
                                IsEnabled = isEnabled,
                                OldValue = parts[1],
                                NewValue = parts[2],
                                IsRegex = bool.TryParse(parts[3], out bool isRegex) && isRegex,
                                IsCaseSensitive = bool.TryParse(parts[4], out bool isCaseSensitive) && isCaseSensitive,
                                IncludeExtension = bool.TryParse(parts[5], out bool includeExtension) && includeExtension
                            };

                            // 验证规则
                            if (rule.Validate(out string errorMessage))
                            {
                                rules.Add(rule);
                            }
                            else
                            {
                                invalidRules.Add($"第 {lineNumber} 行: {errorMessage}");
                            }
                        }
                        else
                        {
                            invalidRules.Add($"第 {lineNumber} 行: 格式不正确，无法解析");
                        }
                    }
                    catch (Exception ex)
                    {
                        invalidRules.Add($"第 {lineNumber} 行: 处理错误 - {ex.Message}");
                    }
                }

                // 如果有无效规则，显示警告
                if (invalidRules.Count > 0)
                {
                    string message = $"导入过程中发现 {invalidRules.Count} 条无效规则:\n\n" +
                                    string.Join("\n", invalidRules.Take(10));

                    if (invalidRules.Count > 10)
                    {
                        message += $"\n\n...以及其他 {invalidRules.Count - 10} 条错误";
                    }

                    MessageBox.Show(message, "导入警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入文件时出错: {ex.Message}", "导入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return rules;
        }

        // 刷新列表
        private void RefreshRulesList()
        {
            // 使用共用的FilterRules方法来显示规则
            currentSearchText = "";
            currentFilterIndex = 0;
            FilterRules();
        }

        // 搜索筛选事件处理
        private string currentSearchText = "";
        private int currentFilterIndex = 0;

        private void SearchTextBox_TextChanged(object? sender, EventArgs e)
        {
            if (sender is TextBox textBox)
            {
                currentSearchText = textBox.Text.Trim();
                FilterRules();
            }
        }

        private void FilterComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (sender is ComboBox comboBox)
            {
                currentFilterIndex = comboBox.SelectedIndex;
                FilterRules();
            }
        }

        // 批量操作事件处理
        private void BatchEnableButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedItems.Count == 0) return;

            foreach (ListViewItem item in rulesListView.SelectedItems)
            {
                if (item.Tag is FileNameReplaceRule rule)
                {
                    rule.IsEnabled = true;
                    item.Checked = true;
                }
            }

            // 更新UI
            rulesListView.Refresh();
        }

        private void BatchDisableButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedItems.Count == 0) return;

            foreach (ListViewItem item in rulesListView.SelectedItems)
            {
                if (item.Tag is FileNameReplaceRule rule)
                {
                    rule.IsEnabled = false;
                    item.Checked = false;
                }
            }

            // 更新UI
            rulesListView.Refresh();
        }

        private void BatchDeleteButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedItems.Count == 0) return;

            if (MessageBox.Show($"确定要删除选中的 {rulesListView.SelectedItems.Count} 条规则吗？",
                "确认批量删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
            {
                return;
            }

            // 收集要删除的规则
            List<FileNameReplaceRule> rulesToDelete = new List<FileNameReplaceRule>();
            foreach (ListViewItem item in rulesListView.SelectedItems)
            {
                if (item.Tag is FileNameReplaceRule rule)
                {
                    rulesToDelete.Add(rule);
                }
            }

            // 删除规则
            foreach (var rule in rulesToDelete)
            {
                Rules.Remove(rule);
            }

            // 重新加载列表
            FilterRules();
        }

        private void UpdateBatchButtonsState()
        {
            bool hasSelection = rulesListView.SelectedItems.Count > 0;

            // 使用保存的批量操作面板引用
            foreach (Control control in batchOperationPanel.Controls)
            {
                if (control is Button button)
                {
                    button.Enabled = hasSelection;
                }
            }
        }

        private void FilterRules()
        {
            if (rulesListView == null) return;

            // 保存当前选中项以便过滤后恢复
            List<FileNameReplaceRule> selectedRules = new List<FileNameReplaceRule>();
            foreach (ListViewItem item in rulesListView.SelectedItems)
            {
                if (item.Tag is FileNameReplaceRule rule)
                {
                    selectedRules.Add(rule);
                }
            }

            // 重新加载并应用过滤
            rulesListView.BeginUpdate();
            rulesListView.Items.Clear();

            foreach (var rule in Rules)
            {
                // 应用搜索过滤
                if (!string.IsNullOrEmpty(currentSearchText))
                {
                    bool matchFound =
                        rule.OldValue.Contains(currentSearchText, StringComparison.OrdinalIgnoreCase) ||
                        rule.NewValue.Contains(currentSearchText, StringComparison.OrdinalIgnoreCase);

                    if (!matchFound)
                        continue;
                }

                // 应用条件筛选
                switch (currentFilterIndex)
                {
                    case 0: // 全部规则
                        break;
                    case 1: // 仅已启用规则
                        if (!rule.IsEnabled) continue;
                        break;
                    case 2: // 仅禁用规则
                        if (rule.IsEnabled) continue;
                        break;
                    case 3: // 仅正则表达式规则
                        if (!rule.IsRegex) continue;
                        break;
                    case 4: // 仅区分大小写规则
                        if (!rule.IsCaseSensitive) continue;
                        break;
                    case 5: // 仅包含扩展名规则
                        if (!rule.IncludeExtension) continue;
                        break;
                }

                var item = new ListViewItem(new[]
                {
                    rule.IsEnabled ? "是" : "否",
                    rule.OldValue,
                    rule.NewValue,
                    rule.IsRegex ? "是" : "否",
                    rule.IsCaseSensitive ? "是" : "否",
                    rule.IncludeExtension ? "是" : "否"
                })
                {
                    Tag = rule,
                    Checked = rule.IsEnabled
                };
                rulesListView.Items.Add(item);

                // 恢复之前选中的项
                if (selectedRules.Contains(rule))
                {
                    item.Selected = true;
                }
            }

            rulesListView.EndUpdate();
            UpdateButtonStates();
            UpdateBatchButtonsState();
        }
    }
}