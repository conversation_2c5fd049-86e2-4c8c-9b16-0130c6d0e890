/*
 * ========================================
 * 文件名: SettingsManager.cs
 * 功能描述: 配置管理器
 * ========================================
 *
 * 主要功能:
 * 1. 配置文件的加载和保存
 * 2. 分模块配置管理
 * 3. 配置验证和默认值处理
 * 4. 配置文件的导入导出
 * 5. 旧版配置的迁移和兼容
 *
 * 核心特性:
 * - 支持JSON格式的配置文件
 * - 模块化配置管理（每个功能独立配置文件）
 * - 自动创建默认配置
 * - 配置文件版本兼容性处理
 * - 完整的错误处理和恢复机制
 *
 * 配置文件结构:
 * - Config/GlobalSettings.json: 全局应用设置
 * - Config/PageSetup.json: 页面设置配置
 * - Config/DeleteContent.json: 内容删除配置
 * - Config/ContentReplace.json: 内容替换配置
 * - Config/GlobalParagraphFormat.json: 全局段落格式
 * - Config/ParagraphMatchRules.json: 段落匹配规则
 * - Config/HeaderFooter.json: 页眉页脚配置
 * - Config/DocumentProperties.json: 文档属性配置
 * - Config/FileNameReplace.json: 文件名替换配置
 * - Config/PdfSettings.json: PDF转换设置
 * - Config/ScheduleSettings.json: 定时任务设置
 * - Config/TxtSettings.json: TXT处理设置
 * - Config/DocumentFormats.json: 文档格式设置
 *
 * 管理功能:
 * - LoadSettings(): 加载所有配置
 * - SaveSettings(): 保存配置到文件
 * - CreateDefaultSettings(): 创建默认配置
 * - ValidateSettings(): 配置验证
 * - MigrateOldSettings(): 旧版配置迁移
 *
 * 注意事项:
 * - 所有配置操作都包含异常处理
 * - 支持配置文件缺失时的自动创建
 * - 实现了配置的原子性保存
 * - 包含配置备份和恢复机制
 */

using System;
using System.IO;
using System.Text.Json;
using System.Collections.Generic;
using AsposeWordFormatter.Models;
using Newtonsoft.Json;

namespace AsposeWordFormatter
{
    public static class SettingsManager
    {
        // 配置文件目录
        private static readonly string ConfigDir = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory,
            "Config");

        // 各配置文件路径
        private static readonly string GlobalSettingsPath = Path.Combine(ConfigDir, "GlobalSettings.json");         // 1.全局设置
        private static readonly string PageSetupPath = Path.Combine(ConfigDir, "PageSetup.json");                   // 2.页面设置
        private static readonly string DeleteContentPath = Path.Combine(ConfigDir, "DeleteContent.json");           // 3.内容删除设置
        private static readonly string ContentReplacePath = Path.Combine(ConfigDir, "ContentReplace.json");         // 4.内容替换设置
        private static readonly string GlobalParagraphFormatPath = Path.Combine(ConfigDir, "GlobalParagraphFormat.json"); // 5.全局段落格式
        private static readonly string ParagraphMatchRulesPath = Path.Combine(ConfigDir, "ParagraphMatchRules.json"); // 6.段落匹配规则
        private static readonly string HeaderFooterPath = Path.Combine(ConfigDir, "HeaderFooter.json");             // 7.页眉页脚设置
        private static readonly string DocumentPropertiesPath = Path.Combine(ConfigDir, "DocumentProperties.json"); // 8.文档属性
        private static readonly string FileNameReplacePath = Path.Combine(ConfigDir, "FileNameReplace.json");       // 9.文件名替换规则
        private static readonly string PdfSettingsPath = Path.Combine(ConfigDir, "PdfSettings.json");               // 10.PDF设置
        private static readonly string TxtSettingsPath = Path.Combine(ConfigDir, "TxtSettings.json");               // 11.TXT文件处理设置
        private static readonly string ScheduleSettingsPath = Path.Combine(ConfigDir, "ScheduleSettings.json");     // 12.调度设置
        private static readonly string DocumentFormatsPath = Path.Combine(ConfigDir, "DocumentFormats.json");       // 13.文档格式设置
        private static readonly string LogSettingsPath = Path.Combine(ConfigDir, "LogSettings.json");                 // 14.日志设置
        private static readonly string FileNameIllegalWordsPath = Path.Combine(ConfigDir, "FileNameIllegalWords.json"); // 15.文件名非法词
        private static readonly string ContentIllegalWordsPath = Path.Combine(ConfigDir, "ContentIllegalWords.json");   // 16.内容非法词

        // 兼容旧版本的设置文件路径
        private static readonly string LegacySettingsPath = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory,
            "settings.json");

        // JSON序列化选项
        private static readonly JsonSerializerOptions JsonOptions = new JsonSerializerOptions {
            WriteIndented = true,
            AllowTrailingCommas = true,
            ReadCommentHandling = JsonCommentHandling.Skip,
            PropertyNameCaseInsensitive = true,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };

        private static Dictionary<string, bool> _documentFormats = new Dictionary<string, bool>();

        // 加载设置
        public static Settings LoadSettings()
        {
            Logger.Instance.Log("开始加载应用程序配置...");

            // 检查配置文件是否缺失
            bool missingFiles = !File.Exists(GlobalSettingsPath) ||
                               !File.Exists(PageSetupPath) ||
                               !File.Exists(DeleteContentPath) ||
                               !File.Exists(ContentReplacePath) ||
                               !File.Exists(GlobalParagraphFormatPath) ||
                               !File.Exists(ParagraphMatchRulesPath) ||
                               !File.Exists(HeaderFooterPath) ||
                               !File.Exists(DocumentPropertiesPath) ||
                               !File.Exists(FileNameReplacePath) ||
                               !File.Exists(PdfSettingsPath) ||
                               !File.Exists(TxtSettingsPath) ||
                               !File.Exists(ScheduleSettingsPath) ||
                               !File.Exists(DocumentFormatsPath) ||
                               !File.Exists(LogSettingsPath) ||
                               !File.Exists(FileNameIllegalWordsPath) ||
                               !File.Exists(ContentIllegalWordsPath);

            if (missingFiles)
            {
                Logger.Instance.Log("检测到配置文件缺失，将创建默认配置文件");
            }

            // 创建默认设置对象作为基础
            var settings = CreateDefaultSettings();

            // 记录默认TxtSettings的值
            Logger.Instance.Log($"创建默认设置: TxtSettings.ConvertToWordBeforeProcessing = {settings.TxtSettings?.ConvertToWordBeforeProcessing}");
            Console.WriteLine($"创建默认设置: TxtSettings.ConvertToWordBeforeProcessing = {settings.TxtSettings?.ConvertToWordBeforeProcessing}");

            // 从现有配置文件加载设置
            if (File.Exists(GlobalSettingsPath))
            {
                var globalSettings = LoadJsonFile<Settings>(GlobalSettingsPath);
                if (globalSettings != null)
                {
                    // 复制全局设置属性（基本属性和功能开关）
                    settings.SourceDirectory = globalSettings.SourceDirectory;
                    settings.OutputDirectory = globalSettings.OutputDirectory;
                    settings.IncludeSubdirectories = globalSettings.IncludeSubdirectories;
                    settings.KeepDirectoryStructure = globalSettings.KeepDirectoryStructure;
                    settings.ConflictHandling = globalSettings.ConflictHandling;
                    settings.ProcessOriginalFiles = globalSettings.ProcessOriginalFiles;
                    settings.MoveFiles = globalSettings.MoveFiles;
                    settings.MaxThreads = globalSettings.MaxThreads;
                    settings.MaxRetryCount = globalSettings.MaxRetryCount;
                    settings.BatchSize = globalSettings.BatchSize;
                    settings.BackupOriginalFiles = globalSettings.BackupOriginalFiles;
                    settings.BackupDirectory = globalSettings.BackupDirectory;
                    settings.RemoveEmptyParagraphs = globalSettings.RemoveEmptyParagraphs;
                    settings.NormalizeWhitespace = globalSettings.NormalizeWhitespace;
                    settings.RemoveMultipleSpaces = globalSettings.RemoveMultipleSpaces;
                    settings.RemoveHiddenContent = globalSettings.RemoveHiddenContent;
                    settings.RemoveComments = globalSettings.RemoveComments;
                    settings.RemoveHeadersFooters = globalSettings.RemoveHeadersFooters;

                    // 复制功能开关
                    settings.EnablePageSetup = globalSettings.EnablePageSetup;
                    settings.EnableDeleteContent = globalSettings.EnableDeleteContent;
                    settings.EnableContentReplace = globalSettings.EnableContentReplace;
                    settings.EnableGlobalParagraphFormat = globalSettings.EnableGlobalParagraphFormat;
                    settings.EnableParagraphMatch = globalSettings.EnableParagraphMatch;
                    settings.EnableHeaderFooter = globalSettings.EnableHeaderFooter;
                    settings.EnableDocumentProperties = globalSettings.EnableDocumentProperties;
                    settings.EnableFileNameReplace = globalSettings.EnableFileNameReplace;
                    settings.EnableWordToPdf = globalSettings.EnableWordToPdf;

                    // 复制段落格式相关设置
                    settings.ApplyDefaultParagraphFormat = globalSettings.ApplyDefaultParagraphFormat;
                    settings.DefaultLineSpacing = globalSettings.DefaultLineSpacing;
                    settings.DefaultFirstLineIndent = globalSettings.DefaultFirstLineIndent;
                    settings.DefaultLeftIndent = globalSettings.DefaultLeftIndent;
                    settings.DefaultRightIndent = globalSettings.DefaultRightIndent;
                    settings.DefaultSpaceBefore = globalSettings.DefaultSpaceBefore;
                    settings.DefaultSpaceAfter = globalSettings.DefaultSpaceAfter;

                    // 复制字体格式相关设置
                    settings.ApplyDefaultFontFormat = globalSettings.ApplyDefaultFontFormat;
                    settings.DefaultFontName = globalSettings.DefaultFontName;
                    settings.DefaultFontSize = globalSettings.DefaultFontSize;
                    settings.PreserveFontStyles = globalSettings.PreserveFontStyles;

                    // 复制导出选项
                    settings.OptimizeFileSize = globalSettings.OptimizeFileSize;
                    settings.UpdateFields = globalSettings.UpdateFields;
                    settings.UpdateTOC = globalSettings.UpdateTOC;
                    settings.SaveFormat = globalSettings.SaveFormat;
                    settings.ReplaceManualLineBreaks = globalSettings.ReplaceManualLineBreaks;

                    Logger.Instance.Log("已从全局配置文件加载设置");
                }
            }

            // 加载页面设置
            if (File.Exists(PageSetupPath))
            {
                var pageSetup = LoadJsonFile<PageSetupFixed>(PageSetupPath);
                if (pageSetup != null)
                {
                    settings.PageSetup = pageSetup;
                    Logger.Instance.Log("已从配置文件加载页面设置");
                }
            }

            // 加载删除内容设置
            if (File.Exists(DeleteContentPath))
            {
                var deleteSettings = LoadJsonFile<DeleteSettings>(DeleteContentPath);
                if (deleteSettings != null)
                {
                    settings.DeleteSettings = deleteSettings;
                    Logger.Instance.Log("已从配置文件加载内容删除设置");
                }
            }

            // 加载全局段落格式
            if (File.Exists(GlobalParagraphFormatPath))
            {
                var globalParagraphFormat = LoadJsonFile<ParagraphMatchRule>(GlobalParagraphFormatPath);
                if (globalParagraphFormat != null)
                {
                    settings.GlobalParagraphFormat = globalParagraphFormat;
                    Logger.Instance.Log("已从配置文件加载全局段落格式");
                }
            }

            // 加载页眉页脚设置
            if (File.Exists(HeaderFooterPath))
            {
                var headerFooterSettings = LoadJsonFile<HeaderFooterSettings>(HeaderFooterPath);
                if (headerFooterSettings != null)
                {
                    settings.HeaderFooterSettings = headerFooterSettings;
                    Logger.Instance.Log("已从配置文件加载页眉页脚设置");
                }
            }

            // 加载文档属性
            if (File.Exists(DocumentPropertiesPath))
            {
                var documentProperties = LoadJsonFile<DocumentProperties>(DocumentPropertiesPath);
                if (documentProperties != null)
                {
                    settings.DocumentProperties = documentProperties;
                    Logger.Instance.Log("已从配置文件加载文档属性设置");
                }
            }

            // 加载PDF设置
            if (File.Exists(PdfSettingsPath))
            {
                var pdfSettings = LoadJsonFile<PdfSettings>(PdfSettingsPath);
                if (pdfSettings != null)
                {
                    settings.PdfSettings = pdfSettings;
                    Logger.Instance.Log("已从配置文件加载PDF设置");
                }
            }

            // 加载TXT设置
            if (File.Exists(TxtSettingsPath))
            {
                var txtSettings = LoadJsonFile<TxtSettings>(TxtSettingsPath);
                if (txtSettings != null)
                {
                    settings.TxtSettings = txtSettings;
                    Logger.Instance.Log($"已从配置文件加载TXT处理设置，ConvertToWordBeforeProcessing = {txtSettings.ConvertToWordBeforeProcessing}");
                    Console.WriteLine($"从文件加载TXT设置: {TxtSettingsPath}, ConvertToWordBeforeProcessing = {txtSettings.ConvertToWordBeforeProcessing}");
                }
                else
                {
                    Logger.Instance.Log("TXT设置加载失败，使用默认设置");
                    Console.WriteLine($"TXT设置加载失败，使用默认设置: ConvertToWordBeforeProcessing = {settings.TxtSettings?.ConvertToWordBeforeProcessing}");
                }
            }
            else
            {
                Logger.Instance.Log($"TXT设置文件不存在: {TxtSettingsPath}，使用默认设置");
                Console.WriteLine($"TXT设置文件不存在: {TxtSettingsPath}，使用默认设置: ConvertToWordBeforeProcessing = {settings.TxtSettings?.ConvertToWordBeforeProcessing}");
            }

            // 加载调度设置
            if (File.Exists(ScheduleSettingsPath))
            {
                var scheduleSettings = LoadJsonFile<ScheduleSettings>(ScheduleSettingsPath);
                if (scheduleSettings != null)
                {
                    settings.ScheduleSettings = scheduleSettings;
                    Logger.Instance.Log("已从配置文件加载调度设置");
                }
            }

            // 加载日志设置
            if (File.Exists(LogSettingsPath))
            {
                var logSettings = LoadJsonFile<LogSettings>(LogSettingsPath);
                if (logSettings != null)
                {
                    settings.LogSettings = logSettings;
                    Logger.Instance.Log("已从配置文件加载日志设置");

                    // 应用日志设置到Logger系统
                    Logger.Instance.EnableLogging = logSettings.EnableLogging;
                    Logger.Instance.SetLogLevelEnabled(LogLevel.Debug, logSettings.EnableDebugLog);
                    Logger.Instance.SetLogLevelEnabled(LogLevel.Info, logSettings.EnableInfoLog);
                    Logger.Instance.SetLogLevelEnabled(LogLevel.Warning, logSettings.EnableWarningLog);
                    Logger.Instance.SetLogLevelEnabled(LogLevel.Error, logSettings.EnableErrorLog);
                    Logger.Instance.SetLogLevelEnabled(LogLevel.Fatal, logSettings.EnableFatalLog);
                    Logger.Instance.UseSeparateLogFiles = logSettings.UseSeparateLogFiles;
                    Logger.Instance.KeepAllLogFile = logSettings.KeepAllLogFile;
                }
            }

            // 加载段落匹配规则
            if (File.Exists(ParagraphMatchRulesPath))
            {
                var paragraphMatchRules = LoadJsonFile<List<ParagraphMatchRule>>(ParagraphMatchRulesPath);
                if (paragraphMatchRules != null)
                {
                    settings.ParagraphMatchRules = paragraphMatchRules;
                    Logger.Instance.Log("已从配置文件加载段落匹配规则");
                }
            }

            // 加载文件名替换规则
            if (File.Exists(FileNameReplacePath))
            {
                var fileNameReplaceRules = LoadJsonFile<List<FileNameReplaceRule>>(FileNameReplacePath);
                if (fileNameReplaceRules != null)
                {
                    settings.FileNameReplaceRules = fileNameReplaceRules;
                    Logger.Instance.Log("已从配置文件加载文件名替换规则");
                }
            }

            // 加载内容替换规则
            if (File.Exists(ContentReplacePath))
            {
                var contentReplaceRules = LoadJsonFile<List<ContentReplaceRule>>(ContentReplacePath);
                if (contentReplaceRules != null)
                {
                    settings.ContentReplaceRules = contentReplaceRules;
                    Logger.Instance.Log("已从配置文件加载内容替换规则");
                }
            }

            // 加载文件名非法词
            if (File.Exists(FileNameIllegalWordsPath))
            {
                var fileNameIllegalWords = LoadJsonFile<List<string>>(FileNameIllegalWordsPath);
                if (fileNameIllegalWords != null && settings.DeleteSettings != null)
                {
                    settings.DeleteSettings.FileNameIllegalWords = fileNameIllegalWords;
                    Logger.Instance.Log("已从配置文件加载文件名非法词");
                }
            }

            // 加载内容非法词
            if (File.Exists(ContentIllegalWordsPath))
            {
                var contentIllegalWords = LoadJsonFile<List<string>>(ContentIllegalWordsPath);
                if (contentIllegalWords != null && settings.DeleteSettings != null)
                {
                    settings.DeleteSettings.ContentIllegalWords = contentIllegalWords;
                    Logger.Instance.Log("已从配置文件加载内容非法词");
                }
            }

            // 如果有配置文件缺失，创建缺失的文件
            if (missingFiles)
            {
                // 只为缺失的文件创建默认配置
                if (!File.Exists(GlobalSettingsPath))
                {
                    var globalSettings = new Settings
                    {
                        // 设置基本属性
                        SourceDirectory = settings.SourceDirectory,
                        OutputDirectory = settings.OutputDirectory,
                        IncludeSubdirectories = settings.IncludeSubdirectories,
                        KeepDirectoryStructure = settings.KeepDirectoryStructure,
                        ConflictHandling = settings.ConflictHandling,
                        // ... 其他属性 ...
                    };
                    File.WriteAllText(GlobalSettingsPath, System.Text.Json.JsonSerializer.Serialize(globalSettings, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(GlobalSettingsPath)}");
                }

                if (!File.Exists(PageSetupPath) && settings.PageSetup != null)
                {
                    File.WriteAllText(PageSetupPath, System.Text.Json.JsonSerializer.Serialize(settings.PageSetup, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(PageSetupPath)}");
                }

                if (!File.Exists(DeleteContentPath) && settings.DeleteSettings != null)
                {
                    File.WriteAllText(DeleteContentPath, System.Text.Json.JsonSerializer.Serialize(settings.DeleteSettings, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(DeleteContentPath)}");
                }

                if (!File.Exists(GlobalParagraphFormatPath) && settings.GlobalParagraphFormat != null)
                {
                    File.WriteAllText(GlobalParagraphFormatPath, System.Text.Json.JsonSerializer.Serialize(settings.GlobalParagraphFormat, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(GlobalParagraphFormatPath)}");
                }

                if (!File.Exists(HeaderFooterPath) && settings.HeaderFooterSettings != null)
                {
                    File.WriteAllText(HeaderFooterPath, System.Text.Json.JsonSerializer.Serialize(settings.HeaderFooterSettings, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(HeaderFooterPath)}");
                }

                if (!File.Exists(DocumentPropertiesPath) && settings.DocumentProperties != null)
                {
                    File.WriteAllText(DocumentPropertiesPath, System.Text.Json.JsonSerializer.Serialize(settings.DocumentProperties, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(DocumentPropertiesPath)}");
                }

                if (!File.Exists(PdfSettingsPath) && settings.PdfSettings != null)
                {
                    File.WriteAllText(PdfSettingsPath, System.Text.Json.JsonSerializer.Serialize(settings.PdfSettings, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(PdfSettingsPath)}");
                }

                if (!File.Exists(TxtSettingsPath) && settings.TxtSettings != null)
                {
                    File.WriteAllText(TxtSettingsPath, System.Text.Json.JsonSerializer.Serialize(settings.TxtSettings, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(TxtSettingsPath)}");
                }

                if (!File.Exists(ScheduleSettingsPath) && settings.ScheduleSettings != null)
                {
                    File.WriteAllText(ScheduleSettingsPath, System.Text.Json.JsonSerializer.Serialize(settings.ScheduleSettings, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: ScheduleSettings.json");
                }

                if (!File.Exists(LogSettingsPath) && settings.LogSettings != null)
                {
                    File.WriteAllText(LogSettingsPath, System.Text.Json.JsonSerializer.Serialize(settings.LogSettings, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(LogSettingsPath)}");
                }

                if (!File.Exists(ParagraphMatchRulesPath) && settings.ParagraphMatchRules != null)
                {
                    File.WriteAllText(ParagraphMatchRulesPath, System.Text.Json.JsonSerializer.Serialize(settings.ParagraphMatchRules, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(ParagraphMatchRulesPath)}");
                }

                if (!File.Exists(FileNameReplacePath))
                {
                    File.WriteAllText(FileNameReplacePath, System.Text.Json.JsonSerializer.Serialize(
                        settings.FileNameReplaceRules ?? new List<FileNameReplaceRule>(), JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(FileNameReplacePath)}");
                }

                if (!File.Exists(ContentReplacePath))
                {
                    File.WriteAllText(ContentReplacePath, System.Text.Json.JsonSerializer.Serialize(
                        settings.ContentReplaceRules ?? new List<ContentReplaceRule>(), JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(ContentReplacePath)}");
                }

                if (!File.Exists(FileNameIllegalWordsPath))
                {
                    var defaultFileNameIllegalWords = new List<string> { "测试", "临时", "temp", "test", "副本", "copy", "备份", "backup" };
                    File.WriteAllText(FileNameIllegalWordsPath, System.Text.Json.JsonSerializer.Serialize(defaultFileNameIllegalWords, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(FileNameIllegalWordsPath)}");
                }

                if (!File.Exists(ContentIllegalWordsPath))
                {
                    var defaultContentIllegalWords = new List<string> { "机密", "内部", "草稿", "draft", "confidential", "internal", "删除", "delete" };
                    File.WriteAllText(ContentIllegalWordsPath, System.Text.Json.JsonSerializer.Serialize(defaultContentIllegalWords, JsonOptions));
                    Logger.Instance.Log($"已创建默认配置文件: {Path.GetFileName(ContentIllegalWordsPath)}");
                }

                Logger.Instance.Log($"已成功创建全部缺失的配置文件，共有16个配置文件");
            }

            // 确保文档格式配置文件存在
            if (!File.Exists(DocumentFormatsPath))
            {
                GetDocumentFormats(); // 会在文件不存在时创建默认配置
            }
            else
            {
                // 加载文档格式配置（通过GetDocumentFormats已经实现）
                GetDocumentFormats();
            }

            Logger.Instance.Log($"配置加载完成 - 共加载 {GetLoadedConfigFilesCount()} 个配置模块");
            Logger.Instance.Log($"配置摘要: 来源目录={settings.SourceDirectory}, 目标目录={settings.OutputDirectory}");
            Logger.Instance.Log($"功能模块状态: 页面设置={settings.EnablePageSetup}, 内容删除={settings.EnableDeleteContent}, 段落格式={settings.EnableGlobalParagraphFormat}");

            return settings;
        }

        /// <summary>
        /// 获取已加载的配置文件数量
        /// </summary>
        private static int GetLoadedConfigFilesCount()
        {
            int count = 0;
            string[] configFiles = {
                GlobalSettingsPath, PageSetupPath, DeleteContentPath, ContentReplacePath,
                GlobalParagraphFormatPath, ParagraphMatchRulesPath, HeaderFooterPath,
                DocumentPropertiesPath, FileNameReplacePath, PdfSettingsPath,
                TxtSettingsPath, ScheduleSettingsPath, LogSettingsPath, DocumentFormatsPath,
                FileNameIllegalWordsPath, ContentIllegalWordsPath
            };

            foreach (string filePath in configFiles)
            {
                if (File.Exists(filePath))
                    count++;
            }

            return count;
        }

        // 创建默认设置
        private static Settings CreateDefaultSettings()
        {
            var settings = new Settings();

            // 设置基本默认值
            settings.SourceDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "Word文档");
            settings.OutputDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "Word文档输出");
            settings.IncludeSubdirectories = true;
            settings.KeepDirectoryStructure = true;
            settings.ConflictHandling = "覆盖";
            settings.MaxThreads = 1;
            settings.MaxRetryCount = 3;
            settings.BatchSize = 50;

            // 启用所有功能模块
            settings.EnablePageSetup = true;
            settings.EnableDeleteContent = true;
            settings.EnableContentReplace = false;
            settings.EnableGlobalParagraphFormat = true;
            settings.EnableParagraphMatch = false;
            settings.EnableHeaderFooter = true;
            settings.EnableDocumentProperties = true;
            settings.EnableFileNameReplace = false;
            settings.EnableWordToPdf = false;

            // 创建默认规则和设置
            settings.PageSetup = new PageSetupFixed();
            settings.DeleteSettings = new DeleteSettings();

            // 初始化ParagraphMatchRule并设置必要的属性
            settings.GlobalParagraphFormat = new ParagraphMatchRule {
                HyperlinkUrl = string.Empty,
                HyperlinkToolTip = string.Empty,
                BookmarkName = string.Empty,
                // 确保底纹设置正确
                HasShading = false,
                ShadingColor = Color.LightGray,
                // 确保字体颜色正确
                FontColor = Color.Black,
                // 确保高亮颜色为null（不设置高亮）
                HighlightColor = null,
                // 设置功能区域启用状态
                EnableBasicFormat = true,
                EnableFontFormat = true,
                EnableBorderShading = false,
                EnablePagination = false,
                EnableTabStops = false,
                // 设置字体相关功能启用状态
                EnableChineseFont = true,
                EnableWesternFont = true,
                EnableComplexScriptFont = true,
                EnableFontColor = true,
                EnableHighlightColor = true,
                // 设置应用范围默认值
                ApplyToMainDocument = true,
                ApplyToHeader = false,
                ApplyToFooter = false,
                ApplyToTextBox = true,
                ApplyToFootnote = false,
                ApplyToEndnote = false,
                ApplyToComment = false
            };

            settings.HeaderFooterSettings = new HeaderFooterSettings();
            settings.DocumentProperties = new DocumentProperties();
            settings.PdfSettings = new PdfSettings();

            // 创建TxtSettings实例，确保默认值与期望一致
            var txtSettings = new TxtSettings();
            txtSettings.ConvertToWordBeforeProcessing = true; // 明确设置为true
            settings.TxtSettings = txtSettings;

            settings.ParagraphMatchRules = new List<ParagraphMatchRule>();
            settings.FileNameReplaceRules = new List<FileNameReplaceRule>();
            settings.ContentReplaceRules = new List<ContentReplaceRule>();
            settings.ScheduleSettings = new ScheduleSettings();
            settings.LogSettings = new LogSettings();

            Logger.Instance.Log("已创建默认配置");
            return settings;
        }

        // 保存设置
        public static void SaveSettings(Settings settings)
        {
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));

            // 确保配置目录存在
            EnsureConfigDirectoryExists();

            try
            {
                // 保存全局设置
                var globalSettings = new Settings
                {
                    // 只保存基本设置，不包括引用类型
                    SourceDirectory = settings.SourceDirectory,
                    OutputDirectory = settings.OutputDirectory,
                    IncludeSubdirectories = settings.IncludeSubdirectories,
                    KeepDirectoryStructure = settings.KeepDirectoryStructure,
                    ConflictHandling = settings.ConflictHandling,
                    ProcessOriginalFiles = settings.ProcessOriginalFiles,
                    MoveFiles = settings.MoveFiles,
                    MaxThreads = settings.MaxThreads,
                    MaxRetryCount = settings.MaxRetryCount,
                    BatchSize = settings.BatchSize,
                    BackupOriginalFiles = settings.BackupOriginalFiles,
                    BackupDirectory = settings.BackupDirectory,
                    RemoveEmptyParagraphs = settings.RemoveEmptyParagraphs,
                    NormalizeWhitespace = settings.NormalizeWhitespace,
                    RemoveMultipleSpaces = settings.RemoveMultipleSpaces,
                    RemoveHiddenContent = settings.RemoveHiddenContent,
                    RemoveComments = settings.RemoveComments,
                    RemoveHeadersFooters = settings.RemoveHeadersFooters,

                    // 功能开关
                    EnablePageSetup = settings.EnablePageSetup,
                    EnableDeleteContent = settings.EnableDeleteContent,
                    EnableContentReplace = settings.EnableContentReplace,
                    EnableGlobalParagraphFormat = settings.EnableGlobalParagraphFormat,
                    EnableParagraphMatch = settings.EnableParagraphMatch,
                    EnableHeaderFooter = settings.EnableHeaderFooter,
                    EnableDocumentProperties = settings.EnableDocumentProperties,
                    EnableFileNameReplace = settings.EnableFileNameReplace,
                    EnableWordToPdf = settings.EnableWordToPdf,

                    // 段落格式相关
                    ApplyDefaultParagraphFormat = settings.ApplyDefaultParagraphFormat,
                    DefaultLineSpacing = settings.DefaultLineSpacing,
                    DefaultFirstLineIndent = settings.DefaultFirstLineIndent,
                    DefaultLeftIndent = settings.DefaultLeftIndent,
                    DefaultRightIndent = settings.DefaultRightIndent,
                    DefaultSpaceBefore = settings.DefaultSpaceBefore,
                    DefaultSpaceAfter = settings.DefaultSpaceAfter,

                    // 字体格式相关
                    ApplyDefaultFontFormat = settings.ApplyDefaultFontFormat,
                    DefaultFontName = settings.DefaultFontName,
                    DefaultFontSize = settings.DefaultFontSize,
                    PreserveFontStyles = settings.PreserveFontStyles,

                    // 导出选项
                    OptimizeFileSize = settings.OptimizeFileSize,
                    UpdateFields = settings.UpdateFields,
                    UpdateTOC = settings.UpdateTOC,
                    SaveFormat = settings.SaveFormat,

                    // 文件内容替换
                    ReplaceManualLineBreaks = settings.ReplaceManualLineBreaks
                };

                File.WriteAllText(GlobalSettingsPath, System.Text.Json.JsonSerializer.Serialize(globalSettings, JsonOptions));

                // 保存调度设置
                if (settings.ScheduleSettings != null)
                {
                    File.WriteAllText(ScheduleSettingsPath, System.Text.Json.JsonSerializer.Serialize(settings.ScheduleSettings, JsonOptions));
                }

                // 保存日志设置
                if (settings.LogSettings != null)
                {
                    File.WriteAllText(LogSettingsPath, System.Text.Json.JsonSerializer.Serialize(settings.LogSettings, JsonOptions));
                }

                // 保存页面设置
                if (settings.PageSetup != null)
                {
                    File.WriteAllText(PageSetupPath, System.Text.Json.JsonSerializer.Serialize(settings.PageSetup, JsonOptions));
                }

                // 保存删除内容设置
                if (settings.DeleteSettings != null)
                {
                    File.WriteAllText(DeleteContentPath, System.Text.Json.JsonSerializer.Serialize(settings.DeleteSettings, JsonOptions));
                }

                // 保存全局段落格式
                if (settings.GlobalParagraphFormat != null)
                {
                    File.WriteAllText(GlobalParagraphFormatPath, System.Text.Json.JsonSerializer.Serialize(settings.GlobalParagraphFormat, JsonOptions));
                }

                // 保存页眉页脚设置
                if (settings.HeaderFooterSettings != null)
                {
                    File.WriteAllText(HeaderFooterPath, System.Text.Json.JsonSerializer.Serialize(settings.HeaderFooterSettings, JsonOptions));
                }

                // 保存文档属性
                if (settings.DocumentProperties != null)
                {
                    File.WriteAllText(DocumentPropertiesPath, System.Text.Json.JsonSerializer.Serialize(settings.DocumentProperties, JsonOptions));
                }

                // 保存PDF设置
                if (settings.PdfSettings != null)
                {
                    File.WriteAllText(PdfSettingsPath, System.Text.Json.JsonSerializer.Serialize(settings.PdfSettings, JsonOptions));
                }

                // 保存TXT设置
                if (settings.TxtSettings != null)
                {
                    File.WriteAllText(TxtSettingsPath, System.Text.Json.JsonSerializer.Serialize(settings.TxtSettings, JsonOptions));
                    Logger.Instance.Log($"已保存TXT设置到文件: {TxtSettingsPath}, ConvertToWordBeforeProcessing = {settings.TxtSettings.ConvertToWordBeforeProcessing}");
                    Console.WriteLine($"已保存TXT设置到文件: {TxtSettingsPath}, ConvertToWordBeforeProcessing = {settings.TxtSettings.ConvertToWordBeforeProcessing}");
                }
                else
                {
                    Logger.Instance.Log("TXT设置为空，无法保存");
                    Console.WriteLine("TXT设置为空，无法保存");
                }

                // 保存段落匹配规则，即使集合为空也创建文件
                if (settings.ParagraphMatchRules != null)
                {
                    File.WriteAllText(ParagraphMatchRulesPath, System.Text.Json.JsonSerializer.Serialize(settings.ParagraphMatchRules, JsonOptions));
                }
                else
                {
                    File.WriteAllText(ParagraphMatchRulesPath, System.Text.Json.JsonSerializer.Serialize(new List<ParagraphMatchRule>(), JsonOptions));
                }

                // 保存文件名替换规则，即使集合为空也创建文件
                if (settings.FileNameReplaceRules != null)
                {
                    File.WriteAllText(FileNameReplacePath, System.Text.Json.JsonSerializer.Serialize(settings.FileNameReplaceRules, JsonOptions));
                }
                else
                {
                    File.WriteAllText(FileNameReplacePath, System.Text.Json.JsonSerializer.Serialize(new List<FileNameReplaceRule>(), JsonOptions));
                }

                // 保存内容替换规则，即使集合为空也创建文件
                if (settings.ContentReplaceRules != null)
                {
                    File.WriteAllText(ContentReplacePath, System.Text.Json.JsonSerializer.Serialize(settings.ContentReplaceRules, JsonOptions));
                }
                else
                {
                    File.WriteAllText(ContentReplacePath, System.Text.Json.JsonSerializer.Serialize(new List<ContentReplaceRule>(), JsonOptions));
                }

                // 保存文件名非法词
                if (settings.DeleteSettings?.FileNameIllegalWords != null)
                {
                    File.WriteAllText(FileNameIllegalWordsPath, System.Text.Json.JsonSerializer.Serialize(settings.DeleteSettings.FileNameIllegalWords, JsonOptions));
                }

                // 保存内容非法词
                if (settings.DeleteSettings?.ContentIllegalWords != null)
                {
                    File.WriteAllText(ContentIllegalWordsPath, System.Text.Json.JsonSerializer.Serialize(settings.DeleteSettings.ContentIllegalWords, JsonOptions));
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.LogError("保存拆分配置文件时出错", ex);
                throw;
            }
        }

        // 从旧版配置文件迁移设置
        private static void MigrateSettingsFromLegacy()
        {
            try
            {
                if (File.Exists(LegacySettingsPath))
                {
                    string jsonString = File.ReadAllText(LegacySettingsPath);
                    var settings = System.Text.Json.JsonSerializer.Deserialize<Settings>(jsonString, JsonOptions);
                    if (settings != null)
                    {
                        // 保存到新的拆分配置文件
                        SaveSettings(settings);

                        // 创建备份
                        string backupPath = LegacySettingsPath + ".bak";
                        File.Copy(LegacySettingsPath, backupPath, true);

                        // 记录迁移日志
                        Logger.Instance.Log($"已将旧版配置文件 {LegacySettingsPath} 迁移到新的拆分配置文件系统。备份保存在 {backupPath}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.LogError("迁移旧版配置文件时出错", ex);
            }
        }

        // 确保配置目录存在
        private static void EnsureConfigDirectoryExists()
        {
            if (!Directory.Exists(ConfigDir))
            {
                Directory.CreateDirectory(ConfigDir);
            }
        }

        // 加载JSON文件到特定类型
        private static T? LoadJsonFile<T>(string filePath) where T : class
        {
            if (!File.Exists(filePath))
                return null;

            try
            {
                string jsonString = File.ReadAllText(filePath);
                return System.Text.Json.JsonSerializer.Deserialize<T>(jsonString, JsonOptions);
            }
            catch (Exception ex)
            {
                Logger.Instance.LogError($"加载配置文件 {Path.GetFileName(filePath)} 时出错", ex);
                return null;
            }
        }

        // 保存对象到JSON文件
        private static void SaveJsonFile<T>(string filePath, T obj, string comments = "") where T : class
        {
            if (obj == null)
                return;

            try
            {
                string jsonString = System.Text.Json.JsonSerializer.Serialize(obj, JsonOptions);

                // 如果有注释，添加在文件开头
                if (!string.IsNullOrEmpty(comments))
                {
                    jsonString = comments + "\n" + jsonString;
                }

                File.WriteAllText(filePath, jsonString);
            }
            catch (Exception ex)
            {
                Logger.Instance.LogError($"保存配置文件 {Path.GetFileName(filePath)} 时出错", ex);
            }
        }

        public static Dictionary<string, bool> GetDocumentFormats()
        {
            // 如果已经加载过，直接返回缓存的结果
            if (_documentFormats.Count > 0)
                return _documentFormats;

            try
            {
                if (File.Exists(DocumentFormatsPath))
                {
                    string jsonContent = File.ReadAllText(DocumentFormatsPath);
                    _documentFormats = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, bool>>(jsonContent, JsonOptions) ?? new Dictionary<string, bool>();
                }
                else
                {
                    // 如果文件不存在，创建默认格式列表
                    _documentFormats = new Dictionary<string, bool>
                    {
                        { ".doc", true },
                        { ".docx", true },
                        { ".rtf", true },
                        { ".txt", true },
                        { ".odt", false },
                        { ".htm", false },
                        { ".html", false },
                        { ".mht", false },
                        { ".xml", false }
                    };

                    // 保存默认格式列表
                    SaveDocumentFormats(_documentFormats);
                    Logger.Instance.Log("已创建默认文档格式配置");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.LogError("加载文档格式配置时出错", ex);
                // 出错时创建默认格式列表
                _documentFormats = new Dictionary<string, bool>
                {
                    { ".doc", true },
                    { ".docx", true },
                    { ".rtf", true },
                    { ".txt", true }
                };
            }

            return _documentFormats;
        }

        public static void SaveDocumentFormats(Dictionary<string, bool> formats)
        {
            if (formats == null)
                throw new ArgumentNullException(nameof(formats));

            try
            {
                string jsonContent = System.Text.Json.JsonSerializer.Serialize(formats, JsonOptions);
                File.WriteAllText(DocumentFormatsPath, jsonContent);
                _documentFormats = formats; // 更新缓存
                Logger.Instance.Log("文档格式配置已保存");
            }
            catch (Exception ex)
            {
                Logger.Instance.LogError("保存文档格式配置时出错", ex);
                throw;
            }
        }

        public static bool IsFormatEnabled(string format)
        {
            var formats = GetDocumentFormats();

            // 移除前导点（如果有）
            string fileExt = format.StartsWith(".") ? format.Substring(1).ToLower() : format.ToLower();

            // 检查字典中是否包含此格式
            if (formats.TryGetValue("." + fileExt, out bool enabled))
            {
                return enabled;
            }

            // 如果找不到格式，默认返回true（支持该格式）
            return true;
        }
    }
}