/*
 * ========================================
 * 文件名: DocumentProperties.cs
 * 功能描述: 文档属性数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义完整的文档属性配置选项
 * 2. 提供文档属性到Aspose.Words的转换方法
 * 3. 支持基本、扩展、统计和自定义属性
 * 4. 包含属性的启用/禁用控制机制
 * 5. 提供属性应用的智能管理
 *
 * 属性分类:
 *
 * 基本文档属性:
 * - Title: 文档标题（支持使用文件名）
 * - Subject: 文档主题
 * - Author: 文档作者
 * - Keywords: 关键词标签
 * - Comments: 文档备注
 * - Category: 文档类别
 * - Company: 所属公司
 * - Manager: 文档管理者
 *
 * 扩展文档属性:
 * - ContentType: 内容类型
 * - ContentStatus: 内容状态
 * - HyperlinkBase: 超链接基址
 * - Template: 使用的模板
 * - CreatedTime: 创建时间
 * - LastSavedTime: 最后保存时间
 * - LastSavedBy: 最后保存者
 * - LastPrinted: 最后打印时间
 * - RevisionNumber: 修订号
 *
 * 统计信息属性（只读）:
 * - PageCount: 页数
 * - WordCount: 字数
 * - CharacterCount: 字符数（不含空格）
 * - CharacterCountWithSpaces: 字符数（含空格）
 * - LineCount: 行数
 * - ParagraphCount: 段落数
 * - ByteCount: 字节数
 *
 * 自定义属性:
 * - Custom1-6: 六个自定义属性
 * - Custom1Label-6Label: 自定义属性标签
 * - 支持自定义属性名称和值
 *
 * 启用控制机制:
 * - 每个属性都有对应的Enable开关
 * - 支持选择性应用属性设置
 * - 避免不必要的文档修改
 * - 提供分组启用状态检查
 *
 * 核心方法:
 * - ApplyTo(): 将属性应用到Aspose.Words文档
 * - HasAnyEnabledProperty(): 检查是否有启用的属性
 * - HasEnabledBasicProperties(): 检查基本属性启用状态
 * - HasEnabledExtendedProperties(): 检查扩展属性启用状态
 * - HasEnabledStatistics(): 检查统计信息启用状态
 * - HasEnabledCustomProperties(): 检查自定义属性启用状态
 *
 * 特殊功能:
 * - UseFilenameAsTitle: 使用文件名作为标题
 * - 智能的自定义属性管理
 * - 属性名称清理和验证
 * - 多种数据类型支持（字符串、数字、日期、布尔）
 *
 * 注意事项:
 * - 支持Aspose.Words的所有文档属性
 * - 包含完整的错误处理机制
 * - 实现了智能的属性冲突处理
 * - 支持属性的动态类型转换
 */

using System;
using AW = Aspose.Words;

namespace AsposeSlidesFormatter.Models
{
    public class DocumentProperties
    {
        // 基本文档属性
        public string? Title { get; set; } = "www.yzwk.com测试文档";
        public string? Subject { get; set; } = "www.yzwk.com测试主题";
        public string? Author { get; set; } = "www.yzwk.com测试作者";
        public string? Keywords { get; set; } = "www.yzwk.com测试标记";
        public string? Comments { get; set; } = "www.yzwk.com测试备注";
        public string? Category { get; set; } = "www.yzwk.com测试类别";
        public string? Company { get; set; } = "www.yzwk.com测试公司";
        public string? Manager { get; set; } = "www.yzwk.com测试管理者";

        // 扩展文档属性
        public string? ContentType { get; set; }
        public string? ContentStatus { get; set; }
        public string? HyperlinkBase { get; set; }
        public string? Template { get; set; }
        public DateTime? CreatedTime { get; set; }
        public DateTime? LastSavedTime { get; set; }
        public string? LastSavedBy { get; set; }
        public DateTime? LastPrinted { get; set; }
        public int? RevisionNumber { get; set; }

        public bool UseFilenameAsTitle { get; set; } = false;

        // 启用标志 - 基本属性
        public bool EnableTitle { get; set; } = true;
        public bool EnableSubject { get; set; } = true;
        public bool EnableAuthor { get; set; } = true;
        public bool EnableKeywords { get; set; } = true;
        public bool EnableComments { get; set; } = true;
        public bool EnableCategory { get; set; } = true;
        public bool EnableCompany { get; set; } = true;
        public bool EnableManager { get; set; } = true;

        // 启用标志 - 扩展属性
        public bool EnableContentType { get; set; } = false;
        public bool EnableContentStatus { get; set; } = false;
        public bool EnableHyperlinkBase { get; set; } = false;
        public bool EnableTemplate { get; set; } = false;
        public bool EnableCreatedTime { get; set; } = false;
        public bool EnableLastSavedTime { get; set; } = false;
        public bool EnableLastSavedBy { get; set; } = false;
        public bool EnableLastPrinted { get; set; } = false;
        public bool EnableRevisionNumber { get; set; } = false;

        // 统计信息属性（只读）
        public int PageCount { get; private set; }
        public int WordCount { get; private set; }
        public int CharacterCount { get; private set; }
        public int CharacterCountWithSpaces { get; private set; }
        public int LineCount { get; private set; }
        public int ParagraphCount { get; private set; }
        public int ByteCount { get; private set; }

        // 统计信息显示标志
        public bool ShowPageCount { get; set; } = false;
        public bool ShowWordCount { get; set; } = false;
        public bool ShowCharacterCount { get; set; } = false;
        public bool ShowCharacterCountWithSpaces { get; set; } = false;
        public bool ShowLineCount { get; set; } = false;
        public bool ShowParagraphCount { get; set; } = false;
        public bool ShowByteCount { get; set; } = false;

        // 自定义属性
        public string Custom1 { get; set; } = string.Empty;
        public bool EnableCustom1 { get; set; } = false;
        public string Custom1Label { get; set; } = "自定义1:";

        public string Custom2 { get; set; } = string.Empty;
        public bool EnableCustom2 { get; set; } = false;
        public string Custom2Label { get; set; } = "自定义2:";

        public string Custom3 { get; set; } = string.Empty;
        public bool EnableCustom3 { get; set; } = false;
        public string Custom3Label { get; set; } = "自定义3:";

        public string Custom4 { get; set; } = string.Empty;
        public bool EnableCustom4 { get; set; } = false;
        public string Custom4Label { get; set; } = "自定义4:";

        public string Custom5 { get; set; } = string.Empty;
        public bool EnableCustom5 { get; set; } = false;
        public string Custom5Label { get; set; } = "自定义5:";

        public string Custom6 { get; set; } = string.Empty;
        public bool EnableCustom6 { get; set; } = false;
        public string Custom6Label { get; set; } = "自定义6:";

        // 检查是否有任何文档属性被启用
        public bool HasAnyEnabledProperty()
        {
            return HasEnabledBasicProperties() ||
                   HasEnabledExtendedProperties() ||
                   HasEnabledCustomProperties();
        }

        // 检查基本信息标签页是否有任何功能被启用
        public bool HasEnabledBasicProperties()
        {
            return EnableTitle || EnableSubject || EnableAuthor || EnableKeywords ||
                   EnableComments || EnableCategory || EnableCompany || EnableManager;
        }

        // 检查扩展信息标签页是否有任何功能被启用
        public bool HasEnabledExtendedProperties()
        {
            return EnableContentType || EnableContentStatus || EnableHyperlinkBase ||
                   EnableTemplate || EnableCreatedTime || EnableLastSavedTime ||
                   EnableLastSavedBy || EnableLastPrinted || EnableRevisionNumber;
        }

        // 检查统计信息标签页是否有任何功能被启用
        public bool HasEnabledStatistics()
        {
            return ShowPageCount || ShowWordCount || ShowCharacterCount ||
                   ShowCharacterCountWithSpaces || ShowLineCount ||
                   ShowParagraphCount || ShowByteCount;
        }

        // 检查自定义信息标签页是否有任何功能被启用
        public bool HasEnabledCustomProperties()
        {
            return EnableCustom1 || EnableCustom2 || EnableCustom3 ||
                   EnableCustom4 || EnableCustom5 || EnableCustom6;
        }

        public void ApplyTo(AW.Document doc)
        {
            if (doc == null)
                throw new ArgumentNullException(nameof(doc));

            // 预先检查是否有任何属性被启用
            bool hasBasicProps = HasEnabledBasicProperties();
            bool hasExtendedProps = HasEnabledExtendedProperties();
            bool hasCustomProps = HasEnabledCustomProperties();

            // 如果没有任何属性被启用，检查是否需要更新统计信息
            if (!hasBasicProps && !hasExtendedProps && !hasCustomProps)
            {
                // 只有当有统计信息需要显示时才更新
                if (HasEnabledStatistics())
                {
                    doc.UpdateWordCount();
                    UpdateStatistics(doc);
                }
                return;
            }

            var builtInProperties = doc.BuiltInDocumentProperties;

            // 更新统计信息 (这部分根据用户选择决定是否执行)
            // 只有当有统计信息需要显示时才调用UpdateWordCount
            if (HasEnabledStatistics())
            {
                doc.UpdateWordCount();
                UpdateStatistics(doc);
            }

            // 检查是否设置基本文档属性
            if (hasBasicProps)
            {
                // 设置基本文档属性
                if (UseFilenameAsTitle && EnableTitle)
                {
                    string fileName = System.IO.Path.GetFileNameWithoutExtension(doc.OriginalFileName);
                    builtInProperties.Title = fileName;
                }
                else if (!string.IsNullOrEmpty(Title) && EnableTitle)
                {
                    builtInProperties.Title = Title;
                }

                if (!string.IsNullOrEmpty(Subject) && EnableSubject)
                    builtInProperties.Subject = Subject;

                if (!string.IsNullOrEmpty(Author) && EnableAuthor)
                    builtInProperties.Author = Author;

                if (!string.IsNullOrEmpty(Keywords) && EnableKeywords)
                    builtInProperties.Keywords = Keywords;

                if (!string.IsNullOrEmpty(Comments) && EnableComments)
                    builtInProperties.Comments = Comments;

                if (!string.IsNullOrEmpty(Category) && EnableCategory)
                    builtInProperties.Category = Category;

                if (!string.IsNullOrEmpty(Company) && EnableCompany)
                    builtInProperties.Company = Company;

                if (!string.IsNullOrEmpty(Manager) && EnableManager)
                    builtInProperties.Manager = Manager;
            }

            // 检查是否设置扩展文档属性
            if (hasExtendedProps)
            {
                // 设置扩展文档属性
                if (!string.IsNullOrEmpty(ContentType) && EnableContentType)
                    builtInProperties.ContentType = ContentType;

                if (!string.IsNullOrEmpty(ContentStatus) && EnableContentStatus)
                    builtInProperties.ContentStatus = ContentStatus;

                if (!string.IsNullOrEmpty(HyperlinkBase) && EnableHyperlinkBase)
                    builtInProperties.HyperlinkBase = HyperlinkBase;

                if (!string.IsNullOrEmpty(Template) && EnableTemplate)
                    builtInProperties.Template = Template;

                if (CreatedTime.HasValue && EnableCreatedTime)
                    builtInProperties.CreatedTime = CreatedTime.Value;

                if (LastSavedTime.HasValue && EnableLastSavedTime)
                    builtInProperties.LastSavedTime = LastSavedTime.Value;

                if (!string.IsNullOrEmpty(LastSavedBy) && EnableLastSavedBy)
                    builtInProperties.LastSavedBy = LastSavedBy;

                if (LastPrinted.HasValue && EnableLastPrinted)
                    builtInProperties.LastPrinted = LastPrinted.Value;

                if (RevisionNumber.HasValue && EnableRevisionNumber)
                    builtInProperties.RevisionNumber = RevisionNumber.Value;
            }

            // 检查是否应用自定义属性
            if (hasCustomProps)
            {
                // 应用自定义属性
                ApplyCustomProperties(doc);
            }
        }

        // 应用自定义属性
        private void ApplyCustomProperties(AW.Document doc)
        {
            // 获取要处理的所有自定义属性名称列表
            var customPropertyNames = new[] { "Custom1", "Custom2", "Custom3", "Custom4", "Custom5", "Custom6" };
            var defaultLabels = new[] { "自定义1:", "自定义2:", "自定义3:", "自定义4:", "自定义5:", "自定义6:" };

            // 记录自定义属性的启用状态
            System.Diagnostics.Debug.WriteLine($"自定义属性启用状态: Custom1={EnableCustom1}, Custom2={EnableCustom2}, Custom3={EnableCustom3}, Custom4={EnableCustom4}, Custom5={EnableCustom5}, Custom6={EnableCustom6}");

            // 检查现有文档中是否包含任何自定义属性，如果未启用则全部删除
            if (!HasEnabledCustomProperties())
            {
                System.Diagnostics.Debug.WriteLine("没有启用任何自定义属性，将删除所有现有自定义属性");

                try
                {
                    // 清理所有可能存在的自定义属性
                    foreach (var name in customPropertyNames)
                    {
                        if (doc.CustomDocumentProperties[name] != null)
                        {
                            // 如果自定义属性标签页没有勾选任何属性，则删除已存在的所有Custom属性
                            doc.CustomDocumentProperties.Remove(name);
                            System.Diagnostics.Debug.WriteLine($"已删除自定义属性: {name}");
                        }
                    }

                    // 还需要检查并删除可能使用自定义标签创建的属性
                    var customLabels = new[] {
                        Custom1Label?.TrimEnd(':'),
                        Custom2Label?.TrimEnd(':'),
                        Custom3Label?.TrimEnd(':'),
                        Custom4Label?.TrimEnd(':'),
                        Custom5Label?.TrimEnd(':'),
                        Custom6Label?.TrimEnd(':')
                    };

                    foreach (var label in customLabels)
                    {
                        if (!string.IsNullOrEmpty(label) && doc.CustomDocumentProperties[label] != null)
                        {
                            doc.CustomDocumentProperties.Remove(label);
                            System.Diagnostics.Debug.WriteLine($"已删除自定义标签属性: {label}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"删除自定义属性时出错: {ex.Message}");
                }

                return; // 没有启用任何自定义属性，直接返回
            }

            System.Diagnostics.Debug.WriteLine("开始应用自定义属性...");

            try
            {
                // 先清除所有现有的自定义属性，以避免冲突
                foreach (var prop in doc.CustomDocumentProperties.ToArray())
                {
                    // 只清除我们可能创建的属性，避免删除文档中其他自定义属性
                    bool isOurProperty = customPropertyNames.Contains(prop.Name) ||
                                        defaultLabels.Contains(prop.Name) ||
                                        customPropertyNames.Any(n => prop.Name.StartsWith(n)) ||
                                        (Custom1Label != null && prop.Name == Custom1Label.TrimEnd(':')) ||
                                        (Custom2Label != null && prop.Name == Custom2Label.TrimEnd(':')) ||
                                        (Custom3Label != null && prop.Name == Custom3Label.TrimEnd(':')) ||
                                        (Custom4Label != null && prop.Name == Custom4Label.TrimEnd(':')) ||
                                        (Custom5Label != null && prop.Name == Custom5Label.TrimEnd(':')) ||
                                        (Custom6Label != null && prop.Name == Custom6Label.TrimEnd(':'));

                    if (isOurProperty)
                    {
                        doc.CustomDocumentProperties.Remove(prop.Name);
                        System.Diagnostics.Debug.WriteLine($"已清除现有自定义属性: {prop.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除现有自定义属性时出错: {ex.Message}");
            }

            // 处理各自定义属性
            if (EnableCustom1)
            {
                System.Diagnostics.Debug.WriteLine($"应用自定义属性1: 标签={Custom1Label}, 值={Custom1}");
                ApplyCustomProperty(doc, "Custom1", Custom1, EnableCustom1, Custom1Label);
            }

            if (EnableCustom2)
            {
                System.Diagnostics.Debug.WriteLine($"应用自定义属性2: 标签={Custom2Label}, 值={Custom2}");
                ApplyCustomProperty(doc, "Custom2", Custom2, EnableCustom2, Custom2Label);
            }

            if (EnableCustom3)
            {
                System.Diagnostics.Debug.WriteLine($"应用自定义属性3: 标签={Custom3Label}, 值={Custom3}");
                ApplyCustomProperty(doc, "Custom3", Custom3, EnableCustom3, Custom3Label);
            }

            if (EnableCustom4)
            {
                System.Diagnostics.Debug.WriteLine($"应用自定义属性4: 标签={Custom4Label}, 值={Custom4}");
                ApplyCustomProperty(doc, "Custom4", Custom4, EnableCustom4, Custom4Label);
            }

            if (EnableCustom5)
            {
                System.Diagnostics.Debug.WriteLine($"应用自定义属性5: 标签={Custom5Label}, 值={Custom5}");
                ApplyCustomProperty(doc, "Custom5", Custom5, EnableCustom5, Custom5Label);
            }

            if (EnableCustom6)
            {
                System.Diagnostics.Debug.WriteLine($"应用自定义属性6: 标签={Custom6Label}, 值={Custom6}");
                ApplyCustomProperty(doc, "Custom6", Custom6, EnableCustom6, Custom6Label);
            }

            // 列出应用后的所有自定义属性
            System.Diagnostics.Debug.WriteLine("文档中的自定义属性列表:");
            try
            {
                foreach (var prop in doc.CustomDocumentProperties)
                {
                    System.Diagnostics.Debug.WriteLine($"  - {prop.Name} = {prop.Value}");
                }

                if (doc.CustomDocumentProperties.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("  文档中没有自定义属性");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"列出自定义属性时出错: {ex.Message}");
            }
        }

        // 设置单个自定义属性
        private void ApplyCustomProperty(AW.Document doc, string name, string value, bool enabled, string customLabel)
        {
            if (!enabled)
            {
                // 如果属性未启用，检查并删除可能存在的属性
                // 检查默认名称
                if (doc.CustomDocumentProperties[name] != null)
                {
                    doc.CustomDocumentProperties.Remove(name);
                    System.Diagnostics.Debug.WriteLine($"已删除未启用的自定义属性: {name}");
                }

                // 检查自定义标签名称
                string labelName = customLabel?.TrimEnd(':');
                if (!string.IsNullOrEmpty(labelName) && doc.CustomDocumentProperties[labelName] != null)
                {
                    doc.CustomDocumentProperties.Remove(labelName);
                    System.Diagnostics.Debug.WriteLine($"已删除未启用的自定义标签属性: {labelName}");
                }

                return;
            }

            // 如果值为空，根据要求跳过该字段
            if (string.IsNullOrEmpty(value))
            {
                System.Diagnostics.Debug.WriteLine($"跳过空值自定义属性: {name}");
                return;
            }

            // 属性已启用，使用自定义标签作为属性名（如果有）
            string propertyName = !string.IsNullOrEmpty(customLabel) ? customLabel.TrimEnd(':') : name;

            // 确保属性名不为空
            if (string.IsNullOrWhiteSpace(propertyName))
            {
                propertyName = name; // 使用默认名称
            }

            // 确保属性名称有效（不包含特殊字符）
            propertyName = CleanPropertyName(propertyName);

            // 检查是否存在旧的默认名称属性，如果存在且使用了自定义标签，则删除旧属性
            if (propertyName != name && doc.CustomDocumentProperties[name] != null)
            {
                doc.CustomDocumentProperties.Remove(name);
                System.Diagnostics.Debug.WriteLine($"已删除旧的默认名称属性: {name}");
            }

            try
            {
                // 检查属性是否已存在
                if (doc.CustomDocumentProperties[propertyName] != null)
                {
                    // 更新已存在的属性
                    doc.CustomDocumentProperties[propertyName].Value = value;
                    System.Diagnostics.Debug.WriteLine($"已更新自定义属性: {propertyName} = {value}");
                }
                else
                {
                    // 属性不存在且已启用，则添加新属性
                    doc.CustomDocumentProperties.Add(propertyName, value);
                    System.Diagnostics.Debug.WriteLine($"已添加新的自定义属性: {propertyName} = {value}");
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不中断处理
                System.Diagnostics.Debug.WriteLine($"应用自定义属性 '{propertyName}' 时出错: {ex.Message}");

                // 尝试使用默认名称作为备选方案
                try
                {
                    // 使用不同的属性类型尝试添加
                    // 尝试作为数字添加
                    if (double.TryParse(value, out double doubleValue))
                    {
                        if (doc.CustomDocumentProperties[name] != null)
                        {
                            doc.CustomDocumentProperties.Remove(name);
                        }
                        doc.CustomDocumentProperties.Add(name, doubleValue);
                        System.Diagnostics.Debug.WriteLine($"已添加数值类型自定义属性: {name} = {doubleValue}");
                        return;
                    }

                    // 尝试作为日期添加
                    if (DateTime.TryParse(value, out DateTime dateValue))
                    {
                        if (doc.CustomDocumentProperties[name] != null)
                        {
                            doc.CustomDocumentProperties.Remove(name);
                        }
                        doc.CustomDocumentProperties.Add(name, dateValue);
                        System.Diagnostics.Debug.WriteLine($"已添加日期类型自定义属性: {name} = {dateValue}");
                        return;
                    }

                    // 尝试作为布尔值添加
                    if (bool.TryParse(value, out bool boolValue))
                    {
                        if (doc.CustomDocumentProperties[name] != null)
                        {
                            doc.CustomDocumentProperties.Remove(name);
                        }
                        doc.CustomDocumentProperties.Add(name, boolValue);
                        System.Diagnostics.Debug.WriteLine($"已添加布尔类型自定义属性: {name} = {boolValue}");
                        return;
                    }

                    // 最后尝试作为字符串添加
                    if (doc.CustomDocumentProperties[name] != null)
                    {
                        doc.CustomDocumentProperties[name].Value = value;
                        System.Diagnostics.Debug.WriteLine($"已更新字符串类型自定义属性: {name} = {value}");
                    }
                    else
                    {
                        doc.CustomDocumentProperties.Add(name, value);
                        System.Diagnostics.Debug.WriteLine($"已添加字符串类型自定义属性: {name} = {value}");
                    }
                }
                catch (Exception innerEx)
                {
                    // 记录备选方案的错误
                    System.Diagnostics.Debug.WriteLine($"备选方案添加自定义属性 '{name}' 时出错: {innerEx.Message}");
                }
            }
        }

        // 清理属性名称，确保其有效
        private string CleanPropertyName(string name)
        {
            if (string.IsNullOrEmpty(name))
                return "Property";

            // 移除不允许的字符
            char[] invalidChars = new char[] { '/', '\\', ':', '*', '?', '"', '<', '>', '|', '[', ']' };
            foreach (char c in invalidChars)
            {
                name = name.Replace(c, '_');
            }

            // 确保名称不以空格开头或结尾
            name = name.Trim();

            // 如果名称为空，使用默认名称
            if (string.IsNullOrEmpty(name))
                return "Property";

            return name;
        }

        // 更新文档统计信息
        private void UpdateStatistics(AW.Document doc)
        {
            if (doc == null)
                return;

            var builtInProperties = doc.BuiltInDocumentProperties;

            // 只更新用户选择显示的统计信息，避免不必要的计算
            if (ShowPageCount)
                PageCount = doc.PageCount;

            if (ShowWordCount)
                WordCount = builtInProperties.Words;

            if (ShowCharacterCount)
                CharacterCount = builtInProperties.Characters;

            if (ShowCharacterCountWithSpaces)
                CharacterCountWithSpaces = builtInProperties.CharactersWithSpaces;

            if (ShowLineCount)
                LineCount = builtInProperties.Lines;

            if (ShowParagraphCount)
                ParagraphCount = builtInProperties.Paragraphs;

            if (ShowByteCount)
                ByteCount = builtInProperties.Bytes;
        }
    }
}