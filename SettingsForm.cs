/*
 * ========================================
 * 文件名: SettingsForm.cs
 * 功能描述: 综合设置窗体
 * ========================================
 *
 * 主要功能:
 * 1. 提供应用程序的综合设置界面
 * 2. 集成多个功能模块的配置窗体
 * 3. 支持标签页方式的设置管理
 * 4. 提供统一的设置保存和取消操作
 * 5. 实现各功能模块设置的集中管理
 *
 * 界面结构:
 * - 标签页控件：将不同功能的设置分组显示
 * - 页面设置标签：页面布局、边距、纸张等设置
 * - 文档属性标签：文档元数据、作者、标题等属性
 * - 段落匹配规则标签：段落格式匹配和应用规则
 * - 文件名替换规则标签：文件重命名规则配置
 * - 页眉页脚设置标签：页眉页脚内容和格式
 * - 内容删除设置标签：文档内容清理规则
 * - 操作按钮面板：确定、取消操作按钮
 *
 * 集成的功能模块:
 *
 * PageSetupForm:
 * - 页面边距、方向、大小设置
 * - 页眉页脚距离配置
 * - 纸张类型和自定义尺寸
 *
 * DocumentPropertiesForm:
 * - 文档标题、作者、主题设置
 * - 关键词、注释、类别配置
 * - 文档统计信息管理
 *
 * ParagraphMatchForm:
 * - 段落匹配规则列表管理
 * - 规则的添加、编辑、删除
 * - 规则优先级和启用状态
 *
 * FileNameReplaceForm:
 * - 文件名替换规则配置
 * - 正则表达式和普通文本替换
 * - 批量重命名规则管理
 *
 * HeaderFooterForm:
 * - 页眉页脚内容设置
 * - 字体、对齐方式配置
 * - 页码格式和显示选项
 *
 * DeleteContentForm:
 * - 文档内容删除规则
 * - 图片、文本、格式清理
 * - 空白内容处理选项
 *
 * 数据管理特性:
 * - 统一的Settings模型集成
 * - 各子窗体的数据同步
 * - 设置的批量保存和验证
 * - 错误处理和回滚机制
 *
 * 界面交互特性:
 * - 标签页切换：方便在不同设置间切换
 * - 模态对话框：确保设置操作的完整性
 * - 固定窗体大小：保持界面的一致性
 * - 居中显示：提供良好的用户体验
 *
 * 操作流程:
 * - 加载当前设置到各子窗体
 * - 用户在各标签页中修改设置
 * - 点击确定时验证所有设置
 * - 保存设置并关闭窗体
 * - 点击取消时放弃所有修改
 *
 * 验证机制:
 * - 各子窗体的独立验证
 * - 整体设置的一致性检查
 * - 错误提示和修正建议
 * - 防止无效设置的保存
 *
 * 注意事项:
 * - 作为各功能设置的统一入口
 * - 确保设置修改的原子性
 * - 提供完整的设置管理功能
 * - 支持复杂的设置组合和验证
 */

using System;
using System.Windows.Forms;
using AW = Aspose.Words;
using AsposeWordFormatter.Models;

namespace AsposeWordFormatter
{
    public class SettingsForm : Form
    {
        private readonly TabControl tabControl = new();
        private readonly PageSetupForm pageSetupForm;
        private readonly DocumentPropertiesForm documentPropertiesForm;
        private readonly ParagraphMatchForm paragraphMatchForm;
        private readonly FileNameReplaceForm fileNameReplaceForm;
        private readonly HeaderFooterForm headerFooterForm;
        private readonly DeleteContentForm deleteContentForm;

        private readonly Settings settings;

        public SettingsForm(Settings settings)
        {
            this.settings = settings ?? throw new ArgumentNullException(nameof(settings));

            // Initialize forms
            documentPropertiesForm = new DocumentPropertiesForm(settings.DocumentProperties ?? new DocumentProperties());
            paragraphMatchForm = new ParagraphMatchForm(settings.ParagraphMatchRules);
            fileNameReplaceForm = new FileNameReplaceForm(settings.FileNameReplaceRules);
            headerFooterForm = new HeaderFooterForm(settings.HeaderFooterSettings ?? new Models.HeaderFooterSettings());
            deleteContentForm = new DeleteContentForm(settings.DeleteSettings ?? new Models.DeleteSettings());
            pageSetupForm = new PageSetupForm(settings);

            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "设置";
            this.Size = new System.Drawing.Size(800, 600);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterParent;

            tabControl.Dock = DockStyle.Fill;

            // 页面设置
            var pageSetupTab = new TabPage("页面设置");
            pageSetupForm.Dock = DockStyle.Fill;
            pageSetupTab.Controls.Add(pageSetupForm);
            tabControl.TabPages.Add(pageSetupTab);

            // 文档属性
            var documentPropertiesTab = new TabPage("文档属性");
            documentPropertiesForm.Dock = DockStyle.Fill;
            documentPropertiesTab.Controls.Add(documentPropertiesForm);
            tabControl.TabPages.Add(documentPropertiesTab);

            // 段落匹配规则
            var paragraphMatchTab = new TabPage("段落匹配规则");
            paragraphMatchForm.Dock = DockStyle.Fill;
            paragraphMatchTab.Controls.Add(paragraphMatchForm);
            tabControl.TabPages.Add(paragraphMatchTab);

            // 文件名替换规则
            var fileNameReplaceTab = new TabPage("文件名替换规则");
            fileNameReplaceForm.Dock = DockStyle.Fill;
            fileNameReplaceTab.Controls.Add(fileNameReplaceForm);
            tabControl.TabPages.Add(fileNameReplaceTab);

            // 页眉页脚设置
            var headerFooterTab = new TabPage("页眉页脚设置");
            headerFooterForm.Dock = DockStyle.Fill;
            headerFooterTab.Controls.Add(headerFooterForm);
            tabControl.TabPages.Add(headerFooterTab);

            // 内容删除设置
            var deleteContentTab = new TabPage("内容删除设置");
            deleteContentForm.Dock = DockStyle.Fill;
            deleteContentTab.Controls.Add(deleteContentForm);
            tabControl.TabPages.Add(deleteContentTab);

            // 按钮面板
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Bottom,
                FlowDirection = FlowDirection.RightToLeft,
                Height = 40,
                Padding = new Padding(10)
            };

            var okButton = new Button { Text = "确定", DialogResult = DialogResult.OK };
            var cancelButton = new Button { Text = "取消", DialogResult = DialogResult.Cancel };

            okButton.Click += OkButton_Click;
            cancelButton.Click += CancelButton_Click;

            buttonPanel.Controls.Add(okButton);
            buttonPanel.Controls.Add(cancelButton);

            this.Controls.Add(tabControl);
            this.Controls.Add(buttonPanel);
        }

        private void OkButton_Click(object? sender, EventArgs e)
        {
            if (pageSetupForm.DialogResult == DialogResult.OK &&
                documentPropertiesForm.DialogResult == DialogResult.OK &&
                paragraphMatchForm.DialogResult == DialogResult.OK &&
                fileNameReplaceForm.DialogResult == DialogResult.OK)
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void CancelButton_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 获取当前设置
        /// </summary>
        /// <returns>当前设置对象</returns>
        public Settings GetSettings()
        {
            return settings;
        }
    }
}