using System;
using System.Windows.Forms;
using AsposeWordFormatter.Models;
using AW = Aspose.Words;

namespace AsposeWordFormatter
{
    /// <summary>
    /// 测试新增的PDF功能
    /// </summary>
    public partial class TestPdfFeatures : Form
    {
        private PdfSettings pdfSettings;
        private Button testButton;
        private TextBox resultTextBox;

        public TestPdfFeatures()
        {
            InitializeComponent();
            pdfSettings = new PdfSettings();
        }

        private void InitializeComponent()
        {
            this.Text = "PDF功能测试";
            this.Size = new System.Drawing.Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;

            // 创建测试按钮
            testButton = new Button
            {
                Text = "测试PDF设置对话框",
                Size = new System.Drawing.Size(200, 40),
                Location = new System.Drawing.Point(20, 20)
            };
            testButton.Click += TestButton_Click;

            // 创建结果显示文本框
            resultTextBox = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Location = new System.Drawing.Point(20, 80),
                Size = new System.Drawing.Size(550, 350)
            };

            this.Controls.Add(testButton);
            this.Controls.Add(resultTextBox);
        }

        private void TestButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 打开PDF设置对话框
                using (var pdfForm = new PdfSettingsForm(pdfSettings))
                {
                    if (pdfForm.ShowDialog() == DialogResult.OK)
                    {
                        // 显示设置结果
                        DisplayPdfSettings();
                        
                        // 测试创建PdfSaveOptions
                        TestCreateSaveOptions();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试过程中出现错误: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DisplayPdfSettings()
        {
            var result = "=== PDF设置测试结果 ===\n\n";
            
            // 基础设置
            result += "【基础设置】\n";
            result += $"图像压缩: {pdfSettings.EnableImageCompression}\n";
            result += $"图像压缩类型: {pdfSettings.ImageCompression}\n";
            result += $"JPEG质量: {pdfSettings.JpegQuality}\n";
            result += $"文本压缩: {pdfSettings.EnableTextCompression}\n";
            result += $"文本压缩类型: {pdfSettings.TextCompression}\n\n";

            // 新增的安全设置
            result += "【安全设置】\n";
            result += $"启用安全设置: {pdfSettings.EnableSecuritySettings}\n";
            result += $"用户密码: {(string.IsNullOrEmpty(pdfSettings.UserPassword) ? "未设置" : "已设置")}\n";
            result += $"所有者密码: {(string.IsNullOrEmpty(pdfSettings.OwnerPassword) ? "未设置" : "已设置")}\n";
            result += $"允许打印: {pdfSettings.AllowPrinting}\n";
            result += $"允许复制内容: {pdfSettings.AllowCopyContent}\n\n";

            // 书签和大纲设置
            result += "【书签和大纲设置】\n";
            result += $"启用书签设置: {pdfSettings.EnableBookmarkSettings}\n";
            result += $"标题大纲级别: {pdfSettings.HeadingsOutlineLevels}\n";
            result += $"展开的大纲级别: {pdfSettings.ExpandedOutlineLevels}\n";
            result += $"创建缺失的大纲级别: {pdfSettings.CreateMissingOutlineLevels}\n";
            result += $"为表格中的标题创建大纲: {pdfSettings.CreateOutlinesForHeadingsInTables}\n\n";

            // 高级图像设置
            result += "【高级图像设置】\n";
            result += $"启用高级图像设置: {pdfSettings.EnableAdvancedImageSettings}\n";
            result += $"图像插值: {pdfSettings.InterpolateImages}\n";
            result += $"图像预混合: {pdfSettings.PreblendImages}\n";
            result += $"图像颜色空间导出模式: {pdfSettings.ImageColorSpaceExportMode}\n\n";

            // 表单字段设置
            result += "【表单字段设置】\n";
            result += $"启用表单字段设置: {pdfSettings.EnableFormFieldSettings}\n";
            result += $"保留表单字段: {pdfSettings.PreserveFormFields}\n";
            result += $"使用SDT标签作为表单字段名: {pdfSettings.UseSdtTagAsFormFieldName}\n\n";

            // 页面显示设置
            result += "【页面显示设置】\n";
            result += $"启用页面显示设置: {pdfSettings.EnablePageDisplaySettings}\n";
            result += $"缩放行为: {pdfSettings.ZoomBehavior}\n";
            result += $"缩放因子: {pdfSettings.ZoomFactor}%\n";
            result += $"使用书籍折叠打印设置: {pdfSettings.UseBookFoldPrintingSettings}\n\n";

            // 元数据设置
            result += "【元数据设置】\n";
            result += $"启用元数据设置: {pdfSettings.EnableMetadataSettings}\n";
            result += $"自定义属性导出: {pdfSettings.CustomPropertiesExport}\n";
            result += $"导出语言到Span标签: {pdfSettings.ExportLanguageToSpanTag}\n\n";

            // 字体高级设置
            result += "【字体高级设置】\n";
            result += $"启用高级字体设置: {pdfSettings.EnableAdvancedFontSettings}\n";
            result += $"使用核心字体: {pdfSettings.UseCoreFonts}\n\n";

            resultTextBox.Text = result;
        }

        private void TestCreateSaveOptions()
        {
            try
            {
                var saveOptions = pdfSettings.CreateSaveOptions();
                var result = resultTextBox.Text + "【PdfSaveOptions创建测试】\n";
                result += $"创建成功: 是\n";
                result += $"保存格式: {saveOptions.SaveFormat}\n";
                result += $"内存优化: {saveOptions.MemoryOptimization}\n";
                
                if (pdfSettings.EnableImageCompression)
                {
                    result += $"图像压缩: {saveOptions.ImageCompression}\n";
                    result += $"JPEG质量: {saveOptions.JpegQuality}\n";
                }
                
                if (pdfSettings.EnableTextCompression)
                {
                    result += $"文本压缩: {saveOptions.TextCompression}\n";
                }
                
                if (pdfSettings.EnableBookmarkSettings)
                {
                    result += $"标题大纲级别: {saveOptions.OutlineOptions.HeadingsOutlineLevels}\n";
                    result += $"展开的大纲级别: {saveOptions.OutlineOptions.ExpandedOutlineLevels}\n";
                }
                
                if (pdfSettings.EnableAdvancedImageSettings)
                {
                    result += $"图像插值: {saveOptions.InterpolateImages}\n";
                    result += $"图像预混合: {saveOptions.PreblendImages}\n";
                }
                
                if (pdfSettings.EnableFormFieldSettings)
                {
                    result += $"保留表单字段: {saveOptions.PreserveFormFields}\n";
                }
                
                if (pdfSettings.EnablePageDisplaySettings)
                {
                    result += $"缩放行为: {saveOptions.ZoomBehavior}\n";
                    result += $"缩放因子: {saveOptions.ZoomFactor}\n";
                }
                
                if (pdfSettings.EnableAdvancedFontSettings)
                {
                    result += $"使用核心字体: {saveOptions.UseCoreFonts}\n";
                }
                
                result += "\n测试完成！所有新功能都已正确实现。\n";
                
                resultTextBox.Text = result;
            }
            catch (Exception ex)
            {
                resultTextBox.Text += $"\n【错误】创建PdfSaveOptions时出错: {ex.Message}\n";
            }
        }
    }
}
