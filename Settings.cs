/*
 * ========================================
 * 文件名: Settings.cs
 * 功能描述: 应用程序设置数据模型（根目录版本）
 * ========================================
 *
 * 主要功能:
 * 1. 定义应用程序的核心配置选项
 * 2. 提供重试机制和性能优化设置
 * 3. 支持文件处理的基本配置
 * 4. 包含各功能模块的启用开关
 * 5. 提供配置数据的结构化存储
 *
 * 核心类定义:
 *
 * RetrySettings类:
 * - 重试机制配置（最大重试次数、延迟时间等）
 * - 文件锁定检测和处理设置
 * - 智能内存管理配置
 * - 指数退避算法参数
 *
 * Settings类:
 * - 基础文件处理设置（源目录、输出目录等）
 * - 功能模块启用开关
 * - 性能和并发控制参数
 * - 各功能模块的配置对象引用
 *
 * 重试机制特性:
 * - MaxRetryCount: 最大重试次数
 * - BaseDelayMs: 基础延迟时间
 * - DelayMultiplier: 延迟时间乘数（指数退避）
 * - MaxDelayMs: 最大延迟时间上限
 * - FileLockCheckMaxAttempts: 文件锁定检测次数
 * - EnableSmartMemoryManagement: 智能内存管理
 *
 * 基础设置特性:
 * - 源目录和输出目录配置
 * - 子目录包含和目录结构保持
 * - 文件冲突处理策略
 * - 原始文件处理模式
 * - 批处理和多线程配置
 *
 * 功能模块开关:
 * - EnableWordToPdf: PDF转换功能
 * - EnableFileNameReplace: 文件名替换
 * - EnableGlobalParagraphFormat: 全局段落格式
 * - EnablePageSetup: 页面设置
 * - EnableHeaderFooter: 页眉页脚
 * - EnableDeleteContent: 内容删除
 * - EnableDocumentProperties: 文档属性
 *
 * 注意事项:
 * - 这是项目根目录的Settings.cs文件
 * - 与Models/Settings.cs功能类似但结构不同
 * - 包含详细的XML文档注释
 * - 支持完整的配置管理功能
 */

using System;
using System.Collections.Generic;

namespace AsposeWordFormatter
{
    public class RetrySettings
    {
        /// <summary>
        /// 最大重试次数 - 处理文件失败时的重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 基础延迟时间（毫秒）- 第一次重试前的等待时间
        /// </summary>
        public int BaseDelayMs { get; set; } = 1000;

        /// <summary>
        /// 延迟时间乘数（用于指数退避）- 每次重试后延迟时间的增长倍数
        /// </summary>
        public float DelayMultiplier { get; set; } = 2.0f;

        /// <summary>
        /// 最大延迟时间（毫秒）- 重试间隔的上限
        /// </summary>
        public int MaxDelayMs { get; set; } = 10000;

        /// <summary>
        /// 文件锁定检测的最大尝试次数 - 检查文件是否被锁定的尝试次数
        /// </summary>
        public int FileLockCheckMaxAttempts { get; set; } = 20;

        /// <summary>
        /// 文件锁定检测的延迟时间（毫秒）- 检查文件锁定状态的间隔时间
        /// </summary>
        public int FileLockCheckDelayMs { get; set; } = 500;

        /// <summary>
        /// 文件被占用时的特殊重试次数 - 当文件被其他进程占用时使用的重试次数
        /// </summary>
        public int FileLockRetryCount { get; set; } = 5;

        /// <summary>
        /// 文件被占用时的基础延迟时间（毫秒）- 文件被占用时第一次重试前的等待时间
        /// </summary>
        public int FileLockBaseDelayMs { get; set; } = 2000;

        /// <summary>
        /// 是否启用智能内存管理 - 根据内存使用情况自动触发垃圾回收
        /// </summary>
        public bool EnableSmartMemoryManagement { get; set; } = true;

        /// <summary>
        /// 内存使用阈值（MB）- 超过此阈值时触发垃圾回收
        /// </summary>
        public int MemoryThresholdMB { get; set; } = 500;
    }

    public class Settings
    {
        /// <summary>
        /// 源目录
        /// </summary>
        public string SourceDirectory { get; set; } = string.Empty;

        /// <summary>
        /// 输出目录
        /// </summary>
        public string OutputDirectory { get; set; } = string.Empty;

        /// <summary>
        /// 是否包含子目录
        /// </summary>
        public bool IncludeSubdirectories { get; set; } = true;

        /// <summary>
        /// 是否保持目录结构
        /// </summary>
        public bool KeepDirectoryStructure { get; set; } = true;

        /// <summary>
        /// 是否移动文件（而不是复制）
        /// </summary>
        public bool MoveFiles { get; set; } = false;

        /// <summary>
        /// 文件冲突处理方式（跳过/重命名/覆盖）
        /// </summary>
        public string ConflictHandling { get; set; } = "重命名";

        /// <summary>
        /// 是否处理原始文件（不创建副本）
        /// </summary>
        public bool ProcessOriginalFiles { get; set; } = false;

        /// <summary>
        /// 是否启用Word转PDF
        /// </summary>
        public bool EnableWordToPdf { get; set; } = false;

        /// <summary>
        /// 是否启用文件名替换
        /// </summary>
        public bool EnableFileNameReplace { get; set; } = false;

        /// <summary>
        /// 文件名替换规则
        /// </summary>
        public List<FileNameRule> FileNameRules { get; set; } = new List<FileNameRule>();

        /// <summary>
        /// 是否启用全文段落格式设置
        /// </summary>
        public bool EnableGlobalParagraphFormat { get; set; } = false;

        /// <summary>
        /// 全文段落格式设置
        /// </summary>
        public GlobalParagraphFormat? GlobalParagraphFormat { get; set; }

        /// <summary>
        /// 是否启用页面设置
        /// </summary>
        public bool EnablePageSetup { get; set; } = false;

        /// <summary>
        /// 页面设置
        /// </summary>
        public PageSetup? PageSetup { get; set; }

        /// <summary>
        /// 是否启用页眉页脚设置
        /// </summary>
        public bool EnableHeaderFooter { get; set; } = false;

        /// <summary>
        /// 页眉页脚设置
        /// </summary>
        public HeaderFooterSettings? HeaderFooterSettings { get; set; }

        /// <summary>
        /// 是否启用删除内容设置
        /// </summary>
        public bool EnableDeleteContent { get; set; } = false;

        /// <summary>
        /// 删除内容设置
        /// </summary>
        public DeleteSettings? DeleteSettings { get; set; }

        /// <summary>
        /// 是否启用文档属性设置
        /// </summary>
        public bool EnableDocumentProperties { get; set; } = false;

        /// <summary>
        /// 文档属性设置
        /// </summary>
        public DocumentProperties? DocumentProperties { get; set; }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 批处理大小
        /// </summary>
        public int BatchSize { get; set; } = 10;

        /// <summary>
        /// 最大线程数
        /// </summary>
        public int MaxThreads { get; set; } = 1;

        /// <summary>
        /// 重试设置
        /// </summary>
        public RetrySettings RetrySettings { get; set; } = new RetrySettings();

        /// <summary>
        /// 文件过滤器
        /// </summary>
        public List<string> FileFilters { get; set; } = new List<string> { "*.doc", "*.docx" };
    }
}