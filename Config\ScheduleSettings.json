// 定时任务设置
// 此文件控制文档处理的自动执行计划
{
  // 基本设置
  "Enabled": false, // 是否启用定时任务
  "ScheduleMode": 1, // 调度模式：0=一次性, 1=定时（每天固定时间）, 2=间隔（每隔一段时间）
  
  // 一次性任务设置
  "OneTimeRunTime": "2023-12-31T23:59:59", // 一次性任务的运行时间
  
  // 定时任务设置（每天固定时间）
  "DailyRunTime": "03:00:00", // 每天运行时间（24小时制）
  "RunDays": [ // 运行的星期几：0=周日, 1=周一, ..., 6=周六
    1, 2, 3, 4, 5
  ],
  
  // 间隔任务设置
  "IntervalHours": 6, // 间隔小时数
  "IntervalMinutes": 0, // 间隔分钟数
  "StartFromTime": "08:00:00", // 开始时间
  "EndAtTime": "18:00:00", // 结束时间
  
  // 运行限制
  "UseLimitedRuns": false, // 是否限制运行次数
  "MaxRunCount": 10, // 最大运行次数
  "RunCount": 0, // 已运行次数
  
  // 到期设置
  "UseExpirationTime": false, // 是否使用过期时间
  "ExpirationTime": "2024-12-31T23:59:59", // 过期时间
  
  // 高级设置
  "LastRunTime": null, // 上次运行时间，null表示从未运行
  "NextRunTime": null, // 下次运行时间，由程序自动计算
  "SkipIfBusy": true, // 如果上一次任务还在运行，是否跳过本次运行
  "WaitForIdleTime": 5, // 等待系统空闲的分钟数
  "RunOnlyWhenUserLoggedIn": false, // 仅在用户登录时运行
  
  // 通知设置
  "ShowNotificationBeforeRun": true, // 运行前是否显示通知
  "NotificationAdvanceTime": 60, // 提前通知秒数
  "ShowCompletionNotification": true, // 完成后是否显示通知
  
  // 运行条件
  "RunOnlyOnBattery": false, // 仅在使用电池时运行
  "RunOnlyWhenIdle": false, // 仅在系统空闲时运行
  "MinimumBatteryPercentage": 20, // 最低电池电量百分比
  "MinimumDiskSpaceGB": 1.0, // 最低可用磁盘空间（GB）
  
  // 运行记录
  "LastRunSuccessful": true, // 上次运行是否成功
  "LastRunDuration": 0, // 上次运行持续时间（秒）
  "TotalRunTime": 0, // 总运行时间（秒）
  "RunHistory": [] // 运行历史记录
} 