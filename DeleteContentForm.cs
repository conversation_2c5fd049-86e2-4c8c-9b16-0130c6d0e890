/*
 * ========================================
 * 文件名: DeleteContentForm.cs
 * 功能描述: 内容删除配置窗体
 * ========================================
 *
 * 主要功能:
 * 1. 图片删除设置（按类型、大小、位置）
 * 2. 联系信息删除（电话、邮箱、网址等）
 * 3. 特定文本内容删除
 * 4. 空白内容清理（空页、空段落、空行）
 * 5. 格式和样式清理
 * 6. 文档元素删除（页眉页脚、批注等）
 *
 * 界面结构:
 * - 图片删除标签页：各种图片删除选项
 * - 联系信息标签页：电话、邮箱、网址删除
 * - 特定文本标签页：自定义文本删除规则
 * - 空白清理标签页：空白内容清理选项
 * - 格式清理标签页：格式和样式清理
 * - 其他内容标签页：文档元素删除
 *
 * 图片删除选项:
 * - DeleteSpecificImages: 删除指定路径的图片
 * - DeleteSizedImages: 按尺寸删除图片
 * - DeleteAllImages: 删除所有图片
 * - DeleteTrailingImages: 删除尾部图片
 * - DeleteBackgroundImages: 删除背景图片
 *
 * 联系信息删除:
 * - DeleteMobileNumbers: 删除手机号码
 * - DeletePhoneNumbers: 删除电话号码
 * - DeleteEmails: 删除邮箱地址
 * - DeleteUrls: 删除网址链接
 * - DeleteHyperlinks: 删除超链接
 *
 * 空白内容清理:
 * - DeleteEmptyPages: 删除空白页
 * - DeleteEmptyParagraphs: 删除空段落
 * - DeleteEmptyLines: 删除空行
 * - DeleteParagraphLeadingSpaces: 删除段落前导空格
 * - DeleteParagraphTrailingSpaces: 删除段落尾随空格
 *
 * 格式清理选项:
 * - DeleteHiddenText: 删除隐藏文本
 * - DeleteRevisionMarks: 删除修订标记
 * - DeleteCompareResults: 删除比较结果
 * - DeleteDocumentVersions: 删除文档版本信息
 * - DeleteDocumentTheme: 删除文档主题
 * - DeleteDocumentStyles: 删除文档样式
 *
 * 数据模型:
 * - DeleteSettings: 删除设置数据模型
 * - 支持所有删除选项的配置
 * - 包含尺寸范围和路径列表
 *
 * 注意事项:
 * - 删除操作不可逆，需要谨慎设置
 * - 图片尺寸过滤支持最小和最大值
 * - 特定文本删除支持正则表达式
 * - 提供预览和确认机制
 */

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System.Linq;
using AsposeWordFormatter.Models;
using System.IO;

namespace AsposeWordFormatter
{
    public class DeleteContentForm : Form
    {
        private readonly DeleteSettings settings;

        // 图片删除相关控件
        private CheckBox deleteSpecificImagesCheck = null!;
        private CheckBox deleteSizedImagesCheck = null!;
        private CheckBox deleteAllImagesCheck = null!;
        private CheckBox deleteTrailingImagesCheck = null!;
        private CheckBox deleteBackgroundImagesCheck = null!;
        private Label imageSizeLabel = null!;
        private NumericUpDown minImageWidthNumeric = null!;
        private NumericUpDown minImageHeightNumeric = null!;
        private NumericUpDown maxImageWidthNumeric = null!;
        private NumericUpDown maxImageHeightNumeric = null!;

        // 添加图片选择相关控件
        private Button selectImagesButton = null!;
        private ListBox selectedImagesListBox = null!;
        private Label selectedImagesLabel = null!;

        // 删除联系方式相关控件
        private CheckBox deleteMobileNumbersCheck = null!;
        private CheckBox deletePhoneNumbersCheck = null!;
        private CheckBox deleteEmailsCheck = null!;
        private CheckBox deleteUrlsCheck = null!;
        private CheckBox deleteHyperlinksCheck = null!;

        // 删除包含指定字符区域相关控件
        private CheckBox deleteParagraphsWithTextCheck = null!;
        private CheckBox deleteTextBoxesWithTextCheck = null!;
        private CheckBox deleteTablesWithTextCheck = null!;
        private Label specificTextLabel = null!;
        private TextBox specificTextBox = null!;

        // 删除空白内容相关控件
        private CheckBox deleteEmptyPagesCheck = null!;
        private CheckBox deleteEmptyParagraphsCheck = null!;
        private CheckBox deleteEmptyLinesCheck = null!;

        // 新增的控件变量
        private CheckBox deleteParagraphLeadingSpacesCheck = null!;
        private CheckBox deleteParagraphTrailingSpacesCheck = null!;
        private CheckBox deleteParagraphLeadingInvisibleCharsCheck = null!;
        private CheckBox deleteParagraphTrailingInvisibleCharsCheck = null!;

        // 删除水印相关控件
        private CheckBox deleteWatermarksCheck = null!;
        private CheckBox deleteHeaderWatermarksCheck = null!;
        private CheckBox deleteFooterWatermarksCheck = null!;

        // 删除格式相关控件
        private CheckBox deleteFormatsCheck = null!;
        private CheckBox deleteFontFormatsCheck = null!;
        private CheckBox deleteParagraphFormatsCheck = null!;
        private CheckBox deleteTableFormatsCheck = null!;
        private CheckBox deleteListFormatsCheck = null!;
        private CheckBox deleteHeaderFooterFormatsCheck = null!;
        private CheckBox preserveBoldCheck = null!;
        private CheckBox preserveItalicCheck = null!;
        private CheckBox preserveUnderlineCheck = null!;
        private CheckBox preserveHyperlinksCheck = null!;
        private Label defaultFontLabel = null!;
        private ComboBox defaultFontCombo = null!;
        private Label defaultFontSizeLabel = null!;
        private NumericUpDown defaultFontSizeNumeric = null!;

        // 换行符和段落标记相关控件
        private CheckBox deleteLineBreaksCheck = null!;
        private CheckBox deleteParagraphMarksCheck = null!;
        private CheckBox deletePageBreaksCheck = null!;
        private CheckBox deleteSectionBreaksCheck = null!;
        private CheckBox deleteCommentsCheck = null!;
        private CheckBox deleteFootnotesCheck = null!;
        private CheckBox replaceLineBreaksWithParagraphMarksCheck = null!;
        private CheckBox mergeMultipleLineBreaksCheck = null!;
        private CheckBox mergeMultipleParagraphMarksCheck = null!;

        // 文档保护相关控件
        private CheckBox forceRemoveEditingPasswordCheck = null!;
        private CheckBox forceRemoveContentProtectionCheck = null!;
        private CheckBox forceAcceptAllRevisionsCheck = null!;
        private CheckBox forceRemoveDigitalSignaturesCheck = null!;

        // 文档元素删除相关控件
        private CheckBox deleteBookmarksCheck = null!;
        private CheckBox deleteFieldsCheck = null!;
        private CheckBox deleteTableOfContentsCheck = null!;
        private CheckBox deleteIndexCheck = null!;
        private CheckBox deleteCrossReferencesCheck = null!;
        private CheckBox deleteFormFieldsCheck = null!;
        private CheckBox deleteSmartArtCheck = null!;
        private CheckBox deleteChartsCheck = null!;
        private CheckBox deleteOleObjectsCheck = null!;
        private CheckBox deleteActiveXControlsCheck = null!;

        // 高级格式删除相关控件
        private CheckBox deleteCharacterSpacingCheck = null!;
        private CheckBox deleteCharacterScalingCheck = null!;
        private CheckBox deleteCharacterPositionCheck = null!;
        private CheckBox deleteTextEffectsCheck = null!;
        private CheckBox deleteParagraphBordersCheck = null!;
        private CheckBox deleteParagraphShadingCheck = null!;
        private CheckBox deleteCellBordersCheck = null!;
        private CheckBox deleteCellShadingCheck = null!;
        private CheckBox deleteCellMergingCheck = null!;
        private CheckBox deleteCellSplittingCheck = null!;

        // 文档结构删除相关控件
        private CheckBox deleteColumnsCheck = null!;
        private CheckBox deleteTextBoxesCheck = null!;
        private CheckBox deleteShapesCheck = null!;
        private CheckBox deleteWordArtCheck = null!;
        private CheckBox deleteMarginsCheck = null!;
        private CheckBox deletePageBordersCheck = null!;
        private CheckBox deletePageBackgroundCheck = null!;

        // 文档属性删除相关控件
        private CheckBox deleteDocumentPropertiesCheck = null!;
        private CheckBox deleteCustomPropertiesCheck = null!;
        private CheckBox deleteDocumentVariablesCheck = null!;
        private CheckBox deleteDocumentStatisticsCheck = null!;

        // 其他内容删除相关控件
        private CheckBox deleteHiddenTextCheck = null!;
        private CheckBox deleteRevisionMarksCheck = null!;
        private CheckBox deleteCompareResultsCheck = null!;
        private CheckBox deleteDocumentVersionsCheck = null!;
        private CheckBox deleteDocumentThemeCheck = null!;
        private CheckBox deleteDocumentStylesCheck = null!;

        // 删除文档相关控件
        private CheckBox enableDocumentDeletionCheck = null!;
        private CheckBox checkFileNameLengthCheck = null!;
        private NumericUpDown minFileNameLengthNumeric = null!;
        private NumericUpDown maxFileNameLengthNumeric = null!;
        private CheckBox checkDocumentSizeCheck = null!;
        private NumericUpDown minDocumentSizeNumeric = null!;
        private NumericUpDown maxDocumentSizeNumeric = null!;
        private ComboBox minDocumentSizeUnitCombo = null!;
        private ComboBox maxDocumentSizeUnitCombo = null!;
        private CheckBox checkContentCharacterCountCheck = null!;
        private NumericUpDown minContentCharacterCountNumeric = null!;
        private NumericUpDown maxContentCharacterCountNumeric = null!;
        private CheckBox checkDocumentPageCountCheck = null!;
        private NumericUpDown minDocumentPageCountNumeric = null!;
        private NumericUpDown maxDocumentPageCountNumeric = null!;
        private CheckBox checkFileNameIllegalWordsCheck = null!;
        private Button editFileNameIllegalWordsButton = null!;
        private CheckBox checkContentIllegalWordsCheck = null!;
        private Button editContentIllegalWordsButton = null!;

        // 按钮
        private Button okButton = null!;
        private Button cancelButton = null!;
        private Button selectAllButton = null!;
        private Button unselectAllButton = null!;

        // TabControl 引用，用于获取当前选中的标签页
        private TabControl tabControl = null!;

        public DeleteContentForm(DeleteSettings settings)
        {
            this.settings = settings;
            InitializeComponent();
            LoadSettings();
        }

        private void InitializeComponent()
        {
            this.Text = "删除内容设置";
            this.Size = new Size(700, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(10)
            };
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 90));
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 10));

            // 创建一个面板容纳TabControl
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            // 创建选项卡控件
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                TabStop = false,
                Multiline = true,  // 允许多行显示标签页
                SizeMode = TabSizeMode.FillToRight,  // 使用填充模式，让标签页填充整个宽度
                Padding = new Point(5, 5),  // 减小内边距
                ItemSize = new Size(0, 40)  // 设置标签页高度为40像素，宽度由SizeMode自动计算
            };

            // 删除文档选项卡（作为第一个标签页）
            var deleteDocumentTab = new TabPage("删除文档");
            deleteDocumentTab.Controls.Add(CreateDeleteDocumentGroup());

            // 图片删除选项卡
            var imagesTab = new TabPage("图片删除设置");
            imagesTab.Controls.Add(CreateImagesGroup());

            // 手机邮箱URL删除选项卡
            var contactsTab = new TabPage("联系方式删除设置");
            contactsTab.Controls.Add(CreateContactsGroup());

            // 文本删除选项卡
            var textTab = new TabPage("文本删除设置");
            textTab.Controls.Add(CreateTextGroup());

            // 空白内容删除选项卡
            var emptyTab = new TabPage("内容删除设置");
            emptyTab.Controls.Add(CreateEmptyGroup());

            // 水印删除选项卡
            var watermarkTab = new TabPage("水印删除设置");
            watermarkTab.Controls.Add(CreateWatermarkGroup());

            // 格式删除选项卡
            var formatsTab = new TabPage("删除全文格式");
            formatsTab.Controls.Add(CreateFormatsGroup());

            // 换行符和段落标记选项卡
            var lineBreakTab = new TabPage("换行符和段落标记");
            lineBreakTab.Controls.Add(CreateLineBreakGroup());

            // 文档保护选项卡
            var protectionTab = new TabPage("文档保护");
            protectionTab.Controls.Add(CreateProtectionGroup());

            // 新增选项卡
            var documentElementsTab = new TabPage("文档元素");
            documentElementsTab.Controls.Add(CreateDocumentElementsGroup());

            var advancedFormatsTab = new TabPage("高级格式");
            advancedFormatsTab.Controls.Add(CreateAdvancedFormatsGroup());

            var documentStructureTab = new TabPage("文档结构");
            documentStructureTab.Controls.Add(CreateDocumentStructureGroup());

            var documentPropertiesTab = new TabPage("文档属性");
            documentPropertiesTab.Controls.Add(CreateDocumentPropertiesGroup());

            // 添加选项卡到选项卡控件（删除文档作为第一个标签页）
            tabControl.Controls.Add(deleteDocumentTab);
            tabControl.Controls.Add(imagesTab);
            tabControl.Controls.Add(contactsTab);
            tabControl.Controls.Add(textTab);
            tabControl.Controls.Add(emptyTab);
            tabControl.Controls.Add(watermarkTab);
            tabControl.Controls.Add(formatsTab);
            tabControl.Controls.Add(lineBreakTab);
            tabControl.Controls.Add(protectionTab);
            tabControl.Controls.Add(documentElementsTab);
            tabControl.Controls.Add(advancedFormatsTab);
            tabControl.Controls.Add(documentStructureTab);
            tabControl.Controls.Add(documentPropertiesTab);

            // 添加选项卡控件到内容面板
            contentPanel.Controls.Add(tabControl);
            mainLayout.Controls.Add(contentPanel, 0, 0);

            // 创建按钮面板
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                AutoSize = true,
                Padding = new Padding(5)
            };

            okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK,
                AutoSize = true
            };
            okButton.Click += OkButton_Click;

            cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                AutoSize = true
            };

            // 添加全选按钮
            selectAllButton = new Button
            {
                Text = "全选",
                AutoSize = true,
                Margin = new Padding(5, 0, 0, 0)
            };
            selectAllButton.Click += SelectAllButton_Click;

            // 添加取消全选按钮
            unselectAllButton = new Button
            {
                Text = "取消全选",
                AutoSize = true,
                Margin = new Padding(5, 0, 0, 0)
            };
            unselectAllButton.Click += UnselectAllButton_Click;

            buttonPanel.Controls.Add(cancelButton);
            buttonPanel.Controls.Add(okButton);
            buttonPanel.Controls.Add(selectAllButton);
            buttonPanel.Controls.Add(unselectAllButton);
            mainLayout.Controls.Add(buttonPanel, 0, 1);

            this.Controls.Add(mainLayout);
            this.AcceptButton = okButton;
            this.CancelButton = cancelButton;
        }

        private GroupBox CreateDeleteDocumentGroup()
        {
            var group = new GroupBox
            {
                Text = "删除文档设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 创建滚动面板
            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 15,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 主开关
            enableDocumentDeletionCheck = new CheckBox
            {
                Text = "启用删除文档功能",
                AutoSize = true,
                Font = new Font(Font, FontStyle.Bold)
            };
            layout.Controls.Add(enableDocumentDeletionCheck, 0, 0);

            // 添加说明标签
            var descriptionLabel = new Label
            {
                Text = "注意：此功能将在处理前判断文档是否符合条件，不符合条件的文档将被删除。\n遵循主面板的文件处理模式（直接处理源文件、复制、移动）。",
                AutoSize = true,
                ForeColor = Color.Red,
                Margin = new Padding(0, 5, 0, 10)
            };
            layout.Controls.Add(descriptionLabel, 0, 1);

            // 1. 文件名长度判断
            checkFileNameLengthCheck = new CheckBox
            {
                Text = "判断文件名长度（不含扩展名）",
                AutoSize = true,
                Enabled = false
            };
            layout.Controls.Add(checkFileNameLengthCheck, 0, 2);

            var fileNameLengthPanel = new TableLayoutPanel
            {
                ColumnCount = 6,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 10)
            };

            fileNameLengthPanel.Controls.Add(new Label { Text = "最小长度:", AutoSize = true }, 0, 0);
            minFileNameLengthNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 255,
                Value = 1,
                Width = 60,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };
            fileNameLengthPanel.Controls.Add(minFileNameLengthNumeric, 1, 0);
            fileNameLengthPanel.Controls.Add(new Label { Text = "字符", AutoSize = true, Margin = new Padding(2, 0, 0, 0) }, 2, 0);

            fileNameLengthPanel.Controls.Add(new Label { Text = "最大长度:", AutoSize = true, Margin = new Padding(10, 0, 0, 0) }, 3, 0);
            maxFileNameLengthNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 255,
                Value = 255,
                Width = 60,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };
            fileNameLengthPanel.Controls.Add(maxFileNameLengthNumeric, 4, 0);
            fileNameLengthPanel.Controls.Add(new Label { Text = "字符", AutoSize = true, Margin = new Padding(2, 0, 0, 0) }, 5, 0);

            layout.Controls.Add(fileNameLengthPanel, 0, 3);

            // 2. 文档大小判断
            checkDocumentSizeCheck = new CheckBox
            {
                Text = "判断文档大小",
                AutoSize = true,
                Enabled = false
            };
            layout.Controls.Add(checkDocumentSizeCheck, 0, 4);

            var documentSizePanel = new TableLayoutPanel
            {
                ColumnCount = 8,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 10)
            };

            documentSizePanel.Controls.Add(new Label { Text = "最小大小:", AutoSize = true }, 0, 0);
            minDocumentSizeNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 999999,
                Value = 0,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };
            documentSizePanel.Controls.Add(minDocumentSizeNumeric, 1, 0);

            minDocumentSizeUnitCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 50,
                Enabled = false
            };
            minDocumentSizeUnitCombo.Items.AddRange(new[] { "B", "KB", "MB" });
            minDocumentSizeUnitCombo.SelectedIndex = 1; // 默认选择KB
            documentSizePanel.Controls.Add(minDocumentSizeUnitCombo, 2, 0);

            documentSizePanel.Controls.Add(new Label { Text = "最大大小:", AutoSize = true, Margin = new Padding(10, 0, 0, 0) }, 3, 0);
            maxDocumentSizeNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 999999,
                Value = 1024,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };
            documentSizePanel.Controls.Add(maxDocumentSizeNumeric, 4, 0);

            maxDocumentSizeUnitCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 50,
                Enabled = false
            };
            maxDocumentSizeUnitCombo.Items.AddRange(new[] { "B", "KB", "MB" });
            maxDocumentSizeUnitCombo.SelectedIndex = 1; // 默认选择KB
            documentSizePanel.Controls.Add(maxDocumentSizeUnitCombo, 5, 0);

            layout.Controls.Add(documentSizePanel, 0, 5);

            // 3. 文档内容字符总数判断
            checkContentCharacterCountCheck = new CheckBox
            {
                Text = "判断文档内容字符总数",
                AutoSize = true,
                Enabled = false
            };
            layout.Controls.Add(checkContentCharacterCountCheck, 0, 6);

            var contentCharacterCountPanel = new TableLayoutPanel
            {
                ColumnCount = 6,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 10)
            };

            contentCharacterCountPanel.Controls.Add(new Label { Text = "最小字符数:", AutoSize = true }, 0, 0);
            minContentCharacterCountNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 999999,
                Value = 0,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };
            contentCharacterCountPanel.Controls.Add(minContentCharacterCountNumeric, 1, 0);
            contentCharacterCountPanel.Controls.Add(new Label { Text = "字符", AutoSize = true, Margin = new Padding(2, 0, 0, 0) }, 2, 0);

            contentCharacterCountPanel.Controls.Add(new Label { Text = "最大字符数:", AutoSize = true, Margin = new Padding(10, 0, 0, 0) }, 3, 0);
            maxContentCharacterCountNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 999999,
                Value = 10000,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };
            contentCharacterCountPanel.Controls.Add(maxContentCharacterCountNumeric, 4, 0);
            contentCharacterCountPanel.Controls.Add(new Label { Text = "字符", AutoSize = true, Margin = new Padding(2, 0, 0, 0) }, 5, 0);

            layout.Controls.Add(contentCharacterCountPanel, 0, 7);

            // 4. 文档页数判断
            checkDocumentPageCountCheck = new CheckBox
            {
                Text = "判断文档页数",
                AutoSize = true,
                Enabled = false
            };
            layout.Controls.Add(checkDocumentPageCountCheck, 0, 8);

            var documentPageCountPanel = new TableLayoutPanel
            {
                ColumnCount = 6,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 10)
            };

            documentPageCountPanel.Controls.Add(new Label { Text = "最小页数:", AutoSize = true }, 0, 0);
            minDocumentPageCountNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 9999,
                Value = 2,
                Width = 60,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };
            documentPageCountPanel.Controls.Add(minDocumentPageCountNumeric, 1, 0);
            documentPageCountPanel.Controls.Add(new Label { Text = "页", AutoSize = true, Margin = new Padding(2, 0, 0, 0) }, 2, 0);

            documentPageCountPanel.Controls.Add(new Label { Text = "最大页数:", AutoSize = true, Margin = new Padding(10, 0, 0, 0) }, 3, 0);
            maxDocumentPageCountNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 9999,
                Value = 200,
                Width = 60,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };
            documentPageCountPanel.Controls.Add(maxDocumentPageCountNumeric, 4, 0);
            documentPageCountPanel.Controls.Add(new Label { Text = "页", AutoSize = true, Margin = new Padding(2, 0, 0, 0) }, 5, 0);

            layout.Controls.Add(documentPageCountPanel, 0, 9);

            // 5. 文件名非法词判断
            checkFileNameIllegalWordsCheck = new CheckBox
            {
                Text = "判断文件名是否包含非法词",
                AutoSize = true,
                Enabled = false
            };
            layout.Controls.Add(checkFileNameIllegalWordsCheck, 0, 10);

            var fileNameIllegalWordsPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 10)
            };

            var fileNameIllegalWordsLabel = new Label
            {
                Text = "如果文件名包含任意一个非法词，则删除该文档",
                AutoSize = true
            };
            fileNameIllegalWordsPanel.Controls.Add(fileNameIllegalWordsLabel, 0, 0);

            editFileNameIllegalWordsButton = new Button
            {
                Text = "编辑非法词",
                AutoSize = true,
                Enabled = false
            };
            editFileNameIllegalWordsButton.Click += EditFileNameIllegalWordsButton_Click;
            fileNameIllegalWordsPanel.Controls.Add(editFileNameIllegalWordsButton, 1, 0);

            layout.Controls.Add(fileNameIllegalWordsPanel, 0, 11);

            // 6. 文档内容非法词判断
            checkContentIllegalWordsCheck = new CheckBox
            {
                Text = "判断文档内容是否包含非法词",
                AutoSize = true,
                Enabled = false
            };
            layout.Controls.Add(checkContentIllegalWordsCheck, 0, 12);

            var contentIllegalWordsPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 10)
            };

            var contentIllegalWordsLabel = new Label
            {
                Text = "如果文档内容包含任意一个非法词，则删除该文档",
                AutoSize = true
            };
            contentIllegalWordsPanel.Controls.Add(contentIllegalWordsLabel, 0, 0);

            editContentIllegalWordsButton = new Button
            {
                Text = "编辑非法词",
                AutoSize = true,
                Enabled = false
            };
            editContentIllegalWordsButton.Click += EditContentIllegalWordsButton_Click;
            contentIllegalWordsPanel.Controls.Add(editContentIllegalWordsButton, 1, 0);

            layout.Controls.Add(contentIllegalWordsPanel, 0, 13);

            // 绑定主开关事件
            enableDocumentDeletionCheck.CheckedChanged += (s, e) =>
            {
                bool enabled = enableDocumentDeletionCheck.Checked;
                checkFileNameLengthCheck.Enabled = enabled;
                checkDocumentSizeCheck.Enabled = enabled;
                checkContentCharacterCountCheck.Enabled = enabled;
                checkDocumentPageCountCheck.Enabled = enabled;
                checkFileNameIllegalWordsCheck.Enabled = enabled;
                checkContentIllegalWordsCheck.Enabled = enabled;

                // 如果主开关关闭，同时关闭所有子开关
                if (!enabled)
                {
                    checkFileNameLengthCheck.Checked = false;
                    checkDocumentSizeCheck.Checked = false;
                    checkContentCharacterCountCheck.Checked = false;
                    checkDocumentPageCountCheck.Checked = false;
                    checkFileNameIllegalWordsCheck.Checked = false;
                    checkContentIllegalWordsCheck.Checked = false;
                }
            };

            // 绑定子开关事件
            checkFileNameLengthCheck.CheckedChanged += (s, e) =>
            {
                bool enabled = checkFileNameLengthCheck.Checked;
                minFileNameLengthNumeric.Enabled = enabled;
                maxFileNameLengthNumeric.Enabled = enabled;
            };

            checkDocumentSizeCheck.CheckedChanged += (s, e) =>
            {
                bool enabled = checkDocumentSizeCheck.Checked;
                minDocumentSizeNumeric.Enabled = enabled;
                maxDocumentSizeNumeric.Enabled = enabled;
                minDocumentSizeUnitCombo.Enabled = enabled;
                maxDocumentSizeUnitCombo.Enabled = enabled;
            };

            checkContentCharacterCountCheck.CheckedChanged += (s, e) =>
            {
                bool enabled = checkContentCharacterCountCheck.Checked;
                minContentCharacterCountNumeric.Enabled = enabled;
                maxContentCharacterCountNumeric.Enabled = enabled;
            };

            checkDocumentPageCountCheck.CheckedChanged += (s, e) =>
            {
                bool enabled = checkDocumentPageCountCheck.Checked;
                minDocumentPageCountNumeric.Enabled = enabled;
                maxDocumentPageCountNumeric.Enabled = enabled;
            };

            checkFileNameIllegalWordsCheck.CheckedChanged += (s, e) =>
            {
                editFileNameIllegalWordsButton.Enabled = checkFileNameIllegalWordsCheck.Checked;
            };

            checkContentIllegalWordsCheck.CheckedChanged += (s, e) =>
            {
                editContentIllegalWordsButton.Enabled = checkContentIllegalWordsCheck.Checked;
            };

            scrollPanel.Controls.Add(layout);
            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateImagesGroup()
        {
            var group = new GroupBox
            {
                Text = "删除图片",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 创建一个滚动面板，包含所有图片删除选项
            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 9,  // 增加行数以容纳新控件
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 删除选项
            deleteSpecificImagesCheck = new CheckBox
            {
                Text = "删除指定图片",
                AutoSize = true
            };
            var deleteSpecificImagesToolTip = new ToolTip();
            deleteSpecificImagesToolTip.SetToolTip(deleteSpecificImagesCheck, "删除文档中与选定图片匹配的图片。使用Shape.ImageData.ImageBytes比较图片内容或使用Shape.ImageData.ToImage()进行视觉比较。");
            layout.Controls.Add(deleteSpecificImagesCheck, 0, 0);

            // 添加图片选择相关控件
            var specificImagesPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 10)
            };

            selectedImagesLabel = new Label
            {
                Text = "已选择的图片:",
                AutoSize = true,
                Enabled = false
            };
            specificImagesPanel.Controls.Add(selectedImagesLabel, 0, 0);
            specificImagesPanel.SetColumnSpan(selectedImagesLabel, 2);

            selectedImagesListBox = new ListBox
            {
                Width = 250,
                Height = 80,
                Enabled = false
            };
            specificImagesPanel.Controls.Add(selectedImagesListBox, 0, 1);

            // 创建一个按钮面板
            var buttonPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.TopDown,
                WrapContents = false
            };

            selectImagesButton = new Button
            {
                Text = "选择图片",
                AutoSize = true,
                Enabled = false,
                Margin = new Padding(0, 0, 0, 5)  // 添加底部间距
            };
            selectImagesButton.Click += (s, e) =>
            {
                using (var dialog = new OpenFileDialog())
                {
                    dialog.Multiselect = true;
                    dialog.Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff;*.webp|所有文件|*.*";
                    dialog.Title = "选择要删除的图片";

                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        foreach (var file in dialog.FileNames)
                        {
                            if (!settings.SpecificImagePaths.Contains(file))
                            {
                                try
                                {
                                    // 验证图片有效性
                                    using (var img = Image.FromFile(file))
                                    {
                                        // 图片有效，添加路径
                                        settings.SpecificImagePaths.Add(file);
                                        selectedImagesListBox.Items.Add(Path.GetFileName(file));
                                    }
                                }
                                catch (Exception ex)
                                {
                                    MessageBox.Show($"无法加载图片 {Path.GetFileName(file)}: {ex.Message}",
                                        "图片加载错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                        }
                    }
                }
            };
            buttonPanel.Controls.Add(selectImagesButton);

            // 添加清除选择按钮
            var clearButton = new Button
            {
                Text = "清除选择的图片",
                AutoSize = true,
                Enabled = false
            };
            clearButton.Click += (s, e) =>
            {
                settings.SpecificImagePaths.Clear();
                selectedImagesListBox.Items.Clear();
            };
            buttonPanel.Controls.Add(clearButton);

            specificImagesPanel.Controls.Add(buttonPanel, 1, 1);

            layout.Controls.Add(specificImagesPanel, 0, 1);

            // 绑定删除指定图片复选框的事件
            deleteSpecificImagesCheck.CheckedChanged += (s, e) =>
            {
                var enabled = deleteSpecificImagesCheck.Checked;
                selectImagesButton.Enabled = enabled;
                selectedImagesListBox.Enabled = enabled;
                selectedImagesLabel.Enabled = enabled;
                clearButton.Enabled = enabled;
            };

            deleteSizedImagesCheck = new CheckBox
            {
                Text = "删除指定大小图片",
                AutoSize = true
            };
            var deleteSizedImagesToolTip = new ToolTip();
            deleteSizedImagesToolTip.SetToolTip(deleteSizedImagesCheck, "根据图片尺寸删除图片。使用Shape.Width和Shape.Height属性（单位：磅点）获取图片大小。1磅点≈1/72英寸。");
            layout.Controls.Add(deleteSizedImagesCheck, 0, 2);

            // 尺寸设置面板
            var sizePanel = new TableLayoutPanel
            {
                ColumnCount = 6,
                RowCount = 2,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 10)
            };

            imageSizeLabel = new Label
            {
                Text = "图片大小范围（单位：磅点，1磅点≈1/72英寸）:",
                AutoSize = true,
                Enabled = false
            };
            sizePanel.Controls.Add(imageSizeLabel, 0, 0);
            sizePanel.SetColumnSpan(imageSizeLabel, 6);

            // 最小宽度
            sizePanel.Controls.Add(new Label { Text = "最小宽度(磅):", AutoSize = true, Enabled = false }, 0, 1);
            minImageWidthNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 10000,
                Value = 0,
                Width = 70,
                Enabled = false,
                DecimalPlaces = 1,
                Increment = 0.5m,
                TextAlign = HorizontalAlignment.Center
            };
            sizePanel.Controls.Add(minImageWidthNumeric, 1, 1);

            // 最小高度
            sizePanel.Controls.Add(new Label { Text = "最小高度(磅):", AutoSize = true, Enabled = false }, 2, 1);
            minImageHeightNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 10000,
                Value = 0,
                Width = 70,
                Enabled = false,
                DecimalPlaces = 1,
                Increment = 0.5m,
                TextAlign = HorizontalAlignment.Center
            };
            sizePanel.Controls.Add(minImageHeightNumeric, 3, 1);

            // 最大宽度
            sizePanel.Controls.Add(new Label { Text = "最大宽度(磅):", AutoSize = true, Enabled = false }, 0, 2);
            maxImageWidthNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 10000,
                Value = 0,
                Width = 70,
                Enabled = false,
                DecimalPlaces = 1,
                Increment = 0.5m,
                TextAlign = HorizontalAlignment.Center
            };
            sizePanel.Controls.Add(maxImageWidthNumeric, 1, 2);

            // 最大高度
            sizePanel.Controls.Add(new Label { Text = "最大高度(磅):", AutoSize = true, Enabled = false }, 2, 2);
            maxImageHeightNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 10000,
                Value = 0,
                Width = 70,
                Enabled = false,
                DecimalPlaces = 1,
                Increment = 0.5m,
                TextAlign = HorizontalAlignment.Center
            };
            sizePanel.Controls.Add(maxImageHeightNumeric, 3, 2);

            layout.Controls.Add(sizePanel, 0, 3);

            deleteAllImagesCheck = new CheckBox
            {
                Text = "删除所有图片",
                AutoSize = true
            };
            var deleteAllImagesToolTip = new ToolTip();
            deleteAllImagesToolTip.SetToolTip(deleteAllImagesCheck, "使用NodeCollection查找并删除所有ShapeType.Image类型的Shape节点，包括内联和浮动图片。");
            layout.Controls.Add(deleteAllImagesCheck, 0, 4);

            deleteTrailingImagesCheck = new CheckBox
            {
                Text = "删除文档结尾处所有图片",
                AutoSize = true
            };
            var deleteTrailingImagesToolTip = new ToolTip();
            deleteTrailingImagesToolTip.SetToolTip(deleteTrailingImagesCheck, "删除文档最后一节或最后一页中的所有图片。使用Section.LastParagraph或Document.LastSection定位文档结尾，然后使用NodeCollection查找该区域内的图片。");
            layout.Controls.Add(deleteTrailingImagesCheck, 0, 5);

            deleteBackgroundImagesCheck = new CheckBox
            {
                Text = "删除背景图片",
                AutoSize = true
            };
            var deleteBackgroundImagesToolTip = new ToolTip();
            deleteBackgroundImagesToolTip.SetToolTip(deleteBackgroundImagesCheck, "删除文档背景图片。使用Document.BackgroundShape属性访问文档背景图片，通过检查BackgroundShape != null并设置为null来删除背景。");
            layout.Controls.Add(deleteBackgroundImagesCheck, 0, 6);

            // 绑定事件
            deleteSizedImagesCheck.CheckedChanged += (s, e) =>
            {
                var enabled = deleteSizedImagesCheck.Checked;
                imageSizeLabel.Enabled = enabled;
                minImageWidthNumeric.Enabled = enabled;
                minImageHeightNumeric.Enabled = enabled;
                maxImageWidthNumeric.Enabled = enabled;
                maxImageHeightNumeric.Enabled = enabled;
                sizePanel.Enabled = enabled;

                // 当选中时，如果最大宽度或高度为0，则设置为一个合理的默认值
                if (enabled)
                {
                    if (maxImageWidthNumeric.Value == 0)
                        maxImageWidthNumeric.Value = 1000; // 1000磅点约等于14英寸

                    if (maxImageHeightNumeric.Value == 0)
                        maxImageHeightNumeric.Value = 1000;
                }

                foreach (Control c in sizePanel.Controls)
                {
                    c.Enabled = enabled;
                }
            };

            // 将布局添加到滚动面板
            scrollPanel.Controls.Add(layout);

            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateContactsGroup()
        {
            var group = new GroupBox
            {
                Text = "删除联系方式",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 创建滚动面板
            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 5,
                AutoSize = true,
                Padding = new Padding(5)
            };

            deleteMobileNumbersCheck = new CheckBox
            {
                Text = "删除手机号码",
                AutoSize = true
            };
            layout.Controls.Add(deleteMobileNumbersCheck, 0, 0);

            deletePhoneNumbersCheck = new CheckBox
            {
                Text = "删除固定电话号码",
                AutoSize = true
            };
            layout.Controls.Add(deletePhoneNumbersCheck, 0, 1);

            deleteEmailsCheck = new CheckBox
            {
                Text = "删除电子邮箱",
                AutoSize = true
            };
            layout.Controls.Add(deleteEmailsCheck, 0, 2);

            deleteUrlsCheck = new CheckBox
            {
                Text = "删除网址",
                AutoSize = true
            };
            layout.Controls.Add(deleteUrlsCheck, 0, 3);

            deleteHyperlinksCheck = new CheckBox
            {
                Text = "删除超链接",
                AutoSize = true
            };
            layout.Controls.Add(deleteHyperlinksCheck, 0, 4);

            // 将布局添加到滚动面板
            scrollPanel.Controls.Add(layout);
            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateTextGroup()
        {
            var group = new GroupBox
            {
                Text = "删除包含特定文本的内容",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 创建滚动面板
            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 5,
                AutoSize = true,
                Padding = new Padding(5)
            };

            deleteParagraphsWithTextCheck = new CheckBox
            {
                Text = "删除包含指定文本的段落",
                AutoSize = true
            };
            layout.Controls.Add(deleteParagraphsWithTextCheck, 0, 0);

            deleteTextBoxesWithTextCheck = new CheckBox
            {
                Text = "删除包含指定文本的文本框",
                AutoSize = true
            };
            layout.Controls.Add(deleteTextBoxesWithTextCheck, 0, 1);

            deleteTablesWithTextCheck = new CheckBox
            {
                Text = "删除包含指定文本的表格",
                AutoSize = true
            };
            layout.Controls.Add(deleteTablesWithTextCheck, 0, 2);

            specificTextLabel = new Label
            {
                Text = "指定文本（每行一个，支持Regex正则表达式）:",
                AutoSize = true,
                Enabled = false
            };
            layout.Controls.Add(specificTextLabel, 0, 3);

            specificTextBox = new TextBox
            {
                Multiline = true,
                Height = 100,
                Width = 400,
                ScrollBars = ScrollBars.Vertical,
                Enabled = false,
                TextAlign = HorizontalAlignment.Left,
                AcceptsReturn = true,
                AcceptsTab = true,
                WordWrap = true
            };
            layout.Controls.Add(specificTextBox, 0, 4);

            // 绑定事件
            EventHandler checkChanged = (s, e) =>
            {
                var anyChecked = deleteParagraphsWithTextCheck.Checked ||
                                 deleteTextBoxesWithTextCheck.Checked ||
                                 deleteTablesWithTextCheck.Checked;

                specificTextLabel.Enabled = anyChecked;
                specificTextBox.Enabled = anyChecked;
            };

            deleteParagraphsWithTextCheck.CheckedChanged += checkChanged;
            deleteTextBoxesWithTextCheck.CheckedChanged += checkChanged;
            deleteTablesWithTextCheck.CheckedChanged += checkChanged;

            // 将布局添加到滚动面板
            scrollPanel.Controls.Add(layout);
            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateEmptyGroup()
        {
            var group = new GroupBox
            {
                Text = "删除空白内容",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 创建滚动面板
            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 7,  // 增加到7行来容纳新功能
                AutoSize = true,
                Padding = new Padding(5)
            };

            deleteEmptyPagesCheck = new CheckBox
            {
                Text = "删除空白页",
                AutoSize = true
            };
            layout.Controls.Add(deleteEmptyPagesCheck, 0, 0);

            deleteEmptyParagraphsCheck = new CheckBox
            {
                Text = "删除空白段落",
                AutoSize = true
            };
            layout.Controls.Add(deleteEmptyParagraphsCheck, 0, 1);

            deleteEmptyLinesCheck = new CheckBox
            {
                Text = "删除空白行（仅包含空格和制表符的Run）",
                AutoSize = true
            };
            layout.Controls.Add(deleteEmptyLinesCheck, 0, 2);

            // 添加分隔线
            var separator = new Label
            {
                Text = "————————————————",
                AutoSize = true,
                ForeColor = Color.Gray,
                Margin = new Padding(0, 5, 0, 5)
            };
            layout.Controls.Add(separator, 0, 3);

            // 添加新功能：删除段落开头的空格
            deleteParagraphLeadingSpacesCheck = new CheckBox
            {
                Text = "删除段落开头的空格",
                AutoSize = true
            };
            var deleteParagraphLeadingSpacesTooltip = new ToolTip();
            deleteParagraphLeadingSpacesTooltip.SetToolTip(deleteParagraphLeadingSpacesCheck,
                "删除文档中所有段落开头的空格。使用Aspose.Words的Paragraph.GetText方法获取段落文本，然后使用String.TrimStart去除开头空格。");
            layout.Controls.Add(deleteParagraphLeadingSpacesCheck, 0, 4);

            // 添加新功能：删除段落结尾的空格
            deleteParagraphTrailingSpacesCheck = new CheckBox
            {
                Text = "删除段落结尾的空格",
                AutoSize = true
            };
            var deleteParagraphTrailingSpacesTooltip = new ToolTip();
            deleteParagraphTrailingSpacesTooltip.SetToolTip(deleteParagraphTrailingSpacesCheck,
                "删除文档中所有段落结尾的空格。使用Aspose.Words的Paragraph.GetText方法获取段落文本，然后使用String.TrimEnd去除结尾空格。");
            layout.Controls.Add(deleteParagraphTrailingSpacesCheck, 0, 5);

            // 添加新功能：删除段落开头的不可见字符
            deleteParagraphLeadingInvisibleCharsCheck = new CheckBox
            {
                Text = "删除段落开头的不可见字符",
                AutoSize = true
            };
            var deleteParagraphLeadingInvisibleCharsTooltip = new ToolTip();
            deleteParagraphLeadingInvisibleCharsTooltip.SetToolTip(deleteParagraphLeadingInvisibleCharsCheck,
                "删除文档中所有段落开头的不可见字符（如零宽度空格、控制字符等）。处理Paragraph.ChildNodes中的Run节点，检查并删除开头部分的不可见字符。");
            layout.Controls.Add(deleteParagraphLeadingInvisibleCharsCheck, 0, 6);

            // 添加新功能：删除段落结尾的不可见字符
            deleteParagraphTrailingInvisibleCharsCheck = new CheckBox
            {
                Text = "删除段落结尾的不可见字符",
                AutoSize = true
            };
            var deleteParagraphTrailingInvisibleCharsTooltip = new ToolTip();
            deleteParagraphTrailingInvisibleCharsTooltip.SetToolTip(deleteParagraphTrailingInvisibleCharsCheck,
                "删除文档中所有段落结尾的不可见字符（如零宽度空格、控制字符等）。处理Paragraph.ChildNodes中的Run节点，检查并删除结尾部分的不可见字符。");
            layout.Controls.Add(deleteParagraphTrailingInvisibleCharsCheck, 0, 7);

            // 将布局添加到滚动面板
            scrollPanel.Controls.Add(layout);
            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateWatermarkGroup()
        {
            var group = new GroupBox
            {
                Text = "删除水印",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 创建滚动面板
            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 3,
                AutoSize = true,
                Padding = new Padding(5)
            };

            deleteWatermarksCheck = new CheckBox
            {
                Text = "删除页面水印",
                AutoSize = true
            };
            layout.Controls.Add(deleteWatermarksCheck, 0, 0);

            // 添加页眉水印选项
            deleteHeaderWatermarksCheck = new CheckBox
            {
                Text = "删除页眉水印图形",
                AutoSize = true
            };
            layout.Controls.Add(deleteHeaderWatermarksCheck, 0, 1);

            // 添加页脚水印选项
            deleteFooterWatermarksCheck = new CheckBox
            {
                Text = "删除页脚水印图形",
                AutoSize = true
            };
            layout.Controls.Add(deleteFooterWatermarksCheck, 0, 2);

            // 将布局添加到滚动面板
            scrollPanel.Controls.Add(layout);
            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateFormatsGroup()
        {
            var group = new GroupBox
            {
                Text = "删除格式设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 创建滚动面板
            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 12, // 增加行数以容纳所有控件
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 主开关
            deleteFormatsCheck = new CheckBox
            {
                Text = "删除全文所有格式",
                AutoSize = true,
                Font = new Font(this.Font, FontStyle.Bold)
            };
            layout.Controls.Add(deleteFormatsCheck, 0, 0);

            // 字体格式
            deleteFontFormatsCheck = new CheckBox
            {
                Text = "删除字体格式（字体、大小、颜色等）",
                AutoSize = true,
                Margin = new Padding(20, 3, 3, 3)
            };
            layout.Controls.Add(deleteFontFormatsCheck, 0, 1);

            // 段落格式
            deleteParagraphFormatsCheck = new CheckBox
            {
                Text = "删除段落格式（对齐、缩进、行距等）",
                AutoSize = true,
                Margin = new Padding(20, 3, 3, 3)
            };
            layout.Controls.Add(deleteParagraphFormatsCheck, 0, 2);

            // 表格格式
            deleteTableFormatsCheck = new CheckBox
            {
                Text = "删除表格格式（边框、底纹、单元格样式等）",
                AutoSize = true,
                Margin = new Padding(20, 3, 3, 3)
            };
            layout.Controls.Add(deleteTableFormatsCheck, 0, 3);

            // 列表格式
            deleteListFormatsCheck = new CheckBox
            {
                Text = "删除列表格式（项目符号、编号、多级列表）",
                AutoSize = true,
                Margin = new Padding(20, 3, 3, 3)
            };
            layout.Controls.Add(deleteListFormatsCheck, 0, 4);

            // 页眉页脚格式
            deleteHeaderFooterFormatsCheck = new CheckBox
            {
                Text = "删除页眉页脚格式（保留内容）",
                AutoSize = true,
                Margin = new Padding(20, 3, 3, 3)
            };
            layout.Controls.Add(deleteHeaderFooterFormatsCheck, 0, 5);

            // 分隔线
            var separator = new Label
            {
                Text = "保留特定格式：",
                AutoSize = true,
                Font = new Font(this.Font, FontStyle.Bold),
                Margin = new Padding(3, 10, 3, 3)
            };
            layout.Controls.Add(separator, 0, 6);

            // 保留粗体
            preserveBoldCheck = new CheckBox
            {
                Text = "保留粗体格式（Run.Font.Bold）",
                AutoSize = true,
                Margin = new Padding(20, 3, 3, 3)
            };
            layout.Controls.Add(preserveBoldCheck, 0, 7);

            // 保留斜体
            preserveItalicCheck = new CheckBox
            {
                Text = "保留斜体格式（Run.Font.Italic）",
                AutoSize = true,
                Margin = new Padding(20, 3, 3, 3)
            };
            layout.Controls.Add(preserveItalicCheck, 0, 8);

            // 保留下划线
            preserveUnderlineCheck = new CheckBox
            {
                Text = "保留下划线格式（Run.Font.Underline）",
                AutoSize = true,
                Margin = new Padding(20, 3, 3, 3)
            };
            layout.Controls.Add(preserveUnderlineCheck, 0, 9);

            // 保留超链接
            preserveHyperlinksCheck = new CheckBox
            {
                Text = "保留超链接（字段和FieldHyperlink）",
                AutoSize = true,
                Margin = new Padding(20, 3, 3, 3)
            };
            layout.Controls.Add(preserveHyperlinksCheck, 0, 10);

            // 默认字体设置
            var fontPanel = new TableLayoutPanel
            {
                ColumnCount = 4,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 10, 3, 3)
            };

            defaultFontLabel = new Label
            {
                Text = "默认字体:",
                AutoSize = true
            };
            fontPanel.Controls.Add(defaultFontLabel, 0, 0);

            // 字体下拉框，添加常用字体
            defaultFontCombo = new ComboBox
            {
                Width = 150,
                DropDownStyle = ComboBoxStyle.DropDownList,
                DrawMode = DrawMode.OwnerDrawFixed
            };
            defaultFontCombo.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = defaultFontCombo.Items[e.Index].ToString();
                    var textSize = e.Graphics.MeasureString(text, e.Font);
                    var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                    var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(text, e.Font, new SolidBrush(e.ForeColor), x, y);
                    e.DrawFocusRectangle();
                }
            };

            // 添加常用中文字体
            var commonFonts = new List<string> {
                "宋体", "黑体", "仿宋", "楷体", "微软雅黑",
                "Arial", "Times New Roman", "Calibri", "Courier New", "Verdana"
            };

            // 尝试添加系统中可用的字体
            foreach (var fontFamily in FontFamily.Families)
            {
                if (!commonFonts.Contains(fontFamily.Name))
                {
                    commonFonts.Add(fontFamily.Name);
                }
            }

            // 排序并添加到下拉框
            commonFonts.Sort();
            foreach (var font in commonFonts)
            {
                defaultFontCombo.Items.Add(font);
            }

            // 默认选择宋体
            defaultFontCombo.SelectedItem = "宋体";
            fontPanel.Controls.Add(defaultFontCombo, 1, 0);

            defaultFontSizeLabel = new Label
            {
                Text = "默认字号:",
                AutoSize = true,
                Margin = new Padding(10, 0, 0, 0)
            };
            fontPanel.Controls.Add(defaultFontSizeLabel, 2, 0);

            defaultFontSizeNumeric = new NumericUpDown
            {
                Minimum = 8,
                Maximum = 72,
                Value = 12,
                Width = 60,
                TextAlign = HorizontalAlignment.Center
            };
            fontPanel.Controls.Add(defaultFontSizeNumeric, 3, 0);

            layout.Controls.Add(fontPanel, 0, 11);

            // 绑定事件 - 主开关控制其他选项的可用状态
            deleteFormatsCheck.CheckedChanged += (s, e) =>
            {
                bool enabled = deleteFormatsCheck.Checked;
                deleteFontFormatsCheck.Enabled = enabled;
                deleteParagraphFormatsCheck.Enabled = enabled;
                deleteTableFormatsCheck.Enabled = enabled;
                deleteListFormatsCheck.Enabled = enabled;
                deleteHeaderFooterFormatsCheck.Enabled = enabled;
                preserveBoldCheck.Enabled = enabled;
                preserveItalicCheck.Enabled = enabled;
                preserveUnderlineCheck.Enabled = enabled;
                preserveHyperlinksCheck.Enabled = enabled;
                defaultFontLabel.Enabled = enabled;
                defaultFontCombo.Enabled = enabled;
                defaultFontSizeLabel.Enabled = enabled;
                defaultFontSizeNumeric.Enabled = enabled;
            };

            // 将布局添加到滚动面板
            scrollPanel.Controls.Add(layout);

            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateLineBreakGroup()
        {
            var group = new GroupBox
            {
                Text = "换行符和段落标记设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 创建滚动面板
            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 10, // 调整行数
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 删除选项
            deleteLineBreaksCheck = new CheckBox
            {
                Text = "删除手动换行符(Shift+Enter)",
                AutoSize = true
            };
            var deleteLineBreaksTooltip = new ToolTip();
            deleteLineBreaksTooltip.SetToolTip(deleteLineBreaksCheck, "删除文本中的手动换行符(Shift+Enter)。后端使用ControlChar.LineBreak处理。");
            layout.Controls.Add(deleteLineBreaksCheck, 0, 0);

            deleteParagraphMarksCheck = new CheckBox
            {
                Text = "删除段落标记(Enter)",
                AutoSize = true
            };
            var deleteParagraphMarksTooltip = new ToolTip();
            deleteParagraphMarksTooltip.SetToolTip(deleteParagraphMarksCheck, "删除文本中的段落标记(Enter)。确保遍历所有Run对象，并使用ControlChar.ParagraphBreak进行识别。");
            layout.Controls.Add(deleteParagraphMarksCheck, 0, 1);

            deletePageBreaksCheck = new CheckBox
            {
                Text = "删除分页符(Ctrl+Enter)",
                AutoSize = true
            };
            var deletePageBreaksTooltip = new ToolTip();
            deletePageBreaksTooltip.SetToolTip(deletePageBreaksCheck, "删除文本中的分页符。后端使用ControlChar.PageBreak处理。");
            layout.Controls.Add(deletePageBreaksCheck, 0, 2);

            deleteSectionBreaksCheck = new CheckBox
            {
                Text = "删除分节符",
                AutoSize = true
            };
            var deleteSectionBreaksTooltip = new ToolTip();
            deleteSectionBreaksTooltip.SetToolTip(deleteSectionBreaksCheck, "删除分节符。通过合并Section对象实现。");
            layout.Controls.Add(deleteSectionBreaksCheck, 0, 3);

            // 添加水平分隔线
            var separator1 = new Label
            {
                Text = "————————————————",
                AutoSize = true,
                ForeColor = Color.Gray,
                Margin = new Padding(0, 5, 0, 5)
            };
            layout.Controls.Add(separator1, 0, 4);

            deleteCommentsCheck = new CheckBox
            {
                Text = "删除批注",
                AutoSize = true
            };
            var deleteCommentsTooltip = new ToolTip();
            deleteCommentsTooltip.SetToolTip(deleteCommentsCheck, "删除文档中的批注。使用NodeType.Comment查找和删除批注。");
            layout.Controls.Add(deleteCommentsCheck, 0, 5);

            deleteFootnotesCheck = new CheckBox
            {
                Text = "删除尾注",
                AutoSize = true
            };
            var deleteFootnotesTooltip = new ToolTip();
            deleteFootnotesTooltip.SetToolTip(deleteFootnotesCheck, "删除文档中的尾注。使用NodeType.Footnote查找和删除尾注。");
            layout.Controls.Add(deleteFootnotesCheck, 0, 6);

            // 添加水平分隔线
            var separator2 = new Label
            {
                Text = "————————————————",
                AutoSize = true,
                ForeColor = Color.Gray,
                Margin = new Padding(0, 5, 0, 5)
            };
            layout.Controls.Add(separator2, 0, 7);

            // 替换选项
            replaceLineBreaksWithParagraphMarksCheck = new CheckBox
            {
                Text = "手动换行符替换为段落标记",
                AutoSize = true
            };
            var replaceLineBreaksTooltip = new ToolTip();
            replaceLineBreaksTooltip.SetToolTip(replaceLineBreaksWithParagraphMarksCheck, "将手动换行符(Shift+Enter)替换为段落标记(Enter)。后端使用ControlChar.LineBreak替换为ControlChar.ParagraphBreak。");
            layout.Controls.Add(replaceLineBreaksWithParagraphMarksCheck, 0, 8);

            // 合并选项
            mergeMultipleLineBreaksCheck = new CheckBox
            {
                Text = "多个连续手动换行符合并为一个",
                AutoSize = true
            };
            var mergeLineBreaksTooltip = new ToolTip();
            mergeLineBreaksTooltip.SetToolTip(mergeMultipleLineBreaksCheck, "将多个连续的手动换行符合并为一个。");
            layout.Controls.Add(mergeMultipleLineBreaksCheck, 0, 9);

            mergeMultipleParagraphMarksCheck = new CheckBox
            {
                Text = "多个连续段落标记合并为一个",
                AutoSize = true
            };
            var mergeParagraphMarksTooltip = new ToolTip();
            mergeParagraphMarksTooltip.SetToolTip(mergeMultipleParagraphMarksCheck, "将多个连续的段落标记合并为一个。");
            layout.Controls.Add(mergeMultipleParagraphMarksCheck, 0, 10);

            // 将布局添加到滚动面板
            scrollPanel.Controls.Add(layout);

            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateProtectionGroup()
        {
            var group = new GroupBox
            {
                Text = "文档保护设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 创建滚动面板
            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(5)
            };

            forceRemoveEditingPasswordCheck = new CheckBox
            {
                Text = "强制移除编辑密码",
                AutoSize = true
            };
            layout.Controls.Add(forceRemoveEditingPasswordCheck, 0, 0);

            forceRemoveContentProtectionCheck = new CheckBox
            {
                Text = "强制移除内容保护",
                AutoSize = true
            };
            layout.Controls.Add(forceRemoveContentProtectionCheck, 0, 1);

            forceAcceptAllRevisionsCheck = new CheckBox
            {
                Text = "强制接受所有修订",
                AutoSize = true
            };
            layout.Controls.Add(forceAcceptAllRevisionsCheck, 0, 2);

            // 添加数字签名移除选项
            forceRemoveDigitalSignaturesCheck = new CheckBox
            {
                Text = "强制移除数字签名",
                AutoSize = true
            };
            layout.Controls.Add(forceRemoveDigitalSignaturesCheck, 0, 3);

            // 将布局添加到滚动面板
            scrollPanel.Controls.Add(layout);

            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateDocumentElementsGroup()
        {
            var group = new GroupBox
            {
                Text = "删除文档元素",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 10,
                AutoSize = true,
                Padding = new Padding(5)
            };

            deleteBookmarksCheck = new CheckBox
            {
                Text = "删除书签",
                AutoSize = true
            };
            layout.Controls.Add(deleteBookmarksCheck, 0, 0);

            deleteFieldsCheck = new CheckBox
            {
                Text = "删除域",
                AutoSize = true
            };
            layout.Controls.Add(deleteFieldsCheck, 0, 1);

            deleteTableOfContentsCheck = new CheckBox
            {
                Text = "删除目录",
                AutoSize = true
            };
            layout.Controls.Add(deleteTableOfContentsCheck, 0, 2);

            deleteIndexCheck = new CheckBox
            {
                Text = "删除索引",
                AutoSize = true
            };
            layout.Controls.Add(deleteIndexCheck, 0, 3);

            deleteCrossReferencesCheck = new CheckBox
            {
                Text = "删除交叉引用",
                AutoSize = true
            };
            layout.Controls.Add(deleteCrossReferencesCheck, 0, 4);

            deleteFormFieldsCheck = new CheckBox
            {
                Text = "删除表单域",
                AutoSize = true
            };
            layout.Controls.Add(deleteFormFieldsCheck, 0, 5);

            deleteSmartArtCheck = new CheckBox
            {
                Text = "删除SmartArt图形",
                AutoSize = true
            };
            layout.Controls.Add(deleteSmartArtCheck, 0, 6);

            deleteChartsCheck = new CheckBox
            {
                Text = "删除图表",
                AutoSize = true
            };
            layout.Controls.Add(deleteChartsCheck, 0, 7);

            deleteOleObjectsCheck = new CheckBox
            {
                Text = "删除OLE对象",
                AutoSize = true
            };
            layout.Controls.Add(deleteOleObjectsCheck, 0, 8);

            deleteActiveXControlsCheck = new CheckBox
            {
                Text = "删除ActiveX控件",
                AutoSize = true
            };
            layout.Controls.Add(deleteActiveXControlsCheck, 0, 9);

            scrollPanel.Controls.Add(layout);

            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateAdvancedFormatsGroup()
        {
            var group = new GroupBox
            {
                Text = "删除高级格式",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 10,
                AutoSize = true,
                Padding = new Padding(5)
            };

            deleteCharacterSpacingCheck = new CheckBox
            {
                Text = "删除字符间距",
                AutoSize = true
            };
            layout.Controls.Add(deleteCharacterSpacingCheck, 0, 0);

            deleteCharacterScalingCheck = new CheckBox
            {
                Text = "删除字符缩放",
                AutoSize = true
            };
            layout.Controls.Add(deleteCharacterScalingCheck, 0, 1);

            deleteCharacterPositionCheck = new CheckBox
            {
                Text = "删除字符位置",
                AutoSize = true
            };
            layout.Controls.Add(deleteCharacterPositionCheck, 0, 2);

            deleteTextEffectsCheck = new CheckBox
            {
                Text = "删除文本效果",
                AutoSize = true
            };
            layout.Controls.Add(deleteTextEffectsCheck, 0, 3);

            deleteParagraphBordersCheck = new CheckBox
            {
                Text = "删除段落边框",
                AutoSize = true
            };
            layout.Controls.Add(deleteParagraphBordersCheck, 0, 4);

            deleteParagraphShadingCheck = new CheckBox
            {
                Text = "删除段落底纹",
                AutoSize = true
            };
            layout.Controls.Add(deleteParagraphShadingCheck, 0, 5);

            deleteCellBordersCheck = new CheckBox
            {
                Text = "删除单元格边框",
                AutoSize = true
            };
            layout.Controls.Add(deleteCellBordersCheck, 0, 6);

            deleteCellShadingCheck = new CheckBox
            {
                Text = "删除单元格底纹",
                AutoSize = true
            };
            layout.Controls.Add(deleteCellShadingCheck, 0, 7);

            deleteCellMergingCheck = new CheckBox
            {
                Text = "删除单元格合并",
                AutoSize = true
            };
            layout.Controls.Add(deleteCellMergingCheck, 0, 8);

            deleteCellSplittingCheck = new CheckBox
            {
                Text = "删除单元格拆分",
                AutoSize = true
            };
            layout.Controls.Add(deleteCellSplittingCheck, 0, 9);

            scrollPanel.Controls.Add(layout);

            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateDocumentStructureGroup()
        {
            var group = new GroupBox
            {
                Text = "删除文档结构",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 7,
                AutoSize = true,
                Padding = new Padding(5)
            };

            deleteColumnsCheck = new CheckBox
            {
                Text = "删除分栏",
                AutoSize = true
            };
            layout.Controls.Add(deleteColumnsCheck, 0, 0);

            deleteTextBoxesCheck = new CheckBox
            {
                Text = "删除文本框",
                AutoSize = true
            };
            layout.Controls.Add(deleteTextBoxesCheck, 0, 1);

            deleteShapesCheck = new CheckBox
            {
                Text = "删除形状",
                AutoSize = true
            };
            layout.Controls.Add(deleteShapesCheck, 0, 2);

            deleteWordArtCheck = new CheckBox
            {
                Text = "删除艺术字",
                AutoSize = true
            };
            layout.Controls.Add(deleteWordArtCheck, 0, 3);

            deleteMarginsCheck = new CheckBox
            {
                Text = "删除页边距",
                AutoSize = true
            };
            layout.Controls.Add(deleteMarginsCheck, 0, 4);

            deletePageBordersCheck = new CheckBox
            {
                Text = "删除页面边框",
                AutoSize = true
            };
            layout.Controls.Add(deletePageBordersCheck, 0, 5);

            deletePageBackgroundCheck = new CheckBox
            {
                Text = "删除页面背景",
                AutoSize = true
            };
            layout.Controls.Add(deletePageBackgroundCheck, 0, 6);

            scrollPanel.Controls.Add(layout);

            group.Controls.Add(scrollPanel);
            return group;
        }

        private GroupBox CreateDocumentPropertiesGroup()
        {
            var group = new GroupBox
            {
                Text = "删除文档属性",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 10,
                AutoSize = true,
                Padding = new Padding(5)
            };

            deleteDocumentPropertiesCheck = new CheckBox
            {
                Text = "删除文档属性",
                AutoSize = true
            };
            layout.Controls.Add(deleteDocumentPropertiesCheck, 0, 0);

            deleteCustomPropertiesCheck = new CheckBox
            {
                Text = "删除自定义属性",
                AutoSize = true
            };
            layout.Controls.Add(deleteCustomPropertiesCheck, 0, 1);

            deleteDocumentVariablesCheck = new CheckBox
            {
                Text = "删除文档变量",
                AutoSize = true
            };
            layout.Controls.Add(deleteDocumentVariablesCheck, 0, 2);

            deleteDocumentStatisticsCheck = new CheckBox
            {
                Text = "删除文档统计信息",
                AutoSize = true
            };
            layout.Controls.Add(deleteDocumentStatisticsCheck, 0, 3);

            deleteHiddenTextCheck = new CheckBox
            {
                Text = "删除隐藏文本",
                AutoSize = true
            };
            layout.Controls.Add(deleteHiddenTextCheck, 0, 4);

            deleteRevisionMarksCheck = new CheckBox
            {
                Text = "删除修订标记",
                AutoSize = true
            };
            layout.Controls.Add(deleteRevisionMarksCheck, 0, 5);

            deleteCompareResultsCheck = new CheckBox
            {
                Text = "删除比较结果",
                AutoSize = true
            };
            layout.Controls.Add(deleteCompareResultsCheck, 0, 6);

            deleteDocumentVersionsCheck = new CheckBox
            {
                Text = "删除文档版本",
                AutoSize = true
            };
            layout.Controls.Add(deleteDocumentVersionsCheck, 0, 7);

            deleteDocumentThemeCheck = new CheckBox
            {
                Text = "删除文档主题",
                AutoSize = true
            };
            layout.Controls.Add(deleteDocumentThemeCheck, 0, 8);

            deleteDocumentStylesCheck = new CheckBox
            {
                Text = "删除文档样式",
                AutoSize = true
            };
            layout.Controls.Add(deleteDocumentStylesCheck, 0, 9);

            scrollPanel.Controls.Add(layout);

            group.Controls.Add(scrollPanel);
            return group;
        }

        private void LoadSettings()
        {
            // 加载删除文档设置
            enableDocumentDeletionCheck.Checked = settings.EnableDocumentDeletion;
            checkFileNameLengthCheck.Checked = settings.CheckFileNameLength;
            minFileNameLengthNumeric.Value = settings.MinFileNameLength;
            maxFileNameLengthNumeric.Value = settings.MaxFileNameLength;
            checkDocumentSizeCheck.Checked = settings.CheckDocumentSize;
            minDocumentSizeNumeric.Value = settings.MinDocumentSize;
            maxDocumentSizeNumeric.Value = settings.MaxDocumentSize;
            minDocumentSizeUnitCombo.SelectedItem = settings.MinDocumentSizeUnit;
            maxDocumentSizeUnitCombo.SelectedItem = settings.MaxDocumentSizeUnit;
            checkContentCharacterCountCheck.Checked = settings.CheckContentCharacterCount;
            minContentCharacterCountNumeric.Value = settings.MinContentCharacterCount;
            maxContentCharacterCountNumeric.Value = settings.MaxContentCharacterCount;
            checkDocumentPageCountCheck.Checked = settings.CheckDocumentPageCount;
            minDocumentPageCountNumeric.Value = settings.MinDocumentPageCount;
            maxDocumentPageCountNumeric.Value = settings.MaxDocumentPageCount;
            checkFileNameIllegalWordsCheck.Checked = settings.CheckFileNameIllegalWords;
            checkContentIllegalWordsCheck.Checked = settings.CheckContentIllegalWords;

            // 加载图片删除设置
            deleteSpecificImagesCheck.Checked = settings.DeleteSpecificImages;
            deleteSizedImagesCheck.Checked = settings.DeleteSizedImages;
            deleteAllImagesCheck.Checked = settings.DeleteAllImages;
            deleteTrailingImagesCheck.Checked = settings.DeleteTrailingImages;
            deleteBackgroundImagesCheck.Checked = settings.DeleteBackgroundImages;
            minImageWidthNumeric.Value = settings.MinImageWidth;
            minImageHeightNumeric.Value = settings.MinImageHeight;
            maxImageWidthNumeric.Value = settings.MaxImageWidth;
            maxImageHeightNumeric.Value = settings.MaxImageHeight;

            // 加载指定图片列表
            selectedImagesListBox.Items.Clear();
            foreach (var imagePath in settings.SpecificImagePaths)
            {
                selectedImagesListBox.Items.Add(Path.GetFileName(imagePath));
            }

            // 加载联系方式删除设置
            deleteMobileNumbersCheck.Checked = settings.DeleteMobileNumbers;
            deletePhoneNumbersCheck.Checked = settings.DeletePhoneNumbers;
            deleteEmailsCheck.Checked = settings.DeleteEmails;
            deleteUrlsCheck.Checked = settings.DeleteUrls;
            deleteHyperlinksCheck.Checked = settings.DeleteHyperlinks;

            // 加载特定文本删除设置
            deleteParagraphsWithTextCheck.Checked = settings.DeleteParagraphsWithText;
            deleteTextBoxesWithTextCheck.Checked = settings.DeleteTextBoxesWithText;
            deleteTablesWithTextCheck.Checked = settings.DeleteTablesWithText;
            specificTextBox.Text = string.Join(Environment.NewLine, settings.SpecificTextList);

            // 加载空白内容删除设置
            deleteEmptyPagesCheck.Checked = settings.DeleteEmptyPages;
            deleteEmptyParagraphsCheck.Checked = settings.DeleteEmptyParagraphs;
            deleteEmptyLinesCheck.Checked = settings.DeleteEmptyLines;

            // 加载新增的段落空格和不可见字符删除设置
            deleteParagraphLeadingSpacesCheck.Checked = settings.DeleteParagraphLeadingSpaces;
            deleteParagraphTrailingSpacesCheck.Checked = settings.DeleteParagraphTrailingSpaces;
            deleteParagraphLeadingInvisibleCharsCheck.Checked = settings.DeleteParagraphLeadingInvisibleChars;
            deleteParagraphTrailingInvisibleCharsCheck.Checked = settings.DeleteParagraphTrailingInvisibleChars;

            // 加载水印删除设置
            deleteWatermarksCheck.Checked = settings.DeleteWatermarks;
            deleteHeaderWatermarksCheck.Checked = settings.DeleteHeaderWatermarks;
            deleteFooterWatermarksCheck.Checked = settings.DeleteFooterWatermarks;

            // 加载格式删除设置
            deleteFormatsCheck.Checked = settings.DeleteFormats;
            deleteFontFormatsCheck.Checked = settings.DeleteFontFormats;
            deleteParagraphFormatsCheck.Checked = settings.DeleteParagraphFormats;
            deleteTableFormatsCheck.Checked = settings.DeleteTableFormats;
            deleteListFormatsCheck.Checked = settings.DeleteListFormats;
            deleteHeaderFooterFormatsCheck.Checked = settings.DeleteHeaderFooterFormats;
            preserveBoldCheck.Checked = settings.PreserveBold;
            preserveItalicCheck.Checked = settings.PreserveItalic;
            preserveUnderlineCheck.Checked = settings.PreserveUnderline;
            preserveHyperlinksCheck.Checked = settings.PreserveHyperlinks;

            // 设置默认字体和字号
            if (!string.IsNullOrEmpty(settings.DefaultFontName) && defaultFontCombo.Items.Contains(settings.DefaultFontName))
            {
                defaultFontCombo.SelectedItem = settings.DefaultFontName;
            }
            else if (defaultFontCombo.Items.Count > 0)
            {
                defaultFontCombo.SelectedIndex = 0;
            }

            defaultFontSizeNumeric.Value = (decimal)settings.DefaultFontSize;

            // 更新UI状态
            var enabled = deleteSizedImagesCheck.Checked;
            imageSizeLabel.Enabled = enabled;
            minImageWidthNumeric.Enabled = enabled;
            minImageHeightNumeric.Enabled = enabled;
            maxImageWidthNumeric.Enabled = enabled;
            maxImageHeightNumeric.Enabled = enabled;

            bool anyTextChecked = deleteParagraphsWithTextCheck.Checked ||
                                  deleteTextBoxesWithTextCheck.Checked ||
                                  deleteTablesWithTextCheck.Checked;
            specificTextLabel.Enabled = anyTextChecked;
            specificTextBox.Enabled = anyTextChecked;

            // 更新格式删除UI状态
            bool formatsEnabled = deleteFormatsCheck.Checked;
            deleteFontFormatsCheck.Enabled = formatsEnabled;
            deleteParagraphFormatsCheck.Enabled = formatsEnabled;
            deleteTableFormatsCheck.Enabled = formatsEnabled;
            deleteListFormatsCheck.Enabled = formatsEnabled;
            deleteHeaderFooterFormatsCheck.Enabled = formatsEnabled;
            preserveBoldCheck.Enabled = formatsEnabled;
            preserveItalicCheck.Enabled = formatsEnabled;
            preserveUnderlineCheck.Enabled = formatsEnabled;
            preserveHyperlinksCheck.Enabled = formatsEnabled;
            defaultFontLabel.Enabled = formatsEnabled;
            defaultFontCombo.Enabled = formatsEnabled;
            defaultFontSizeLabel.Enabled = formatsEnabled;
            defaultFontSizeNumeric.Enabled = formatsEnabled;

            // 加载换行符和段落标记设置
            deleteLineBreaksCheck.Checked = settings.DeleteLineBreaks;
            deleteParagraphMarksCheck.Checked = settings.DeleteParagraphMarks;
            deletePageBreaksCheck.Checked = settings.DeletePageBreaks;
            deleteSectionBreaksCheck.Checked = settings.DeleteSectionBreaks;
            deleteCommentsCheck.Checked = settings.DeleteComments;
            deleteFootnotesCheck.Checked = settings.DeleteFootnotes;
            replaceLineBreaksWithParagraphMarksCheck.Checked = settings.ReplaceLineBreaksWithParagraphMarks;
            mergeMultipleLineBreaksCheck.Checked = settings.MergeMultipleLineBreaks;
            mergeMultipleParagraphMarksCheck.Checked = settings.MergeMultipleParagraphMarks;

            // 加载文档保护设置
            forceRemoveEditingPasswordCheck.Checked = settings.ForceRemoveEditingPassword;
            forceRemoveContentProtectionCheck.Checked = settings.ForceRemoveContentProtection;
            forceAcceptAllRevisionsCheck.Checked = settings.ForceAcceptAllRevisions;
            forceRemoveDigitalSignaturesCheck.Checked = settings.ForceRemoveDigitalSignatures;

            // 加载文档元素删除设置
            deleteBookmarksCheck.Checked = settings.DeleteBookmarks;
            deleteFieldsCheck.Checked = settings.DeleteFields;
            deleteTableOfContentsCheck.Checked = settings.DeleteTableOfContents;
            deleteIndexCheck.Checked = settings.DeleteIndex;
            deleteCrossReferencesCheck.Checked = settings.DeleteCrossReferences;
            deleteFormFieldsCheck.Checked = settings.DeleteFormFields;
            deleteSmartArtCheck.Checked = settings.DeleteSmartArt;
            deleteChartsCheck.Checked = settings.DeleteCharts;
            deleteOleObjectsCheck.Checked = settings.DeleteOleObjects;
            deleteActiveXControlsCheck.Checked = settings.DeleteActiveXControls;

            // 加载高级格式删除设置
            deleteCharacterSpacingCheck.Checked = settings.DeleteCharacterSpacing;
            deleteCharacterScalingCheck.Checked = settings.DeleteCharacterScaling;
            deleteCharacterPositionCheck.Checked = settings.DeleteCharacterPosition;
            deleteTextEffectsCheck.Checked = settings.DeleteTextEffects;
            deleteParagraphBordersCheck.Checked = settings.DeleteParagraphBorders;
            deleteParagraphShadingCheck.Checked = settings.DeleteParagraphShading;
            deleteCellBordersCheck.Checked = settings.DeleteCellBorders;
            deleteCellShadingCheck.Checked = settings.DeleteCellShading;
            deleteCellMergingCheck.Checked = settings.DeleteCellMerging;
            deleteCellSplittingCheck.Checked = settings.DeleteCellSplitting;

            // 加载文档结构删除设置
            deleteColumnsCheck.Checked = settings.DeleteColumns;
            deleteTextBoxesCheck.Checked = settings.DeleteTextBoxes;
            deleteShapesCheck.Checked = settings.DeleteShapes;
            deleteWordArtCheck.Checked = settings.DeleteWordArt;
            deleteMarginsCheck.Checked = settings.DeleteMargins;
            deletePageBordersCheck.Checked = settings.DeletePageBorders;
            deletePageBackgroundCheck.Checked = settings.DeletePageBackground;

            // 加载文档属性删除设置
            deleteDocumentPropertiesCheck.Checked = settings.DeleteDocumentProperties;
            deleteCustomPropertiesCheck.Checked = settings.DeleteCustomProperties;
            deleteDocumentVariablesCheck.Checked = settings.DeleteDocumentVariables;
            deleteDocumentStatisticsCheck.Checked = settings.DeleteDocumentStatistics;
            deleteHiddenTextCheck.Checked = settings.DeleteHiddenText;
            deleteRevisionMarksCheck.Checked = settings.DeleteRevisionMarks;
            deleteCompareResultsCheck.Checked = settings.DeleteCompareResults;
            deleteDocumentVersionsCheck.Checked = settings.DeleteDocumentVersions;
            deleteDocumentThemeCheck.Checked = settings.DeleteDocumentTheme;
            deleteDocumentStylesCheck.Checked = settings.DeleteDocumentStyles;
        }

        private void OkButton_Click(object? sender, EventArgs e)
        {
            // 保存删除文档设置
            settings.EnableDocumentDeletion = enableDocumentDeletionCheck.Checked;
            settings.CheckFileNameLength = checkFileNameLengthCheck.Checked;
            settings.MinFileNameLength = (int)minFileNameLengthNumeric.Value;
            settings.MaxFileNameLength = (int)maxFileNameLengthNumeric.Value;
            settings.CheckDocumentSize = checkDocumentSizeCheck.Checked;
            settings.MinDocumentSize = (int)minDocumentSizeNumeric.Value;
            settings.MaxDocumentSize = (int)maxDocumentSizeNumeric.Value;
            settings.MinDocumentSizeUnit = minDocumentSizeUnitCombo.SelectedItem?.ToString() ?? "KB";
            settings.MaxDocumentSizeUnit = maxDocumentSizeUnitCombo.SelectedItem?.ToString() ?? "KB";
            settings.CheckContentCharacterCount = checkContentCharacterCountCheck.Checked;
            settings.MinContentCharacterCount = (int)minContentCharacterCountNumeric.Value;
            settings.MaxContentCharacterCount = (int)maxContentCharacterCountNumeric.Value;
            settings.CheckDocumentPageCount = checkDocumentPageCountCheck.Checked;
            settings.MinDocumentPageCount = (int)minDocumentPageCountNumeric.Value;
            settings.MaxDocumentPageCount = (int)maxDocumentPageCountNumeric.Value;
            settings.CheckFileNameIllegalWords = checkFileNameIllegalWordsCheck.Checked;
            settings.CheckContentIllegalWords = checkContentIllegalWordsCheck.Checked;

            // 保存图片删除设置
            settings.DeleteSpecificImages = deleteSpecificImagesCheck.Checked;
            settings.DeleteSizedImages = deleteSizedImagesCheck.Checked;
            settings.DeleteAllImages = deleteAllImagesCheck.Checked;
            settings.DeleteTrailingImages = deleteTrailingImagesCheck.Checked;
            settings.DeleteBackgroundImages = deleteBackgroundImagesCheck.Checked;
            settings.MinImageWidth = (int)minImageWidthNumeric.Value;
            settings.MinImageHeight = (int)minImageHeightNumeric.Value;
            settings.MaxImageWidth = (int)maxImageWidthNumeric.Value;
            settings.MaxImageHeight = (int)maxImageHeightNumeric.Value;

            // 不需要保存 SpecificImagePaths，因为它们已经是引用类型，在选择时就已经更新了

            // 保存联系方式删除设置
            settings.DeleteMobileNumbers = deleteMobileNumbersCheck.Checked;
            settings.DeletePhoneNumbers = deletePhoneNumbersCheck.Checked;
            settings.DeleteEmails = deleteEmailsCheck.Checked;
            settings.DeleteUrls = deleteUrlsCheck.Checked;
            settings.DeleteHyperlinks = deleteHyperlinksCheck.Checked;

            // 保存特定文本删除设置
            settings.DeleteParagraphsWithText = deleteParagraphsWithTextCheck.Checked;
            settings.DeleteTextBoxesWithText = deleteTextBoxesWithTextCheck.Checked;
            settings.DeleteTablesWithText = deleteTablesWithTextCheck.Checked;
            settings.SpecificTextList = specificTextBox.Text
                .Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries)
                .Select(s => s.Trim())
                .Where(s => !string.IsNullOrEmpty(s))
                .ToList();

            // 保存空白内容删除设置
            settings.DeleteEmptyPages = deleteEmptyPagesCheck.Checked;
            settings.DeleteEmptyParagraphs = deleteEmptyParagraphsCheck.Checked;
            settings.DeleteEmptyLines = deleteEmptyLinesCheck.Checked;

            // 保存新增的段落空格和不可见字符删除设置
            settings.DeleteParagraphLeadingSpaces = deleteParagraphLeadingSpacesCheck.Checked;
            settings.DeleteParagraphTrailingSpaces = deleteParagraphTrailingSpacesCheck.Checked;
            settings.DeleteParagraphLeadingInvisibleChars = deleteParagraphLeadingInvisibleCharsCheck.Checked;
            settings.DeleteParagraphTrailingInvisibleChars = deleteParagraphTrailingInvisibleCharsCheck.Checked;

            // 保存水印删除设置
            settings.DeleteWatermarks = deleteWatermarksCheck.Checked;
            settings.DeleteHeaderWatermarks = deleteHeaderWatermarksCheck.Checked;
            settings.DeleteFooterWatermarks = deleteFooterWatermarksCheck.Checked;

            // 保存格式删除设置
            settings.DeleteFormats = deleteFormatsCheck.Checked;
            settings.DeleteFontFormats = deleteFontFormatsCheck.Checked;
            settings.DeleteParagraphFormats = deleteParagraphFormatsCheck.Checked;
            settings.DeleteTableFormats = deleteTableFormatsCheck.Checked;
            settings.DeleteListFormats = deleteListFormatsCheck.Checked;
            settings.DeleteHeaderFooterFormats = deleteHeaderFooterFormatsCheck.Checked;
            settings.PreserveBold = preserveBoldCheck.Checked;
            settings.PreserveItalic = preserveItalicCheck.Checked;
            settings.PreserveUnderline = preserveUnderlineCheck.Checked;
            settings.PreserveHyperlinks = preserveHyperlinksCheck.Checked;
            settings.DefaultFontName = defaultFontCombo.SelectedItem?.ToString() ?? "宋体";
            settings.DefaultFontSize = (double)defaultFontSizeNumeric.Value;

            // 保存换行符和段落标记设置
            settings.DeleteLineBreaks = deleteLineBreaksCheck.Checked;
            settings.DeleteParagraphMarks = deleteParagraphMarksCheck.Checked;
            settings.DeletePageBreaks = deletePageBreaksCheck.Checked;
            settings.DeleteSectionBreaks = deleteSectionBreaksCheck.Checked;
            settings.DeleteComments = deleteCommentsCheck.Checked;
            settings.DeleteFootnotes = deleteFootnotesCheck.Checked;
            settings.ReplaceLineBreaksWithParagraphMarks = replaceLineBreaksWithParagraphMarksCheck.Checked;
            settings.MergeMultipleLineBreaks = mergeMultipleLineBreaksCheck.Checked;
            settings.MergeMultipleParagraphMarks = mergeMultipleParagraphMarksCheck.Checked;

            // 保存文档保护设置
            settings.ForceRemoveEditingPassword = forceRemoveEditingPasswordCheck.Checked;
            settings.ForceRemoveContentProtection = forceRemoveContentProtectionCheck.Checked;
            settings.ForceAcceptAllRevisions = forceAcceptAllRevisionsCheck.Checked;
            settings.ForceRemoveDigitalSignatures = forceRemoveDigitalSignaturesCheck.Checked;

            // 保存文档元素删除设置
            settings.DeleteBookmarks = deleteBookmarksCheck.Checked;
            settings.DeleteFields = deleteFieldsCheck.Checked;
            settings.DeleteTableOfContents = deleteTableOfContentsCheck.Checked;
            settings.DeleteIndex = deleteIndexCheck.Checked;
            settings.DeleteCrossReferences = deleteCrossReferencesCheck.Checked;
            settings.DeleteFormFields = deleteFormFieldsCheck.Checked;
            settings.DeleteSmartArt = deleteSmartArtCheck.Checked;
            settings.DeleteCharts = deleteChartsCheck.Checked;
            settings.DeleteOleObjects = deleteOleObjectsCheck.Checked;
            settings.DeleteActiveXControls = deleteActiveXControlsCheck.Checked;

            // 保存高级格式删除设置
            settings.DeleteCharacterSpacing = deleteCharacterSpacingCheck.Checked;
            settings.DeleteCharacterScaling = deleteCharacterScalingCheck.Checked;
            settings.DeleteCharacterPosition = deleteCharacterPositionCheck.Checked;
            settings.DeleteTextEffects = deleteTextEffectsCheck.Checked;
            settings.DeleteParagraphBorders = deleteParagraphBordersCheck.Checked;
            settings.DeleteParagraphShading = deleteParagraphShadingCheck.Checked;
            settings.DeleteCellBorders = deleteCellBordersCheck.Checked;
            settings.DeleteCellShading = deleteCellShadingCheck.Checked;
            settings.DeleteCellMerging = deleteCellMergingCheck.Checked;
            settings.DeleteCellSplitting = deleteCellSplittingCheck.Checked;

            // 保存文档结构删除设置
            settings.DeleteColumns = deleteColumnsCheck.Checked;
            settings.DeleteTextBoxes = deleteTextBoxesCheck.Checked;
            settings.DeleteShapes = deleteShapesCheck.Checked;
            settings.DeleteWordArt = deleteWordArtCheck.Checked;
            settings.DeleteMargins = deleteMarginsCheck.Checked;
            settings.DeletePageBorders = deletePageBordersCheck.Checked;
            settings.DeletePageBackground = deletePageBackgroundCheck.Checked;

            // 保存文档属性删除设置
            settings.DeleteDocumentProperties = deleteDocumentPropertiesCheck.Checked;
            settings.DeleteCustomProperties = deleteCustomPropertiesCheck.Checked;
            settings.DeleteDocumentVariables = deleteDocumentVariablesCheck.Checked;
            settings.DeleteDocumentStatistics = deleteDocumentStatisticsCheck.Checked;
            settings.DeleteHiddenText = deleteHiddenTextCheck.Checked;
            settings.DeleteRevisionMarks = deleteRevisionMarksCheck.Checked;
            settings.DeleteCompareResults = deleteCompareResultsCheck.Checked;
            settings.DeleteDocumentVersions = deleteDocumentVersionsCheck.Checked;
            settings.DeleteDocumentTheme = deleteDocumentThemeCheck.Checked;
            settings.DeleteDocumentStyles = deleteDocumentStylesCheck.Checked;
        }

        private void EditFileNameIllegalWordsButton_Click(object? sender, EventArgs e)
        {
            using (var editForm = new IllegalWordsEditForm(settings.FileNameIllegalWords, "文件名非法词"))
            {
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    settings.FileNameIllegalWords = editForm.IllegalWords;
                }
            }
        }

        private void EditContentIllegalWordsButton_Click(object? sender, EventArgs e)
        {
            using (var editForm = new IllegalWordsEditForm(settings.ContentIllegalWords, "内容非法词"))
            {
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    settings.ContentIllegalWords = editForm.IllegalWords;
                }
            }
        }

        private void SelectAllButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var selectedTab = tabControl.SelectedTab;
                if (selectedTab == null) return;

                // 根据当前选中的标签页执行相应的全选操作
                switch (selectedTab.Text)
                {
                    case "删除文档":
                        SelectAllDeleteDocument();
                        break;
                    case "图片删除设置":
                        SelectAllImages();
                        break;
                    case "联系方式删除设置":
                        SelectAllContacts();
                        break;
                    case "文本删除设置":
                        SelectAllText();
                        break;
                    case "内容删除设置":
                        SelectAllEmpty();
                        break;
                    case "水印删除设置":
                        SelectAllWatermark();
                        break;
                    case "删除全文格式":
                        SelectAllFormats();
                        break;
                    case "换行符和段落标记":
                        SelectAllLineBreak();
                        break;
                    case "文档保护":
                        SelectAllProtection();
                        break;
                    case "文档元素":
                        SelectAllDocumentElements();
                        break;
                    case "高级格式":
                        SelectAllAdvancedFormats();
                        break;
                    case "文档结构":
                        SelectAllDocumentStructure();
                        break;
                    case "文档属性":
                        SelectAllDocumentProperties();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"全选操作出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UnselectAllButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var selectedTab = tabControl.SelectedTab;
                if (selectedTab == null) return;

                // 根据当前选中的标签页执行相应的取消全选操作
                switch (selectedTab.Text)
                {
                    case "删除文档":
                        UnselectAllDeleteDocument();
                        break;
                    case "图片删除设置":
                        UnselectAllImages();
                        break;
                    case "联系方式删除设置":
                        UnselectAllContacts();
                        break;
                    case "文本删除设置":
                        UnselectAllText();
                        break;
                    case "内容删除设置":
                        UnselectAllEmpty();
                        break;
                    case "水印删除设置":
                        UnselectAllWatermark();
                        break;
                    case "删除全文格式":
                        UnselectAllFormats();
                        break;
                    case "换行符和段落标记":
                        UnselectAllLineBreak();
                        break;
                    case "文档保护":
                        UnselectAllProtection();
                        break;
                    case "文档元素":
                        UnselectAllDocumentElements();
                        break;
                    case "高级格式":
                        UnselectAllAdvancedFormats();
                        break;
                    case "文档结构":
                        UnselectAllDocumentStructure();
                        break;
                    case "文档属性":
                        UnselectAllDocumentProperties();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"取消全选操作出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 删除文档标签页的全选和取消全选方法
        private void SelectAllDeleteDocument()
        {
            enableDocumentDeletionCheck.Checked = true;
            checkFileNameLengthCheck.Checked = true;
            checkDocumentSizeCheck.Checked = true;
            checkContentCharacterCountCheck.Checked = true;
            checkDocumentPageCountCheck.Checked = true;
            checkFileNameIllegalWordsCheck.Checked = true;
            checkContentIllegalWordsCheck.Checked = true;
        }

        private void UnselectAllDeleteDocument()
        {
            enableDocumentDeletionCheck.Checked = false;
            checkFileNameLengthCheck.Checked = false;
            checkDocumentSizeCheck.Checked = false;
            checkContentCharacterCountCheck.Checked = false;
            checkDocumentPageCountCheck.Checked = false;
            checkFileNameIllegalWordsCheck.Checked = false;
            checkContentIllegalWordsCheck.Checked = false;
        }

        // 图片删除标签页的全选和取消全选方法
        private void SelectAllImages()
        {
            deleteSpecificImagesCheck.Checked = true;
            deleteSizedImagesCheck.Checked = true;
            deleteAllImagesCheck.Checked = true;
            deleteTrailingImagesCheck.Checked = true;
            deleteBackgroundImagesCheck.Checked = true;
        }

        private void UnselectAllImages()
        {
            deleteSpecificImagesCheck.Checked = false;
            deleteSizedImagesCheck.Checked = false;
            deleteAllImagesCheck.Checked = false;
            deleteTrailingImagesCheck.Checked = false;
            deleteBackgroundImagesCheck.Checked = false;
        }

        // 联系方式删除标签页的全选和取消全选方法
        private void SelectAllContacts()
        {
            deleteMobileNumbersCheck.Checked = true;
            deletePhoneNumbersCheck.Checked = true;
            deleteEmailsCheck.Checked = true;
            deleteUrlsCheck.Checked = true;
            deleteHyperlinksCheck.Checked = true;
        }

        private void UnselectAllContacts()
        {
            deleteMobileNumbersCheck.Checked = false;
            deletePhoneNumbersCheck.Checked = false;
            deleteEmailsCheck.Checked = false;
            deleteUrlsCheck.Checked = false;
            deleteHyperlinksCheck.Checked = false;
        }

        // 文本删除标签页的全选和取消全选方法
        private void SelectAllText()
        {
            deleteParagraphsWithTextCheck.Checked = true;
            deleteTextBoxesWithTextCheck.Checked = true;
            deleteTablesWithTextCheck.Checked = true;
        }

        private void UnselectAllText()
        {
            deleteParagraphsWithTextCheck.Checked = false;
            deleteTextBoxesWithTextCheck.Checked = false;
            deleteTablesWithTextCheck.Checked = false;
        }

        // 内容删除标签页的全选和取消全选方法
        private void SelectAllEmpty()
        {
            deleteEmptyPagesCheck.Checked = true;
            deleteEmptyParagraphsCheck.Checked = true;
            deleteEmptyLinesCheck.Checked = true;
            deleteParagraphLeadingSpacesCheck.Checked = true;
            deleteParagraphTrailingSpacesCheck.Checked = true;
            deleteParagraphLeadingInvisibleCharsCheck.Checked = true;
            deleteParagraphTrailingInvisibleCharsCheck.Checked = true;
        }

        private void UnselectAllEmpty()
        {
            deleteEmptyPagesCheck.Checked = false;
            deleteEmptyParagraphsCheck.Checked = false;
            deleteEmptyLinesCheck.Checked = false;
            deleteParagraphLeadingSpacesCheck.Checked = false;
            deleteParagraphTrailingSpacesCheck.Checked = false;
            deleteParagraphLeadingInvisibleCharsCheck.Checked = false;
            deleteParagraphTrailingInvisibleCharsCheck.Checked = false;
        }

        // 水印删除标签页的全选和取消全选方法
        private void SelectAllWatermark()
        {
            deleteWatermarksCheck.Checked = true;
            deleteHeaderWatermarksCheck.Checked = true;
            deleteFooterWatermarksCheck.Checked = true;
        }

        private void UnselectAllWatermark()
        {
            deleteWatermarksCheck.Checked = false;
            deleteHeaderWatermarksCheck.Checked = false;
            deleteFooterWatermarksCheck.Checked = false;
        }

        // 格式删除标签页的全选和取消全选方法
        private void SelectAllFormats()
        {
            deleteFormatsCheck.Checked = true;
            deleteFontFormatsCheck.Checked = true;
            deleteParagraphFormatsCheck.Checked = true;
            deleteTableFormatsCheck.Checked = true;
            deleteListFormatsCheck.Checked = true;
            deleteHeaderFooterFormatsCheck.Checked = true;
            preserveBoldCheck.Checked = true;
            preserveItalicCheck.Checked = true;
            preserveUnderlineCheck.Checked = true;
            preserveHyperlinksCheck.Checked = true;
        }

        private void UnselectAllFormats()
        {
            deleteFormatsCheck.Checked = false;
            deleteFontFormatsCheck.Checked = false;
            deleteParagraphFormatsCheck.Checked = false;
            deleteTableFormatsCheck.Checked = false;
            deleteListFormatsCheck.Checked = false;
            deleteHeaderFooterFormatsCheck.Checked = false;
            preserveBoldCheck.Checked = false;
            preserveItalicCheck.Checked = false;
            preserveUnderlineCheck.Checked = false;
            preserveHyperlinksCheck.Checked = false;
        }

        // 换行符和段落标记标签页的全选和取消全选方法
        private void SelectAllLineBreak()
        {
            deleteLineBreaksCheck.Checked = true;
            deleteParagraphMarksCheck.Checked = true;
            deletePageBreaksCheck.Checked = true;
            deleteSectionBreaksCheck.Checked = true;
            deleteCommentsCheck.Checked = true;
            deleteFootnotesCheck.Checked = true;
            replaceLineBreaksWithParagraphMarksCheck.Checked = true;
            mergeMultipleLineBreaksCheck.Checked = true;
            mergeMultipleParagraphMarksCheck.Checked = true;
        }

        private void UnselectAllLineBreak()
        {
            deleteLineBreaksCheck.Checked = false;
            deleteParagraphMarksCheck.Checked = false;
            deletePageBreaksCheck.Checked = false;
            deleteSectionBreaksCheck.Checked = false;
            deleteCommentsCheck.Checked = false;
            deleteFootnotesCheck.Checked = false;
            replaceLineBreaksWithParagraphMarksCheck.Checked = false;
            mergeMultipleLineBreaksCheck.Checked = false;
            mergeMultipleParagraphMarksCheck.Checked = false;
        }

        // 文档保护标签页的全选和取消全选方法
        private void SelectAllProtection()
        {
            forceRemoveEditingPasswordCheck.Checked = true;
            forceRemoveContentProtectionCheck.Checked = true;
            forceAcceptAllRevisionsCheck.Checked = true;
            forceRemoveDigitalSignaturesCheck.Checked = true;
        }

        private void UnselectAllProtection()
        {
            forceRemoveEditingPasswordCheck.Checked = false;
            forceRemoveContentProtectionCheck.Checked = false;
            forceAcceptAllRevisionsCheck.Checked = false;
            forceRemoveDigitalSignaturesCheck.Checked = false;
        }

        // 文档元素标签页的全选和取消全选方法
        private void SelectAllDocumentElements()
        {
            deleteBookmarksCheck.Checked = true;
            deleteFieldsCheck.Checked = true;
            deleteTableOfContentsCheck.Checked = true;
            deleteIndexCheck.Checked = true;
            deleteCrossReferencesCheck.Checked = true;
            deleteFormFieldsCheck.Checked = true;
            deleteSmartArtCheck.Checked = true;
            deleteChartsCheck.Checked = true;
            deleteOleObjectsCheck.Checked = true;
            deleteActiveXControlsCheck.Checked = true;
        }

        private void UnselectAllDocumentElements()
        {
            deleteBookmarksCheck.Checked = false;
            deleteFieldsCheck.Checked = false;
            deleteTableOfContentsCheck.Checked = false;
            deleteIndexCheck.Checked = false;
            deleteCrossReferencesCheck.Checked = false;
            deleteFormFieldsCheck.Checked = false;
            deleteSmartArtCheck.Checked = false;
            deleteChartsCheck.Checked = false;
            deleteOleObjectsCheck.Checked = false;
            deleteActiveXControlsCheck.Checked = false;
        }

        // 高级格式标签页的全选和取消全选方法
        private void SelectAllAdvancedFormats()
        {
            deleteCharacterSpacingCheck.Checked = true;
            deleteCharacterScalingCheck.Checked = true;
            deleteCharacterPositionCheck.Checked = true;
            deleteTextEffectsCheck.Checked = true;
            deleteParagraphBordersCheck.Checked = true;
            deleteParagraphShadingCheck.Checked = true;
            deleteCellBordersCheck.Checked = true;
            deleteCellShadingCheck.Checked = true;
            deleteCellMergingCheck.Checked = true;
            deleteCellSplittingCheck.Checked = true;
        }

        private void UnselectAllAdvancedFormats()
        {
            deleteCharacterSpacingCheck.Checked = false;
            deleteCharacterScalingCheck.Checked = false;
            deleteCharacterPositionCheck.Checked = false;
            deleteTextEffectsCheck.Checked = false;
            deleteParagraphBordersCheck.Checked = false;
            deleteParagraphShadingCheck.Checked = false;
            deleteCellBordersCheck.Checked = false;
            deleteCellShadingCheck.Checked = false;
            deleteCellMergingCheck.Checked = false;
            deleteCellSplittingCheck.Checked = false;
        }

        // 文档结构标签页的全选和取消全选方法
        private void SelectAllDocumentStructure()
        {
            deleteColumnsCheck.Checked = true;
            deleteTextBoxesCheck.Checked = true;
            deleteShapesCheck.Checked = true;
            deleteWordArtCheck.Checked = true;
            deleteMarginsCheck.Checked = true;
            deletePageBordersCheck.Checked = true;
            deletePageBackgroundCheck.Checked = true;
        }

        private void UnselectAllDocumentStructure()
        {
            deleteColumnsCheck.Checked = false;
            deleteTextBoxesCheck.Checked = false;
            deleteShapesCheck.Checked = false;
            deleteWordArtCheck.Checked = false;
            deleteMarginsCheck.Checked = false;
            deletePageBordersCheck.Checked = false;
            deletePageBackgroundCheck.Checked = false;
        }

        // 文档属性标签页的全选和取消全选方法
        private void SelectAllDocumentProperties()
        {
            deleteDocumentPropertiesCheck.Checked = true;
            deleteCustomPropertiesCheck.Checked = true;
            deleteDocumentVariablesCheck.Checked = true;
            deleteDocumentStatisticsCheck.Checked = true;
            deleteHiddenTextCheck.Checked = true;
            deleteRevisionMarksCheck.Checked = true;
            deleteCompareResultsCheck.Checked = true;
            deleteDocumentVersionsCheck.Checked = true;
            deleteDocumentThemeCheck.Checked = true;
            deleteDocumentStylesCheck.Checked = true;
        }

        private void UnselectAllDocumentProperties()
        {
            deleteDocumentPropertiesCheck.Checked = false;
            deleteCustomPropertiesCheck.Checked = false;
            deleteDocumentVariablesCheck.Checked = false;
            deleteDocumentStatisticsCheck.Checked = false;
            deleteHiddenTextCheck.Checked = false;
            deleteRevisionMarksCheck.Checked = false;
            deleteCompareResultsCheck.Checked = false;
            deleteDocumentVersionsCheck.Checked = false;
            deleteDocumentThemeCheck.Checked = false;
            deleteDocumentStylesCheck.Checked = false;
        }
    }
}