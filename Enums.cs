/*
 * ========================================
 * 文件名: Enums.cs
 * 功能描述: 枚举类型定义
 * ========================================
 *
 * 主要功能:
 * 1. 定义应用程序中使用的枚举类型
 * 2. 提供类型安全的常量值
 * 3. 简化代码中的选项处理
 *
 * 包含的枚举:
 *
 * PageOrientation:
 * - Portrait: 纵向（竖向）页面方向
 * - Landscape: 横向页面方向
 *
 * ParagraphAlignment:
 * - Left: 左对齐
 * - Center: 居中对齐
 * - Right: 右对齐
 * - Justify: 两端对齐
 *
 * 使用场景:
 * - 页面设置中的页面方向选择
 * - 段落格式设置中的对齐方式选择
 * - 配置界面中的选项绑定
 * - 文档处理逻辑中的参数传递
 *
 * 注意事项:
 * - 枚举值与Aspose.Words库的对应值保持一致
 * - 提供了类型安全的选项处理
 * - 便于扩展新的枚举值
 */

namespace AsposeSlidesFormatter
{
    public enum PageOrientation
    {
        Portrait,
        Landscape
    }

    public enum ParagraphAlignment
    {
        Left,
        Center,
        Right,
        Justify
    }

    // 缩进单位枚举
    public enum IndentUnit
    {
        Characters = 0,  // 字符
        Centimeters = 1, // 厘米
        Millimeters = 2, // 毫米
        Points = 3,      // 磅
        Inches = 4       // 英寸
    }

    // 行距单位枚举
    public enum LineSpacingUnit
    {
        Points = 0,      // 磅
        Centimeters = 1, // 厘米
        Millimeters = 2, // 毫米
        Inches = 3       // 英寸
    }

    // 单位转换工具类
    public static class UnitConverter
    {
        // 字符单位转换为磅 (1字符 = 12磅，这是Word中的标准转换)
        public static double CharactersToPoints(double characters)
        {
            return characters * 12.0;
        }

        // 磅转换为字符单位
        public static double PointsToCharacters(double points)
        {
            return points / 12.0;
        }

        // 厘米转换为磅 (1厘米 = 28.3464567磅)
        public static double CentimetersToPoints(double centimeters)
        {
            return centimeters * 28.3464567;
        }

        // 磅转换为厘米
        public static double PointsToCentimeters(double points)
        {
            return points / 28.3464567;
        }

        // 毫米转换为磅 (1毫米 = 2.83464567磅)
        public static double MillimetersToPoints(double millimeters)
        {
            return millimeters * 2.83464567;
        }

        // 磅转换为毫米
        public static double PointsToMillimeters(double points)
        {
            return points / 2.83464567;
        }

        // 英寸转换为磅 (1英寸 = 72磅)
        public static double InchesToPoints(double inches)
        {
            return inches * 72.0;
        }

        // 磅转换为英寸
        public static double PointsToInches(double points)
        {
            return points / 72.0;
        }

        // 根据单位类型转换为磅
        public static double ConvertToPoints(double value, IndentUnit unit)
        {
            return unit switch
            {
                IndentUnit.Characters => CharactersToPoints(value),
                IndentUnit.Centimeters => CentimetersToPoints(value),
                IndentUnit.Millimeters => MillimetersToPoints(value),
                IndentUnit.Points => value,
                IndentUnit.Inches => InchesToPoints(value),
                _ => value
            };
        }

        // 根据单位类型从磅转换
        public static double ConvertFromPoints(double points, IndentUnit unit)
        {
            return unit switch
            {
                IndentUnit.Characters => PointsToCharacters(points),
                IndentUnit.Centimeters => PointsToCentimeters(points),
                IndentUnit.Millimeters => PointsToMillimeters(points),
                IndentUnit.Points => points,
                IndentUnit.Inches => PointsToInches(points),
                _ => points
            };
        }

        // 根据单位类型转换为磅（行距单位）
        public static double ConvertToPoints(double value, LineSpacingUnit unit)
        {
            return unit switch
            {
                LineSpacingUnit.Points => value,
                LineSpacingUnit.Centimeters => CentimetersToPoints(value),
                LineSpacingUnit.Millimeters => MillimetersToPoints(value),
                LineSpacingUnit.Inches => InchesToPoints(value),
                _ => value
            };
        }

        // 根据单位类型从磅转换（行距单位）
        public static double ConvertFromPoints(double points, LineSpacingUnit unit)
        {
            return unit switch
            {
                LineSpacingUnit.Points => points,
                LineSpacingUnit.Centimeters => PointsToCentimeters(points),
                LineSpacingUnit.Millimeters => PointsToMillimeters(points),
                LineSpacingUnit.Inches => PointsToInches(points),
                _ => points
            };
        }

        // 获取单位显示文本
        public static string GetUnitText(IndentUnit unit)
        {
            return unit switch
            {
                IndentUnit.Characters => "字符",
                IndentUnit.Centimeters => "厘米",
                IndentUnit.Millimeters => "毫米",
                IndentUnit.Points => "磅",
                IndentUnit.Inches => "英寸",
                _ => "磅"
            };
        }

        // 获取单位显示文本（行距单位）
        public static string GetUnitText(LineSpacingUnit unit)
        {
            return unit switch
            {
                LineSpacingUnit.Points => "磅",
                LineSpacingUnit.Centimeters => "厘米",
                LineSpacingUnit.Millimeters => "毫米",
                LineSpacingUnit.Inches => "英寸",
                _ => "磅"
            };
        }
    }
}