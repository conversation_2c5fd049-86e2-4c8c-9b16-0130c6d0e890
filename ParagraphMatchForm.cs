/*
 * ========================================
 * 文件名: ParagraphMatchForm.cs
 * 功能描述: 段落匹配规则管理窗体
 * ========================================
 *
 * 主要功能:
 * 1. 段落匹配规则的创建、编辑和删除
 * 2. 预设标题规则的快速创建
 * 3. 规则的启用/禁用状态管理
 * 4. 规则列表的显示和管理
 * 5. 复杂匹配条件的配置
 * 6. 段落格式的详细设置
 *
 * 界面结构:
 * - 预设按钮面板：9个常用标题类型的快速创建按钮
 * - 规则列表区域：显示所有已定义的匹配规则
 * - 操作按钮区域：添加、编辑、删除、确定、取消
 *
 * 预设标题类型:
 * - 文章大标题：居中对齐，22号字体，一级大纲
 * - 文章小标题：居中对齐，18号字体，二级大纲
 * - 一级标题：16号字体，一级大纲，首行缩进
 * - 二级标题：15号字体，二级大纲，首行缩进
 * - 三级标题：14号字体，三级大纲，首行缩进
 * - 四级标题：13号字体，四级大纲，首行缩进
 * - 五级标题：12号字体，五级大纲，首行缩进
 * - 六级标题：12号字体，六级大纲，首行缩进
 * - 七级标题：12号字体，七级大纲，首行缩进
 *
 * 匹配条件类型:
 * - 开头匹配：段落以指定文本开头
 * - 包含匹配：段落包含指定文本
 * - 结尾匹配：段落以指定文本结尾
 * - 正则表达式：使用正则表达式匹配
 * - 条件组合：多个条件的逻辑组合（与/或）
 *
 * 规则显示信息:
 * - 状态：启用/禁用状态
 * - 规则名称：用户定义的规则名称
 * - 匹配项：显示匹配条件的详细信息
 * - 字体：字体名称
 * - 字号：字体大小
 * - 对齐方式：段落对齐方式
 * - 大纲级别：文档大纲级别
 * - 缩进：缩进类型和数值
 *
 * 核心功能:
 * - 预设规则的快速创建和自定义
 * - 复杂匹配条件的灵活配置
 * - 规则的启用状态可视化管理
 * - 双击编辑和右键菜单操作
 * - 规则的优先级和执行顺序
 *
 * 数据管理:
 * - 与ParagraphMatchRule模型集成
 * - 支持规则的实时预览和验证
 * - 规则状态的持久化保存
 * - 规则冲突检测和处理
 *
 * 注意事项:
 * - 规则的执行顺序影响匹配结果
 * - 支持复杂的条件逻辑组合
 * - 包含完整的状态管理机制
 * - 实现了用户友好的操作界面
 */

#nullable enable
using System;
using System.Windows.Forms;
using System.Collections.Generic;
using System.Linq;
using System.Drawing;
using System.Drawing.Drawing2D;
using AsposeWordFormatter.Models;
using AW = Aspose.Words;

namespace AsposeWordFormatter
{
    public partial class ParagraphMatchForm : Form
    {
        public List<ParagraphMatchRule> Rules { get; private set; }
        private ListView? rulesListView;

        // 预设标题名称
        private readonly string[] presetTitles = new string[]
        {
            "文章大标题", "文章小标题", "一级标题",
            "二级标题", "三级标题", "四级标题",
            "五级标题", "六级标题", "七级标题"
        };

        public ParagraphMatchForm(List<ParagraphMatchRule>? rules = null)
        {
            Rules = rules ?? new List<ParagraphMatchRule>();

            // 在初始化前记录所有规则的启用状态
            System.Diagnostics.Debug.WriteLine("初始化前检查所有规则的启用状态:");
            foreach (var rule in Rules)
            {
                System.Diagnostics.Debug.WriteLine($"规则 {rule.Name}: IsEnabled={rule.IsEnabled}");
            }

            InitializeComponent();

            // 初始化后再次确认所有规则的启用状态
            System.Diagnostics.Debug.WriteLine("初始化后检查所有规则的启用状态:");
            foreach (var rule in Rules)
            {
                System.Diagnostics.Debug.WriteLine($"规则 {rule.Name}: IsEnabled={rule.IsEnabled}");
            }

            // 确保在初始化后立即刷新列表，以正确显示规则的启用状态
            RefreshRulesList();
        }

        private void InitializeComponent()
        {
            this.Text = "段落匹配规则";
            this.Size = new System.Drawing.Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.AutoScroll = true;

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(10),
                AutoSize = true
            };

            // 添加预设按钮面板
            var presetButtonsPanel = CreatePresetButtonsPanel();
            mainLayout.Controls.Add(presetButtonsPanel, 0, 0);
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            // 规则列表
            var rulesGroup = new GroupBox { Text = "已定义的匹配规则", Dock = DockStyle.Fill };
            rulesListView = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = false,
                CheckBoxes = false // 不使用勾选框，改为文字显示启用状态
            };

            rulesListView.Columns.AddRange(new[]
            {
                new ColumnHeader { Text = "状态", Width = 40, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "规则名称", Width = 160, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "匹配项", Width = 175, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "字体", Width = 100, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "字号", Width = 40, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "对齐方式", Width = 70, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "大纲级别", Width = 80, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "缩进", Width = 80, TextAlign = HorizontalAlignment.Center }
            });

            // 添加双击编辑功能
            rulesListView.DoubleClick += new EventHandler(RulesListView_DoubleClick);

            // 添加单击事件处理，确保单击项目时能正确选中项目
            rulesListView.MouseUp += (s, e) => {
                // 获取点击的项
                var hitInfo = rulesListView.HitTest(e.X, e.Y);

                // 如果点击的是项（而不是空白区域）
                if (hitInfo.Item != null)
                {
                    // 确保项目被选中
                    hitInfo.Item.Selected = true;
                }
            };

            // 添加单击事件处理
            rulesListView.MouseClick += (s, e) => {
                // 获取点击的项和子项
                var hitInfo = rulesListView.HitTest(e.X, e.Y);

                // 如果点击的是项（而不是空白区域）
                if (hitInfo.Item != null)
                {
                    // 确保项目被选中
                    hitInfo.Item.Selected = true;
                }
            };



            // 按钮面板
            var buttonPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.RightToLeft,
                Dock = DockStyle.Bottom,
                Height = 40,
                Padding = new Padding(5)
            };

            var okButton = new Button { Text = "确定", Width = 80, Height = 30 };
            okButton.Click += (s, e) => {
                // 最后再次确认所有规则的启用状态
                System.Diagnostics.Debug.WriteLine("确定按钮点击时最终检查所有规则的启用状态:");
                foreach (var rule in Rules)
                {
                    System.Diagnostics.Debug.WriteLine($"规则 {rule.Name}: IsEnabled={rule.IsEnabled}");
                }

                this.DialogResult = DialogResult.OK;
            };

            var cancelButton = new Button { Text = "取消", Width = 80, Height = 30 };
            cancelButton.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            var addButton = new Button { Text = "添加", Width = 80, Height = 30 };
            addButton.Click += new EventHandler(AddButton_Click);

            var editButton = new Button { Text = "编辑", Width = 80, Height = 30 };
            editButton.Click += new EventHandler(EditButton_Click);

            var removeButton = new Button { Text = "删除", Width = 80, Height = 30 };
            removeButton.Click += new EventHandler(RemoveButton_Click);

            buttonPanel.Controls.Add(okButton);
            buttonPanel.Controls.Add(cancelButton);
            buttonPanel.Controls.Add(addButton);
            buttonPanel.Controls.Add(editButton);
            buttonPanel.Controls.Add(removeButton);

            // 添加控件到主布局
            mainLayout.Controls.Add(rulesGroup, 0, 1);
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            mainLayout.Controls.Add(buttonPanel, 0, 2);
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            rulesGroup.Controls.Add(rulesListView);

            this.Controls.Add(mainLayout);

            // 初始刷新列表
            RefreshRulesList();
        }

        private TableLayoutPanel CreatePresetButtonsPanel()
        {
            var panel = new TableLayoutPanel
            {
                ColumnCount = 3,
                RowCount = 3,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 设置列宽
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));

            // 设置行高
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

            // 添加9个预设按钮
            for (int i = 0; i < presetTitles.Length; i++)
            {
                int row = i / 3;
                int col = i % 3;
                string title = presetTitles[i];

                var button = new Button
                {
                    Text = title,
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Margin = new Padding(5),
                    UseVisualStyleBackColor = true
                };

                button.Click += (s, e) => {
                    // 查找是否已存在相同名称的规则
                    var existingRule = Rules.FirstOrDefault(r => r.Name == title);
                    var rule = existingRule ?? CreateDefaultRuleForPreset(title);

                    using (var ruleForm = new ParagraphPresetRuleForm(rule, title))
                    {
                        if (ruleForm.ShowDialog() == DialogResult.OK)
                        {
                            // 如果不存在，则添加到规则列表中
                            if (existingRule == null)
                            {
                                Rules.Add(rule);
                            }
                            RefreshRulesList();
                        }
                    }
                };
                panel.Controls.Add(button, col, row);
            }

            return panel;
        }

        private ParagraphMatchRule CreateDefaultRuleForPreset(string presetTitle)
        {
            var rule = new ParagraphMatchRule
            {
                Name = presetTitle,
                HyperlinkUrl = string.Empty,
                HyperlinkToolTip = string.Empty,
                BookmarkName = string.Empty
            };

            // 根据预设标题设置默认值
            switch (presetTitle)
            {
                case "文章大标题":
                    rule.Bold = true;
                    rule.FontSize = 22;
                    rule.Alignment = AW.ParagraphAlignment.Center;
                    rule.OutlineLevel = AW.OutlineLevel.Level1;
                    break;
                case "文章小标题":
                    rule.Bold = true;
                    rule.FontSize = 18;
                    rule.Alignment = AW.ParagraphAlignment.Center;
                    rule.OutlineLevel = AW.OutlineLevel.Level2;
                    break;
                case "一级标题":
                    rule.Bold = true;
                    rule.FontSize = 16;
                    rule.OutlineLevel = AW.OutlineLevel.Level1;
                    rule.SpecialIndent = Models.SpecialIndent.FirstLine;
                    rule.SpecialIndentValue = 14.4; // 2字符（约14.4磅）
                    break;
                case "二级标题":
                    rule.Bold = true;
                    rule.FontSize = 15;
                    rule.OutlineLevel = AW.OutlineLevel.Level2;
                    rule.SpecialIndent = Models.SpecialIndent.FirstLine;
                    rule.SpecialIndentValue = 14.4; // 2字符（约14.4磅）
                    break;
                case "三级标题":
                    rule.Bold = true;
                    rule.FontSize = 14;
                    rule.OutlineLevel = AW.OutlineLevel.Level3;
                    rule.SpecialIndent = Models.SpecialIndent.FirstLine;
                    rule.SpecialIndentValue = 14.4; // 2字符（约14.4磅）
                    break;
                case "四级标题":
                    rule.Bold = true;
                    rule.FontSize = 13;
                    rule.OutlineLevel = AW.OutlineLevel.Level4;
                    rule.SpecialIndent = Models.SpecialIndent.FirstLine;
                    rule.SpecialIndentValue = 14.4; // 2字符（约14.4磅）
                    break;
                case "五级标题":
                    rule.Bold = true;
                    rule.FontSize = 12;
                    rule.OutlineLevel = AW.OutlineLevel.Level5;
                    rule.SpecialIndent = Models.SpecialIndent.FirstLine;
                    rule.SpecialIndentValue = 14.4; // 2字符（约14.4磅）
                    break;
                case "六级标题":
                    rule.Bold = true;
                    rule.FontSize = 12;
                    rule.OutlineLevel = AW.OutlineLevel.Level6;
                    rule.SpecialIndent = Models.SpecialIndent.FirstLine;
                    rule.SpecialIndentValue = 14.4; // 2字符（约14.4磅）
                    break;
                case "七级标题":
                    rule.Bold = true;
                    rule.FontSize = 12;
                    rule.OutlineLevel = AW.OutlineLevel.Level7;
                    rule.SpecialIndent = Models.SpecialIndent.FirstLine;
                    rule.SpecialIndentValue = 14.4; // 2字符（约14.4磅）
                    break;
            }

            return rule;
        }

        private void RefreshRulesList()
        {
            if (rulesListView == null) return;

            try
            {
                // 在刷新列表前，记录所有规则的启用状态
                System.Diagnostics.Debug.WriteLine("刷新列表前检查所有规则的启用状态:");
                foreach (var rule in Rules)
                {
                    System.Diagnostics.Debug.WriteLine($"规则 {rule.Name}: IsEnabled={rule.IsEnabled}");
                }

                // 保存当前选中的规则
                ParagraphMatchRule? selectedRule = null;
                if (rulesListView.SelectedItems.Count > 0 && rulesListView.SelectedItems[0].Tag is ParagraphMatchRule selected)
                {
                    selectedRule = selected;
                }

                rulesListView.Items.Clear();
                foreach (var rule in Rules)
                {
                    System.Diagnostics.Debug.WriteLine($"为规则 {rule.Name} 创建列表项，启用状态: {rule.IsEnabled}");

                    // 创建列表项，第一列显示启用状态
                    var item = new ListViewItem(rule.IsEnabled ? "启用" : "禁用")
                    {
                        Tag = rule,
                        ForeColor = rule.IsEnabled ? Color.Black : Color.Red // 启用状态使用黑色，禁用状态使用红色
                    };

                    item.SubItems.Add(rule.Name);

                // 匹配项内容
                string matchItems = "";

                // 优先显示段落位置匹配
                if (rule.UseParagraphPosition)
                {
                    matchItems = $"第{rule.ParagraphPosition}段";
                }
                // 显示条件组合
                else if (rule.ConditionItems.Count > 0)
                {
                    matchItems += "条件组合: ";
                    for (int i = 0; i < rule.ConditionItems.Count; i++)
                    {
                        var condItem = rule.ConditionItems[i];
                        string conditionType = "";

                        switch (condItem.Type)
                        {
                            case ConditionType.StartsWith:
                                conditionType = "开头";
                                matchItems += $"{conditionType}:{condItem.Value}";
                                break;
                            case ConditionType.Contains:
                                conditionType = "包含";
                                matchItems += $"{conditionType}:{condItem.Value}";
                                break;
                            case ConditionType.EndsWith:
                                conditionType = "结尾";
                                matchItems += $"{conditionType}:{condItem.Value}";
                                break;
                            case ConditionType.Regex:
                                conditionType = "正则";
                                matchItems += $"{conditionType}:{condItem.Value}";
                                break;
                            case ConditionType.ParagraphPosition:
                                // 段落位置匹配显示为"第X段"
                                if (int.TryParse(condItem.Value, out int position))
                                {
                                    matchItems += $"第{position}段";
                                }
                                else
                                {
                                    matchItems += $"段落位置:{condItem.Value}";
                                }
                                break;
                        }

                        if (i < rule.ConditionItems.Count - 1)
                        {
                            // 使用下一个条件项的Logic属性来显示逻辑运算符
                            var nextCondItem = rule.ConditionItems[i + 1];
                            matchItems += nextCondItem.Logic == LogicOperator.And ? " 且 " : " 或 ";
                        }
                    }
                }
                // 如果没有条件组合，则显示传统匹配条件
                else
                {
                    if (rule.StartWithPatterns.Count > 0)
                        matchItems += "开头: " + string.Join(", ", rule.StartWithPatterns) + "; ";
                    if (rule.ContainsPatterns.Count > 0)
                        matchItems += "包含: " + string.Join(", ", rule.ContainsPatterns) + "; ";
                    if (rule.EndWithPatterns.Count > 0)
                        matchItems += "结尾: " + string.Join(", ", rule.EndWithPatterns) + "; ";
                    if (!string.IsNullOrEmpty(rule.Pattern))
                        matchItems += "模式: " + rule.Pattern;
                }

                item.SubItems.Add(matchItems);
                item.SubItems.Add(rule.FontName);
                item.SubItems.Add(rule.FontSize.ToString());
                item.SubItems.Add(GetAlignmentName(rule.Alignment));
                item.SubItems.Add(GetOutlineLevelName(rule.OutlineLevel));

                // 缩进信息
                string indentInfo = "";
                switch (rule.SpecialIndent)
                {
                    case Models.SpecialIndent.FirstLine:
                        indentInfo = $"首行缩进 {rule.SpecialIndentValue}";
                        break;
                    case Models.SpecialIndent.Hanging:
                        indentInfo = $"悬挂缩进 {rule.SpecialIndentValue}";
                        break;
                    default:
                        indentInfo = $"左: {rule.LeftIndent}, 右: {rule.RightIndent}";
                        break;
                }
                item.SubItems.Add(indentInfo);

                rulesListView.Items.Add(item);
            }
            }
            finally
            {
                // 刷新完成
            }
        }

        private void RulesListView_DoubleClick(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedItems.Count == 0)
                return;

            if (rulesListView.SelectedItems[0].Tag is ParagraphMatchRule selectedRule)
            {
                // 统一使用 ParagraphPresetRuleForm
                using (var ruleForm = new ParagraphPresetRuleForm(selectedRule, selectedRule.Name))
                {
                    if (ruleForm.ShowDialog() == DialogResult.OK)
                    {
                        RefreshRulesList();
                    }
                }
            }
        }

        private void AddButton_Click(object? sender, EventArgs e)
        {
            // 弹出对话框让用户输入规则名称
            string ruleName = "";

            using (var inputDialog = new Form())
            {
                inputDialog.Text = "输入规则名称";
                inputDialog.Size = new Size(350, 150);
                inputDialog.StartPosition = FormStartPosition.CenterParent;
                inputDialog.FormBorderStyle = FormBorderStyle.FixedDialog;
                inputDialog.MaximizeBox = false;
                inputDialog.MinimizeBox = false;

                var label = new Label
                {
                    Text = "请输入新规则名称:",
                    AutoSize = true,
                    Location = new Point(20, 20)
                };

                var textBox = new TextBox
                {
                    Width = 300,
                    Location = new Point(20, 50),
                    Text = "新建规则"
                };
                textBox.Select(0, textBox.Text.Length); // 选中默认文本

                var okButton = new Button
                {
                    Text = "确定",
                    DialogResult = DialogResult.OK,
                    Location = new Point(160, 80)
                };

                var cancelButton = new Button
                {
                    Text = "取消",
                    DialogResult = DialogResult.Cancel,
                    Location = new Point(240, 80)
                };

                inputDialog.AcceptButton = okButton;
                inputDialog.CancelButton = cancelButton;
                inputDialog.Controls.Add(label);
                inputDialog.Controls.Add(textBox);
                inputDialog.Controls.Add(okButton);
                inputDialog.Controls.Add(cancelButton);

                if (inputDialog.ShowDialog() == DialogResult.OK)
                {
                    ruleName = string.IsNullOrWhiteSpace(textBox.Text) ? "新建规则" : textBox.Text;
                }
                else
                {
                    return;
                }
            }

            // 检查是否已存在同名规则
            if (Rules.Any(r => r.Name == ruleName))
            {
                MessageBox.Show($"已存在名为 \"{ruleName}\" 的规则，请使用其他名称。", "名称重复", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var rule = new ParagraphMatchRule
            {
                Name = ruleName,
                HyperlinkUrl = string.Empty,
                HyperlinkToolTip = string.Empty,
                BookmarkName = string.Empty
            };

            // 打开规则编辑对话框
            using (var ruleForm = new ParagraphPresetRuleForm(rule, ruleName))
            {
                if (ruleForm.ShowDialog() == DialogResult.OK && rule != null)
                {
                    Rules.Add(rule);
                    RefreshRulesList();
                }
            }
        }

        private void EditButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("请选择要编辑的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (rulesListView.SelectedItems[0].Tag is ParagraphMatchRule selectedRule)
            {
                // 无论是预设规则还是自定义规则，都使用 ParagraphPresetRuleForm
                using (var ruleForm = new ParagraphPresetRuleForm(selectedRule, selectedRule.Name))
                {
                    if (ruleForm.ShowDialog() == DialogResult.OK)
                    {
                        RefreshRulesList();
                    }
                }
            }
        }

        private void RemoveButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("请选择要删除的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (rulesListView.SelectedItems[0].Tag is ParagraphMatchRule selectedRule && selectedRule != null)
            {
                Rules.Remove(selectedRule);
                RefreshRulesList();
            }
        }

        private string GetAlignmentName(AW.ParagraphAlignment alignment)
        {
            switch (alignment)
            {
                case AW.ParagraphAlignment.Left: return "左对齐";
                case AW.ParagraphAlignment.Center: return "居中";
                case AW.ParagraphAlignment.Right: return "右对齐";
                case AW.ParagraphAlignment.Justify: return "两端对齐";
                case AW.ParagraphAlignment.Distributed: return "分散对齐";
                default: return "左对齐";
            }
        }

        private string GetOutlineLevelName(AW.OutlineLevel outlineLevel)
        {
            switch (outlineLevel)
            {
                case AW.OutlineLevel.Level1: return "1级";
                case AW.OutlineLevel.Level2: return "2级";
                case AW.OutlineLevel.Level3: return "3级";
                case AW.OutlineLevel.Level4: return "4级";
                case AW.OutlineLevel.Level5: return "5级";
                case AW.OutlineLevel.Level6: return "6级";
                case AW.OutlineLevel.Level7: return "7级";
                case AW.OutlineLevel.Level8: return "8级";
                case AW.OutlineLevel.Level9: return "9级";
                case AW.OutlineLevel.BodyText: return "正文";
                default: return "正文";
            }
        }
    }
}
