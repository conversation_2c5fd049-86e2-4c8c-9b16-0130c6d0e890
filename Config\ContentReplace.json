// 内容替换配置
// 此文件定义了文档内容的查找和替换规则
[
  {
    "Find": "XXX公司", // 要查找的文本
    "Replace": "ABC有限公司", // 替换为的文本
    "CaseSensitive": true, // 是否区分大小写
    "UseRegex": false, // 是否使用正则表达式
    "IsEnabled": true, // 是否启用此规则
    "FindWholeWordsOnly": true, // 是否只匹配整个单词
    
    // 搜索范围设置
    "SearchInMainText": true, // 是否在正文中查找替换
    "SearchInHeaders": true, // 是否在页眉中查找替换
    "SearchInFooters": true, // 是否在页脚中查找替换
    "SearchInTextBoxes": true, // 是否在文本框中查找替换
    "SearchInFootnotes": true, // 是否在脚注中查找替换
    "SearchInComments": false // 是否在批注中查找替换
  },
  {
    "Find": "(\\d{3})-(\\d{4})-(\\d{4})", // 电话号码格式（正则表达式）
    "Replace": "($1) $2-$3", // 使用正则表达式分组引用替换
    "CaseSensitive": false,
    "UseRegex": true, // 使用正则表达式
    "IsEnabled": true,
    "FindWholeWordsOnly": false,
    "SearchInMainText": true,
    "SearchInHeaders": false,
    "SearchInFooters": false,
    "SearchInTextBoxes": true,
    "SearchInFootnotes": false,
    "SearchInComments": false
  },
  {
    "Find": "http://", // 将HTTP链接更新为HTTPS
    "Replace": "https://",
    "CaseSensitive": false,
    "UseRegex": false,
    "IsEnabled": true,
    "FindWholeWordsOnly": false,
    "SearchInMainText": true,
    "SearchInHeaders": true,
    "SearchInFooters": true,
    "SearchInTextBoxes": true,
    "SearchInFootnotes": true,
    "SearchInComments": false
  },
  {
    "Find": "([一二三四五六七八九十]+)章", // 将中文数字章节转为阿拉伯数字
    "Replace": "第$1章",
    "CaseSensitive": true,
    "UseRegex": true,
    "IsEnabled": true,
    "FindWholeWordsOnly": false,
    "SearchInMainText": true,
    "SearchInHeaders": false,
    "SearchInFooters": false,
    "SearchInTextBoxes": false,
    "SearchInFootnotes": false,
    "SearchInComments": false
  },
  {
    "Find": "\\s+", // 替换连续空白字符为单个空格
    "Replace": " ",
    "CaseSensitive": false,
    "UseRegex": true,
    "IsEnabled": false, // 此规则默认禁用
    "FindWholeWordsOnly": false,
    "SearchInMainText": true,
    "SearchInHeaders": true,
    "SearchInFooters": true,
    "SearchInTextBoxes": true,
    "SearchInFootnotes": true,
    "SearchInComments": false
  }
] 