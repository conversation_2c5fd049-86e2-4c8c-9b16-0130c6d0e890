/*
 * ========================================
 * 文件名: ContentReplaceForm.cs
 * 功能描述: 内容替换规则管理窗体
 * ========================================
 *
 * 主要功能:
 * 1. 内容替换规则的创建、编辑和删除
 * 2. 替换规则的批量管理和操作
 * 3. 规则的搜索、筛选和排序
 * 4. 替换范围的精确控制
 * 5. 规则的导入导出功能
 * 6. 正则表达式和普通文本替换支持
 *
 * 界面结构:
 * - 规则列表区域：显示所有替换规则
 * - 搜索筛选面板：快速查找和筛选规则
 * - 批量操作面板：批量启用、禁用、删除等
 * - 编辑规则区域：单个规则的详细编辑
 * - 替换范围设置：精确控制替换的文档区域
 * - 功能按钮区域：移动、导入导出等操作
 *
 * 核心特性:
 * - 支持正则表达式和普通文本替换
 * - 区分大小写和全字匹配选项
 * - 精确的替换范围控制（正文、页眉、页脚等）
 * - 规则的优先级管理（上移、下移、置顶等）
 * - 批量操作支持（启用、禁用、删除、范围设置）
 * - CSV格式的导入导出功能
 *
 * 替换范围选项:
 * - SearchInMainText: 正文内容
 * - SearchInHeaders: 页眉内容
 * - SearchInFooters: 页脚内容
 * - SearchInTextBoxes: 文本框内容
 * - SearchInFootnotes: 脚注内容
 * - SearchInComments: 批注内容
 *
 * 规则属性:
 * - Find: 查找的文本或正则表达式
 * - Replace: 替换的目标文本
 * - UseRegex: 是否使用正则表达式
 * - CaseSensitive: 是否区分大小写
 * - FindWholeWordsOnly: 是否全字匹配
 * - IsEnabled: 规则是否启用
 *
 * 数据管理:
 * - 与ContentReplaceRule模型集成
 * - 支持规则的实时预览和验证
 * - 自动保存和恢复功能
 *
 * 注意事项:
 * - 正则表达式的语法验证
 * - 替换操作的安全性检查
 * - 大量规则的性能优化
 * - 规则执行顺序的重要性
 */

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System.IO;
using System.Linq;
using System.Text;
using AsposeWordFormatter.Models;

namespace AsposeWordFormatter
{
    public class ContentReplaceForm : Form
    {
        private ListView? rulesListView;
        private TextBox? findTextBox;
        private TextBox? replaceTextBox;
        private CheckBox? useRegexCheck;
        private CheckBox? isCaseSensitiveCheck;
        private CheckBox? isEnabledCheck;
        private CheckBox? findWholeWordsCheck;
        private CheckBox? searchInMainTextCheck;
        private CheckBox? searchInHeadersCheck;
        private CheckBox? searchInFootersCheck;
        private CheckBox? searchInTextBoxesCheck;
        private CheckBox? searchInFootnotesCheck;
        private CheckBox? searchInCommentsCheck;
        private Button? addButton;
        private Button? editButton;
        private Button? deleteButton;
        private Button? moveUpButton;
        private Button? moveDownButton;
        private Button? moveToTopButton;
        private Button? moveToBottomButton;
        private Button? importButton;
        private Button? exportButton;
        private Button? okButton;
        private Button? cancelButton;

        public List<ContentReplaceRule> Rules { get; private set; }

        public ContentReplaceForm(List<ContentReplaceRule> rules)
        {
            Rules = new List<ContentReplaceRule>(rules);
            InitializeComponent();
            LoadRules();
        }

        private void InitializeComponent()
        {
            this.Text = "内容替换规则";
            this.AutoSize = false;
            this.AutoSizeMode = AutoSizeMode.GrowOnly;
            this.MinimumSize = new System.Drawing.Size(800, 920);
            this.Size = new System.Drawing.Size(800, 920);
            this.Padding = new Padding(8);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = false;

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                AutoSize = false,
                Padding = new Padding(5)
            };

            // 规则列表
            var rulesGroup = new GroupBox { Text = "替换规则", Dock = DockStyle.Fill, Padding = new Padding(10) };

            // 添加搜索和筛选面板
            var searchFilterPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 4,
                RowCount = 1,
                Height = 35,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 设置列宽
            searchFilterPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 50)); // 搜索标签
            searchFilterPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70)); // 搜索框
            searchFilterPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80)); // 筛选标签
            searchFilterPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30)); // 筛选下拉框

            // 添加搜索标签和搜索框
            var searchLabel = new Label { Text = "搜索:", Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft };
            var searchTextBox = new TextBox { Dock = DockStyle.Fill };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            // 添加筛选标签和筛选下拉框
            var filterLabel = new Label { Text = "筛选:", Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft };
            var filterComboBox = new ComboBox { Dock = DockStyle.Fill, DropDownStyle = ComboBoxStyle.DropDownList };
            filterComboBox.Items.AddRange(new object[] {
                "全部规则",
                "仅已启用规则",
                "仅禁用规则",
                "仅正则表达式规则",
                "仅区分大小写规则",
                "仅全字匹配规则",
                "仅应用于正文",
                "仅应用于页眉",
                "仅应用于页脚"
            });
            filterComboBox.SelectedIndex = 0;
            filterComboBox.SelectedIndexChanged += FilterComboBox_SelectedIndexChanged;

            // 添加到搜索筛选面板
            searchFilterPanel.Controls.Add(searchLabel, 0, 0);
            searchFilterPanel.Controls.Add(searchTextBox, 1, 0);
            searchFilterPanel.Controls.Add(filterLabel, 2, 0);
            searchFilterPanel.Controls.Add(filterComboBox, 3, 0);

            // 批量操作面板
            var batchOperationPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Bottom,
                ColumnCount = 4,
                RowCount = 1,
                Height = 35,
                Margin = new Padding(0, 5, 0, 0)
            };

            // 设置列宽均匀
            for (int i = 0; i < 4; i++)
            {
                batchOperationPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));
            }

            // 批量操作按钮
            var batchEnableButton = new Button
            {
                Text = "批量启用",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            batchEnableButton.Click += BatchEnableButton_Click;

            var batchDisableButton = new Button
            {
                Text = "批量禁用",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            batchDisableButton.Click += BatchDisableButton_Click;

            var batchDeleteButton = new Button
            {
                Text = "批量删除",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            batchDeleteButton.Click += BatchDeleteButton_Click;

            var batchScopeButton = new Button
            {
                Text = "批量设置范围",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            batchScopeButton.Click += BatchScopeButton_Click;

            // 添加按钮到批量操作面板
            batchOperationPanel.Controls.Add(batchEnableButton, 0, 0);
            batchOperationPanel.Controls.Add(batchDisableButton, 1, 0);
            batchOperationPanel.Controls.Add(batchDeleteButton, 2, 0);
            batchOperationPanel.Controls.Add(batchScopeButton, 3, 0);

            rulesListView = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = true, // 允许多选
                CheckBoxes = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            rulesListView.Columns.AddRange(new[]
            {
                new ColumnHeader { Text = "启用", Width = 45, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "查找", Width = 180 },
                new ColumnHeader { Text = "替换为", Width = 180 },
                new ColumnHeader { Text = "正则表达式", Width = 80, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "区分大小写", Width = 80, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "全字匹配", Width = 80, TextAlign = HorizontalAlignment.Center },
                new ColumnHeader { Text = "范围", Width = 100, TextAlign = HorizontalAlignment.Center }
            });

            // 事件处理
            rulesListView.SelectedIndexChanged += (s, e) => UpdateButtonState();
            rulesListView.SelectedIndexChanged += RulesListView_SelectedIndexChanged;
            rulesListView.ItemChecked += RulesListView_ItemChecked;

            // 组合规则组面板
            var rulesGroupLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(0)
            };

            rulesGroupLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35)); // 搜索筛选面板
            rulesGroupLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // 规则列表
            rulesGroupLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35)); // 批量操作面板

            rulesGroupLayout.Controls.Add(searchFilterPanel, 0, 0);
            rulesGroupLayout.Controls.Add(rulesListView, 0, 1);
            rulesGroupLayout.Controls.Add(batchOperationPanel, 0, 2);
            rulesGroup.Controls.Add(rulesGroupLayout);

            // 编辑规则面板
            var editGroup = new GroupBox { Text = "编辑规则", Dock = DockStyle.Fill, Padding = new Padding(10) };
            var editLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 6,
                AutoSize = false,
                Padding = new Padding(3)
            };

            // 设置列宽
            editLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            editLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 设置行高
            editLayout.RowStyles.Clear();
            editLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35)); // 启用规则
            editLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35)); // 查找
            editLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35)); // 替换为
            editLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35)); // 复选框面板
            editLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 110)); // 替换范围(增加高度)
            editLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 90)); // 格式说明

            // 调整主布局面板的行比例
            mainLayout.RowStyles.Clear();
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 40)); // 规则列表
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 45)); // 编辑区域 (增加比例)
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 80)); // 按钮区域

            // 添加启用复选框
            isEnabledCheck = new CheckBox
            {
                Text = "启用规则",
                Checked = true,
                AutoSize = true,
                Margin = new Padding(3)
            };
            editLayout.Controls.Add(isEnabledCheck, 0, 0);
            editLayout.SetColumnSpan(isEnabledCheck, 2);

            var findLabel = new Label { Text = "查找:", Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft };
            findTextBox = new TextBox { Dock = DockStyle.Fill };
            editLayout.Controls.Add(findLabel, 0, 1);
            editLayout.Controls.Add(findTextBox, 1, 1);

            var replaceLabel = new Label { Text = "替换为:", Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft };
            replaceTextBox = new TextBox { Dock = DockStyle.Fill };
            editLayout.Controls.Add(replaceLabel, 0, 2);
            editLayout.Controls.Add(replaceTextBox, 1, 2);

            // 将正则表达式和区分大小写复选框放在同一行
            var checkBoxPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Margin = new Padding(0)
            };

            useRegexCheck = new CheckBox
            {
                Text = "使用正则表达式",
                Checked = false,
                AutoSize = true,
                Margin = new Padding(3)
            };
            isCaseSensitiveCheck = new CheckBox
            {
                Text = "区分大小写",
                Checked = false,
                AutoSize = true,
                Margin = new Padding(3)
            };

            // 添加全字匹配复选框
            findWholeWordsCheck = new CheckBox
            {
                Text = "全字匹配",
                Checked = false,
                AutoSize = true,
                Margin = new Padding(3)
            };

            checkBoxPanel.Controls.Add(useRegexCheck);
            checkBoxPanel.Controls.Add(isCaseSensitiveCheck);
            checkBoxPanel.Controls.Add(findWholeWordsCheck);

            editLayout.Controls.Add(checkBoxPanel, 0, 3);
            editLayout.SetColumnSpan(checkBoxPanel, 2);

            // 添加范围限制分组
            var scopeGroup = new GroupBox
            {
                Text = "替换范围",
                Dock = DockStyle.Fill,
                AutoSize = false,
                Margin = new Padding(0, 5, 0, 0),
                Padding = new Padding(10)
            };

            var scopePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(3),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 设置列宽均匀分布
            for (int i = 0; i < 3; i++)
            {
                scopePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            }

            // 设置行高均匀分布
            for (int i = 0; i < 2; i++)
            {
                scopePanel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            }

            // 添加范围复选框
            searchInMainTextCheck = new CheckBox
            {
                Text = "正文",
                Checked = true,
                AutoSize = true,
                Margin = new Padding(5, 4, 5, 4),
                Anchor = AnchorStyles.Left
            };

            searchInHeadersCheck = new CheckBox
            {
                Text = "页眉",
                Checked = true,
                AutoSize = true,
                Margin = new Padding(5, 4, 5, 4),
                Anchor = AnchorStyles.Left
            };

            searchInFootersCheck = new CheckBox
            {
                Text = "页脚",
                Checked = true,
                AutoSize = true,
                Margin = new Padding(5, 4, 5, 4),
                Anchor = AnchorStyles.Left
            };

            searchInTextBoxesCheck = new CheckBox
            {
                Text = "文本框",
                Checked = true,
                AutoSize = true,
                Margin = new Padding(5, 4, 5, 4),
                Anchor = AnchorStyles.Left
            };

            searchInFootnotesCheck = new CheckBox
            {
                Text = "脚注",
                Checked = true,
                AutoSize = true,
                Margin = new Padding(5, 4, 5, 4),
                Anchor = AnchorStyles.Left
            };

            searchInCommentsCheck = new CheckBox
            {
                Text = "批注",
                Checked = false,
                AutoSize = true,
                Margin = new Padding(5, 4, 5, 4),
                Anchor = AnchorStyles.Left
            };

            // 将复选框添加到布局
            scopePanel.Controls.Add(searchInMainTextCheck, 0, 0);
            scopePanel.Controls.Add(searchInHeadersCheck, 1, 0);
            scopePanel.Controls.Add(searchInFootersCheck, 2, 0);
            scopePanel.Controls.Add(searchInTextBoxesCheck, 0, 1);
            scopePanel.Controls.Add(searchInFootnotesCheck, 1, 1);
            scopePanel.Controls.Add(searchInCommentsCheck, 2, 1);

            scopeGroup.Controls.Add(scopePanel);

            // 调整行高以容纳范围分组
            editLayout.SetColumnSpan(scopeGroup, 2);
            editLayout.Controls.Add(scopeGroup, 0, 4);

            // 调整行数和行高以容纳新添加的控件
            editLayout.RowCount = 6;
            editLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 90));

            // 优化导入格式说明显示效果 - 创建独立区域
            var helpPanel = new GroupBox
            {
                Text = "导入格式说明",
                Dock = DockStyle.Fill,
                AutoSize = false,
                Height = 70,
                Margin = new Padding(0, 5, 0, 0)
            };

            var importHelpLabel = new Label
            {
                Text = "CSV文件，每行一条规则\n格式：启用,查找,替换为,是否正则表达式,是否区分大小写,是否全字匹配,正文,页眉,页脚,文本框,脚注,批注\n例如: true,old,new,false,true,false,true,true,true,true,true,false",
                AutoSize = false,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(Font.FontFamily, 8),
                ForeColor = Color.DimGray,
                Padding = new Padding(5)
            };

            helpPanel.Controls.Add(importHelpLabel);
            editLayout.Controls.Add(helpPanel, 0, 5);
            editLayout.SetColumnSpan(helpPanel, 2);

            editGroup.Controls.Add(editLayout);

            // 按钮区域
            var buttonPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = false,
                Margin = new Padding(0, 10, 0, 0)
            };

            // 设置行比例
            buttonPanel.RowStyles.Clear();
            buttonPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));  // 第一行固定高度
            buttonPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));  // 第二行固定高度

            // 创建移动按钮面板（第一行）
            var moveButtonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 5,
                RowCount = 1,
                AutoSize = false,
                Margin = new Padding(0, 0, 0, 3)
            };

            for (int i = 0; i < 5; i++)
            {
                moveButtonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20));
            }

            // 创建功能按钮面板（第二行）
            var functionButtonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 6,
                RowCount = 1,
                AutoSize = false,
                Margin = new Padding(0, 3, 0, 0)
            };

            for (int i = 0; i < 6; i++)
            {
                functionButtonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f / 6));
            }

            // 添加移至顶部按钮
            moveToTopButton = new Button
            {
                Text = "移至顶部",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            moveToTopButton.Click += MoveToTopButton_Click;

            // 上移按钮
            moveUpButton = new Button
            {
                Text = "上移",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            moveUpButton.Click += MoveUpButton_Click;

            // 下移按钮
            moveDownButton = new Button
            {
                Text = "下移",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            moveDownButton.Click += MoveDownButton_Click;

            // 移至底部按钮
            moveToBottomButton = new Button
            {
                Text = "移至底部",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            moveToBottomButton.Click += MoveToBottomButton_Click;

            // 编辑按钮
            editButton = new Button
            {
                Text = "更新",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            editButton.Click += EditButton_Click;

            // 添加按钮
            addButton = new Button
            {
                Text = "添加",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            addButton.Click += AddButton_Click;

            // 导入按钮
            importButton = new Button
            {
                Text = "导入",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            importButton.Click += ImportButton_Click;

            // 导出按钮
            exportButton = new Button
            {
                Text = "导出",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            exportButton.Click += ExportButton_Click;

            // 删除按钮
            deleteButton = new Button
            {
                Text = "删除",
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };
            deleteButton.Click += DeleteButton_Click;

            // 确定按钮
            okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK,
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };

            // 取消按钮
            cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                AutoSize = false,
                Dock = DockStyle.Fill,
                Padding = new Padding(2),
                Margin = new Padding(2),
                Height = 25
            };

            // 将所有按钮添加到面板
            moveButtonsPanel.Controls.Add(moveToTopButton, 0, 0);
            moveButtonsPanel.Controls.Add(moveUpButton, 1, 0);
            moveButtonsPanel.Controls.Add(moveDownButton, 2, 0);
            moveButtonsPanel.Controls.Add(moveToBottomButton, 3, 0);
            moveButtonsPanel.Controls.Add(editButton, 4, 0);

            functionButtonsPanel.Controls.Add(addButton, 0, 0);
            functionButtonsPanel.Controls.Add(importButton, 1, 0);
            functionButtonsPanel.Controls.Add(exportButton, 2, 0);
            functionButtonsPanel.Controls.Add(deleteButton, 3, 0);
            functionButtonsPanel.Controls.Add(okButton, 4, 0);
            functionButtonsPanel.Controls.Add(cancelButton, 5, 0);

            // 将按钮面板添加到主按钮面板
            buttonPanel.Controls.Add(moveButtonsPanel, 0, 0);
            buttonPanel.Controls.Add(functionButtonsPanel, 0, 1);

            // 添加控件到主布局
            mainLayout.Controls.Add(rulesGroup, 0, 0);
            mainLayout.Controls.Add(editGroup, 0, 1);
            mainLayout.Controls.Add(buttonPanel, 0, 2);

            this.Controls.Add(mainLayout);
            this.AcceptButton = okButton;
            this.CancelButton = cancelButton;

            // 确保自适应大小生效
            this.PerformLayout();
        }

        private bool eventsRegistered = false;

        private void ListView_DrawSubItem(object? sender, DrawListViewSubItemEventArgs e)
        {
            if (rulesListView == null) return;

            // 只对范围列（索引6）进行特殊处理
            if (e.ColumnIndex == 6)
            {
                // 创建文本区域
                Rectangle bounds = e.Bounds;
                StringFormat format = new StringFormat()
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };

                // 设置背景色
                if (e.Item.Selected)
                {
                    e.Graphics.FillRectangle(SystemBrushes.Highlight, bounds);
                    string text = e.SubItem?.Text ?? string.Empty;
                    Font font = e.SubItem?.Font ?? (rulesListView != null ? rulesListView.Font : Control.DefaultFont);
                    e.Graphics.DrawString(text, font, SystemBrushes.HighlightText, bounds, format);
                }
                else
                {
                    e.Graphics.FillRectangle(new SolidBrush(e.Item.BackColor), bounds);
                    string text = e.SubItem?.Text ?? string.Empty;
                    Font font = e.SubItem?.Font ?? (rulesListView != null ? rulesListView.Font : Control.DefaultFont);
                    Color foreColor = e.Item.ForeColor;
                    e.Graphics.DrawString(text, font, new SolidBrush(foreColor), bounds, format);
                }

                // 告诉ListView我们已经处理了这个项目
                e.DrawDefault = false;
            }
            else
            {
                e.DrawDefault = true;
            }
        }

        private void ListView_DrawColumnHeader(object? sender, DrawListViewColumnHeaderEventArgs e)
        {
            // 为所有列表头应用一致的样式，所有列表头文字都居中
            using (StringFormat sf = new StringFormat())
            {
                // 所有列表头文字都居中显示
                sf.Alignment = StringAlignment.Center;
                sf.LineAlignment = StringAlignment.Center;
                sf.Trimming = StringTrimming.EllipsisCharacter;

                // 绘制背景
                using (var bgBrush = new SolidBrush(SystemColors.Control))
                {
                    e.Graphics.FillRectangle(bgBrush, e.Bounds);
                }

                // 计算文本绘制区域，添加一些内边距
                Rectangle textRect = new Rectangle(
                    e.Bounds.X + 4,
                    e.Bounds.Y + 2,
                    e.Bounds.Width - 8,
                    e.Bounds.Height - 4);

                // 绘制文本
                using (var textBrush = new SolidBrush(SystemColors.WindowText))
                {
                    e.Graphics.DrawString(e.Header.Text, rulesListView!.Font, textBrush, textRect, sf);
                }

                // 绘制边框以保持一致性
                using (Pen pen = new Pen(SystemColors.ControlDark))
                {
                    // 底部边框
                    e.Graphics.DrawLine(pen,
                        e.Bounds.Left, e.Bounds.Bottom - 1,
                        e.Bounds.Right, e.Bounds.Bottom - 1);

                    // 右侧边框
                    e.Graphics.DrawLine(pen,
                        e.Bounds.Right - 1, e.Bounds.Top,
                        e.Bounds.Right - 1, e.Bounds.Bottom);
                }
            }

            // 告诉ListView我们已经处理了所有表头
            e.DrawDefault = false;
        }

        private void LoadRules()
        {
            // 使用共用的FilterRules方法来显示规则
            currentSearchText = "";
            currentFilterIndex = 0;
            FilterRules();
        }

        private void UpdateButtonState()
        {
            if (rulesListView == null || editButton == null || deleteButton == null ||
                moveUpButton == null || moveDownButton == null || exportButton == null ||
                moveToTopButton == null || moveToBottomButton == null)
                return;

            bool hasSelection = rulesListView.SelectedIndices.Count > 0;
            int selectedIndex = hasSelection ? rulesListView.SelectedIndices[0] : -1;

            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
            moveUpButton.Enabled = hasSelection && selectedIndex > 0;
            moveDownButton.Enabled = hasSelection && selectedIndex < rulesListView.Items.Count - 1;
            exportButton.Enabled = rulesListView.Items.Count > 0;
            moveToTopButton.Enabled = hasSelection && selectedIndex > 0;
            moveToBottomButton.Enabled = hasSelection && selectedIndex < rulesListView.Items.Count - 1;
        }

        private void ClearInputFields()
        {
            if (isEnabledCheck == null || findTextBox == null || replaceTextBox == null ||
                useRegexCheck == null || isCaseSensitiveCheck == null || findWholeWordsCheck == null ||
                searchInMainTextCheck == null || searchInHeadersCheck == null || searchInFootersCheck == null ||
                searchInTextBoxesCheck == null || searchInFootnotesCheck == null || searchInCommentsCheck == null)
                return;

            isEnabledCheck.Checked = true;
            findTextBox.Clear();
            replaceTextBox.Clear();
            useRegexCheck.Checked = false;
            isCaseSensitiveCheck.Checked = false;
            findWholeWordsCheck.Checked = false;

            // 重置范围复选框为默认值
            searchInMainTextCheck.Checked = true;
            searchInHeadersCheck.Checked = true;
            searchInFootersCheck.Checked = true;
            searchInTextBoxesCheck.Checked = true;
            searchInFootnotesCheck.Checked = true;
            searchInCommentsCheck.Checked = false;
        }

        private void RulesListView_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (rulesListView == null || findTextBox == null || replaceTextBox == null ||
                useRegexCheck == null || isCaseSensitiveCheck == null || isEnabledCheck == null ||
                findWholeWordsCheck == null || searchInMainTextCheck == null || searchInHeadersCheck == null ||
                searchInFootersCheck == null || searchInTextBoxesCheck == null || searchInFootnotesCheck == null ||
                searchInCommentsCheck == null)
                return;

            if (rulesListView.SelectedItems.Count > 0)
            {
                var idx = rulesListView.SelectedItems[0].Index;
                var rule = Rules[idx];

                findTextBox.Text = rule.Find;
                replaceTextBox.Text = rule.Replace;
                useRegexCheck.Checked = rule.UseRegex;
                isCaseSensitiveCheck.Checked = rule.CaseSensitive;
                isEnabledCheck.Checked = rule.IsEnabled;
                findWholeWordsCheck.Checked = rule.FindWholeWordsOnly;
                searchInMainTextCheck.Checked = rule.SearchInMainText;
                searchInHeadersCheck.Checked = rule.SearchInHeaders;
                searchInFootersCheck.Checked = rule.SearchInFooters;
                searchInTextBoxesCheck.Checked = rule.SearchInTextBoxes;
                searchInFootnotesCheck.Checked = rule.SearchInFootnotes;
                searchInCommentsCheck.Checked = rule.SearchInComments;
            }
            else
            {
                ClearInputFields();
            }
        }

        private void RulesListView_ItemChecked(object? sender, ItemCheckedEventArgs e)
        {
            if (e.Item.Tag is ContentReplaceRule rule)
            {
                rule.IsEnabled = e.Item.Checked;
                // 如果当前项是选中项，同步更新编辑区域的启用复选框
                if (e.Item.Selected && isEnabledCheck != null)
                {
                    isEnabledCheck.Checked = e.Item.Checked;
                }
            }
        }

        private void AddButton_Click(object? sender, EventArgs e)
        {
            if (findTextBox == null || string.IsNullOrWhiteSpace(findTextBox.Text))
            {
                MessageBox.Show("查找内容不能为空", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (isEnabledCheck == null || replaceTextBox == null || useRegexCheck == null ||
                isCaseSensitiveCheck == null || findWholeWordsCheck == null ||
                searchInMainTextCheck == null || searchInHeadersCheck == null ||
                searchInFootersCheck == null || searchInTextBoxesCheck == null ||
                searchInFootnotesCheck == null || searchInCommentsCheck == null ||
                rulesListView == null)
            {
                MessageBox.Show("界面控件初始化不完整", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            var rule = new ContentReplaceRule
            {
                IsEnabled = isEnabledCheck.Checked,
                Find = findTextBox.Text,
                Replace = replaceTextBox.Text,
                UseRegex = useRegexCheck.Checked,
                CaseSensitive = isCaseSensitiveCheck.Checked,
                FindWholeWordsOnly = findWholeWordsCheck.Checked,
                // 保存范围设置
                SearchInMainText = searchInMainTextCheck.Checked,
                SearchInHeaders = searchInHeadersCheck.Checked,
                SearchInFooters = searchInFootersCheck.Checked,
                SearchInTextBoxes = searchInTextBoxesCheck.Checked,
                SearchInFootnotes = searchInFootnotesCheck.Checked,
                SearchInComments = searchInCommentsCheck.Checked
            };

            Rules.Add(rule);
            LoadRules();
            rulesListView.Items[Rules.Count - 1].Selected = true;
            ClearInputFields();
        }

        private void EditButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedIndices.Count == 0)
            {
                return;
            }

            if (findTextBox == null || string.IsNullOrWhiteSpace(findTextBox.Text))
            {
                MessageBox.Show("查找内容不能为空", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (isEnabledCheck == null || replaceTextBox == null || useRegexCheck == null ||
                isCaseSensitiveCheck == null || findWholeWordsCheck == null ||
                searchInMainTextCheck == null || searchInHeadersCheck == null ||
                searchInFootersCheck == null || searchInTextBoxesCheck == null ||
                searchInFootnotesCheck == null || searchInCommentsCheck == null)
            {
                MessageBox.Show("界面控件初始化不完整", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            int index = rulesListView.SelectedIndices[0];
            var rule = Rules[index];

            rule.IsEnabled = isEnabledCheck.Checked;
            rule.Find = findTextBox.Text;
            rule.Replace = replaceTextBox.Text;
            rule.UseRegex = useRegexCheck.Checked;
            rule.CaseSensitive = isCaseSensitiveCheck.Checked;
            rule.FindWholeWordsOnly = findWholeWordsCheck.Checked;

            // 保存范围设置
            rule.SearchInMainText = searchInMainTextCheck.Checked;
            rule.SearchInHeaders = searchInHeadersCheck.Checked;
            rule.SearchInFooters = searchInFootersCheck.Checked;
            rule.SearchInTextBoxes = searchInTextBoxesCheck.Checked;
            rule.SearchInFootnotes = searchInFootnotesCheck.Checked;
            rule.SearchInComments = searchInCommentsCheck.Checked;

            LoadRules();
            rulesListView.Items[index].Selected = true;
        }

        private void DeleteButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedIndices.Count == 0) return;

            int index = rulesListView.SelectedIndices[0];
            if (MessageBox.Show("确定要删除此规则吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                Rules.RemoveAt(index);
                LoadRules();
                if (rulesListView.Items.Count > 0)
                {
                    rulesListView.Items[Math.Min(index, rulesListView.Items.Count - 1)].Selected = true;
                }
            }
        }

        private void MoveUpButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedIndices.Count == 0) return;

            int index = rulesListView.SelectedIndices[0];
            if (index > 0)
            {
                var rule = Rules[index];
                Rules.RemoveAt(index);
                Rules.Insert(index - 1, rule);
                LoadRules();
                rulesListView.Items[index - 1].Selected = true;
            }
        }

        private void MoveDownButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedIndices.Count == 0) return;

            int index = rulesListView.SelectedIndices[0];
            if (index >= 0 && index < Rules.Count - 1)
            {
                var rule = Rules[index];
                Rules.RemoveAt(index);
                Rules.Insert(index + 1, rule);
                LoadRules();
                rulesListView.Items[index + 1].Selected = true;
            }
        }

        private void MoveToTopButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedIndices.Count == 0) return;

            int index = rulesListView.SelectedIndices[0];
            if (index > 0)
            {
                var rule = Rules[index];
                Rules.RemoveAt(index);
                Rules.Insert(0, rule);
                LoadRules();
                rulesListView.Items[0].Selected = true;
            }
        }

        private void MoveToBottomButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedIndices.Count == 0) return;

            int index = rulesListView.SelectedIndices[0];
            if (index >= 0 && index < Rules.Count - 1)
            {
                var rule = Rules[index];
                Rules.RemoveAt(index);
                Rules.Add(rule);
                LoadRules();
                rulesListView.Items[Rules.Count - 1].Selected = true;
            }
        }

        private void ImportButton_Click(object? sender, EventArgs e)
        {
            try
            {
                using (var openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "文本文件 (*.txt;*.csv)|*.txt;*.csv|所有文件 (*.*)|*.*";
                    openFileDialog.Title = "选择要导入的规则文件";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        var importedRules = ImportRules(openFileDialog.FileName);
                        if (importedRules.Count > 0)
                        {
                            // 添加导入的规则到现有规则列表
                            Rules.AddRange(importedRules);
                            LoadRules();
                            MessageBox.Show($"成功导入 {importedRules.Count} 条规则", "导入成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("没有找到有效的规则", "导入提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (Rules.Count == 0)
                {
                    MessageBox.Show("当前没有规则可导出", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using (var saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Filter = "CSV文件 (*.csv)|*.csv|文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*";
                    saveFileDialog.Title = "导出规则到文件";
                    saveFileDialog.DefaultExt = "csv";
                    saveFileDialog.FileName = "内容替换规则.csv";

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        ExportRules(saveFileDialog.FileName);
                        MessageBox.Show($"成功导出 {Rules.Count} 条规则到文件", "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private List<ContentReplaceRule> ImportRules(string filePath)
        {
            var rules = new List<ContentReplaceRule>();
            var lines = File.ReadAllLines(filePath);
            bool isFirstLine = true;

            foreach (var line in lines)
            {
                if (isFirstLine) // 跳过标题行
                {
                    isFirstLine = false;
                    continue;
                }

                if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                    continue; // 跳过空行和注释行

                var parts = line.Split(',').Select(p => p.Trim()).ToArray();

                if (parts.Length < 3) // 至少需要启用、查找和替换为
                    continue;

                var rule = new ContentReplaceRule
                {
                    IsEnabled = parts.Length > 0 && bool.TryParse(parts[0], out bool isEnabled) ? isEnabled : true,
                    Find = parts[1],
                    Replace = parts[2],
                    UseRegex = parts.Length > 3 && bool.TryParse(parts[3], out bool useRegex) ? useRegex : false,
                    CaseSensitive = parts.Length > 4 && bool.TryParse(parts[4], out bool caseSensitive) ? caseSensitive : false,
                    FindWholeWordsOnly = parts.Length > 5 && bool.TryParse(parts[5], out bool findWholeWordsOnly) ? findWholeWordsOnly : false,
                    // 导入范围设置（若有）
                    SearchInMainText = parts.Length > 6 && bool.TryParse(parts[6], out bool searchInMainText) ? searchInMainText : true,
                    SearchInHeaders = parts.Length > 7 && bool.TryParse(parts[7], out bool searchInHeaders) ? searchInHeaders : true,
                    SearchInFooters = parts.Length > 8 && bool.TryParse(parts[8], out bool searchInFooters) ? searchInFooters : true,
                    SearchInTextBoxes = parts.Length > 9 && bool.TryParse(parts[9], out bool searchInTextBoxes) ? searchInTextBoxes : true,
                    SearchInFootnotes = parts.Length > 10 && bool.TryParse(parts[10], out bool searchInFootnotes) ? searchInFootnotes : true,
                    SearchInComments = parts.Length > 11 && bool.TryParse(parts[11], out bool searchInComments) ? searchInComments : false
                };

                rules.Add(rule);
            }

            return rules;
        }

        private void ExportRules(string filePath)
        {
            var sb = new StringBuilder();

            // 添加注释行说明格式
            sb.AppendLine("# 内容替换规则格式: 启用,查找,替换为,是否正则表达式,是否区分大小写,是否全字匹配,正文,页眉,页脚,文本框,脚注,批注");
            sb.AppendLine("# 例如: true,old,new,false,true,false,true,true,true,true,true,false");
            sb.AppendLine();

            // 添加标题行
            sb.AppendLine("启用,查找,替换为,是否正则表达式,是否区分大小写,是否全字匹配,正文,页眉,页脚,文本框,脚注,批注");

            foreach (var rule in Rules)
            {
                sb.AppendLine($"{rule.IsEnabled},{rule.Find},{rule.Replace},{rule.UseRegex},{rule.CaseSensitive},{rule.FindWholeWordsOnly}," +
                              $"{rule.SearchInMainText},{rule.SearchInHeaders},{rule.SearchInFooters},{rule.SearchInTextBoxes}," +
                              $"{rule.SearchInFootnotes},{rule.SearchInComments}");
            }

            File.WriteAllText(filePath, sb.ToString(), Encoding.UTF8);
        }

        // 获取范围显示文本
        private string GetScopeDisplayText(ContentReplaceRule rule)
        {
            // 如果所有范围都选中，显示"全部"
            if (rule.SearchInMainText && rule.SearchInHeaders && rule.SearchInFooters &&
                rule.SearchInTextBoxes && rule.SearchInFootnotes && rule.SearchInComments)
            {
                return "全部";
            }

            // 如果只有部分范围选中，显示选择的范围数量
            int scopeCount = 0;
            if (rule.SearchInMainText) scopeCount++;
            if (rule.SearchInHeaders) scopeCount++;
            if (rule.SearchInFooters) scopeCount++;
            if (rule.SearchInTextBoxes) scopeCount++;
            if (rule.SearchInFootnotes) scopeCount++;
            if (rule.SearchInComments) scopeCount++;

            if (scopeCount == 0)
            {
                return "无";
            }
            else
            {
                return $"已选{scopeCount}项";
            }
        }

        // 搜索筛选事件处理
        private string currentSearchText = "";
        private int currentFilterIndex = 0;

        private void SearchTextBox_TextChanged(object? sender, EventArgs e)
        {
            if (sender is TextBox textBox)
            {
                currentSearchText = textBox.Text.Trim();
                FilterRules();
            }
        }

        private void FilterComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (sender is ComboBox comboBox)
            {
                currentFilterIndex = comboBox.SelectedIndex;
                FilterRules();
            }
        }

        private void FilterRules()
        {
            if (rulesListView == null) return;

            // 保存当前选中项以便过滤后恢复
            List<ContentReplaceRule> selectedRules = new List<ContentReplaceRule>();
            foreach (ListViewItem item in rulesListView.SelectedItems)
            {
                if (item.Tag is ContentReplaceRule rule)
                {
                    selectedRules.Add(rule);
                }
            }

            // 重新加载并应用过滤
            rulesListView.BeginUpdate();
            rulesListView.Items.Clear();

            foreach (var rule in Rules)
            {
                // 应用搜索过滤
                if (!string.IsNullOrEmpty(currentSearchText))
                {
                    bool matchFound =
                        rule.Find?.Contains(currentSearchText, StringComparison.OrdinalIgnoreCase) == true ||
                        rule.Replace?.Contains(currentSearchText, StringComparison.OrdinalIgnoreCase) == true;

                    if (!matchFound)
                        continue;
                }

                // 应用条件筛选
                switch (currentFilterIndex)
                {
                    case 0: // 全部规则
                        break;
                    case 1: // 仅已启用规则
                        if (!rule.IsEnabled) continue;
                        break;
                    case 2: // 仅禁用规则
                        if (rule.IsEnabled) continue;
                        break;
                    case 3: // 仅正则表达式规则
                        if (!rule.UseRegex) continue;
                        break;
                    case 4: // 仅区分大小写规则
                        if (!rule.CaseSensitive) continue;
                        break;
                    case 5: // 仅全字匹配规则
                        if (!rule.FindWholeWordsOnly) continue;
                        break;
                    case 6: // 仅应用于正文
                        if (!rule.SearchInMainText || rule.SearchInHeaders || rule.SearchInFooters ||
                            rule.SearchInTextBoxes || rule.SearchInFootnotes || rule.SearchInComments) continue;
                        break;
                    case 7: // 仅应用于页眉
                        if (!rule.SearchInHeaders || rule.SearchInMainText) continue;
                        break;
                    case 8: // 仅应用于页脚
                        if (!rule.SearchInFooters || rule.SearchInMainText) continue;
                        break;
                }

                var item = new ListViewItem("")
                {
                    Checked = rule.IsEnabled,
                    Tag = rule
                };
                item.SubItems.Add(rule.Find ?? string.Empty);
                item.SubItems.Add(rule.Replace ?? string.Empty);
                item.SubItems.Add(rule.UseRegex ? "是" : "否");
                item.SubItems.Add(rule.CaseSensitive ? "是" : "否");
                item.SubItems.Add(rule.FindWholeWordsOnly ? "是" : "否");

                // 范围信息摘要
                string scopeText = GetScopeDisplayText(rule);
                var scopeItem = item.SubItems.Add(scopeText);

                rulesListView.Items.Add(item);

                // 恢复之前选中的项
                if (selectedRules.Contains(rule))
                {
                    item.Selected = true;
                }
            }

            // 添加事件处理器统一设置文本居中
            rulesListView.OwnerDraw = true;
            if (!eventsRegistered)
            {
                rulesListView.DrawSubItem += ListView_DrawSubItem;
                rulesListView.DrawColumnHeader += ListView_DrawColumnHeader;
                eventsRegistered = true;
            }

            rulesListView.EndUpdate();
            UpdateButtonState();
            UpdateBatchButtonsState();
        }

        // 批量操作事件处理
        private void BatchEnableButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedItems.Count == 0) return;

            foreach (ListViewItem item in rulesListView.SelectedItems)
            {
                if (item.Tag is ContentReplaceRule rule)
                {
                    rule.IsEnabled = true;
                    item.Checked = true;
                }
            }

            // 更新UI
            rulesListView.Refresh();
        }

        private void BatchDisableButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedItems.Count == 0) return;

            foreach (ListViewItem item in rulesListView.SelectedItems)
            {
                if (item.Tag is ContentReplaceRule rule)
                {
                    rule.IsEnabled = false;
                    item.Checked = false;
                }
            }

            // 更新UI
            rulesListView.Refresh();
        }

        private void BatchDeleteButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedItems.Count == 0) return;

            if (MessageBox.Show($"确定要删除选中的 {rulesListView.SelectedItems.Count} 条规则吗？",
                "确认批量删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
            {
                return;
            }

            // 收集要删除的规则
            List<ContentReplaceRule> rulesToDelete = new List<ContentReplaceRule>();
            foreach (ListViewItem item in rulesListView.SelectedItems)
            {
                if (item.Tag is ContentReplaceRule rule)
                {
                    rulesToDelete.Add(rule);
                }
            }

            // 删除规则
            foreach (var rule in rulesToDelete)
            {
                Rules.Remove(rule);
            }

            // 重新加载列表
            FilterRules();
        }

        private void BatchScopeButton_Click(object? sender, EventArgs e)
        {
            if (rulesListView == null || rulesListView.SelectedItems.Count == 0) return;

            // 创建范围设置对话框
            using (var scopeForm = new BatchScopeForm())
            {
                if (scopeForm.ShowDialog() == DialogResult.OK)
                {
                    foreach (ListViewItem item in rulesListView.SelectedItems)
                    {
                        if (item.Tag is ContentReplaceRule rule)
                        {
                            // 应用选择的范围设置
                            if (scopeForm.MainTextChecked.HasValue)
                                rule.SearchInMainText = scopeForm.MainTextChecked.Value;

                            if (scopeForm.HeadersChecked.HasValue)
                                rule.SearchInHeaders = scopeForm.HeadersChecked.Value;

                            if (scopeForm.FootersChecked.HasValue)
                                rule.SearchInFooters = scopeForm.FootersChecked.Value;

                            if (scopeForm.TextBoxesChecked.HasValue)
                                rule.SearchInTextBoxes = scopeForm.TextBoxesChecked.Value;

                            if (scopeForm.FootnotesChecked.HasValue)
                                rule.SearchInFootnotes = scopeForm.FootnotesChecked.Value;

                            if (scopeForm.CommentsChecked.HasValue)
                                rule.SearchInComments = scopeForm.CommentsChecked.Value;
                        }
                    }

                    // 更新UI
                    FilterRules();
                }
            }
        }

        private void UpdateBatchButtonsState()
        {
            if (rulesListView == null || rulesListView.Parent == null) return;

            bool hasSelection = rulesListView.SelectedItems.Count > 0;

            // 获取批量操作按钮并更新状态
            Control batchOperationPanel = rulesListView.Parent.Controls[2]; // 批量操作面板是第2个控件

            foreach (Control control in batchOperationPanel.Controls)
            {
                if (control is Button button)
                {
                    button.Enabled = hasSelection;
                }
            }
        }
    }

    // 批量设置范围对话框
    public class BatchScopeForm : Form
    {
        private CheckBox? mainTextCheckBox;
        private CheckBox? headersCheckBox;
        private CheckBox? footersCheckBox;
        private CheckBox? textBoxesCheckBox;
        private CheckBox? footnotesCheckBox;
        private CheckBox? commentsCheckBox;

        public bool? MainTextChecked { get; private set; }
        public bool? HeadersChecked { get; private set; }
        public bool? FootersChecked { get; private set; }
        public bool? TextBoxesChecked { get; private set; }
        public bool? FootnotesChecked { get; private set; }
        public bool? CommentsChecked { get; private set; }

        public BatchScopeForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "批量设置替换范围";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimizeBox = false;
            this.MaximizeBox = false;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.AutoScaleMode = AutoScaleMode.Font;

            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(10),
                RowStyles = {
                    new RowStyle(SizeType.Percent, 80),
                    new RowStyle(SizeType.Absolute, 1),
                    new RowStyle(SizeType.Absolute, 40)
                }
            };

            // 创建说明文本
            var infoLabel = new Label
            {
                Text = "选择要修改的范围选项。勾选表示启用该范围，取消勾选表示禁用该范围，不勾选表示保持不变。",
                Dock = DockStyle.Fill,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 0, 0, 10)
            };

            // 创建范围面板
            var scopeGroupBox = new GroupBox
            {
                Text = "替换范围",
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            var scopePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 6,
                AutoSize = true
            };

            // 设置均匀行高
            for (int i = 0; i < 6; i++)
            {
                scopePanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100f / 6));
            }

            // 添加三态复选框
            mainTextCheckBox = CreateThreeStateCheckBox("正文");
            headersCheckBox = CreateThreeStateCheckBox("页眉");
            footersCheckBox = CreateThreeStateCheckBox("页脚");
            textBoxesCheckBox = CreateThreeStateCheckBox("文本框");
            footnotesCheckBox = CreateThreeStateCheckBox("脚注");
            commentsCheckBox = CreateThreeStateCheckBox("批注");

            // 添加到面板
            scopePanel.Controls.Add(mainTextCheckBox, 0, 0);
            scopePanel.Controls.Add(headersCheckBox, 0, 1);
            scopePanel.Controls.Add(footersCheckBox, 0, 2);
            scopePanel.Controls.Add(textBoxesCheckBox, 1, 0);
            scopePanel.Controls.Add(footnotesCheckBox, 1, 1);
            scopePanel.Controls.Add(commentsCheckBox, 1, 2);

            // 添加按钮面板
            var separator = new Panel
            {
                Dock = DockStyle.Fill,
                Height = 1,
                BackColor = SystemColors.ControlDark
            };

            var buttonPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1
            };

            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));

            var okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK,
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            okButton.Click += OkButton_Click;

            var cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };

            buttonPanel.Controls.Add(new Panel(), 0, 0); // 空面板作为填充
            buttonPanel.Controls.Add(okButton, 1, 0);
            buttonPanel.Controls.Add(cancelButton, 2, 0);

            // 组装UI
            scopeGroupBox.Controls.Add(scopePanel);

            var topPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2
            };

            topPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            topPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            topPanel.Controls.Add(infoLabel, 0, 0);
            topPanel.Controls.Add(scopeGroupBox, 0, 1);

            mainPanel.Controls.Add(topPanel, 0, 0);
            mainPanel.Controls.Add(separator, 0, 1);
            mainPanel.Controls.Add(buttonPanel, 0, 2);

            this.Controls.Add(mainPanel);
            this.AcceptButton = okButton;
            this.CancelButton = cancelButton;
        }

        private CheckBox CreateThreeStateCheckBox(string text)
        {
            var checkBox = new CheckBox
            {
                Text = text,
                ThreeState = true,
                CheckState = CheckState.Indeterminate,
                AutoSize = true,
                Margin = new Padding(5, 10, 5, 10)
            };

            return checkBox;
        }

        private void OkButton_Click(object? sender, EventArgs e)
        {
            // 将CheckState转换为bool?，Indeterminate为null
            MainTextChecked = mainTextCheckBox != null ? ConvertCheckState(mainTextCheckBox.CheckState) : null;
            HeadersChecked = headersCheckBox != null ? ConvertCheckState(headersCheckBox.CheckState) : null;
            FootersChecked = footersCheckBox != null ? ConvertCheckState(footersCheckBox.CheckState) : null;
            TextBoxesChecked = textBoxesCheckBox != null ? ConvertCheckState(textBoxesCheckBox.CheckState) : null;
            FootnotesChecked = footnotesCheckBox != null ? ConvertCheckState(footnotesCheckBox.CheckState) : null;
            CommentsChecked = commentsCheckBox != null ? ConvertCheckState(commentsCheckBox.CheckState) : null;
        }

        private bool? ConvertCheckState(CheckState state)
        {
            switch (state)
            {
                case CheckState.Checked: return true;
                case CheckState.Unchecked: return false;
                default: return null; // Indeterminate
            }
        }
    }
}