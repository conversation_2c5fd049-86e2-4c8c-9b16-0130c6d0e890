// 文件名替换规则配置
// 此文件定义了处理后文件的命名规则
[
  {
    // 基本信息
    "Name": "移除特殊字符", // 规则名称
    "IsEnabled": true, // 是否启用此规则
    
    // 匹配设置
    "Pattern": "[\\\\/:*?\"<>|]", // 匹配模式，此处匹配所有Windows不允许的文件名字符
    "UseRegex": true, // 是否使用正则表达式
    "CaseSensitive": false, // 是否区分大小写
    
    // 替换设置
    "Replacement": "_", // 替换为下划线
    "ReplaceAll": true, // 是否替换所有匹配项
    
    // 文件名范围限制
    "MaxFileNameLength": 255, // 最大文件名长度（Windows下NTFS文件系统限制）
    "TruncateFileNameIfTooLong": true, // 如果文件名太长是否截断
    "AddHashSuffixOnTruncate": true, // 截断时是否添加哈希后缀以避免冲突
    
    // 高级设置
    "ApplyToDirectoryName": false, // 是否应用于目录名
    "ApplyToExtension": false, // 是否应用于扩展名
    "PreserveExtension": true // 是否保留文件扩展名
  },
  {
    "Name": "添加日期前缀",
    "IsEnabled": true,
    "Pattern": "^",
    "UseRegex": true,
    "CaseSensitive": false,
    "Replacement": "{date:yyyy-MM-dd}_", // 使用日期标记
    "ReplaceAll": false,
    "MaxFileNameLength": 255,
    "TruncateFileNameIfTooLong": true,
    "AddHashSuffixOnTruncate": true,
    "ApplyToDirectoryName": false,
    "ApplyToExtension": false,
    "PreserveExtension": true
  },
  {
    "Name": "清理空格",
    "IsEnabled": true,
    "Pattern": "\\s+",
    "UseRegex": true,
    "CaseSensitive": false,
    "Replacement": "_",
    "ReplaceAll": true,
    "MaxFileNameLength": 255,
    "TruncateFileNameIfTooLong": true,
    "AddHashSuffixOnTruncate": true,
    "ApplyToDirectoryName": false,
    "ApplyToExtension": false,
    "PreserveExtension": true
  },
  {
    "Name": "标准化扩展名",
    "IsEnabled": true,
    "Pattern": "\\.docx?$",
    "UseRegex": true,
    "CaseSensitive": false,
    "Replacement": ".docx", // 统一使用docx格式
    "ReplaceAll": false,
    "MaxFileNameLength": 255,
    "TruncateFileNameIfTooLong": true,
    "AddHashSuffixOnTruncate": true,
    "ApplyToDirectoryName": false,
    "ApplyToExtension": true,
    "PreserveExtension": false
  },
  {
    "Name": "添加处理标记",
    "IsEnabled": false, // 默认禁用
    "Pattern": "$",
    "UseRegex": true,
    "CaseSensitive": false,
    "Replacement": "_processed", // 在文件名末尾添加标记
    "ReplaceAll": false,
    "MaxFileNameLength": 255,
    "TruncateFileNameIfTooLong": true,
    "AddHashSuffixOnTruncate": true,
    "ApplyToDirectoryName": false,
    "ApplyToExtension": false,
    "PreserveExtension": true
  }
] 