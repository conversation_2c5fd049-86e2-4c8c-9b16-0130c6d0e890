/*
 * ========================================
 * 文件名: ContentReplaceRuleForm.cs
 * 功能描述: 内容替换规则编辑窗体
 * ========================================
 *
 * 主要功能:
 * 1. 单个内容替换规则的详细编辑
 * 2. 查找和替换文本的输入和编辑
 * 3. 正则表达式选项配置
 * 4. 大小写敏感性设置
 * 5. 全字匹配选项配置
 * 6. 规则启用状态控制
 *
 * 界面结构:
 * - 规则启用开关：控制规则是否生效
 * - 查找文本框：输入要查找的文本或正则表达式
 * - 替换文本框：输入替换后的目标文本
 * - 选项复选框：正则表达式、区分大小写、全字匹配
 * - 帮助说明：正则表达式捕获组使用说明
 * - 操作按钮：确定、取消
 *
 * 核心功能:
 * - 支持多行文本的查找和替换
 * - 正则表达式模式支持
 * - 捕获组功能说明和引导
 * - 实时数据绑定和验证
 * - 用户友好的界面布局
 *
 * 正则表达式支持:
 * - 标准正则表达式语法
 * - 捕获组功能：使用()创建捕获组
 * - 反向引用：在替换中使用$1、$2等引用捕获组
 * - 提供详细的使用说明和示例
 *
 * 数据绑定:
 * - Find: 查找的文本或正则表达式
 * - Replace: 替换的目标文本
 * - UseRegex: 是否使用正则表达式
 * - CaseSensitive: 是否区分大小写
 * - FindWholeWordsOnly: 是否全字匹配
 * - IsEnabled: 规则是否启用
 *
 * 界面特性:
 * - 自适应窗体大小
 * - 多行文本框支持
 * - 醒目的帮助说明
 * - 直观的选项布局
 * - 实时数据更新
 *
 * 注意事项:
 * - 正则表达式语法验证
 * - 捕获组的正确使用
 * - 特殊字符的转义处理
 * - 替换结果的预期效果
 */

using System;
using System.Drawing;
using System.Windows.Forms;
using AsposeWordFormatter.Models;

namespace AsposeWordFormatter
{
    public class ContentReplaceRuleForm : Form
    {
        private readonly ContentReplaceRule rule;
        private TextBox findTextBox = null!;
        private TextBox replaceTextBox = null!;
        private CheckBox useRegexCheckBox = null!;
        private CheckBox caseSensitiveCheckBox = null!;
        private CheckBox isEnabledCheckBox = null!;
        private CheckBox findWholeWordsCheckBox = null!;

        public ContentReplaceRule Rule => rule;

        public ContentReplaceRuleForm(ContentReplaceRule rule)
        {
            this.rule = rule ?? throw new ArgumentNullException(nameof(rule));
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "编辑内容替换规则";
            this.AutoSize = true;
            this.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            this.MinimumSize = new System.Drawing.Size(400, 0);
            this.Padding = new Padding(10);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 6,  // 减少行数，因为两个复选框合并到一行
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 设置列宽
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20));
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 80));

            // 添加启用复选框
            isEnabledCheckBox = new CheckBox
            {
                Text = "启用规则",
                Checked = rule.IsEnabled,
                AutoSize = true,
                Margin = new Padding(3)
            };
            isEnabledCheckBox.CheckedChanged += (s, e) => rule.IsEnabled = isEnabledCheckBox.Checked;
            mainLayout.Controls.Add(isEnabledCheckBox, 0, 0);
            mainLayout.SetColumnSpan(isEnabledCheckBox, 2);

            // 查找文本
            var findLabel = new Label { Text = "查找:", Dock = DockStyle.Fill, TextAlign = ContentAlignment.MiddleLeft };
            mainLayout.Controls.Add(findLabel, 0, 1);

            findTextBox = new TextBox
            {
                Text = rule.Find,
                Dock = DockStyle.Fill,
                Multiline = true,
                MinimumSize = new Size(0, 60),
                Margin = new Padding(3)
            };
            findTextBox.TextChanged += (s, e) => rule.Find = findTextBox.Text;
            mainLayout.Controls.Add(findTextBox, 1, 1);

            // 替换文本
            var replaceLabel = new Label { Text = "替换为:", Dock = DockStyle.Fill, TextAlign = ContentAlignment.MiddleLeft };
            mainLayout.Controls.Add(replaceLabel, 0, 2);

            replaceTextBox = new TextBox
            {
                Text = rule.Replace,
                Dock = DockStyle.Fill,
                Multiline = true,
                MinimumSize = new Size(0, 60),
                Margin = new Padding(3)
            };
            replaceTextBox.TextChanged += (s, e) => rule.Replace = replaceTextBox.Text;
            mainLayout.Controls.Add(replaceTextBox, 1, 2);

            // 添加导入导出说明
            var helpLabel = new Label
            {
                Text = "【提示】正则表达式支持捕获组功能：\n在查找文本中使用 (内容) 创建捕获组，在替换中用 $1, $2 等引用它们。",
                AutoSize = true,
                BackColor = Color.LightYellow,  // 添加背景色使其更醒目
                BorderStyle = BorderStyle.FixedSingle,  // 添加边框使其更醒目
                ForeColor = Color.Red,  // 将文本颜色改为红色使其更醒目
                Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold),  // 使文本加粗
                Padding = new Padding(5),  // 添加内边距
                Margin = new Padding(3, 10, 3, 10),  // 保持上下边距
                Width = 400  // 指定宽度确保换行正确显示
            };
            mainLayout.Controls.Add(helpLabel, 0, 3);  // 将位置改为从第一列开始
            mainLayout.SetColumnSpan(helpLabel, 2);  // 设置为跨两列

            // 两个复选框合并到一行
            var checkBoxLayout = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight,
                Margin = new Padding(3)
            };

            useRegexCheckBox = new CheckBox
            {
                Text = "使用正则表达式",
                Checked = rule.UseRegex,
                AutoSize = true,
                Margin = new Padding(3)
            };
            useRegexCheckBox.CheckedChanged += (s, e) => rule.UseRegex = useRegexCheckBox.Checked;

            caseSensitiveCheckBox = new CheckBox
            {
                Text = "区分大小写",
                Checked = rule.CaseSensitive,
                AutoSize = true,
                Margin = new Padding(3)
            };
            caseSensitiveCheckBox.CheckedChanged += (s, e) => rule.CaseSensitive = caseSensitiveCheckBox.Checked;

            findWholeWordsCheckBox = new CheckBox
            {
                Text = "全字匹配",
                Checked = rule.FindWholeWordsOnly,
                AutoSize = true,
                Margin = new Padding(3)
            };
            findWholeWordsCheckBox.CheckedChanged += (s, e) => rule.FindWholeWordsOnly = findWholeWordsCheckBox.Checked;

            checkBoxLayout.Controls.Add(useRegexCheckBox);
            checkBoxLayout.Controls.Add(caseSensitiveCheckBox);
            checkBoxLayout.Controls.Add(findWholeWordsCheckBox);
            mainLayout.Controls.Add(checkBoxLayout, 0, 3);
            mainLayout.SetColumnSpan(checkBoxLayout, 2);

            // 按钮
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                AutoSize = true,
                Margin = new Padding(0, 10, 0, 0)
            };

            var okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK,
                AutoSize = true,
                Padding = new Padding(10, 5, 10, 5)
            };

            var cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                AutoSize = true,
                Padding = new Padding(10, 5, 10, 5),
                Margin = new Padding(10, 0, 0, 0)
            };

            buttonPanel.Controls.Add(cancelButton);
            buttonPanel.Controls.Add(okButton);
            mainLayout.Controls.Add(buttonPanel, 0, 5);
            mainLayout.SetColumnSpan(buttonPanel, 2);

            this.Controls.Add(mainLayout);
            this.AcceptButton = okButton;
            this.CancelButton = cancelButton;

            // 确保自适应大小生效
            this.PerformLayout();
        }
    }
}