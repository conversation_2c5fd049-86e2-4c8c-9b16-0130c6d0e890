/*
 * ========================================
 * 文件名: ScheduleSettings.cs
 * 功能描述: 定时任务设置数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义定时任务的完整配置选项
 * 2. 支持多种定时模式（一次性、指定时间、定时循环）
 * 3. 提供任务调度的计算和验证逻辑
 * 4. 包含任务执行记录和状态管理
 * 5. 支持任务的保存和加载功能
 *
 * 定时模式类型:
 * - 0: 一次性定时（指定具体时间执行一次）
 * - 1: 指定时间定时（按频率在指定时间执行）
 * - 2: 定时循环（按间隔时间循环执行）
 *
 * 基本设置:
 * - Enabled: 是否启用定时任务
 * - ScheduleMode: 定时模式类型
 *
 * 一次性定时设置:
 * - OneTimeRunTime: 一次性执行的具体时间
 *
 * 指定时间定时设置:
 * - RunFrequency: 执行频率（每年、每月、每天、每小时）
 * - Year/Month/Day/Hour/Minute/Second: 具体的时间参数
 * - RunYearly/RunMonthly/RunDaily/RunHourly: 兼容性属性
 *
 * 定时循环设置:
 * - StartTime: 开始时间
 * - IntervalDays/IntervalHours/IntervalMinutes/IntervalSeconds: 时间间隔
 *
 * 高级设置:
 * - UseLimitedRuns: 是否限制运行次数
 * - UseExpirationTime: 是否设置过期时间
 * - MaxRunCount: 最大运行次数
 * - RunCount: 已运行次数
 * - ExpirationTime: 过期时间
 * - LastRunTime: 最后运行时间
 * - NextRunTime: 下次运行时间
 *
 * 核心方法:
 * - CalculateNextRunTime(): 计算下次运行时间
 * - ShouldRunNow(): 检查当前是否应该运行任务
 * - SaveRunRecord(): 记录任务执行完成
 * - SaveToFile(): 保存设置到XML文件
 * - LoadFromFile(): 从XML文件加载设置
 *
 * 时间计算逻辑:
 * - 支持复杂的时间间隔计算
 * - 处理月份天数变化（如2月28/29日）
 * - 包含错过执行时间的补偿机制
 * - 提供30秒的执行时间容差
 *
 * 数据持久化:
 * - 使用XML序列化保存和加载
 * - 包含完整的异常处理
 * - 支持设置的版本兼容性
 *
 * 注意事项:
 * - 支持复杂的定时任务调度需求
 * - 包含完整的时间计算和验证逻辑
 * - 实现了任务状态的持久化管理
 * - 提供了灵活的定时模式选择
 */

using System;
using System.IO;
using System.Xml.Serialization;

namespace AsposeWordFormatter.Models
{
    public class ScheduleSettings
    {
        // 基本设置
        public bool Enabled { get; set; } = false;
        public int ScheduleMode { get; set; } = 0; // 0=一次性, 1=指定时间, 2=定时

        // 一次性启动设置
        public DateTime OneTimeRunTime { get; set; } = DateTime.Now.AddMinutes(5);

        // 指定时间启动设置
        // 将多个布尔值改为单一的频率枚举值
        // 0=每年, 1=每月, 2=每天, 3=每时
        public int RunFrequency { get; set; } = 0;

        // 保留这些属性用于兼容性，但不再直接使用
        public bool RunYearly { get; set; } = false;
        public bool RunMonthly { get; set; } = false;
        public bool RunDaily { get; set; } = true;
        public bool RunHourly { get; set; } = false;

        // 日期时间值
        public int Year { get; set; } = DateTime.Now.Year;
        public int Month { get; set; } = DateTime.Now.Month;
        public int Day { get; set; } = DateTime.Now.Day;
        public int Hour { get; set; } = DateTime.Now.Hour;
        public int Minute { get; set; } = 0;
        public int Second { get; set; } = 0;

        // 定时启动设置
        public DateTime StartTime { get; set; } = DateTime.Now;
        public int IntervalDays { get; set; } = 0;
        public int IntervalHours { get; set; } = 1;
        public int IntervalMinutes { get; set; } = 0;
        public int IntervalSeconds { get; set; } = 0;

        // 高级设置
        public bool UseLimitedRuns { get; set; } = false;
        public bool UseExpirationTime { get; set; } = false;
        public int MaxRunCount { get; set; } = 1;
        public int RunCount { get; set; } = 0;
        public DateTime ExpirationTime { get; set; } = DateTime.Now.AddDays(7);
        public DateTime LastRunTime { get; set; } = DateTime.MinValue;
        public DateTime NextRunTime { get; set; } = DateTime.MinValue;

        // 用于计算下次运行时间的方法
        public DateTime CalculateNextRunTime()
        {
            // 如果定时处理未启用，返回 MinValue
            if (!Enabled)
                return DateTime.MinValue;

            // 一次性定时处理
            if (ScheduleMode == 0)
            {
                // 如果已经过期，返回 MinValue
                if (OneTimeRunTime <= DateTime.Now)
                    return DateTime.MinValue;

                return OneTimeRunTime;
            }
            // 指定时间定时处理
            else if (ScheduleMode == 1)
            {
                DateTime now = DateTime.Now;
                DateTime next = DateTime.MinValue;

                // 根据频率计算下一次运行时间
                switch (RunFrequency)
                {
                    // 每年运行
                    case 0:
                        next = new DateTime(now.Year, Month, Day, Hour, Minute, Second);
                        if (next <= now)
                            next = next.AddYears(1);
                        break;

                    // 每月运行
                    case 1:
                        // 处理天数超过当月最大天数的情况
                        int lastDayOfMonth = DateTime.DaysInMonth(now.Year, now.Month);
                        int dayToUse = Math.Min(Day, lastDayOfMonth);

                        next = new DateTime(now.Year, now.Month, dayToUse, Hour, Minute, Second);
                        if (next <= now)
                        {
                            if (now.Month == 12)
                                next = new DateTime(now.Year + 1, 1, dayToUse, Hour, Minute, Second);
                            else
                            {
                                int nextMonth = now.Month + 1;
                                lastDayOfMonth = DateTime.DaysInMonth(now.Year, nextMonth);
                                dayToUse = Math.Min(Day, lastDayOfMonth);
                                next = new DateTime(now.Year, nextMonth, dayToUse, Hour, Minute, Second);
                            }
                        }
                        break;

                    // 每天运行
                    case 2:
                        next = new DateTime(now.Year, now.Month, now.Day, Hour, Minute, Second);
                        if (next <= now)
                            next = next.AddDays(1);
                        break;

                    // 每小时运行
                    case 3:
                        next = new DateTime(now.Year, now.Month, now.Day, now.Hour, Minute, Second);
                        if (next <= now)
                            next = next.AddHours(1);
                        break;
                }

                return next;
            }
            // 定时启动处理
            else if (ScheduleMode == 2)
            {
                // 如果还未开始，且开始时间在当前时间之后
                if (LastRunTime == DateTime.MinValue && StartTime > DateTime.Now)
                    return StartTime;

                // 如果已经运行过，计算下一次运行时间
                if (LastRunTime != DateTime.MinValue)
                {
                    // 计算时间间隔（以秒为单位）
                    int intervalInSeconds = (IntervalDays * 86400) + (IntervalHours * 3600) + (IntervalMinutes * 60) + IntervalSeconds;

                    // 计算下一次运行时间
                    DateTime next = LastRunTime.AddSeconds(intervalInSeconds);

                    // 如果下一次运行时间已经过去，计算未来最近的运行时间
                    if (next < DateTime.Now)
                    {
                        // 计算已错过的次数
                        TimeSpan timeSinceLast = DateTime.Now - LastRunTime;
                        int missedIntervals = (int)(timeSinceLast.TotalSeconds / intervalInSeconds) + 1;

                        // 计算下一次运行时间
                        next = LastRunTime.AddSeconds(intervalInSeconds * missedIntervals);
                    }

                    return next;
                }

                // 首次运行，使用开始时间
                return StartTime;
            }

            return DateTime.MinValue;
        }

        // 检查是否应该运行任务
        public bool ShouldRunNow()
        {
            if (!Enabled)
                return false;

            // 检查运行次数限制
            if (UseLimitedRuns && RunCount >= MaxRunCount)
                return false;

            // 检查过期时间限制
            if (UseExpirationTime && DateTime.Now > ExpirationTime)
                return false;

            DateTime now = DateTime.Now;

            // 一次性定时处理
            if (ScheduleMode == 0)
            {
                // 检查是否到达运行时间（允许30秒误差）
                TimeSpan diff = now - OneTimeRunTime;
                return diff.TotalSeconds >= 0 && diff.TotalSeconds < 30;
            }

            // 指定时间定时处理
            else if (ScheduleMode == 1)
            {
                // 根据频率检查是否应该运行
                bool shouldRun = false;

                switch (RunFrequency)
                {
                    // 每年运行
                    case 0:
                        shouldRun = now.Month == Month && now.Day == Day &&
                                   now.Hour == Hour && now.Minute == Minute &&
                                   now.Second >= Second && now.Second < Second + 30;
                        break;

                    // 每月运行
                    case 1:
                        shouldRun = now.Day == Day && now.Hour == Hour &&
                                   now.Minute == Minute && now.Second >= Second &&
                                   now.Second < Second + 30;
                        break;

                    // 每天运行
                    case 2:
                        shouldRun = now.Hour == Hour && now.Minute == Minute &&
                                   now.Second >= Second && now.Second < Second + 30;
                        break;

                    // 每小时运行
                    case 3:
                        shouldRun = now.Minute == Minute && now.Second >= Second &&
                                   now.Second < Second + 30;
                        break;
                }

                return shouldRun;
            }

            // 定时启动处理
            else if (ScheduleMode == 2)
            {
                // 如果尚未运行过且已经超过开始时间
                if (LastRunTime == DateTime.MinValue && now >= StartTime)
                    return true;

                // 如果已经运行过，检查是否到达下一次运行时间
                if (LastRunTime != DateTime.MinValue)
                {
                    // 计算时间间隔（以秒为单位）
                    int intervalInSeconds = (IntervalDays * 86400) + (IntervalHours * 3600) + (IntervalMinutes * 60) + IntervalSeconds;

                    // 计算下一次运行时间
                    DateTime next = LastRunTime.AddSeconds(intervalInSeconds);

                    // 允许30秒误差
                    TimeSpan diff = now - next;
                    return diff.TotalSeconds >= 0 && diff.TotalSeconds < 30;
                }
            }

            return false;
        }

        // 记录已完成一次运行
        public void SaveRunRecord()
        {
            LastRunTime = DateTime.Now;
            RunCount++;
            NextRunTime = CalculateNextRunTime();
        }

        // 保存设置到文件
        public void SaveToFile(string filePath)
        {
            try
            {
                XmlSerializer serializer = new XmlSerializer(typeof(ScheduleSettings));

                using (StreamWriter writer = new StreamWriter(filePath))
                {
                    serializer.Serialize(writer, this);
                }
            }
            catch (Exception)
            {
                // 保存失败时的处理
            }
        }

        // 从文件加载设置
        public static ScheduleSettings LoadFromFile(string filePath)
        {
            ScheduleSettings settings = new ScheduleSettings();

            try
            {
                if (File.Exists(filePath))
                {
                    XmlSerializer serializer = new XmlSerializer(typeof(ScheduleSettings));

                    using (StreamReader reader = new StreamReader(filePath))
                    {
                        var deserializedSettings = serializer.Deserialize(reader);
                        if (deserializedSettings != null)
                        {
                            settings = (ScheduleSettings)deserializedSettings;
                            // 计算下一次运行时间
                            settings.NextRunTime = settings.CalculateNextRunTime();
                        }
                        else
                        {
                            settings = new ScheduleSettings();
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 加载失败时返回默认设置
                settings = new ScheduleSettings();
            }

            return settings;
        }
    }
}