/*
 * ========================================
 * 文件名: FontFormat.cs
 * 功能描述: 字体格式数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义字体格式的基本配置选项
 * 2. 支持多语言字体设置
 * 3. 提供字体样式和大小配置
 * 4. 包含字体颜色和效果设置
 * 5. 支持中文、西文、复杂脚本的独立字体配置
 *
 * 基础字体属性:
 * - FontName: 默认字体名称
 * - FontSize: 默认字体大小
 * - FontColor: 字体颜色
 * - Bold: 是否粗体
 * - Italic: 是否斜体
 *
 * 多语言字体支持:
 * - ChineseFontName: 中文字体名称
 * - WesternFontName: 西文字体名称
 * - ComplexScriptFontName: 复杂脚本字体名称
 * - ChineseFontStyle: 中文字体样式（常规、粗体、斜体等）
 * - WesternFontStyle: 西文字体样式
 * - ComplexScriptFontStyle: 复杂脚本字体样式
 * - ChineseFontSize: 中文字体大小
 * - WesternFontSize: 西文字体大小
 * - ComplexScriptFontSize: 复杂脚本字体大小
 *
 * 字体样式类型:
 * - Regular: 常规样式
 * - Bold: 粗体样式
 * - Italic: 斜体样式
 * - BoldItalic: 粗斜体样式
 *
 * 应用场景:
 * - 全局字体格式设置
 * - 段落匹配规则中的字体配置
 * - 多语言文档的字体处理
 * - 字体样式的统一管理
 *
 * 注意事项:
 * - 支持不同语言文字的独立字体设置
 * - 提供了完整的字体样式选项
 * - 包含字体颜色的Color类型支持
 * - 适用于复杂的多语言文档处理
 */

using System.Drawing;

namespace AsposeWordFormatter
{
    public class FontFormat
    {
        public string FontName { get; set; } = "宋体";
        public double FontSize { get; set; } = 12;
        public Color FontColor { get; set; } = Color.Black;
        public bool Bold { get; set; } = false;
        public bool Italic { get; set; } = false;
        public string ChineseFontName { get; set; } = "宋体";
        public string WesternFontName { get; set; } = "Times New Roman";
        public string ComplexScriptFontName { get; set; } = "Arial";
        public FontStyle ChineseFontStyle { get; set; } = FontStyle.Regular;
        public FontStyle WesternFontStyle { get; set; } = FontStyle.Regular;
        public FontStyle ComplexScriptFontStyle { get; set; } = FontStyle.Regular;
        public double ChineseFontSize { get; set; } = 12;
        public double WesternFontSize { get; set; } = 12;
        public double ComplexScriptFontSize { get; set; } = 12;
    }
}