/*
 * ========================================
 * 文件名: SlidesFormatter.cs
 * 功能描述: 核心演示文稿处理引擎
 * ========================================
 *
 * 主要功能:
 * 1. PowerPoint演示文稿的批量处理和格式化
 * 2. 多种演示文稿操作功能的统一调度
 * 3. 文件处理进度监控和状态报告
 * 4. 异步处理和取消操作支持
 * 5. 错误处理和重试机制
 *
 * 核心处理模块:
 * - 幻灯片管理: 幻灯片添加、删除、复制、排序
 * - 内容删除: 空白内容、特定元素、格式清理
 * - 内容替换: 文本替换、正则表达式匹配
 * - 文本格式: 文本框格式化和样式设置
 * - 图片处理: 图片压缩、优化、水印处理
 * - 文档属性: 元数据和属性管理
 * - 文件名处理: 文件重命名规则
 * - PDF转换: PowerPoint转PDF功能
 *
 * 技术特性:
 * - 支持多线程并发处理
 * - 实现了完整的进度报告机制
 * - 包含详细的错误处理和日志记录
 * - 支持处理取消和暂停功能
 * - 优化了内存使用和性能
 *
 * 依赖关系:
 * - Aspose.Slides: 核心演示文稿处理库
 * - Logger: 日志记录系统
 * - Models: 各种配置数据模型
 * - ConvertToPdf: PDF转换模块
 *
 * 事件系统:
 * - ProgressChanged: 处理进度更新
 * - StatusChanged: 状态变化通知
 * - ErrorOccurred: 错误事件报告
 *
 * 注意事项:
 * - 所有演示文稿操作都是线程安全的
 * - 支持大批量文件的高效处理
 * - 包含完整的异常恢复机制
 * - 实现了资源的自动释放和清理
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Aspose.Slides;
using Aspose.Slides.Export;
using AsposeSlidesFormatter.Models;
using AS = Aspose.Slides;
using System.Drawing;
using System.Collections.Concurrent;
using System.Text;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace AsposeSlidesFormatter
{
    public class ProgressEventArgs : EventArgs
    {
        public double Progress { get; }
        public int TotalFiles { get; }
        public int CompletedFiles { get; }
        public int FailedFiles { get; }
        public int RetriedFiles { get; }

        public ProgressEventArgs(double progress, int totalFiles, int completedFiles, int failedFiles, int retriedFiles)
        {
            Progress = progress;
            TotalFiles = totalFiles;
            CompletedFiles = completedFiles;
            FailedFiles = failedFiles;
            RetriedFiles = retriedFiles;
        }
    }

    public class ErrorEventArgs : EventArgs
    {
        public string FilePath { get; }
        public Exception Exception { get; }

        public ErrorEventArgs(string filePath, Exception exception)
        {
            FilePath = filePath;
            Exception = exception;
        }
    }

    public class SlidesFormatter : IDisposable
    {
        private Models.Settings settings;
        private readonly Logger logger;
        private string sourceDirectory;
        private string outputDirectory;
        private CancellationTokenSource? cancellationTokenSource;
        private bool _isStopped;
        private int _totalFiles;
        private int _completedFiles;
        private int _failedFiles;
        private int _retriedFiles;
        private ConcurrentDictionary<string, int> _retryAttempts = new ConcurrentDictionary<string, int>();
        private ManualResetEvent pauseEvent = new ManualResetEvent(true);

        // 用于保存进度的变量
        private List<string> _remainingFiles = new List<string>();
        private string _progressFile;
        private MemoryMonitor _memoryMonitor;
        private bool _isResuming = false;

        // 公开统计属性
        public int TotalFiles => _totalFiles;
        public int CompletedFiles => _completedFiles;
        public int FailedFiles => _failedFiles;
        public int RetriedFiles => _retriedFiles;

        public event EventHandler<ProgressEventArgs>? ProgressChanged;
        public event EventHandler<string>? StatusChanged;
        public event EventHandler<ErrorEventArgs>? ErrorOccurred;

        public SlidesFormatter(Models.Settings settings, Logger logger, string sourceDirectory, string outputDirectory)
        {
            this.settings = settings ?? throw new ArgumentNullException(nameof(settings));
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
            this.sourceDirectory = sourceDirectory ?? throw new ArgumentNullException(nameof(sourceDirectory));
            this.outputDirectory = outputDirectory ?? throw new ArgumentNullException(nameof(outputDirectory));

            // 设置进度文件路径
            _progressFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "progress.dat");

            // 初始化内存监控器
            _memoryMonitor = new MemoryMonitor(logger);
        }

        public void StartProcessing(string sourceDirectory, string outputDirectory)
        {
            try
            {
                this.sourceDirectory = sourceDirectory;
                this.outputDirectory = outputDirectory;

                // 记录开始处理的日志
                logger.Log($"开始处理文件：源目录 {sourceDirectory}, 目标目录 {outputDirectory}");

                cancellationTokenSource?.Dispose();
                cancellationTokenSource = new CancellationTokenSource();

                // 设置处理状态
                _isStopped = false;

                // 重置所有统计计数器
                _totalFiles = 0;
                _completedFiles = 0;
                _failedFiles = 0;
                _retriedFiles = 0;
                _retryAttempts.Clear();
                _remainingFiles.Clear();

                // 强制删除旧的进度文件，确保总是从头开始新的处理任务
                DeleteProgressFile();
                _isResuming = false;

                // 启动异步处理，并捕获任何异常
                Task.Run(async () =>
                {
                    try
                    {
                        await StartAsync(cancellationTokenSource.Token);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("文件处理过程中发生错误", ex);
                        OnStatusChanged($"处理过程中发生错误: {ex.Message}");
                        _isStopped = true;
                    }
                });

                // 记录处理已启动的日志
                logger.Log("文件处理已启动，正在搜索文件...");
            }
            catch (Exception ex)
            {
                logger.LogError("启动处理时发生错误", ex);
                OnStatusChanged($"启动处理时发生错误: {ex.Message}");
                _isStopped = true;
                throw;
            }
        }

        public void StopProcessing()
        {
            _isStopped = true;
            pauseEvent.Set();  // 释放所有等待的线程
            cancellationTokenSource?.Cancel();

            // 保存当前进度
            SaveProgress();

            // 通知调用者处理已停止
            OnStatusChanged("处理已停止");
        }

        private async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                _isStopped = false;

                // 如果不是恢复处理，获取文件列表
                if (!_isResuming)
                {
                    var files = await Task.Run(() => GetPresentationFiles(sourceDirectory), cancellationToken);
                    _remainingFiles.AddRange(files);
                    _totalFiles = files.Count;
                    OnStatusChanged($"找到 {_totalFiles} 个演示文稿文件");
                }
                else
                {
                    // 恢复处理，使用保存的文件列表
                    _totalFiles = _completedFiles + _remainingFiles.Count;
                    OnStatusChanged($"恢复处理：已完成 {_completedFiles}/{_totalFiles} 个文件，剩余 {_remainingFiles.Count} 个文件");

                    // 启动时记录内存使用情况
                    _memoryMonitor.LogMemoryUsage("恢复处理开始");
                }

                // 检查是否有文件需要处理
                if (_remainingFiles.Count == 0)
                {
                    OnStatusChanged("没有找到需要处理的文件");
                    _isStopped = true;
                    return;
                }

                // 开始批量处理
                await ProcessFilesInBatches(cancellationToken);

                // 处理完成
                if (!_isStopped && !cancellationToken.IsCancellationRequested)
                {
                    OnStatusChanged($"处理完成！共处理 {_completedFiles} 个文件，失败 {_failedFiles} 个");
                    DeleteProgressFile(); // 删除进度文件
                }
            }
            catch (OperationCanceledException)
            {
                OnStatusChanged("处理已取消");
                logger.Log("文件处理已取消");
            }
            catch (Exception ex)
            {
                logger.LogError("处理过程中发生错误", ex);
                OnStatusChanged($"处理过程中发生错误: {ex.Message}");
            }
            finally
            {
                _isStopped = true;
            }
        }

        private List<string> GetPresentationFiles(string directory)
        {
            var files = new List<string>();

            try
            {
                logger.Log($"开始搜索演示文稿文件，目录: {directory}");

                // 支持的演示文稿文件扩展名
                var supportedExtensions = new[] { ".ppt", ".pptx", ".pptm", ".pot", ".potx", ".potm", ".pps", ".ppsx", ".ppsm", ".odp" };

                var searchOption = settings.IncludeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;

                foreach (var extension in supportedExtensions)
                {
                    try
                    {
                        var extensionFiles = Directory.GetFiles(directory, $"*{extension}", searchOption);
                        files.AddRange(extensionFiles);
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning($"搜索 {extension} 文件时出错: {ex.Message}");
                    }
                }

                // 过滤掉临时文件
                files = files.Where(f => !IsOfficeTemporaryFile(f)).ToList();

                logger.Log($"找到 {files.Count} 个演示文稿文件");
                return files;
            }
            catch (Exception ex)
            {
                logger.LogError($"搜索演示文稿文件时出错: {ex.Message}", ex);
                return files;
            }
        }

        private bool IsOfficeTemporaryFile(string filePath)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);

                // Office临时文件通常以~$开头
                if (fileName.StartsWith("~$"))
                    return true;

                // PowerPoint临时文件模式
                if (fileName.StartsWith("ppt") && fileName.Contains("tmp"))
                    return true;

                return false;
            }
            catch
            {
                return false;
            }
        }

        private async Task ProcessFilesInBatches(CancellationToken cancellationToken)
        {
            try
            {
                int batchSize = settings.BatchSize > 0 ? settings.BatchSize : 50;

                while (_remainingFiles.Count > 0 && !_isStopped && !cancellationToken.IsCancellationRequested)
                {
                    // 获取当前批次的文件
                    var currentBatch = _remainingFiles.Take(batchSize).ToList();

                    // 处理当前批次
                    await ProcessBatch(currentBatch, cancellationToken);

                    // 从待处理列表中移除已处理的文件
                    foreach (var file in currentBatch)
                    {
                        _remainingFiles.Remove(file);
                    }

                    // 保存进度
                    SaveProgress();
                }
            }
            catch (Exception ex)
            {
                logger.LogError("批量处理文件时出错", ex);
                throw;
            }
        }

        private async Task ProcessBatch(List<string> fileBatch, CancellationToken cancellationToken)
        {
            var tasks = new List<Task>();
            var semaphore = new SemaphoreSlim(settings.MaxThreads > 0 ? settings.MaxThreads : 1);

            foreach (var filePath in fileBatch)
            {
                if (_isStopped || cancellationToken.IsCancellationRequested)
                    break;

                await semaphore.WaitAsync(cancellationToken);

                var task = Task.Run(async () =>
                {
                    try
                    {
                        await ProcessFileWithRetryAsync(filePath, cancellationToken);
                        OnProgressChanged();
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"处理文件时出错: {filePath}", ex);
                        OnErrorOccurred(filePath, ex);
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }, cancellationToken);

                tasks.Add(task);
            }

            await Task.WhenAll(tasks);
        }

        private async Task ProcessFileWithRetryAsync(string filePath, CancellationToken cancellationToken)
        {
            int maxRetries = settings.MaxRetryCount;
            int retryCount = 0;

            while (retryCount <= maxRetries)
            {
                try
                {
                    bool success = await ProcessFileAsync(filePath, cancellationToken);
                    if (success)
                    {
                        Interlocked.Increment(ref _completedFiles);
                        if (retryCount > 0)
                        {
                            Interlocked.Increment(ref _retriedFiles);
                        }
                        return;
                    }
                    else
                    {
                        throw new Exception("文件处理失败");
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    retryCount++;
                    if (retryCount > maxRetries)
                    {
                        Interlocked.Increment(ref _failedFiles);
                        logger.LogError($"文件处理失败，已达到最大重试次数: {filePath}", ex);
                        OnErrorOccurred(filePath, ex);
                        return;
                    }
                    else
                    {
                        logger.LogWarning($"文件处理失败，正在重试 ({retryCount}/{maxRetries}): {filePath} - {ex.Message}");
                        await Task.Delay(1000 * retryCount, cancellationToken); // 递增延迟
                    }
                }
            }
        }

        private async Task<bool> ProcessFileAsync(string filePath, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                logger.LogWarning("文件路径为空");
                return false;
            }

            // 检查是否是临时文件
            if (IsOfficeTemporaryFile(filePath))
            {
                logger.LogWarning($"跳过Office临时文件: {filePath}");
                return true;
            }

            try
            {
                var fileName = Path.GetFileName(filePath);
                OnStatusChanged($"正在处理: {fileName}");

                // 开始计时
                var stopwatch = Stopwatch.StartNew();

                // 记录处理开始时的内存使用情况
                _memoryMonitor.LogMemoryUsage($"处理文件 {fileName} 开始");

                cancellationToken.ThrowIfCancellationRequested();

                // 添加对暂停状态的检查
                pauseEvent.WaitOne();

                // 检查是否取消
                if (cancellationToken.IsCancellationRequested)
                {
                    return false;
                }

                // 确定输出文件路径
                string outputFilePath = GetOutputFilePath(filePath);

                // 检查冲突处理
                if (File.Exists(outputFilePath))
                {
                    switch (settings.ConflictHandling)
                    {
                        case "跳过":
                            logger.Log($"文件已存在，跳过: {outputFilePath}");
                            return true;
                        case "重命名":
                            outputFilePath = GetUniqueFileName(outputFilePath);
                            break;
                        case "覆盖":
                        default:
                            // 继续处理，覆盖现有文件
                            break;
                    }
                }

                // 处理演示文稿文件
                bool success = await ProcessPresentationFile(filePath, outputFilePath, cancellationToken);

                stopwatch.Stop();
                logger.Log($"文件处理完成: {fileName}, 耗时: {stopwatch.ElapsedMilliseconds}ms");

                return success;
            }
            catch (Exception ex)
            {
                logger.LogError($"处理文件时出错: {filePath}", ex);
                throw;
            }
        }

        private async Task<bool> ProcessPresentationFile(string inputPath, string outputPath, CancellationToken cancellationToken)
        {
            try
            {
                // 加载演示文稿
                using (var presentation = new Presentation(inputPath))
                {
                    logger.Log($"成功加载演示文稿: {Path.GetFileName(inputPath)}");

                    // 应用各种处理功能
                    await ApplyPresentationProcessing(presentation, cancellationToken);

                    // 确保输出目录存在
                    var outputDir = Path.GetDirectoryName(outputPath);
                    if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
                    {
                        Directory.CreateDirectory(outputDir);
                    }

                    // 保存演示文稿
                    if (settings.ProcessOriginalFiles)
                    {
                        // 直接保存到原文件
                        presentation.Save(inputPath, SaveFormat.Pptx);
                        logger.Log($"已更新原文件: {inputPath}");
                    }
                    else
                    {
                        // 保存到输出路径
                        presentation.Save(outputPath, SaveFormat.Pptx);
                        logger.Log($"已保存到: {outputPath}");

                        // 如果是移动模式，删除原文件
                        if (settings.MoveFiles && inputPath != outputPath)
                        {
                            File.Delete(inputPath);
                            logger.Log($"已删除原文件: {inputPath}");
                        }
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"处理演示文稿文件时出错: {inputPath}", ex);
                return false;
            }
        }

        private async Task ApplyPresentationProcessing(Presentation presentation, CancellationToken cancellationToken)
        {
            try
            {
                // 应用内容删除设置
                if (settings.EnableDeleteContent)
                {
                    await ApplyContentDeletion(presentation, cancellationToken);
                }

                // 应用内容替换设置
                if (settings.EnableContentReplace)
                {
                    await ApplyContentReplacement(presentation, cancellationToken);
                }

                // 应用文档属性设置
                if (settings.EnableDocumentProperties)
                {
                    await ApplyDocumentProperties(presentation, cancellationToken);
                }

                // 应用PDF转换设置（如果启用）
                if (settings.EnableWordToPdf)
                {
                    // 注意：这里应该是PPT转PDF，但保持设置名称兼容性
                    await ApplyPdfConversion(presentation, cancellationToken);
                }

                logger.Log("演示文稿处理设置已应用");
            }
            catch (Exception ex)
            {
                logger.LogError("应用演示文稿处理设置时出错", ex);
                throw;
            }
        }

        private async Task ApplyContentDeletion(Presentation presentation, CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                try
                {
                    logger.Log("开始应用内容删除设置...");

                    // 删除空白幻灯片
                    if (settings.DeleteSettings?.DeleteEmptySlides == true)
                    {
                        DeleteEmptySlides(presentation);
                    }

                    // 删除图片
                    if (settings.DeleteSettings?.DeleteImages == true)
                    {
                        DeleteImages(presentation);
                    }

                    // 删除备注
                    if (settings.DeleteSettings?.DeleteNotes == true)
                    {
                        DeleteNotes(presentation);
                    }

                    logger.Log("内容删除设置已应用");
                }
                catch (Exception ex)
                {
                    logger.LogError("应用内容删除设置时出错", ex);
                    throw;
                }
            }, cancellationToken);
        }

        private void DeleteEmptySlides(Presentation presentation)
        {
            try
            {
                var slidesToRemove = new List<ISlide>();

                foreach (ISlide slide in presentation.Slides)
                {
                    bool isEmpty = true;

                    // 检查幻灯片是否有内容
                    foreach (IShape shape in slide.Shapes)
                    {
                        if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                        {
                            string text = autoShape.TextFrame.Text?.Trim();
                            if (!string.IsNullOrEmpty(text))
                            {
                                isEmpty = false;
                                break;
                            }
                        }
                        else if (shape is IPictureFrame || shape is IChart || shape is ITable)
                        {
                            isEmpty = false;
                            break;
                        }
                    }

                    if (isEmpty)
                    {
                        slidesToRemove.Add(slide);
                    }
                }

                // 删除空白幻灯片
                foreach (var slide in slidesToRemove)
                {
                    presentation.Slides.Remove(slide);
                }

                logger.Log($"已删除 {slidesToRemove.Count} 张空白幻灯片");
            }
            catch (Exception ex)
            {
                logger.LogError("删除空白幻灯片时出错", ex);
            }
        }

        private void DeleteImages(Presentation presentation)
        {
            try
            {
                int deletedCount = 0;

                foreach (ISlide slide in presentation.Slides)
                {
                    var shapesToRemove = new List<IShape>();

                    foreach (IShape shape in slide.Shapes)
                    {
                        if (shape is IPictureFrame)
                        {
                            shapesToRemove.Add(shape);
                            deletedCount++;
                        }
                    }

                    // 删除图片形状
                    foreach (var shape in shapesToRemove)
                    {
                        slide.Shapes.Remove(shape);
                    }
                }

                logger.Log($"已删除 {deletedCount} 张图片");
            }
            catch (Exception ex)
            {
                logger.LogError("删除图片时出错", ex);
            }
        }

        private void DeleteNotes(Presentation presentation)
        {
            try
            {
                int deletedCount = 0;

                foreach (ISlide slide in presentation.Slides)
                {
                    if (slide.NotesSlideManager.NotesSlide != null)
                    {
                        slide.NotesSlideManager.RemoveNotesSlide();
                        deletedCount++;
                    }
                }

                logger.Log($"已删除 {deletedCount} 张幻灯片的备注");
            }
            catch (Exception ex)
            {
                logger.LogError("删除备注时出错", ex);
            }
        }

        private async Task ApplyContentReplacement(Presentation presentation, CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                try
                {
                    logger.Log("开始应用内容替换设置...");

                    if (settings.ContentReplaceRules != null && settings.ContentReplaceRules.Count > 0)
                    {
                        foreach (var rule in settings.ContentReplaceRules.Where(r => r.IsEnabled))
                        {
                            ApplyTextReplacement(presentation, rule);
                        }
                    }

                    logger.Log("内容替换设置已应用");
                }
                catch (Exception ex)
                {
                    logger.LogError("应用内容替换设置时出错", ex);
                    throw;
                }
            }, cancellationToken);
        }

        private void ApplyTextReplacement(Presentation presentation, ContentReplaceRule rule)
        {
            try
            {
                int replacementCount = 0;

                foreach (ISlide slide in presentation.Slides)
                {
                    foreach (IShape shape in slide.Shapes)
                    {
                        if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                        {
                            string originalText = autoShape.TextFrame.Text;
                            string newText = originalText;

                            if (rule.UseRegex)
                            {
                                var regex = new Regex(rule.Find, rule.CaseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase);
                                newText = regex.Replace(originalText, rule.Replace);
                            }
                            else
                            {
                                var comparison = rule.CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
                                if (rule.FindWholeWordsOnly)
                                {
                                    // 实现全字匹配逻辑
                                    var regex = new Regex($@"\b{Regex.Escape(rule.Find)}\b",
                                        rule.CaseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase);
                                    newText = regex.Replace(originalText, rule.Replace);
                                }
                                else
                                {
                                    newText = originalText.Replace(rule.Find, rule.Replace, comparison);
                                }
                            }

                            if (newText != originalText)
                            {
                                autoShape.TextFrame.Text = newText;
                                replacementCount++;
                            }
                        }
                    }
                }

                if (replacementCount > 0)
                {
                    logger.Log($"规则 '{rule.Find}' -> '{rule.Replace}' 应用了 {replacementCount} 次替换");
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"应用文本替换规则时出错: {rule.Find}", ex);
            }
        }

        private async Task ApplyDocumentProperties(Presentation presentation, CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                try
                {
                    logger.Log("开始应用文档属性设置...");

                    var properties = presentation.DocumentProperties;

                    if (settings.DocumentPropertiesSettings != null)
                    {
                        var docProps = settings.DocumentPropertiesSettings;

                        if (!string.IsNullOrEmpty(docProps.Title))
                            properties.Title = docProps.Title;

                        if (!string.IsNullOrEmpty(docProps.Author))
                            properties.Author = docProps.Author;

                        if (!string.IsNullOrEmpty(docProps.Subject))
                            properties.Subject = docProps.Subject;

                        if (!string.IsNullOrEmpty(docProps.Keywords))
                            properties.Keywords = docProps.Keywords;

                        if (!string.IsNullOrEmpty(docProps.Comments))
                            properties.Comments = docProps.Comments;

                        if (!string.IsNullOrEmpty(docProps.Category))
                            properties.Category = docProps.Category;

                        if (!string.IsNullOrEmpty(docProps.Manager))
                            properties.Manager = docProps.Manager;

                        if (!string.IsNullOrEmpty(docProps.Company))
                            properties.Company = docProps.Company;
                    }

                    logger.Log("文档属性设置已应用");
                }
                catch (Exception ex)
                {
                    logger.LogError("应用文档属性设置时出错", ex);
                    throw;
                }
            }, cancellationToken);
        }

        private async Task ApplyPdfConversion(Presentation presentation, CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                try
                {
                    logger.Log("开始PPT转PDF处理...");
                    // 这里可以添加PDF转换逻辑
                    // 由于这是一个复杂的功能，暂时保留接口
                    logger.Log("PPT转PDF处理已完成");
                }
                catch (Exception ex)
                {
                    logger.LogError("PPT转PDF处理时出错", ex);
                    throw;
                }
            }, cancellationToken);
        }

        private string GetOutputFilePath(string inputPath)
        {
            try
            {
                if (settings.ProcessOriginalFiles)
                {
                    return inputPath;
                }

                string relativePath = Path.GetRelativePath(sourceDirectory, inputPath);
                string outputPath = Path.Combine(outputDirectory, relativePath);

                // 应用文件名替换规则
                if (settings.EnableFileNameReplace && settings.FileNameReplaceRules != null)
                {
                    string fileName = Path.GetFileName(outputPath);
                    string directory = Path.GetDirectoryName(outputPath) ?? "";

                    foreach (var rule in settings.FileNameReplaceRules.Where(r => r.IsEnabled))
                    {
                        if (rule.UseRegex)
                        {
                            var regex = new Regex(rule.Find, rule.CaseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase);
                            fileName = regex.Replace(fileName, rule.Replace);
                        }
                        else
                        {
                            var comparison = rule.CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
                            fileName = fileName.Replace(rule.Find, rule.Replace, comparison);
                        }
                    }

                    outputPath = Path.Combine(directory, fileName);
                }

                return outputPath;
            }
            catch (Exception ex)
            {
                logger.LogError($"获取输出文件路径时出错: {inputPath}", ex);
                return inputPath;
            }
        }

        private string GetUniqueFileName(string filePath)
        {
            try
            {
                string directory = Path.GetDirectoryName(filePath) ?? "";
                string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(filePath);
                string extension = Path.GetExtension(filePath);

                int counter = 1;
                string newFilePath = filePath;

                while (File.Exists(newFilePath))
                {
                    string newFileName = $"{fileNameWithoutExtension}({counter}){extension}";
                    newFilePath = Path.Combine(directory, newFileName);
                    counter++;
                }

                return newFilePath;
            }
            catch (Exception ex)
            {
                logger.LogError($"生成唯一文件名时出错: {filePath}", ex);
                return filePath;
            }
        }

        // 事件处理方法
        private void OnProgressChanged()
        {
            try
            {
                double progress = _totalFiles > 0 ? (double)_completedFiles / _totalFiles * 100 : 0;
                ProgressChanged?.Invoke(this, new ProgressEventArgs(progress, _totalFiles, _completedFiles, _failedFiles, _retriedFiles));
            }
            catch (Exception ex)
            {
                logger.LogError("触发进度更新事件时出错", ex);
            }
        }

        private void OnStatusChanged(string status)
        {
            try
            {
                StatusChanged?.Invoke(this, status);
            }
            catch (Exception ex)
            {
                logger.LogError("触发状态更新事件时出错", ex);
            }
        }

        private void OnErrorOccurred(string filePath, Exception exception)
        {
            try
            {
                ErrorOccurred?.Invoke(this, new ErrorEventArgs(filePath, exception));
            }
            catch (Exception ex)
            {
                logger.LogError("触发错误事件时出错", ex);
            }
        }

        // 进度保存和恢复方法
        private void SaveProgress()
        {
            try
            {
                var progressData = new
                {
                    TotalFiles = _totalFiles,
                    CompletedFiles = _completedFiles,
                    FailedFiles = _failedFiles,
                    RetriedFiles = _retriedFiles,
                    RemainingFiles = _remainingFiles,
                    SourceDirectory = sourceDirectory,
                    OutputDirectory = outputDirectory
                };

                string json = System.Text.Json.JsonSerializer.Serialize(progressData, new System.Text.Json.JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_progressFile, json);
            }
            catch (Exception ex)
            {
                logger.LogError("保存进度时出错", ex);
            }
        }

        private void LoadProgress()
        {
            try
            {
                if (File.Exists(_progressFile))
                {
                    string json = File.ReadAllText(_progressFile);
                    using var document = System.Text.Json.JsonDocument.Parse(json);
                    var root = document.RootElement;

                    _totalFiles = root.GetProperty("TotalFiles").GetInt32();
                    _completedFiles = root.GetProperty("CompletedFiles").GetInt32();
                    _failedFiles = root.GetProperty("FailedFiles").GetInt32();
                    _retriedFiles = root.GetProperty("RetriedFiles").GetInt32();

                    _remainingFiles.Clear();
                    var remainingFilesArray = root.GetProperty("RemainingFiles");
                    foreach (var fileElement in remainingFilesArray.EnumerateArray())
                    {
                        _remainingFiles.Add(fileElement.GetString() ?? "");
                    }

                    sourceDirectory = root.GetProperty("SourceDirectory").GetString() ?? "";
                    outputDirectory = root.GetProperty("OutputDirectory").GetString() ?? "";

                    _isResuming = true;
                    logger.Log($"已加载进度：总计 {_totalFiles} 个文件，已完成 {_completedFiles} 个，剩余 {_remainingFiles.Count} 个");
                }
            }
            catch (Exception ex)
            {
                logger.LogError("加载进度时出错", ex);
                _isResuming = false;
            }
        }

        private void DeleteProgressFile()
        {
            try
            {
                if (File.Exists(_progressFile))
                {
                    File.Delete(_progressFile);
                    logger.Log("已删除进度文件");
                }
            }
            catch (Exception ex)
            {
                logger.LogError("删除进度文件时出错", ex);
            }
        }

        public void ResumeProcessing()
        {
            try
            {
                LoadProgress();

                if (_isResuming && _remainingFiles.Count > 0)
                {
                    logger.Log($"恢复处理：剩余 {_remainingFiles.Count} 个文件");

                    cancellationTokenSource?.Dispose();
                    cancellationTokenSource = new CancellationTokenSource();
                    _isStopped = false;

                    Task.Run(async () =>
                    {
                        try
                        {
                            await StartAsync(cancellationTokenSource.Token);
                        }
                        catch (Exception ex)
                        {
                            logger.LogError("恢复处理过程中发生错误", ex);
                            OnStatusChanged($"恢复处理过程中发生错误: {ex.Message}");
                            _isStopped = true;
                        }
                    });
                }
                else
                {
                    OnStatusChanged("没有找到可恢复的处理任务");
                }
            }
            catch (Exception ex)
            {
                logger.LogError("恢复处理时发生错误", ex);
                OnStatusChanged($"恢复处理时发生错误: {ex.Message}");
            }
        }

        public void PauseProcessing()
        {
            try
            {
                pauseEvent.Reset();
                OnStatusChanged("处理已暂停");
                logger.Log("处理已暂停");
            }
            catch (Exception ex)
            {
                logger.LogError("暂停处理时出错", ex);
            }
        }

        public void ResumeFromPause()
        {
            try
            {
                pauseEvent.Set();
                OnStatusChanged("处理已恢复");
                logger.Log("处理已从暂停状态恢复");
            }
            catch (Exception ex)
            {
                logger.LogError("从暂停状态恢复时出错", ex);
            }
        }

        public bool IsStopped => _isStopped;

        public bool HasProgressFile => File.Exists(_progressFile);

        // IDisposable 实现
        public void Dispose()
        {
            try
            {
                _isStopped = true;
                cancellationTokenSource?.Cancel();
                cancellationTokenSource?.Dispose();
                pauseEvent?.Dispose();
                _memoryMonitor?.Dispose();
            }
            catch (Exception ex)
            {
                logger?.LogError("释放资源时出错", ex);
            }
        }
    }

    // 内存监控器类
    public class MemoryMonitor : IDisposable
    {
        private readonly Logger logger;
        private readonly Timer? timer;
        private long lastMemoryUsage = 0;

        public MemoryMonitor(Logger logger)
        {
            this.logger = logger;
            // 每30秒检查一次内存使用情况
            timer = new Timer(CheckMemoryUsage, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        private void CheckMemoryUsage(object? state)
        {
            try
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                long currentMemory = GC.GetTotalMemory(false);
                long memoryDiff = currentMemory - lastMemoryUsage;

                if (Math.Abs(memoryDiff) > 10 * 1024 * 1024) // 10MB差异时记录
                {
                    logger.Log($"内存使用情况：当前 {currentMemory / 1024 / 1024:F1} MB，变化 {memoryDiff / 1024 / 1024:F1} MB");
                    lastMemoryUsage = currentMemory;
                }

                // 如果内存使用超过1GB，强制垃圾回收
                if (currentMemory > 1024 * 1024 * 1024)
                {
                    logger.LogWarning($"内存使用过高：{currentMemory / 1024 / 1024:F1} MB，执行强制垃圾回收");
                    GC.Collect(2, GCCollectionMode.Forced);
                    GC.WaitForPendingFinalizers();
                }
            }
            catch (Exception ex)
            {
                logger.LogError("检查内存使用情况时出错", ex);
            }
        }

        public void LogMemoryUsage(string context)
        {
            try
            {
                long currentMemory = GC.GetTotalMemory(false);
                logger.Log($"[{context}] 内存使用：{currentMemory / 1024 / 1024:F1} MB");
            }
            catch (Exception ex)
            {
                logger.LogError($"记录内存使用情况时出错 [{context}]", ex);
            }
        }

        public void Dispose()
        {
            timer?.Dispose();
        }
    }
}
