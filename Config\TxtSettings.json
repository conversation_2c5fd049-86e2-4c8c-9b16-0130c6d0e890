// TXT文件处理设置
// 此文件控制TXT文本文件的导入/导出处理选项
{
  // 转换设置
  "ConvertToWordBeforeProcessing": true, // 是否将TXT转换为Word后再处理
  "ConvertToFormat": 20, // 转换的目标格式：20=Docx, 16=Doc

  // 编码设置
  "EnableEncodingDetection": true, // 是否启用编码自动检测
  "DefaultEncoding": "UTF-8", // 默认编码格式

  // 段落处理
  "EnableParagraphProcessing": true, // 是否启用段落处理
  "TreatConsecutiveBlankLinesAsParagraph": true, // 将连续空行视为段落分隔
  "BlankLinesThreshold": 2, // 连续空行阈值（超过此数量视为段落分隔）
  "PreserveLineBreaks": true, // 是否保留原始换行符

  // 列表检测
  "EnableListDetection": true, // 是否启用列表检测
  "DetectNumberedLists": true, // 检测编号列表（1. 2. 3.等）
  "DetectBulletedLists": true, // 检测项目符号列表（• - *等）

  // 表格检测
  "EnableTableDetection": true, // 是否启用表格检测
  "TableDelimiter": "\t", // 表格分隔符（默认Tab制表符）

  // 标题检测
  "EnableHeadingDetection": true, // 是否启用标题检测
  "MaxHeadingLevel": 3, // 最大标题级别（1-9）

  // 文本样式
  "EnableTextStyling": true, // 是否启用文本样式
  "DefaultFontName": "宋体", // 默认字体名称
  "DefaultFontSize": 12.0 // 默认字体大小
}