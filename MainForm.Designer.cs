/*
 * ========================================
 * 文件名: MainForm.Designer.cs
 * 功能描述: 主窗体设计器生成代码
 * ========================================
 *
 * 主要功能:
 * 1. Visual Studio设计器自动生成的界面代码
 * 2. 定义主窗体的控件布局和属性
 * 3. 包含控件的初始化和配置
 * 4. 提供窗体的基础界面结构
 *
 * 注意事项:
 * - 此文件由Visual Studio设计器自动生成
 * - 不建议手动修改此文件中的代码
 * - 界面修改应通过设计器进行
 * - 包含窗体的基本控件定义和布局
 */

#nullable enable
using System;
using System.Drawing;
using System.Windows.Forms;

namespace AsposeWordFormatter
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = new System.ComponentModel.Container();

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            sourceFolderTextBox = new TextBox();
            targetFolderTextBox = new TextBox();
            logTextBox = new TextBox();
            startButton = new Button();
            stopButton = new Button();
            settingsButton = new Button();
            progressBar = new ProgressBar();
            statusLabel = new Label();
            SuspendLayout();
            //
            // sourceFolderTextBox
            //
            sourceFolderTextBox.Location = new Point(19, 19);
            sourceFolderTextBox.Margin = new Padding(5, 5, 5, 5);
            sourceFolderTextBox.Name = "sourceFolderTextBox";
            sourceFolderTextBox.Size = new Size(469, 30);
            sourceFolderTextBox.TabIndex = 7;
            //
            // targetFolderTextBox
            //
            targetFolderTextBox.Location = new Point(19, 66);
            targetFolderTextBox.Margin = new Padding(5, 5, 5, 5);
            targetFolderTextBox.Name = "targetFolderTextBox";
            targetFolderTextBox.Size = new Size(469, 30);
            targetFolderTextBox.TabIndex = 6;
            //
            // logTextBox
            //
            logTextBox.Location = new Point(19, 112);
            logTextBox.Margin = new Padding(5, 5, 5, 5);
            logTextBox.Multiline = true;
            logTextBox.Name = "logTextBox";
            logTextBox.ScrollBars = ScrollBars.Both;
            logTextBox.Size = new Size(721, 318);
            logTextBox.TabIndex = 5;
            //
            // startButton
            //
            startButton.Location = new Point(500, 19);
            startButton.Margin = new Padding(5, 5, 5, 5);
            startButton.Name = "startButton";
            startButton.Size = new Size(118, 37);
            startButton.TabIndex = 4;
            startButton.Text = "Start";
            //
            // stopButton
            //
            stopButton.Location = new Point(627, 19);
            stopButton.Margin = new Padding(5, 5, 5, 5);
            stopButton.Name = "stopButton";
            stopButton.Size = new Size(118, 37);
            stopButton.TabIndex = 3;
            stopButton.Text = "Stop";
            //
            // settingsButton
            //
            settingsButton.Location = new Point(500, 66);
            settingsButton.Margin = new Padding(5, 5, 5, 5);
            settingsButton.Name = "settingsButton";
            settingsButton.Size = new Size(118, 37);
            settingsButton.TabIndex = 2;
            settingsButton.Text = "Settings";
            //
            // progressBar
            //
            progressBar.Location = new Point(19, 442);
            progressBar.Margin = new Padding(5, 5, 5, 5);
            progressBar.Name = "progressBar";
            progressBar.Size = new Size(723, 37);
            progressBar.TabIndex = 1;
            //
            // statusLabel
            //
            statusLabel.AutoSize = true;
            statusLabel.Location = new Point(19, 483);
            statusLabel.Margin = new Padding(5, 0, 5, 0);
            statusLabel.Name = "statusLabel";
            statusLabel.Size = new Size(64, 24);
            statusLabel.TabIndex = 0;
            statusLabel.Text = "Ready";
            //
            // MainForm
            //
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(761, 530);
            Controls.Add(statusLabel);
            Controls.Add(progressBar);
            Controls.Add(settingsButton);
            Controls.Add(stopButton);
            Controls.Add(startButton);
            Controls.Add(logTextBox);
            Controls.Add(targetFolderTextBox);
            Controls.Add(sourceFolderTextBox);
            var iconObj = resources.GetObject("$this.Icon");
            Icon = iconObj != null ? (Icon)iconObj : null;
            Margin = new Padding(5, 5, 5, 5);
            Name = "MainForm";
            Text = "Word Formatter";
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private TextBox sourceFolderTextBox = null!;
        private TextBox targetFolderTextBox = null!;
        private TextBox logTextBox = null!;
        private Button? settingsButton;
        private ProgressBar progressBar = null!;
        private Label statusLabel = null!;
    }
}