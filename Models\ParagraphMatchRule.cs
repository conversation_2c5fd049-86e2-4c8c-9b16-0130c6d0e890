/*
 * ========================================
 * 文件名: ParagraphMatchRule.cs
 * 功能描述: 段落匹配规则数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义完整的段落匹配和格式化规则
 * 2. 支持复杂的匹配条件和格式应用
 * 3. 提供段落、字体、图片、水印、表格等全面格式设置
 * 4. 包含格式应用范围的精确控制
 * 5. 支持多语言字体和复杂文本处理
 *
 * 核心组件:
 *
 * 枚举定义:
 * - SpecialIndent: 特殊缩进类型（无、首行、悬挂）
 * - TextDirection: 文本方向（从左到右、从右到左）
 * - ImageColorMode: 图片颜色模式（彩色、灰度、黑白）
 * - HyperlinkType: 超链接类型（网址、书签、文件、邮件）
 * - FormatApplyScope: 格式应用范围（整段、到句号、到换行等）
 *
 * 匹配规则:
 * - Pattern: 基本匹配模式
 * - UseRegex: 是否使用正则表达式
 * - CaseSensitive: 是否区分大小写
 * - StartWithPatterns: 开头匹配模式列表
 * - ContainsPatterns: 包含匹配模式列表
 * - EndWithPatterns: 结尾匹配模式列表
 * - UseParagraphPosition: 是否使用段落位置匹配
 * - ConditionItems: 复杂条件组合列表
 *
 * 字体格式:
 * - 基础字体：FontName、FontSize、Bold、Italic、Underline
 * - 多语言字体：中文、西文、复杂脚本字体独立设置
 * - 字体颜色和高亮颜色
 * - 文本效果：删除线、小型大写字母、隐藏文本
 * - 字符间距和文本缩放
 * - 中文版式特性：竖排、标点压缩、网格对齐
 *
 * 段落格式:
 * - 对齐方式、大纲级别、双向文本
 * - 行距设置（规则和数值）
 * - 段前段后间距
 * - 缩进设置（左、右、首行、悬挂）
 * - 边框和底纹
 * - 分页控制（与下段同页、段中不分页等）
 * - 制表位设置
 *
 * 图片格式:
 * - 尺寸设置：精确尺寸或缩放比例
 * - 位置和对齐：相对位置、绝对位置、对齐方式
 * - 文本环绕：嵌入式、四周型、紧密型等
 * - 图片效果：亮度、对比度、透明度、颜色模式
 * - 图片裁剪：四边裁剪设置
 * - 图片边框：样式、宽度、颜色
 * - 旋转和翻转：角度、水平翻转、垂直翻转
 * - 超链接设置：URL、书签、工具提示
 * - 锁定选项：位置、纵横比、格式锁定
 *
 * 水印功能:
 * - 文本水印：内容、字体、颜色、透明度、旋转
 * - 图片水印：路径、布局、尺寸、透明度
 * - 位置控制：居中或自定义位置
 * - 页面范围：全部页面或指定页面
 * - 格式控制：边框、环绕、锁定、打印显示
 *
 * 表格格式:
 * - 宽度自适应：内容、窗口、固定宽度
 * - 表格布局：对齐、缩进、环绕、间距
 * - 表格样式：边框、网格线、底纹
 * - 单元格格式：对齐、边距、边框、底纹
 *
 * 启用控制:
 * - 分区域启用控制（基本格式、边框底纹、分页等）
 * - 分功能启用控制（对齐、缩进、间距等）
 * - 支持选择性应用格式设置
 * - 避免不必要的格式修改
 *
 * 颜色管理:
 * - 使用Color类型存储颜色
 * - 提供Hex字符串属性用于JSON序列化
 * - 支持颜色的转换和验证
 *
 * 注意事项:
 * - 支持Aspose.Words的所有格式选项
 * - 包含完整的数据验证机制
 * - 实现了复杂的条件匹配逻辑
 * - 支持多种格式应用范围
 * - 提供丰富的格式化选项和效果设置
 */

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text.RegularExpressions;
using AW = Aspose.Words;

namespace AsposeWordFormatter.Models
{
    public enum SpecialIndent
    {
        None,
        FirstLine,
        Hanging
    }

    // 在Models命名空间下定义TextDirection枚举
    public enum TextDirection
    {
        LeftToRight,
        RightToLeft
    }

    // 在Models命名空间下定义ImageColorMode枚举
    public enum ImageColorMode
    {
        Color,      // 彩色
        Grayscale,  // 灰度
        BlackWhite  // 黑白
    }

    // 在Models命名空间下定义HyperlinkType枚举
    public enum HyperlinkType
    {
        Url,        // 网址链接
        Bookmark,   // 文档内部书签
        File,       // 文件链接
        Email       // 电子邮件链接
    }

    // 定义格式应用范围枚举
    public enum FormatApplyScope
    {
        WholeParagrph,         // 整段应用
        UntilFirstPeriod,      // 匹配部分到第一个句号结束
        UntilLineBreak,        // 匹配部分到换行结束
        UntilSpecificCharInclusive,   // 匹配部分到指定字符结束（含此字符）
        UntilSpecificCharExclusive,    // 匹配部分到指定字符结束（不含此字符）
        BetweenPeriods,        // 匹配部分的前一个句号到后一个句号之间部分（含后一个句号）
        FromStartToMatchInclusive,    // 本段开头到匹配部分结束（含匹配部分）
        FromStartToMatchExclusive,    // 本段开头到匹配部分结束（不含匹配部分）
        FromCharToMatchInclusive,     // 特定字符到匹配部分结束（含匹配部分）
        FromCharToMatchExclusive      // 特定字符到匹配部分结束（不含匹配部分）
    }

    // 定义文档区域应用范围枚举
    public enum DocumentAreaScope
    {
        MainDocument,          // 正文
        Header,               // 页眉
        Footer,               // 页脚
        TextBox,              // 文本框
        Footnote,             // 脚注
        Endnote,              // 尾注
        Comment               // 批注
    }

    /// <summary>
    /// 段落匹配规则
    /// </summary>
    public class ParagraphMatchRule
    {
        // 基本信息
        public string Name { get; set; } = string.Empty;

        // 是否启用该规则
        public bool IsEnabled { get; set; } = true;

        // 匹配规则
        public string Pattern { get; set; } = string.Empty;
        public bool UseRegex { get; set; }
        public bool CaseSensitive { get; set; }

        // 格式应用范围
        public FormatApplyScope ApplyScope { get; set; } = FormatApplyScope.WholeParagrph;

        // 新增的匹配规则
        public List<string> StartWithPatterns { get; set; } = new List<string>();
        public List<string> ContainsPatterns { get; set; } = new List<string>();
        public List<string> EndWithPatterns { get; set; } = new List<string>();

        // 段落位置匹配
        public bool UseParagraphPosition { get; set; } = false;
        public int ParagraphPosition { get; set; } = 1; // 默认第1段

        // 字体格式
        public string StyleName { get; set; } = string.Empty;
        public string FontName { get; set; } = "宋体";
        public double FontSize { get; set; } = 16; // 默认16磅
        public bool Bold { get; set; }
        public bool Italic { get; set; }
        public AW.Underline Underline { get; set; } = AW.Underline.None;

        // 字体颜色 - 内部存储为Color?类型
        [System.Text.Json.Serialization.JsonIgnore]
        public Color? FontColor { get; set; } = Color.Black; // 默认黑色

        // 字体颜色 - 用于JSON序列化的辅助属性，存储为十六进制字符串
        public string FontColorHex
        {
            get => FontColor.HasValue ? ColorToHex(FontColor.Value) : "#000000";
            set
            {
                if (!string.IsNullOrEmpty(value))
                    FontColor = HexToColor(value);
                else
                    FontColor = Color.Black;
            }
        }

        // 新增多语言字体格式
        public string ChineseFontName { get; set; } = "宋体";
        public string WesternFontName { get; set; } = "Times New Roman";
        public string ComplexScriptFontName { get; set; } = "Arial";
        public FontStyle ChineseFontStyle { get; set; } = FontStyle.Regular;
        public FontStyle WesternFontStyle { get; set; } = FontStyle.Regular;
        public FontStyle ComplexScriptFontStyle { get; set; } = FontStyle.Regular;
        public double ChineseFontSize { get; set; } = 16; // 默认16磅
        public double WesternFontSize { get; set; } = 16; // 默认16磅
        public double ComplexScriptFontSize { get; set; } = 16; // 默认16磅

        // 添加文字突出显示颜色（背景色）属性 - 内部存储为Color?类型
        [System.Text.Json.Serialization.JsonIgnore]
        public Color? HighlightColor { get; set; } = null;

        // 高亮颜色 - 用于JSON序列化的辅助属性，存储为十六进制字符串
        public string HighlightColorHex
        {
            get => HighlightColor.HasValue ? ColorToHex(HighlightColor.Value) : null;
            set
            {
                if (!string.IsNullOrEmpty(value))
                    HighlightColor = HexToColor(value);
                else
                    HighlightColor = null;
            }
        }

        // 段落格式
        public AW.ParagraphAlignment Alignment { get; set; } = AW.ParagraphAlignment.Left; // 左对齐
        public AW.OutlineLevel OutlineLevel { get; set; } = AW.OutlineLevel.BodyText; // 大纲级别：正文文本
        public bool Bidi { get; set; } = false; // 双向文本
        public double LineSpacing { get; set; } = 28; // 行距：固定值28磅
        public AW.LineSpacingRule LineSpacingRule { get; set; } = AW.LineSpacingRule.Exactly; // 行距规则：固定值
        public double SpaceBefore { get; set; } = 0;
        public double SpaceAfter { get; set; } = 0;
        public double FirstLineIndent { get; set; } = 0;
        public double LeftIndent { get; set; } = 0;
        public double RightIndent { get; set; } = 0;
        public SpecialIndent SpecialIndent { get; set; } = SpecialIndent.FirstLine; // 特殊缩进类型：首行缩进
        public double SpecialIndentValue { get; set; } = 14.4; // 特殊缩进值：2字符（约14.4磅）

        // 长度限制
        public int MinLength { get; set; } = 0;
        public int MaxLength { get; set; } = int.MaxValue;

        // 段落格式 - 文本方向，使用本地定义的枚举
        public TextDirection TextDirection { get; set; } = TextDirection.LeftToRight;

        // 段落格式区域启用状态控制
        public bool EnableBasicFormat { get; set; } = true; // 启用基本段落格式
        public bool EnableBorderShading { get; set; } = true; // 启用边框和底纹
        public bool EnablePagination { get; set; } = true; // 启用分页和连接控制
        public bool EnableTabStops { get; set; } = true; // 启用制表位设置

        // 文档区域应用范围控制
        public bool ApplyToMainDocument { get; set; } = true; // 应用到正文
        public bool ApplyToHeader { get; set; } = false; // 应用到页眉
        public bool ApplyToFooter { get; set; } = false; // 应用到页脚
        public bool ApplyToTextBox { get; set; } = true; // 应用到文本框
        public bool ApplyToFootnote { get; set; } = false; // 应用到脚注
        public bool ApplyToEndnote { get; set; } = false; // 应用到尾注
        public bool ApplyToComment { get; set; } = false; // 应用到批注

        // 段落格式中各功能的启用状态
        public bool EnableAlignment { get; set; } = true; // 启用对齐方式
        public bool EnableOutlineLevel { get; set; } = true; // 启用大纲级别
        public bool EnableTextDirection { get; set; } = true; // 启用对齐方向
        public bool EnableIndent { get; set; } = true; // 启用缩进
        public bool EnableSpacing { get; set; } = true; // 启用间距

        // 高级段落格式功能启用控制
        public bool EnableAdvancedFormat { get; set; } = false; // 启用高级段落格式功能
        public bool EnableChineseTypography { get; set; } = false; // 启用中文排版功能

        // 字体格式各功能的启用状态
        public bool EnableChineseFont { get; set; } = true; // 启用中文字体
        public bool EnableWesternFont { get; set; } = true; // 启用西文字体
        public bool EnableComplexScriptFont { get; set; } = true; // 启用复杂文字字体
        public bool EnableFontColor { get; set; } = true; // 启用字体颜色
        public bool EnableHighlightColor { get; set; } = true; // 启用高亮颜色
        public bool EnableFontFormat { get; set; } = true; // 启用字体格式

        // 新增段落格式选项 - 边框
        public bool HasBorders { get; set; } = false;
        public AW.LineStyle BorderType { get; set; } = AW.LineStyle.Single;
        public double BorderWidth { get; set; } = 0.5;

        [System.Text.Json.Serialization.JsonIgnore]
        public Color BorderColor { get; set; } = Color.Black;

        // 边框颜色 - 用于JSON序列化的辅助属性
        public string BorderColorHex
        {
            get => ColorToHex(BorderColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                    BorderColor = HexToColor(value);
                else
                    BorderColor = Color.Black;
            }
        }

        public bool HasTopBorder { get; set; } = true;
        public bool HasBottomBorder { get; set; } = true;
        public bool HasLeftBorder { get; set; } = true;
        public bool HasRightBorder { get; set; } = true;

        // 新增段落格式选项 - 底纹
        public bool HasShading { get; set; } = false;

        [System.Text.Json.Serialization.JsonIgnore]
        public Color ShadingColor { get; set; } = Color.LightGray;

        // 底纹颜色 - 用于JSON序列化的辅助属性
        public string ShadingColorHex
        {
            get => ColorToHex(ShadingColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    ShadingColor = HexToColor(value);
                }
                else
                {
                    ShadingColor = Color.LightGray;
                }
            }
        }

        public AW.TextureIndex ShadingPattern { get; set; } = AW.TextureIndex.TextureSolid;

        // 新增段落格式选项 - 分页和连接控制
        public bool KeepWithNext { get; set; } = false;        // 与下段同页
        public bool KeepLinesTogether { get; set; } = false;   // 段中不分页
        public bool PageBreakBefore { get; set; } = false;     // 段前分页
        public bool WidowOrphanControl { get; set; } = true;   // 孤行控制
        public bool NoSpaceBetweenParagraphs { get; set; } = false; // 与相同样式段落间无间距

        // 新增段落格式选项 - 制表位设置
        public List<TabStop> TabStops { get; set; } = new List<TabStop>();

        // 用户自定义结束字符
        private string _endingChar = "";
        public string EndingChar
        {
            get => _endingChar;
            set
            {
                // 验证结束字符的长度，不应过长
                if (value != null && value.Length > 10)
                {
                    System.Diagnostics.Debug.WriteLine($"结束字符过长，已截断: {value}");
                    _endingChar = value.Substring(0, 10);
                }
                else
                {
                    _endingChar = value ?? "";
                }
            }
        }

        // 用户自定义起始字符（用于向前匹配）
        private string _startingChar = "";
        public string StartingChar
        {
            get => _startingChar;
            set
            {
                // 验证起始字符的长度，不应过长
                if (value != null && value.Length > 10)
                {
                    System.Diagnostics.Debug.WriteLine($"起始字符过长，已截断: {value}");
                    _startingChar = value.Substring(0, 10);
                }
                else
                {
                    _startingChar = value ?? "";
                }
            }
        }

        // 新增图片格式设置
        public bool EnableImageFormat { get; set; } = false;

        // 图片格式区域启用控制
        public bool EnableImageSize { get; set; } = false; // 启用图片尺寸设置
        public bool EnableImageWrap { get; set; } = false; // 启用文本环绕设置
        public bool EnableImageEffect { get; set; } = false; // 启用图片效果设置
        public bool EnableBrightness { get; set; } = true; // 启用亮度调整
        public bool EnableContrast { get; set; } = true; // 启用对比度调整
        public bool EnableTransparency { get; set; } = true; // 启用透明度调整
        public bool EnableColorMode { get; set; } = true; // 启用颜色模式设置

        public bool PreserveAspectRatio { get; set; } = true;
        public double ImageWidth { get; set; } = 0;  // 0表示不指定
        public double ImageHeight { get; set; } = 0; // 0表示不指定
        public double ImageScaleX { get; set; } = 100; // 横向缩放百分比
        public double ImageScaleY { get; set; } = 100; // 纵向缩放百分比

        // 新增图片文本环绕设置
        public AW.Drawing.WrapType ImageWrapType { get; set; } = AW.Drawing.WrapType.Inline; // 默认嵌入式
        public bool WrapTextBehind { get; set; } = false; // 衬于文字下方
        public bool WrapTextFront { get; set; } = false; // 衬于文字上方

        // 新增图片效果设置
        public int ImageBrightness { get; set; } = 0; // 亮度调整，范围-100到100
        public int ImageContrast { get; set; } = 0; // 对比度调整，范围-100到100
        public int ImageTransparency { get; set; } = 0; // 透明度，范围0到100
        public ImageColorMode ImageColorMode { get; set; } = ImageColorMode.Color; // 颜色模式
        public bool EnableImageCrop { get; set; } = false; // 是否启用裁剪
        public double CropTop { get; set; } = 0; // 顶部裁剪，单位点
        public double CropBottom { get; set; } = 0; // 底部裁剪，单位点
        public double CropLeft { get; set; } = 0; // 左侧裁剪，单位点
        public double CropRight { get; set; } = 0; // 右侧裁剪，单位点

        // 新增图片边框设置
        public bool EnableImageBorder { get; set; } = false; // 是否启用图片边框
        public AW.LineStyle ImageBorderStyle { get; set; } = AW.LineStyle.Single; // 边框样式
        public double ImageBorderWidth { get; set; } = 0.5; // 边框宽度，默认0.5磅

        [System.Text.Json.Serialization.JsonIgnore]
        public Color ImageBorderColor { get; set; } = Color.Black; // 边框颜色

        // 图片边框颜色 - 用于JSON序列化的辅助属性
        public string ImageBorderColorHex
        {
            get => ColorToHex(ImageBorderColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                    ImageBorderColor = HexToColor(value);
                else
                    ImageBorderColor = Color.Black;
            }
        }

        // 新增图片位置和对齐设置
        public bool EnableImagePosition { get; set; } = false; // 是否启用图片位置设置
        public AW.Drawing.RelativeHorizontalPosition HorizontalRelativePosition { get; set; } = AW.Drawing.RelativeHorizontalPosition.Column; // 水平相对位置
        public AW.Drawing.RelativeVerticalPosition VerticalRelativePosition { get; set; } = AW.Drawing.RelativeVerticalPosition.Paragraph; // 垂直相对位置
        public double HorizontalPosition { get; set; } = 0; // 水平位置值（点）
        public double VerticalPosition { get; set; } = 0; // 垂直位置值（点）
        public AW.Drawing.HorizontalAlignment HorizontalAlignment { get; set; } = AW.Drawing.HorizontalAlignment.Default; // 水平对齐
        public AW.Drawing.VerticalAlignment VerticalAlignment { get; set; } = AW.Drawing.VerticalAlignment.Default; // 垂直对齐
        public bool UseImageAlignment { get; set; } = true; // 使用对齐方式而非精确位置

        // 新增图片旋转和翻转设置
        public bool EnableImageRotateFlip { get; set; } = false; // 是否启用图片旋转和翻转
        public double RotationAngle { get; set; } = 0; // 旋转角度（0-359度）
        public bool FlipHorizontal { get; set; } = false; // 水平翻转
        public bool FlipVertical { get; set; } = false; // 垂直翻转

        // 图片超链接设置
        public bool EnableImageHyperlink { get; set; }
        public required string HyperlinkUrl { get; set; } = string.Empty;
        public required string HyperlinkToolTip { get; set; } = string.Empty; // 修复属性名称，之前使用的是HyperlinkTooltip
        public HyperlinkType HyperlinkType { get; set; }  // 0=URL, 1=文档内部位置, 2=文件, 3=电子邮件
        public required string BookmarkName { get; set; } = string.Empty; // 文档内部位置的书签名称
        public bool OpenHyperlinkInNewWindow { get; set; } // 在新窗口中打开链接

        // 图片锁定设置
        public bool EnableImageLocking { get; set; } = false; // 是否启用图片锁定功能
        public bool LockPosition { get; set; } = false; // 锁定图片位置
        public bool LockAspectRatio { get; set; } = false; // 锁定图片纵横比
        public bool LockFormatting { get; set; } = false; // 锁定图片格式（防止修改图片效果设置）

        // 水印属性
        public bool EnableWatermark { get; set; } = false;
        public string WatermarkText { get; set; } = "机密";
        public string WatermarkFontFamily { get; set; } = "Arial";
        public float WatermarkFontSize { get; set; } = 36;

        [System.Text.Json.Serialization.JsonIgnore]
        public Color WatermarkColor { get; set; } = Color.Gray;

        // 水印颜色 - 用于JSON序列化的辅助属性
        public string WatermarkColorHex
        {
            get => ColorToHex(WatermarkColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                    WatermarkColor = HexToColor(value);
                else
                    WatermarkColor = Color.Gray;
            }
        }

        public double WatermarkOpacity { get; set; } = 0.5;
        public double WatermarkRotationAngle { get; set; } = 45;

        // 图片水印属性
        public bool EnableImageWatermark { get; set; } = false;
        public string WatermarkImagePath { get; set; } = ""; // 水印图片的路径
        public bool PreserveImageWatermarkAspectRatio { get; set; } = true;
        public double ImageWatermarkWidth { get; set; } = 150;
        public double ImageWatermarkHeight { get; set; } = 150;
        public double ImageWatermarkScaleX { get; set; } = 100;
        public double ImageWatermarkScaleY { get; set; } = 100;
        public double ImageWatermarkOpacity { get; set; } = 0.5;
        public ImageColorMode ImageWatermarkColorMode { get; set; } = ImageColorMode.Color;
        public AW.WatermarkLayout ImageWatermarkLayout { get; set; } = AW.WatermarkLayout.Diagonal;

        // 水印位置和布局属性
        public bool EnableWatermarkPosition { get; set; } = false;
        public bool WatermarkAtPageCenter { get; set; } = true;
        public double WatermarkHorizontalPosition { get; set; } = 0;
        public double WatermarkVerticalPosition { get; set; } = 0;
        public bool WatermarkOnAllPages { get; set; } = true;
        public string WatermarkPageRange { get; set; } = "";
        public bool WatermarkBehindContent { get; set; } = false;

        // 水印格式控制属性
        public bool EnableWatermarkFormat { get; set; } = false;
        public bool WatermarkHasBorder { get; set; } = false;
        public AW.LineStyle WatermarkBorderStyle { get; set; } = AW.LineStyle.Single;
        public double WatermarkBorderWidth { get; set; } = 0.5; // 单位：磅

        [System.Text.Json.Serialization.JsonIgnore]
        public Color WatermarkBorderColor { get; set; } = Color.Black;

        // 水印边框颜色 - 用于JSON序列化的辅助属性
        public string WatermarkBorderColorHex
        {
            get => ColorToHex(WatermarkBorderColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                    WatermarkBorderColor = HexToColor(value);
                else
                    WatermarkBorderColor = Color.Black;
            }
        }
        public AW.Drawing.WrapType WatermarkWrapType { get; set; } = AW.Drawing.WrapType.Square;
        public bool LockWatermark { get; set; } = false;
        public bool ShowWatermarkInPrint { get; set; } = true;

        // 新增属性：条件组合列表和逻辑关系
        public List<ConditionItem> ConditionItems { get; set; } = new List<ConditionItem>();

        // 添加文本效果与装饰相关属性
        public bool UnderlineEnabled { get; set; } = false; // 默认不启用
        public int UnderlineStyle { get; set; } = 0; // 0: 单线, 1: 双线, 2: 虚线, 3: 点线, 4: 波浪线
        public bool Strikethrough { get; set; } = false; // 默认不启用
        public int StrikethroughStyle { get; set; } = 0; // 0: 单线, 1: 双线
        public bool SmallCaps { get; set; } = false; // 默认不启用
        public bool Hidden { get; set; } = false; // 默认不启用

        // 文本间距与缩放属性
        public bool EnableCharacterSpacing { get; set; } = false;
        public float CharacterSpacing { get; set; } = 0; // 单位：磅/20，0表示正常，正值表示加宽，负值表示紧缩
        public bool EnableTextScaling { get; set; } = false;
        public float TextScaling { get; set; } = 100; // 缩放百分比，范围1-600

        // 中文版式特性属性
        public bool VerticalText { get; set; } = false; // false=横排, true=竖排
        public bool EnablePunctuationCompression { get; set; } = false;
        public int PunctuationCompressionLevel { get; set; } = 0; // 0=默认, 1=半角, 2=不压缩
        public bool EnableChineseGridAlignment { get; set; } = false;
        public int GridAlignmentLevel { get; set; } = 0; // 0=默认, 1=低, 2=中, 3=高

        // 新增：立即可以添加的功能（API支持且重要）
        // 中英文间距自动调整
        public bool AddSpaceBetweenFarEastAndAlpha { get; set; } = false; // 远东文字与拉丁文字间自动调整字符间距
        public bool AddSpaceBetweenFarEastAndDigit { get; set; } = false; // 远东文字与数字间自动调整字符间距

        // 基线对齐设置
        public AW.BaselineAlignment BaselineAlignment { get; set; } = AW.BaselineAlignment.Auto; // 字体在行上的垂直位置

        // 首字下沉功能
        public bool EnableDropCap { get; set; } = false; // 是否启用首字下沉
        public AW.DropCapPosition DropCapPosition { get; set; } = AW.DropCapPosition.None; // 首字下沉位置
        public int LinesToDrop { get; set; } = 3; // 首字下沉行数（默认3行）

        // 自动间距控制
        public bool SpaceAfterAuto { get; set; } = false; // 段后间距自动
        public bool SpaceBeforeAuto { get; set; } = false; // 段前间距自动

        // 连字符控制
        public bool SuppressAutoHyphens { get; set; } = false; // 禁止自动连字符

        // 网格对齐
        public bool SnapToGrid { get; set; } = false; // 对齐到文档网格

        // 单词换行控制
        public bool WordWrap { get; set; } = false; // 单词换行控制（默认启用）

        // 新增：中文排版增强功能
        // 远东换行规则
        public bool FarEastLineBreakControl { get; set; } = false; // 远东换行规则控制

        // 悬挂标点符号
        public bool HangingPunctuation { get; set; } = false; // 悬挂标点符号

        // 网格线单位间距
        public bool EnableLineUnitSpacing { get; set; } = false; // 是否启用网格线单位间距
        public double LineUnitAfter { get; set; } = 0; // 以网格线为单位的段后间距
        public double LineUnitBefore { get; set; } = 0; // 以网格线为单位的段前间距

        // 图片行距控制
        public bool AdjustImageLineSpacing { get; set; } = false; // 是否调整图片所在行的行距

        // 新增：字符单位设置
        public bool EnableCharacterUnitIndent { get; set; } = false; // 是否启用字符单位缩进
        public double CharacterUnitFirstLineIndent { get; set; } = 0; // 以字符为单位的首行缩进
        public double CharacterUnitLeftIndent { get; set; } = 0; // 以字符为单位的左缩进
        public double CharacterUnitRightIndent { get; set; } = 0; // 以字符为单位的右缩进

        // 字符单位间距设置
        public bool EnableCharacterUnitSpacing { get; set; } = false; // 是否启用字符单位间距
        public double CharacterUnitBefore { get; set; } = 0; // 以字符为单位的段前间距
        public double CharacterUnitAfter { get; set; } = 0; // 以字符为单位的段后间距

        // 字符单位行距设置
        public bool EnableCharacterUnitLineSpacing { get; set; } = false; // 是否启用字符单位行距
        public double CharacterUnitLineSpacing { get; set; } = 1.0; // 以字符为单位的行距

        // 其他高级控制选项
        public bool SuppressLineNumbers { get; set; } = false; // 禁止行号显示

        // 镜像缩进（用于双面打印）
        public bool MirrorIndents { get; set; } = false; // 镜像缩进

        // 表格格式设置
        // 表格宽度自适应
        public bool EnableTableAutoFit { get; set; } = false; // 是否启用表格宽度自适应
        public bool AutoFitToContents { get; set; } = true; // 自适应内容
        public bool AutoFitToWindow { get; set; } = false; // 自适应窗口
        public bool UseFixedColumnWidth { get; set; } = false; // 使用固定列宽
        public double PreferredTableWidth { get; set; } = 100; // 首选表格宽度（百分比）

        // 表格布局
        public bool EnableTableLayout { get; set; } = false; // 是否启用表格布局
        public AW.Tables.TableAlignment TableAlignment { get; set; } = AW.Tables.TableAlignment.Left; // 表格对齐方式
        public double TableLeftIndent { get; set; } = 0; // 表格左缩进
        public double TableRightIndent { get; set; } = 0; // 表格右缩进
        public AW.Tables.TextWrapping TableTextWrapping { get; set; } = AW.Tables.TextWrapping.Around; // 文本环绕方式
        public bool TableAllowAutoFit { get; set; } = true; // 允许自动调整
        public double TableDefaultCellSpacing { get; set; } = 0; // 默认单元格间距
        public double TablePreferredWidth { get; set; } = 0; // 表格首选宽度

        // 表格样式
        public bool EnableTableStyle { get; set; } = false; // 是否启用表格样式
        public bool TableHasBorders { get; set; } = true; // 表格是否有边框
        public AW.LineStyle TableBorderStyle { get; set; } = AW.LineStyle.Single; // 表格边框样式
        public double TableBorderWidth { get; set; } = 0.5; // 表格边框宽度

        [System.Text.Json.Serialization.JsonIgnore]
        public Color TableBorderColor { get; set; } = Color.Black; // 表格边框颜色

        // 表格边框颜色 - 用于JSON序列化的辅助属性
        public string TableBorderColorHex
        {
            get => ColorToHex(TableBorderColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                    TableBorderColor = HexToColor(value);
                else
                    TableBorderColor = Color.Black;
            }
        }

        public bool TableHasGridlines { get; set; } = true; // 表格是否显示网格线
        public AW.LineStyle TableGridlineStyle { get; set; } = AW.LineStyle.Single; // 表格网格线样式
        public double TableGridlineWidth { get; set; } = 0.5; // 表格网格线宽度

        [System.Text.Json.Serialization.JsonIgnore]
        public Color TableGridlineColor { get; set; } = Color.LightGray; // 表格网格线颜色

        // 表格网格线颜色 - 用于JSON序列化的辅助属性
        public string TableGridlineColorHex
        {
            get => ColorToHex(TableGridlineColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                    TableGridlineColor = HexToColor(value);
                else
                    TableGridlineColor = Color.LightGray;
            }
        }
        public bool TableHasShading { get; set; } = false; // 表格是否有底纹

        [System.Text.Json.Serialization.JsonIgnore]
        public Color TableShadingColor { get; set; } = Color.LightGray; // 表格底纹颜色

        // 表格底纹颜色 - 用于JSON序列化的辅助属性
        public string TableShadingColorHex
        {
            get => ColorToHex(TableShadingColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                    TableShadingColor = HexToColor(value);
                else
                    TableShadingColor = Color.LightGray;
            }
        }

        public AW.TextureIndex TableShadingPattern { get; set; } = AW.TextureIndex.TextureSolid; // 表格底纹样式

        // 单元格操作
        public bool EnableCellFormat { get; set; } = false; // 是否启用单元格格式
        public AW.Tables.CellVerticalAlignment CellVerticalAlignment { get; set; } = AW.Tables.CellVerticalAlignment.Center; // 单元格垂直对齐方式
        public double CellMarginTop { get; set; } = 0; // 单元格上边距
        public double CellMarginBottom { get; set; } = 0; // 单元格下边距
        public double CellMarginLeft { get; set; } = 5.4; // 单元格左边距
        public double CellMarginRight { get; set; } = 5.4; // 单元格右边距
        public double CellPaddingTop { get; set; } = 0; // 单元格上内边距
        public double CellPaddingBottom { get; set; } = 0; // 单元格下内边距
        public double CellPaddingLeft { get; set; } = 0; // 单元格左内边距
        public double CellPaddingRight { get; set; } = 0; // 单元格右内边距
        public bool CellHasShading { get; set; } = false; // 单元格是否有底纹

        [System.Text.Json.Serialization.JsonIgnore]
        public Color CellShadingColor { get; set; } = Color.LightGray; // 单元格底纹颜色

        // 单元格底纹颜色 - 用于JSON序列化的辅助属性
        public string CellShadingColorHex
        {
            get => ColorToHex(CellShadingColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                    CellShadingColor = HexToColor(value);
                else
                    CellShadingColor = Color.LightGray;
            }
        }

        public AW.TextureIndex CellShadingPattern { get; set; } = AW.TextureIndex.TextureSolid; // 单元格底纹样式
        public bool CellHasBorders { get; set; } = true; // 单元格是否有边框
        public AW.LineStyle CellBorderStyle { get; set; } = AW.LineStyle.Single; // 单元格边框样式
        public double CellBorderWidth { get; set; } = 0.5; // 单元格边框宽度

        [System.Text.Json.Serialization.JsonIgnore]
        public Color CellBorderColor { get; set; } = Color.Black; // 单元格边框颜色

        // 单元格边框颜色 - 用于JSON序列化的辅助属性
        public string CellBorderColorHex
        {
            get => ColorToHex(CellBorderColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                    CellBorderColor = HexToColor(value);
                else
                    CellBorderColor = Color.Black;
            }
        }

        // 水印设置
        // public bool EnableWatermark { get; set; } = false; // 删除重复定义的EnableWatermark

        public ParagraphMatchRule()
        {
            // 初始化所有required属性
            HyperlinkUrl = string.Empty;
            HyperlinkToolTip = string.Empty;
            BookmarkName = string.Empty;
        }

        // 颜色转换辅助方法

        // 将Color转换为十六进制字符串
        private static string ColorToHex(Color color)
        {
            return $"#{color.R:X2}{color.G:X2}{color.B:X2}";
        }

        // 将十六进制字符串转换为Color
        private static Color HexToColor(string hex)
        {
            // 移除可能的#前缀
            hex = hex.TrimStart('#');

            try
            {
                if (hex.Length == 6)
                {
                    int r = Convert.ToInt32(hex.Substring(0, 2), 16);
                    int g = Convert.ToInt32(hex.Substring(2, 2), 16);
                    int b = Convert.ToInt32(hex.Substring(4, 2), 16);
                    return Color.FromArgb(r, g, b);
                }
                else if (hex.Length == 8) // 包含Alpha通道
                {
                    int a = Convert.ToInt32(hex.Substring(0, 2), 16);
                    int r = Convert.ToInt32(hex.Substring(2, 2), 16);
                    int g = Convert.ToInt32(hex.Substring(4, 2), 16);
                    int b = Convert.ToInt32(hex.Substring(6, 2), 16);
                    return Color.FromArgb(a, r, g, b);
                }
            }
            catch (Exception ex)
            {
                // 转换失败时返回黑色作为默认值
                System.Diagnostics.Debug.WriteLine($"颜色转换失败 ({hex}): {ex.Message}，使用黑色");
                return Color.Black;
            }

            // 默认返回黑色
            System.Diagnostics.Debug.WriteLine($"无效的颜色格式 ({hex})，使用黑色");
            return Color.Black;
        }

        public bool IsMatch(string text)
        {
            return IsMatch(text, -1); // -1表示不使用段落位置匹配
        }

        public bool IsMatch(string text, int paragraphPosition)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            // 检查长度限制
            if (text.Length < MinLength || (MaxLength > 0 && text.Length > MaxLength))
                return false;

            // 如果有条件组合，优先使用条件组合进行匹配
            if (ConditionItems.Count > 0)
            {
                return MatchWithCompoundConditions(text, paragraphPosition);
            }

            // 开头匹配模式检查
            if (StartWithPatterns.Count > 0)
            {
                bool matchStart = false;
                foreach (var pattern in StartWithPatterns)
                {
                    if (StartsWithVisibleText(text, pattern))
                    {
                        matchStart = true;
                        break;
                    }
                }
                if (!matchStart)
                    return false;
            }

            // 包含关键词检查
            if (ContainsPatterns.Count > 0)
            {
                bool matchContains = false;
                foreach (var pattern in ContainsPatterns)
                {
                    if (CaseSensitive ?
                           text.Contains(pattern) :
                           text.Contains(pattern, StringComparison.OrdinalIgnoreCase))
                    {
                        matchContains = true;
                        break;
                    }
                }
                if (!matchContains)
                    return false;
            }

            // 结尾匹配模式检查
            if (EndWithPatterns.Count > 0)
            {
                bool matchEnd = false;
                foreach (var pattern in EndWithPatterns)
                {
                    if (CaseSensitive ?
                           text.EndsWith(pattern) :
                           text.EndsWith(pattern, StringComparison.OrdinalIgnoreCase))
                    {
                        matchEnd = true;
                        break;
                    }
                }
                if (!matchEnd)
                    return false;
            }

            // 原有匹配逻辑
            if (!string.IsNullOrEmpty(Pattern))
            {
                if (UseRegex)
                {
                    try
                    {
                        var options = CaseSensitive ?
                            System.Text.RegularExpressions.RegexOptions.None :
                            System.Text.RegularExpressions.RegexOptions.IgnoreCase;
                        return System.Text.RegularExpressions.Regex.IsMatch(text, Pattern, options);
                    }
                    catch
                    {
                        return false;
                    }
                }
                else
                {
                    return CaseSensitive ?
                        text.Contains(Pattern) :
                        text.Contains(Pattern, StringComparison.OrdinalIgnoreCase);
                }
            }

            // 如果没有设置Pattern且通过了上述匹配，则判定为匹配成功
            return true;
        }

        public void ApplyTo(AW.Run run)
        {
            if (run == null)
                throw new ArgumentNullException(nameof(run));

            // 只有启用字体格式时才应用字体格式
            if (EnableFontFormat)
            {
                // 设置基本字体属性
                run.Font.Name = FontName;
                run.Font.Size = FontSize;
                run.Font.Bold = Bold;
                run.Font.Italic = Italic;
                run.Font.Underline = Underline;

                // 设置中文字体（如果启用）
                if (EnableChineseFont && !string.IsNullOrEmpty(ChineseFontName))
                {
                    run.Font.NameFarEast = ChineseFontName;

                    // 设置中文字体样式
                    if (ChineseFontStyle.HasFlag(FontStyle.Bold))
                        run.Font.BoldBi = true;
                    if (ChineseFontStyle.HasFlag(FontStyle.Italic))
                        run.Font.ItalicBi = true;

                    // 设置中文字体大小
                    if (ChineseFontSize > 0)
                        run.Font.SizeBi = ChineseFontSize;
                }

                // 设置西文字体（如果启用）
                if (EnableWesternFont && !string.IsNullOrEmpty(WesternFontName))
                {
                    run.Font.NameAscii = WesternFontName;

                    // 设置西文字体样式
                    if (WesternFontStyle != FontStyle.Regular)
                    {
                        run.Font.Bold = WesternFontStyle.HasFlag(FontStyle.Bold);
                        run.Font.Italic = WesternFontStyle.HasFlag(FontStyle.Italic);
                    }

                    // 设置西文字体大小
                    if (WesternFontSize > 0)
                        run.Font.Size = WesternFontSize;
                }

                // 设置复杂文字字体（如果启用）
                if (EnableComplexScriptFont && !string.IsNullOrEmpty(ComplexScriptFontName))
                {
                    run.Font.NameOther = ComplexScriptFontName;

                    // 设置复杂文字字体样式
                    if (ComplexScriptFontStyle != FontStyle.Regular)
                    {
                        run.Font.Bold = ComplexScriptFontStyle.HasFlag(FontStyle.Bold);
                        run.Font.Italic = ComplexScriptFontStyle.HasFlag(FontStyle.Italic);
                    }
                }

                // 设置字体颜色（如果启用）
                if (EnableFontColor && FontColor.HasValue)
                {
                    run.Font.Color = FontColor.Value;
                }

                // 设置文字突出显示颜色（如果启用）
                if (EnableHighlightColor && HighlightColor.HasValue)
                {
                    run.Font.Shading.BackgroundPatternColor = HighlightColor.Value;
                }
            }
        }

        public void ApplyToParagraph(AW.Paragraph paragraph)
        {
            if (paragraph == null)
                throw new ArgumentNullException(nameof(paragraph));

            // 根据不同的应用范围采用不同的处理逻辑
            switch (ApplyScope)
            {
                case FormatApplyScope.WholeParagrph:
                    ApplyFormatToParagraph(paragraph);
                    break;
                case FormatApplyScope.UntilFirstPeriod:
                    ApplyFormatUntilPeriod(paragraph);
                    break;
                case FormatApplyScope.UntilLineBreak:
                    ApplyFormatUntilLineBreak(paragraph);
                    break;
                case FormatApplyScope.UntilSpecificCharInclusive:
                    ApplyFormatUntilSpecificCharInclusive(paragraph);
                    break;
                case FormatApplyScope.UntilSpecificCharExclusive:
                    ApplyFormatUntilSpecificCharExclusive(paragraph);
                    break;
                case FormatApplyScope.BetweenPeriods:
                    ApplyFormatBetweenPeriods(paragraph);
                    break;
                case FormatApplyScope.FromStartToMatchInclusive:
                    ApplyFormatFromStartToMatchInclusive(paragraph);
                    break;
                case FormatApplyScope.FromStartToMatchExclusive:
                    ApplyFormatFromStartToMatchExclusive(paragraph);
                    break;
                case FormatApplyScope.FromCharToMatchInclusive:
                    ApplyFormatFromCharToMatchInclusive(paragraph);
                    break;
                case FormatApplyScope.FromCharToMatchExclusive:
                    ApplyFormatFromCharToMatchExclusive(paragraph);
                    break;
                default:
                    ApplyFormatToParagraph(paragraph);
                    break;
            }
        }

        // 应用格式到整个段落
        private void ApplyFormatToParagraph(AW.Paragraph paragraph)
        {
            try
            {
                // 应用段落级格式（包括边框和底纹）
                ApplyParagraphLevelFormat(paragraph);

                // 注意：边框和底纹已经在ApplyParagraphLevelFormat中处理了，这里不需要重复应用

                // 应用字体格式到整个段落的所有Run
                ApplyFormatToAllRuns(paragraph);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用段落格式时出错: {ex.Message}");
                // 在这里可以选择是否重新抛出异常
            }
        }

        // 应用边框设置
        private void ApplyBorders(AW.Paragraph paragraph)
        {
            if (HasTopBorder)
                paragraph.ParagraphFormat.Borders.Top.LineStyle = BorderType;
            if (HasBottomBorder)
                paragraph.ParagraphFormat.Borders.Bottom.LineStyle = BorderType;
            if (HasLeftBorder)
                paragraph.ParagraphFormat.Borders.Left.LineStyle = BorderType;
            if (HasRightBorder)
                paragraph.ParagraphFormat.Borders.Right.LineStyle = BorderType;

            // 设置边框颜色和宽度
            foreach (AW.Border border in paragraph.ParagraphFormat.Borders)
            {
                if (border.LineStyle != AW.LineStyle.None)
                {
                    border.LineWidth = BorderWidth;
                    border.Color = BorderColor;
                }
            }
        }

        // 应用底纹设置
        private void ApplyShading(AW.Paragraph paragraph)
        {
            try
            {
                // 确保底纹颜色不是黑色，避免文档背景变黑
                Color shadingColor = ShadingColor;

                // 详细的调试日志
                System.Diagnostics.Debug.WriteLine($"[ApplyShading] 规则名称: {Name}");
                System.Diagnostics.Debug.WriteLine($"[ApplyShading] 原始底纹颜色: {ShadingColor} (ARGB: {ShadingColor.ToArgb()})");
                System.Diagnostics.Debug.WriteLine($"[ApplyShading] HasShading: {HasShading}");
                System.Diagnostics.Debug.WriteLine($"[ApplyShading] EnableBorderShading: {EnableBorderShading}");

                if (shadingColor.ToArgb() == Color.Black.ToArgb())
                {
                    shadingColor = Color.LightGray;
                    System.Diagnostics.Debug.WriteLine($"[ApplyShading] 警告：底纹颜色被设置为黑色，已自动调整为浅灰色以避免文档背景变黑");
                }

                // 额外检查：如果颜色仍然是黑色或接近黑色，强制设置为浅灰色
                if (shadingColor.R < 50 && shadingColor.G < 50 && shadingColor.B < 50)
                {
                    System.Diagnostics.Debug.WriteLine($"[ApplyShading] 警告：检测到深色底纹 (R:{shadingColor.R}, G:{shadingColor.G}, B:{shadingColor.B})，已调整为浅灰色");
                    shadingColor = Color.LightGray;
                }

                System.Diagnostics.Debug.WriteLine($"[ApplyShading] 最终应用的底纹颜色: {shadingColor} (ARGB: {shadingColor.ToArgb()})");
                System.Diagnostics.Debug.WriteLine($"[ApplyShading] 底纹图案: {ShadingPattern}");

                // 先清除现有的底纹设置，防止意外的默认值
                paragraph.ParagraphFormat.Shading.ClearFormatting();

                // 然后设置新的底纹
                paragraph.ParagraphFormat.Shading.Texture = ShadingPattern;
                paragraph.ParagraphFormat.Shading.BackgroundPatternColor = shadingColor;

                // 验证设置是否正确应用
                var appliedColor = paragraph.ParagraphFormat.Shading.BackgroundPatternColor;
                System.Diagnostics.Debug.WriteLine($"[ApplyShading] 验证：实际应用的底纹颜色: {appliedColor} (ARGB: {appliedColor.ToArgb()})");

                // 如果应用后的颜色仍然是黑色，强制重新设置
                if (appliedColor.ToArgb() == Color.Black.ToArgb())
                {
                    System.Diagnostics.Debug.WriteLine($"[ApplyShading] 严重警告：应用后检测到黑色底纹，强制重新设置为浅灰色");
                    paragraph.ParagraphFormat.Shading.BackgroundPatternColor = Color.LightGray;
                    paragraph.ParagraphFormat.Shading.ForegroundPatternColor = Color.LightGray;
                }

                System.Diagnostics.Debug.WriteLine($"[ApplyShading] 底纹应用完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ApplyShading] 应用底纹时出错: {ex.Message}");
                // 出错时设置安全的默认底纹
                try
                {
                    paragraph.ParagraphFormat.Shading.ClearFormatting();
                    paragraph.ParagraphFormat.Shading.BackgroundPatternColor = Color.LightGray;
                    System.Diagnostics.Debug.WriteLine($"[ApplyShading] 已设置安全的默认底纹颜色");
                }
                catch (Exception ex2)
                {
                    System.Diagnostics.Debug.WriteLine($"[ApplyShading] 设置安全默认底纹时也出错: {ex2.Message}");
                }
            }
        }

        // 应用格式到匹配部分到第一个句号结束
        private void ApplyFormatUntilPeriod(AW.Paragraph paragraph)
        {
            // 应用段落级格式（这些格式无法仅应用到部分文本）
            ApplyParagraphLevelFormat(paragraph);

            string paragraphText = paragraph.GetText();

            // 查找匹配位置
            int matchPosition = FindMatchPosition(paragraphText);

            if (matchPosition >= 0)
            {
                // 查找匹配位置后的第一个句号
                int periodPosition = paragraphText.IndexOf('.', matchPosition);

                // 如果找到句号，则应用格式到该范围
                if (periodPosition >= 0)
                {
                    // 计算应用格式的范围
                    int startPos = matchPosition;
                    int endPos = periodPosition + 1; // 包含句号

                    // 应用格式到指定范围的Run
                    ApplyFormatToRange(paragraph, startPos, endPos);
                }
                else
                {
                    // 如果未找到句号，则应用到匹配位置到段落结束
                    ApplyFormatToRange(paragraph, matchPosition, paragraphText.Length);
                }
            }
            else
            {
                // 如果没有匹配位置，则应用到整个段落
                ApplyFormatToAllRuns(paragraph);
            }
        }

        // 应用格式到匹配部分到换行结束
        private void ApplyFormatUntilLineBreak(AW.Paragraph paragraph)
        {
            // 应用段落级格式
            ApplyParagraphLevelFormat(paragraph);

            string paragraphText = paragraph.GetText();

            // 查找匹配位置
            int matchPosition = FindMatchPosition(paragraphText);

            if (matchPosition >= 0)
            {
                // 查找匹配位置后的第一个换行符
                int lineBreakPosition = paragraphText.IndexOf('\r', matchPosition);
                if (lineBreakPosition < 0)
                    lineBreakPosition = paragraphText.IndexOf('\n', matchPosition);

                // 如果找到换行符，则应用格式到该范围
                if (lineBreakPosition >= 0)
                {
                    // 计算应用格式的范围
                    int startPos = matchPosition;
                    int endPos = lineBreakPosition + 1; // 包含换行符

                    // 应用格式到指定范围的Run
                    ApplyFormatToRange(paragraph, startPos, endPos);
                }
                else
                {
                    // 如果未找到换行符，则应用到匹配位置到段落结束
                    ApplyFormatToRange(paragraph, matchPosition, paragraphText.Length);
                }
            }
            else
            {
                // 如果没有匹配位置，则应用到整个段落
                ApplyFormatToAllRuns(paragraph);
            }
        }

        // 应用格式到所有Runs
        private void ApplyFormatToAllRuns(AW.Paragraph paragraph)
        {
            foreach (var node in paragraph.Runs)
            {
                if (node is AW.Run run)
                {
                    ApplyTo(run);
                }
            }
        }

        // 应用格式到指定范围的文本
        private void ApplyFormatToRange(AW.Paragraph paragraph, int startPos, int endPos)
        {
            if (startPos < 0 || endPos <= startPos)
                return;

            string paragraphText = paragraph.GetText();
            if (startPos >= paragraphText.Length)
                return;

            // 确保endPos不超过文本长度
            endPos = Math.Min(endPos, paragraphText.Length);

            try
            {
                // 遍历所有Run，找出在指定范围内的Run并应用格式
                int currentPos = 0;
                foreach (var node in paragraph.GetChildNodes(AW.NodeType.Run, false))
                {
                    if (node is AW.Run run)
                    {
                        string runText = run.GetText();
                        int runLength = runText.Length;
                        int runStart = currentPos;
                        int runEnd = currentPos + runLength;

                        // 检查Run是否与指定范围有重叠
                        if (runEnd > startPos && runStart < endPos)
                        {
                            // 完全在范围内，直接应用格式
                            if (runStart >= startPos && runEnd <= endPos)
                            {
                                ApplyTo(run);
                            }
                            // 部分在范围内，需要拆分Run
                            else
                            {
                                // 尝试拆分Run并应用格式到重叠部分
                                // 注意：拆分Run是复杂操作，可能会影响文档结构
                                // 这里简化处理，直接应用格式到整个Run
                                // 在实际应用中，可能需要更复杂的逻辑来处理部分重叠
                                ApplyTo(run);
                            }
                        }

                        currentPos += runLength;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用格式到指定范围时出错: {ex.Message}");
                // 出错时回退到应用格式到整个段落
                ApplyFormatToAllRuns(paragraph);
            }
        }

        // 应用只能应用于段落级别的格式
        private void ApplyParagraphLevelFormat(AW.Paragraph paragraph)
        {
            // 只有启用基本段落格式时才应用基本格式
            if (EnableBasicFormat)
            {
                // 应用对齐方式（如果启用）
                if (EnableAlignment)
                {
                    paragraph.ParagraphFormat.Alignment = Alignment;
                }

                // 应用大纲级别（如果启用）
                if (EnableOutlineLevel)
                {
                    paragraph.ParagraphFormat.OutlineLevel = OutlineLevel;
                }

                // 应用文本方向（如果启用）
                if (EnableTextDirection)
                {
                    paragraph.ParagraphFormat.Bidi = Bidi;
                }

                // 应用缩进设置（如果启用且不被字符单位缩进覆盖）
                if (EnableIndent && !EnableCharacterUnitIndent)
                {
                    paragraph.ParagraphFormat.LeftIndent = LeftIndent;
                    paragraph.ParagraphFormat.RightIndent = RightIndent;

                    // 应用特殊缩进
                    paragraph.ParagraphFormat.FirstLineIndent = SpecialIndent switch
                    {
                        SpecialIndent.FirstLine => SpecialIndentValue,
                        SpecialIndent.Hanging => -SpecialIndentValue,
                        _ => FirstLineIndent
                    };
                }

                // 应用间距设置（如果启用）
                if (EnableSpacing)
                {
                    // 检查是否需要跳过图片所在行的行距设置
                    bool shouldApplyLineSpacing = true;

                    // 如果不调整图片所在行的行距，则检查段落是否包含图片
                    if (!AdjustImageLineSpacing)
                    {
                        // 检查段落是否包含图片
                        var shapes = paragraph.GetChildNodes(AW.NodeType.Shape, true);
                        if (shapes.Count > 0)
                        {
                            // 检查是否有图片形状
                            foreach (AW.Drawing.Shape shape in shapes)
                            {
                                if (shape.HasImage)
                                {
                                    shouldApplyLineSpacing = false;
                                    System.Diagnostics.Debug.WriteLine($"跳过图片所在段落的行距设置: {paragraph.GetText().Trim()}");
                                    break;
                                }
                            }
                        }
                    }

                    // 应用行距设置（如果需要且不被字符单位行距覆盖）
                    if (shouldApplyLineSpacing && !EnableCharacterUnitLineSpacing)
                    {
                        paragraph.ParagraphFormat.LineSpacingRule = LineSpacingRule;
                        // 根据行距规则设置行距值 - 所有情况都直接使用LineSpacing值
                        paragraph.ParagraphFormat.LineSpacing = LineSpacing;
                        System.Diagnostics.Debug.WriteLine($"应用行距设置到段落: {paragraph.GetText().Trim()}, 行距规则: {LineSpacingRule}, 行距值: {LineSpacing}");
                    }

                    // 段前段后间距总是应用（不受图片影响）
                    // 但需要检查是否被字符单位间距覆盖
                    if (!EnableCharacterUnitSpacing)
                    {
                        paragraph.ParagraphFormat.SpaceBefore = SpaceBefore;
                        paragraph.ParagraphFormat.SpaceAfter = SpaceAfter;
                    }
                }
            }

            // 应用样式
            if (!string.IsNullOrEmpty(StyleName))
            {
                paragraph.ParagraphFormat.StyleName = StyleName;
            }

            // 应用制表位设置（如果启用）
            if (EnableTabStops && TabStops.Count > 0)
            {
                // 清除现有的制表位
                paragraph.ParagraphFormat.TabStops.Clear();

                // 添加新的制表位
                foreach (var tabStop in TabStops)
                {
                    paragraph.ParagraphFormat.TabStops.Add(
                        tabStop.Position,
                        tabStop.Alignment,
                        tabStop.Leader);
                }
            }

            // 应用分页和连接控制（如果启用）
            if (EnablePagination)
            {
                paragraph.ParagraphFormat.KeepWithNext = KeepWithNext;
                paragraph.ParagraphFormat.KeepTogether = KeepLinesTogether;
                paragraph.ParagraphFormat.PageBreakBefore = PageBreakBefore;
                paragraph.ParagraphFormat.WidowControl = WidowOrphanControl;
                paragraph.ParagraphFormat.NoSpaceBetweenParagraphsOfSameStyle = NoSpaceBetweenParagraphs;
            }

            // 应用高级段落格式功能（只有启用高级格式时才处理）
            if (EnableAdvancedFormat)
            {
                // 中英文间距自动调整
                paragraph.ParagraphFormat.AddSpaceBetweenFarEastAndAlpha = AddSpaceBetweenFarEastAndAlpha;
                paragraph.ParagraphFormat.AddSpaceBetweenFarEastAndDigit = AddSpaceBetweenFarEastAndDigit;

                // 基线对齐设置
                paragraph.ParagraphFormat.BaselineAlignment = BaselineAlignment;

                // 自动间距控制
                paragraph.ParagraphFormat.SpaceAfterAuto = SpaceAfterAuto;
                paragraph.ParagraphFormat.SpaceBeforeAuto = SpaceBeforeAuto;

                // 连字符控制
                paragraph.ParagraphFormat.SuppressAutoHyphens = SuppressAutoHyphens;

                // 网格对齐
                paragraph.ParagraphFormat.SnapToGrid = SnapToGrid;

                // 单词换行控制
                paragraph.ParagraphFormat.WordWrap = WordWrap;
            }

            // 应用首字下沉功能（独立启用控制）
            if (EnableDropCap)
            {
                paragraph.ParagraphFormat.DropCapPosition = DropCapPosition;
                if (LinesToDrop > 0 && LinesToDrop <= 10) // 限制在合理范围内
                {
                    paragraph.ParagraphFormat.LinesToDrop = LinesToDrop;
                }
            }
            else
            {
                paragraph.ParagraphFormat.DropCapPosition = AW.DropCapPosition.None;
            }

            // 应用中文排版增强功能（只有启用中文排版时才处理）
            if (EnableChineseTypography)
            {
                // 远东换行规则
                paragraph.ParagraphFormat.FarEastLineBreakControl = FarEastLineBreakControl;

                // 悬挂标点符号
                paragraph.ParagraphFormat.HangingPunctuation = HangingPunctuation;
            }

            // 网格线单位间距
            if (EnableLineUnitSpacing)
            {
                paragraph.ParagraphFormat.LineUnitAfter = LineUnitAfter;
                paragraph.ParagraphFormat.LineUnitBefore = LineUnitBefore;
            }

            // 应用字符单位设置
            if (EnableCharacterUnitIndent)
            {
                paragraph.ParagraphFormat.CharacterUnitFirstLineIndent = CharacterUnitFirstLineIndent;
                paragraph.ParagraphFormat.CharacterUnitLeftIndent = CharacterUnitLeftIndent;
                paragraph.ParagraphFormat.CharacterUnitRightIndent = CharacterUnitRightIndent;
            }

            // 应用字符单位间距设置
            if (EnableCharacterUnitSpacing)
            {
                // 注意：Aspose.Words API中没有直接的字符单位间距属性
                // 这里我们可以通过转换为磅值来实现类似效果
                // 假设1个字符 = 12磅（可根据实际字体大小调整）
                double charToPointRatio = 12.0;

                if (CharacterUnitBefore > 0)
                {
                    paragraph.ParagraphFormat.SpaceBefore = CharacterUnitBefore * charToPointRatio;
                }

                if (CharacterUnitAfter > 0)
                {
                    paragraph.ParagraphFormat.SpaceAfter = CharacterUnitAfter * charToPointRatio;
                }
            }

            // 应用字符单位行距设置
            if (EnableCharacterUnitLineSpacing)
            {
                // 字符单位行距通过倍数行距实现
                // 假设1个字符高度 = 1.0倍行距（可根据实际需要调整）
                paragraph.ParagraphFormat.LineSpacingRule = AW.LineSpacingRule.Multiple;
                paragraph.ParagraphFormat.LineSpacing = CharacterUnitLineSpacing;
            }

            // 其他高级控制选项（这些选项有独立的逻辑，不受EnableAdvancedFormat控制）
            paragraph.ParagraphFormat.SuppressLineNumbers = SuppressLineNumbers;

            // 镜像缩进（用于双面打印）
            paragraph.ParagraphFormat.MirrorIndents = MirrorIndents;

            // 应用边框和底纹设置（如果启用）
            if (EnableBorderShading)
            {
                System.Diagnostics.Debug.WriteLine($"[ApplyParagraphLevelFormat] 规则名称: {Name}, EnableBorderShading: true");

                // 应用边框设置
                if (HasBorders)
                {
                    System.Diagnostics.Debug.WriteLine($"[ApplyParagraphLevelFormat] 应用边框设置");
                    ApplyBorders(paragraph);
                }

                // 应用底纹设置
                if (HasShading)
                {
                    System.Diagnostics.Debug.WriteLine($"[ApplyParagraphLevelFormat] 应用底纹设置 - HasShading: true");
                    ApplyShading(paragraph);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[ApplyParagraphLevelFormat] 跳过底纹设置 - HasShading: false");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[ApplyParagraphLevelFormat] 规则名称: {Name}, EnableBorderShading: false - 跳过边框和底纹");
            }
        }

        // 查找匹配位置
        private int FindMatchPosition(string text)
        {
            if (string.IsNullOrEmpty(text))
                return -1;

            try
            {
                // 首先检查条件组合
                if (ConditionItems.Count > 0)
                {
                    return FindMatchPositionWithConditionItems(text);
                }

                // 如果条件组合没有匹配，则检查传统匹配方式
                return FindMatchPositionWithTraditionalMethod(text);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找匹配位置时出错: {ex.Message}");
                return -1; // 出错时返回-1
            }
        }

        // 使用条件组合查找匹配位置
        private int FindMatchPositionWithConditionItems(string text)
        {
            // 遍历所有条件项，找到第一个匹配的位置
            foreach (var item in ConditionItems)
            {
                int position = FindPositionByConditionItem(text, item);
                if (position >= 0)
                    return position;
            }

            return -1; // 没有找到匹配
        }

        // 根据条件项查找位置
        private int FindPositionByConditionItem(string text, ConditionItem item)
        {
            if (string.IsNullOrEmpty(item.Value))
                return -1;

            switch (item.Type)
            {
                case ConditionType.StartsWith:
                    return FindPositionByStartsWith(text, item.Value);

                case ConditionType.Contains:
                    return FindPositionByContains(text, item.Value);

                case ConditionType.EndsWith:
                    return FindPositionByEndsWith(text, item.Value);

                case ConditionType.Regex:
                    return FindPositionByRegex(text, item.Value);

                default:
                    return -1;
            }
        }

        // 使用传统方法查找匹配位置
        private int FindMatchPositionWithTraditionalMethod(string text)
        {
            // 检查开头匹配
            if (StartWithPatterns.Count > 0)
            {
                foreach (var pattern in StartWithPatterns)
                {
                    if (string.IsNullOrEmpty(pattern))
                        continue;

                    if (StartsWithVisibleText(text, pattern))
                    {
                        // 返回第一个可见字符的位置
                        return GetFirstVisibleCharIndex(text);
                    }
                }
            }

            // 检查包含匹配
            if (ContainsPatterns.Count > 0)
            {
                foreach (var pattern in ContainsPatterns)
                {
                    if (string.IsNullOrEmpty(pattern))
                        continue;

                    int position;
                    if (CaseSensitive)
                    {
                        position = text.IndexOf(pattern);
                    }
                    else
                    {
                        position = text.IndexOf(pattern, StringComparison.OrdinalIgnoreCase);
                    }

                    if (position >= 0)
                        return position;
                }
            }

            // 检查结尾匹配
            if (EndWithPatterns.Count > 0)
            {
                foreach (var pattern in EndWithPatterns)
                {
                    if (string.IsNullOrEmpty(pattern))
                        continue;

                    if (CaseSensitive)
                    {
                        if (text.EndsWith(pattern))
                            return text.Length - pattern.Length;
                    }
                    else
                    {
                        if (text.EndsWith(pattern, StringComparison.OrdinalIgnoreCase))
                            return text.Length - pattern.Length;
                    }
                }
            }

            // 检查正则表达式
            if (!string.IsNullOrEmpty(Pattern))
            {
                if (UseRegex)
                {
                    return FindPositionByRegex(text, Pattern);
                }
                else
                {
                    int position;
                    if (CaseSensitive)
                    {
                        position = text.IndexOf(Pattern);
                    }
                    else
                    {
                        position = text.IndexOf(Pattern, StringComparison.OrdinalIgnoreCase);
                    }

                    if (position >= 0)
                        return position;
                }
            }

            return -1; // 没有找到匹配
        }

        // 查找以指定模式开头的位置
        private int FindPositionByStartsWith(string text, string patternValue)
        {
            foreach (var pattern in patternValue.Split(new[] { "===" }, StringSplitOptions.RemoveEmptyEntries))
            {
                string comparePattern = pattern.Trim();
                if (string.IsNullOrEmpty(comparePattern))
                    continue;

                if (StartsWithVisibleText(text, comparePattern))
                {
                    // 返回第一个可见字符的位置
                    return GetFirstVisibleCharIndex(text);
                }
            }

            return -1;
        }

        // 查找包含指定模式的位置
        private int FindPositionByContains(string text, string patternValue)
        {
            foreach (var pattern in patternValue.Split(new[] { "===" }, StringSplitOptions.RemoveEmptyEntries))
            {
                string comparePattern = pattern.Trim();
                if (string.IsNullOrEmpty(comparePattern))
                    continue;

                int position;
                if (CaseSensitive)
                {
                    position = text.IndexOf(comparePattern);
                }
                else
                {
                    position = text.IndexOf(comparePattern, StringComparison.OrdinalIgnoreCase);
                }

                if (position >= 0)
                    return position;
            }

            return -1;
        }

        // 查找以指定模式结尾的位置
        private int FindPositionByEndsWith(string text, string patternValue)
        {
            foreach (var pattern in patternValue.Split(new[] { "===" }, StringSplitOptions.RemoveEmptyEntries))
            {
                string comparePattern = pattern.Trim();
                if (string.IsNullOrEmpty(comparePattern))
                    continue;

                if (CaseSensitive)
                {
                    if (text.EndsWith(comparePattern))
                        return text.Length - comparePattern.Length;
                }
                else
                {
                    if (text.EndsWith(comparePattern, StringComparison.OrdinalIgnoreCase))
                        return text.Length - comparePattern.Length;
                }
            }

            return -1;
        }

        // 使用正则表达式查找位置
        private int FindPositionByRegex(string text, string pattern)
        {
            if (string.IsNullOrEmpty(pattern))
                return -1;

            try
            {
                var options = CaseSensitive ?
                    System.Text.RegularExpressions.RegexOptions.None :
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase;

                var match = System.Text.RegularExpressions.Regex.Match(text, pattern, options);
                if (match.Success)
                    return match.Index;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"正则表达式匹配出错: {ex.Message}");
            }

            return -1;
        }

        // 处理到自定义字符结束（包含该字符）的格式应用
        private void ApplyFormatUntilSpecificCharInclusive(AW.Paragraph paragraph)
        {
            try
            {
                string paragraphText = paragraph.GetText();
                int matchPosition = FindMatchPosition(paragraphText);

                if (matchPosition >= 0 && !string.IsNullOrEmpty(EndingChar))
                {
                    // 验证结束字符
                    if (EndingChar.Length > 10)
                    {
                        System.Diagnostics.Debug.WriteLine($"结束字符过长，已截断: {EndingChar}");
                        EndingChar = EndingChar.Substring(0, 10);
                    }

                    // 从匹配位置开始查找结束字符
                    int endingCharPosition = paragraphText.IndexOf(EndingChar, matchPosition);

                    // 如果找到结束字符，应用格式到该位置（包含结束字符）
                    if (endingCharPosition >= 0)
                    {
                        // 应用段落级格式
                        ApplyParagraphLevelFormat(paragraph);

                        // 计算应用格式的范围
                        int startPos = matchPosition;
                        int endPos = endingCharPosition + EndingChar.Length; // 包含结束字符

                        // 确保范围有效
                        if (startPos < 0) startPos = 0;
                        if (endPos > paragraphText.Length) endPos = paragraphText.Length;
                        if (endPos <= startPos) endPos = startPos + 1;

                        // 应用格式到指定范围的Run
                        ApplyFormatToRange(paragraph, startPos, endPos);
                    }
                    else
                    {
                        // 如果未找到结束字符，默认应用到段落结束
                        ApplyFormatToRange(paragraph, matchPosition, paragraphText.Length);
                    }
                }
                else
                {
                    // 如果没有匹配或没有设置结束字符，应用到整个段落
                    ApplyFormatToAllRuns(paragraph);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用格式到指定字符结束（包含）时出错: {ex.Message}");
                // 出错时应用到整个段落
                ApplyFormatToAllRuns(paragraph);
            }
        }

        // 处理到自定义字符结束（不包含该字符）的格式应用
        private void ApplyFormatUntilSpecificCharExclusive(AW.Paragraph paragraph)
        {
            try
            {
                string paragraphText = paragraph.GetText();
                int matchPosition = FindMatchPosition(paragraphText);

                if (matchPosition >= 0 && !string.IsNullOrEmpty(EndingChar))
                {
                    // 验证结束字符
                    if (EndingChar.Length > 10)
                    {
                        System.Diagnostics.Debug.WriteLine($"结束字符过长，已截断: {EndingChar}");
                        EndingChar = EndingChar.Substring(0, 10);
                    }

                    // 从匹配位置开始查找结束字符
                    int endingCharPosition = paragraphText.IndexOf(EndingChar, matchPosition);

                    // 如果找到结束字符，应用格式到该位置（不包含结束字符）
                    if (endingCharPosition >= 0)
                    {
                        // 应用段落级格式
                        ApplyParagraphLevelFormat(paragraph);

                        // 计算应用格式的范围
                        int startPos = matchPosition;
                        int endPos = endingCharPosition; // 不包含结束字符

                        // 确保范围有效
                        if (startPos < 0) startPos = 0;
                        if (endPos > paragraphText.Length) endPos = paragraphText.Length;
                        if (endPos <= startPos) endPos = startPos + 1;

                        // 应用格式到指定范围的Run
                        ApplyFormatToRange(paragraph, startPos, endPos);
                    }
                    else
                    {
                        // 如果未找到结束字符，默认应用到段落结束
                        ApplyFormatToRange(paragraph, matchPosition, paragraphText.Length);
                    }
                }
                else
                {
                    // 如果没有匹配或没有设置结束字符，应用到整个段落
                    ApplyFormatToAllRuns(paragraph);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用格式到指定字符结束（不包含）时出错: {ex.Message}");
                // 出错时应用到整个段落
                ApplyFormatToAllRuns(paragraph);
            }
        }

        // 应用格式至匹配部分前一个句号到后一个句号之间部分
        private void ApplyFormatBetweenPeriods(AW.Paragraph paragraph)
        {
            string paragraphText = paragraph.GetText();
            int matchPosition = FindMatchPosition(paragraphText);

            if (matchPosition >= 0)
            {
                // 查找匹配位置前的最后一个句号
                int previousPeriodPosition = -1;
                for (int i = matchPosition - 1; i >= 0; i--)
                {
                    if (paragraphText[i] == '.')
                    {
                        previousPeriodPosition = i;
                        break;
                    }
                }

                // 查找匹配位置后的第一个句号
                int nextPeriodPosition = paragraphText.IndexOf('.', matchPosition);

                // 确定格式应用范围
                int startPosition = previousPeriodPosition + 1; // 前一个句号之后
                if (previousPeriodPosition < 0)
                    startPosition = 0; // 如果没有前一个句号，从段落开头开始

                int endPosition = paragraphText.Length;
                if (nextPeriodPosition >= 0)
                    endPosition = nextPeriodPosition + 1; // 包含后一个句号

                // 应用段落级格式
                ApplyParagraphLevelFormat(paragraph);

                // 应用格式到指定范围的Run
                ApplyFormatToRange(paragraph, startPosition, endPosition);
            }
            else
            {
                // 如果没有匹配位置，则应用到整个段落
                ApplyFormatToAllRuns(paragraph);
            }
        }

        // 应用格式从段落开头到匹配部分结束（含匹配部分）
        private void ApplyFormatFromStartToMatchInclusive(AW.Paragraph paragraph)
        {
            string paragraphText = paragraph.GetText();
            int matchPosition = FindMatchPosition(paragraphText);

            if (matchPosition >= 0)
            {
                // 找到匹配部分的结束位置
                int matchEndPosition = FindMatchEndPosition(paragraphText);
                if (matchEndPosition < 0)
                    matchEndPosition = paragraphText.Length;

                // 应用段落级格式
                ApplyParagraphLevelFormat(paragraph);

                // 应用格式到指定范围的Run
                ApplyFormatToRange(paragraph, 0, matchEndPosition);
            }
            else
            {
                // 如果没有匹配位置，则应用到整个段落
                ApplyFormatToAllRuns(paragraph);
            }
        }

        // 应用格式从段落开头到匹配部分结束（不含匹配部分）
        private void ApplyFormatFromStartToMatchExclusive(AW.Paragraph paragraph)
        {
            string paragraphText = paragraph.GetText();
            int matchPosition = FindMatchPosition(paragraphText);

            if (matchPosition >= 0)
            {
                // 应用段落级格式
                ApplyParagraphLevelFormat(paragraph);

                // 应用格式到指定范围的Run
                ApplyFormatToRange(paragraph, 0, matchPosition);
            }
            else
            {
                // 如果没有匹配位置，则应用到整个段落
                ApplyFormatToAllRuns(paragraph);
            }
        }

        // 应用格式从特定字符到匹配部分结束（含匹配部分）
        private void ApplyFormatFromCharToMatchInclusive(AW.Paragraph paragraph)
        {
            try
            {
                string paragraphText = paragraph.GetText();
                int matchPosition = FindMatchPosition(paragraphText);

                if (matchPosition >= 0 && !string.IsNullOrEmpty(StartingChar))
                {
                    // 验证起始字符
                    if (StartingChar.Length > 10)
                    {
                        System.Diagnostics.Debug.WriteLine($"起始字符过长，已截断: {StartingChar}");
                        StartingChar = StartingChar.Substring(0, 10);
                    }

                    // 查找特定字符的位置（从段落开头到匹配位置之间）
                    int charPosition = paragraphText.IndexOf(StartingChar, 0, matchPosition);

                    // 找到匹配部分的结束位置
                    int matchEndPosition = FindMatchEndPosition(paragraphText);
                    if (matchEndPosition < 0)
                        matchEndPosition = paragraphText.Length;

                    if (charPosition >= 0)
                    {
                        // 应用段落级格式
                        ApplyParagraphLevelFormat(paragraph);

                        // 确保范围有效
                        if (charPosition < 0) charPosition = 0;
                        if (matchEndPosition > paragraphText.Length) matchEndPosition = paragraphText.Length;
                        if (matchEndPosition <= charPosition) matchEndPosition = charPosition + 1;

                        // 应用格式到指定范围的Run
                        ApplyFormatToRange(paragraph, charPosition, matchEndPosition);
                    }
                    else
                    {
                        // 如果未找到特定字符，则从段落开头到匹配结束
                        ApplyFormatFromStartToMatchInclusive(paragraph);
                    }
                }
                else
                {
                    // 如果没有匹配位置或特定字符未设置，则应用到整个段落
                    ApplyFormatToAllRuns(paragraph);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用格式从特定字符到匹配部分结束（含匹配部分）时出错: {ex.Message}");
                // 出错时应用到整个段落
                ApplyFormatToAllRuns(paragraph);
            }
        }

        // 应用格式从特定字符到匹配部分结束（不含匹配部分）
        private void ApplyFormatFromCharToMatchExclusive(AW.Paragraph paragraph)
        {
            try
            {
                string paragraphText = paragraph.GetText();
                int matchPosition = FindMatchPosition(paragraphText);

                if (matchPosition >= 0 && !string.IsNullOrEmpty(StartingChar))
                {
                    // 验证起始字符
                    if (StartingChar.Length > 10)
                    {
                        System.Diagnostics.Debug.WriteLine($"起始字符过长，已截断: {StartingChar}");
                        StartingChar = StartingChar.Substring(0, 10);
                    }

                    // 查找特定字符的位置（从段落开头到匹配位置之间）
                    int charPosition = paragraphText.IndexOf(StartingChar, 0, matchPosition);

                    if (charPosition >= 0)
                    {
                        // 应用段落级格式
                        ApplyParagraphLevelFormat(paragraph);

                        // 确保范围有效
                        if (charPosition < 0) charPosition = 0;
                        if (matchPosition > paragraphText.Length) matchPosition = paragraphText.Length;
                        if (matchPosition <= charPosition) matchPosition = charPosition + 1;

                        // 应用格式到指定范围的Run
                        ApplyFormatToRange(paragraph, charPosition, matchPosition);
                    }
                    else
                    {
                        // 如果未找到特定字符，则从段落开头到匹配结束（不含匹配部分）
                        ApplyFormatFromStartToMatchExclusive(paragraph);
                    }
                }
                else
                {
                    // 如果没有匹配位置或特定字符未设置，则应用到整个段落
                    ApplyFormatToAllRuns(paragraph);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用格式从特定字符到匹配部分结束（不含匹配部分）时出错: {ex.Message}");
                // 出错时应用到整个段落
                ApplyFormatToAllRuns(paragraph);
            }
        }

        // 辅助方法：查找匹配部分的结束位置
        private int FindMatchEndPosition(string text)
        {
            if (string.IsNullOrEmpty(text))
                return -1;

            try
            {
                int position = FindMatchPosition(text);
                if (position < 0)
                    return -1;

                // 首先检查条件组合
                if (ConditionItems.Count > 0)
                {
                    int endPosition = FindMatchEndPositionWithConditionItems(text, position);
                    if (endPosition > position)
                        return endPosition;
                }

                // 如果条件组合没有找到结束位置，则检查传统匹配方式
                int traditionalEndPosition = FindMatchEndPositionWithTraditionalMethod(text, position);
                if (traditionalEndPosition > position)
                    return traditionalEndPosition;

                // 默认返回匹配位置+1，表示只匹配一个字符
                return position + 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找匹配结束位置时出错: {ex.Message}");
                return -1; // 出错时返回-1
            }
        }

        // 使用条件组合查找匹配结束位置
        private int FindMatchEndPositionWithConditionItems(string text, int position)
        {
            // 遍历所有条件项，找到匹配的结束位置
            foreach (var item in ConditionItems)
            {
                int endPosition = FindEndPositionByConditionItem(text, item, position);
                if (endPosition > position)
                    return endPosition;
            }

            return position; // 没有找到结束位置，返回匹配位置
        }

        // 根据条件项查找结束位置
        private int FindEndPositionByConditionItem(string text, ConditionItem item, int position)
        {
            if (string.IsNullOrEmpty(item.Value))
                return position;

            switch (item.Type)
            {
                case ConditionType.StartsWith:
                    if (position == 0) // 只有当匹配位置在开头时才有效
                    {
                        foreach (var pattern in item.Value.Split(new[] { "===" }, StringSplitOptions.RemoveEmptyEntries))
                        {
                            string comparePattern = pattern.Trim();
                            if (!string.IsNullOrEmpty(comparePattern))
                                return comparePattern.Length;
                        }
                    }
                    break;

                case ConditionType.Contains:
                    foreach (var pattern in item.Value.Split(new[] { "===" }, StringSplitOptions.RemoveEmptyEntries))
                    {
                        string comparePattern = pattern.Trim();
                        if (string.IsNullOrEmpty(comparePattern))
                            continue;

                        int matchIndex = CaseSensitive ?
                            text.IndexOf(comparePattern, position) :
                            text.IndexOf(comparePattern, position, StringComparison.OrdinalIgnoreCase);

                        if (matchIndex >= 0 && matchIndex == position)
                            return matchIndex + comparePattern.Length;
                    }
                    break;

                case ConditionType.EndsWith:
                    foreach (var pattern in item.Value.Split(new[] { "===" }, StringSplitOptions.RemoveEmptyEntries))
                    {
                        string comparePattern = pattern.Trim();
                        if (string.IsNullOrEmpty(comparePattern))
                            continue;

                        int matchIndex = CaseSensitive ?
                            text.IndexOf(comparePattern, position) :
                            text.IndexOf(comparePattern, position, StringComparison.OrdinalIgnoreCase);

                        if (matchIndex >= 0)
                            return matchIndex + comparePattern.Length;
                    }
                    break;

                case ConditionType.Regex:
                    try
                    {
                        var options = CaseSensitive ?
                            System.Text.RegularExpressions.RegexOptions.None :
                            System.Text.RegularExpressions.RegexOptions.IgnoreCase;

                        var regex = new System.Text.RegularExpressions.Regex(item.Value, options);
                        var match = regex.Match(text, position);
                        if (match.Success && match.Index == position)
                            return match.Index + match.Length;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"正则表达式匹配结束位置出错: {ex.Message}");
                    }
                    break;
            }

            return position; // 没有找到结束位置，返回匹配位置
        }

        // 使用传统方法查找匹配结束位置
        private int FindMatchEndPositionWithTraditionalMethod(string text, int position)
        {
            // 如果有正则表达式模式
            if (!string.IsNullOrEmpty(Pattern) && UseRegex)
            {
                try
                {
                    var options = CaseSensitive ?
                        System.Text.RegularExpressions.RegexOptions.None :
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase;

                    var regex = new System.Text.RegularExpressions.Regex(Pattern, options);
                    var match = regex.Match(text);
                    if (match.Success && match.Index == position)
                        return match.Index + match.Length;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"正则表达式匹配结束位置出错: {ex.Message}");
                }
            }
            else if (!string.IsNullOrEmpty(Pattern))
            {
                // 非正则表达式模式
                if (CaseSensitive)
                {
                    if (text.IndexOf(Pattern, position) == position)
                        return position + Pattern.Length;
                }
                else
                {
                    if (text.IndexOf(Pattern, position, StringComparison.OrdinalIgnoreCase) == position)
                        return position + Pattern.Length;
                }
            }

            // 如果有结尾匹配模式
            if (EndWithPatterns.Count > 0)
            {
                foreach (var pattern in EndWithPatterns)
                {
                    if (string.IsNullOrEmpty(pattern))
                        continue;

                    int matchIndex = CaseSensitive ?
                        text.IndexOf(pattern, position) :
                        text.IndexOf(pattern, position, StringComparison.OrdinalIgnoreCase);

                    if (matchIndex >= 0)
                        return matchIndex + pattern.Length;
                }
            }

            // 如果有包含匹配模式
            if (ContainsPatterns.Count > 0)
            {
                foreach (var pattern in ContainsPatterns)
                {
                    if (string.IsNullOrEmpty(pattern))
                        continue;

                    int matchIndex = CaseSensitive ?
                        text.IndexOf(pattern, position) :
                        text.IndexOf(pattern, position, StringComparison.OrdinalIgnoreCase);

                    if (matchIndex >= 0 && matchIndex == position)
                        return matchIndex + pattern.Length;
                }
            }

            // 如果有开头匹配模式
            if (StartWithPatterns.Count > 0 && position == 0)
            {
                foreach (var pattern in StartWithPatterns)
                {
                    if (!string.IsNullOrEmpty(pattern))
                        return pattern.Length;
                }
            }

            return position; // 没有找到结束位置，返回匹配位置
        }

        // 在ApplyTo方法中应用图片格式
        /// <summary>
        /// 应用图片格式到Shape对象
        /// 只有勾选的子功能才会被处理，未勾选的功能会被跳过
        /// </summary>
        /// <param name="shape">要应用格式的Shape对象</param>
        public void ApplyToShape(AW.Drawing.Shape shape)
        {
            if (!EnableImageFormat || !shape.HasImage)
                return;

            // 应用图片尺寸设置（只有启用图片尺寸功能时才处理）
            if (EnableImageSize)
            {
                if (PreserveAspectRatio)
                {
                    // 计算宽高比
                    double aspectRatio = shape.Width / shape.Height;

                // 如果设置了宽度和高度，按照比例应用
                if (ImageWidth > 0 && ImageHeight > 0)
                {
                    // 同时设置宽高，保持纵横比
                    double newWidth = ImageWidth;
                    double newHeight = newWidth / aspectRatio;

                    if (newHeight > ImageHeight)
                    {
                        newHeight = ImageHeight;
                        newWidth = newHeight * aspectRatio;
                    }

                    shape.Width = newWidth;
                    shape.Height = newHeight;
                }
                // 只设置了宽度
                else if (ImageWidth > 0)
                {
                    double newWidth = ImageWidth;
                    shape.Width = newWidth;
                    shape.Height = newWidth / aspectRatio;
                }
                // 只设置了高度
                else if (ImageHeight > 0)
                {
                    double newHeight = ImageHeight;
                    shape.Height = newHeight;
                    shape.Width = newHeight * aspectRatio;
                }
            }
            else
            {
                // 不保持纵横比，直接设置宽度和高度
                if (ImageWidth > 0)
                    shape.Width = ImageWidth;

                if (ImageHeight > 0)
                    shape.Height = ImageHeight;
                }
            }

            // 应用百分比缩放
            if (ImageScaleX != 100 || ImageScaleY != 100)
            {
                double scale = 1.0;
                // 如果设置了精确宽高，不应用百分比缩放
                if (ImageWidth <= 0 && ImageHeight <= 0)
                {
                    if (ImageScaleX != 100)
                    {
                        scale = ImageScaleX / 100.0;
                        shape.Width = shape.Width * scale;
                    }

                    if (ImageScaleY != 100)
                    {
                        scale = ImageScaleY / 100.0;
                        shape.Height = shape.Height * scale;
                    }
                }
            }

            // 应用文本环绕设置（只有启用文本环绕功能时才处理）
            if (EnableImageWrap)
            {
                // 首先处理特殊的衬于文字上下方设置
                if (WrapTextBehind)
                {
                    shape.WrapType = AW.Drawing.WrapType.None;
                    shape.BehindText = true;
                    System.Diagnostics.Debug.WriteLine("设置图片衬于文字下方");
                }
                else if (WrapTextFront)
                {
                    shape.WrapType = AW.Drawing.WrapType.None;
                    shape.BehindText = false;
                    System.Diagnostics.Debug.WriteLine("设置图片衬于文字上方");
                }
                else
                {
                    // 应用标准环绕类型
                    shape.WrapType = ImageWrapType;
                    shape.BehindText = false; // 确保不在文字后面
                    System.Diagnostics.Debug.WriteLine($"设置图片环绕类型: {ImageWrapType}");
                }
            }

            // 应用图片效果（只有启用图片效果功能时才处理）
            if (EnableImageEffect)
            {
                try
                {
                    // 1. 图片裁剪
                    if (EnableImageCrop)
                    {
                        // 设置裁剪值，单位为点
                        if (CropLeft > 0)
                            shape.ImageData.CropLeft = CropLeft;
                        if (CropRight > 0)
                            shape.ImageData.CropRight = CropRight;
                        if (CropTop > 0)
                            shape.ImageData.CropTop = CropTop;
                        if (CropBottom > 0)
                            shape.ImageData.CropBottom = CropBottom;
                    }

                    // 2. 图片亮度、对比度和透明度
                    // 直接使用Aspose.Words API设置这些属性
                    try
                    {
                        // 设置亮度 (-1.0 到 1.0 范围)
                        if (ImageBrightness != 0)
                        {
                            // 将百分比值转换为 -1.0 到 1.0 范围
                            double brightnessValue = ImageBrightness / 100.0;
                            // 确保值在有效范围内
                            brightnessValue = Math.Max(-1.0, Math.Min(1.0, brightnessValue));
                            shape.ImageData.Brightness = brightnessValue;
                        }

                        // 设置对比度 (-1.0 到 1.0 范围)
                        if (ImageContrast != 0)
                        {
                            // 将百分比值转换为 -1.0 到 1.0 范围
                            double contrastValue = ImageContrast / 100.0;
                            // 确保值在有效范围内
                            contrastValue = Math.Max(-1.0, Math.Min(1.0, contrastValue));
                            shape.ImageData.Contrast = contrastValue;
                        }

                        // 透明度设置 - 使用正确的API
                        if (ImageTransparency > 0)
                        {
                            try
                            {
                                double opacity = 1.0 - (ImageTransparency / 100.0);
                                opacity = Math.Max(0.0, Math.Min(1.0, opacity));

                                // 方法1：尝试使用ImageData的透明度属性
                                var imageData = shape.ImageData;
                                if (imageData != null)
                                {
                                    // 检查是否有Transparency属性
                                    var transparencyProp = imageData.GetType().GetProperty("Transparency");
                                    if (transparencyProp != null && transparencyProp.CanWrite)
                                    {
                                        transparencyProp.SetValue(imageData, ImageTransparency / 100.0);
                                        System.Diagnostics.Debug.WriteLine($"使用ImageData.Transparency设置透明度: {ImageTransparency}%");
                                    }
                                    else
                                    {
                                        // 方法2：尝试使用Fill.Transparency
                                        var fillTransparencyProp = shape.Fill.GetType().GetProperty("Transparency");
                                        if (fillTransparencyProp != null && fillTransparencyProp.CanWrite)
                                        {
                                            fillTransparencyProp.SetValue(shape.Fill, ImageTransparency / 100.0);
                                            System.Diagnostics.Debug.WriteLine($"使用Fill.Transparency设置透明度: {ImageTransparency}%");
                                        }
                                        else
                                        {
                                            // 方法3：使用Fill.Opacity作为备选方案
                                            shape.Fill.Opacity = opacity;
                                            System.Diagnostics.Debug.WriteLine($"使用Fill.Opacity设置透明度: {opacity}");
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"设置图片透明度时出错: {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"设置图片亮度/对比度时出错: {ex.Message}");
                    }

                // 3. 颜色模式处理 - 使用标准API和枚举
                try
                {
                    if (ImageColorMode != ImageColorMode.Color)
                    {
                        var imageData = shape.ImageData;
                        if (imageData != null)
                        {
                            // 方法1：尝试使用标准的ImageColorMode枚举
                            try
                            {
                                var colorModeProperty = imageData.GetType().GetProperty("ColorMode");
                                if (colorModeProperty != null && colorModeProperty.CanWrite)
                                {
                                    // 尝试获取Aspose.Words的ImageColorMode枚举类型
                                    var asposeColorModeType = Type.GetType("Aspose.Words.Drawing.ImageColorMode, Aspose.Words");
                                    if (asposeColorModeType != null)
                                    {
                                        object colorModeValue = null;
                                        switch (ImageColorMode)
                                        {
                                            case ImageColorMode.Grayscale:
                                                if (Enum.IsDefined(asposeColorModeType, "Grayscale"))
                                                    colorModeValue = Enum.Parse(asposeColorModeType, "Grayscale");
                                                else if (Enum.IsDefined(asposeColorModeType, "GrayScale"))
                                                    colorModeValue = Enum.Parse(asposeColorModeType, "GrayScale");
                                                break;
                                            case ImageColorMode.BlackWhite:
                                                if (Enum.IsDefined(asposeColorModeType, "BlackAndWhite"))
                                                    colorModeValue = Enum.Parse(asposeColorModeType, "BlackAndWhite");
                                                else if (Enum.IsDefined(asposeColorModeType, "BiLevel"))
                                                    colorModeValue = Enum.Parse(asposeColorModeType, "BiLevel");
                                                break;
                                        }

                                        if (colorModeValue != null)
                                        {
                                            colorModeProperty.SetValue(imageData, colorModeValue);
                                            System.Diagnostics.Debug.WriteLine($"使用标准枚举设置图片颜色模式: {ImageColorMode}");
                                        }
                                        else
                                        {
                                            // 方法2：如果枚举不匹配，使用数值作为备选
                                            int colorModeInt = ImageColorMode == ImageColorMode.Grayscale ? 1 : 2;
                                            colorModeProperty.SetValue(imageData, colorModeInt);
                                            System.Diagnostics.Debug.WriteLine($"使用数值设置图片颜色模式: {colorModeInt}");
                                        }
                                    }
                                    else
                                    {
                                        // 方法3：直接使用数值（兼容性备选方案）
                                        int colorModeInt = ImageColorMode == ImageColorMode.Grayscale ? 1 : 2;
                                        colorModeProperty.SetValue(imageData, colorModeInt);
                                        System.Diagnostics.Debug.WriteLine($"使用兼容性数值设置图片颜色模式: {colorModeInt}");
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"设置图片颜色模式时出错: {ex.Message}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"处理图片颜色模式时出错: {ex.Message}");
                }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"应用图片效果时出错: {ex.Message}");
                }
            }

            // 应用图片边框设置
            if (EnableImageBorder)
            {
                try
                {
                    // 使用简单方法设置图片边框
                    // 设置是否显示边框
                    shape.Stroked = true;

                    // 设置边框宽度
                    shape.StrokeWeight = ImageBorderWidth;

                    // 设置边框颜色
                    shape.StrokeColor = ImageBorderColor;

                    // 设置边框样式 - 根据文档类无法直接设置虚线等样式
                    // 可能需要特定版本的Aspose.Words支持
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"应用图片边框时出错: {ex.Message}");
                }
            }
            else
            {
                // 如果禁用边框，则不显示边框
                try
                {
                    shape.Stroked = false;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"禁用图片边框时出错: {ex.Message}");
                }
            }

            // 应用图片位置和对齐设置
            if (EnableImagePosition)
            {
                try
                {
                    // 设置相对位置参考点
                    shape.RelativeHorizontalPosition = HorizontalRelativePosition;
                    shape.RelativeVerticalPosition = VerticalRelativePosition;

                    // 如果使用精确位置
                    if (HorizontalAlignment == AW.Drawing.HorizontalAlignment.Default &&
                        VerticalAlignment == AW.Drawing.VerticalAlignment.Default)
                    {
                        // 使用精确位置值
                        shape.Left = HorizontalPosition;
                        shape.Top = VerticalPosition;
                    }
                    else
                    {
                        // 使用对齐方式
                        if (HorizontalAlignment != AW.Drawing.HorizontalAlignment.Default)
                        {
                            shape.HorizontalAlignment = HorizontalAlignment;
                        }

                        if (VerticalAlignment != AW.Drawing.VerticalAlignment.Default)
                        {
                            shape.VerticalAlignment = VerticalAlignment;
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"应用图片位置和对齐设置时出错: {ex.Message}");
                }
            }

            // 应用旋转和翻转设置
            if (EnableImageRotateFlip)
            {
                try
                {
                    // 应用旋转角度 - 直接使用API
                    shape.Rotation = RotationAngle;

                    // 翻转功能 - 使用更可靠的方法
                    if (FlipHorizontal || FlipVertical)
                    {
                        try
                        {
                            // 方法1：尝试使用标准的翻转属性
                            bool horizontalFlipSet = false;
                            bool verticalFlipSet = false;

                            // 尝试水平翻转
                            if (FlipHorizontal)
                            {
                                var flipHorizontalProperty = shape.GetType().GetProperty("FlipHorizontal");
                                if (flipHorizontalProperty != null && flipHorizontalProperty.CanWrite)
                                {
                                    flipHorizontalProperty.SetValue(shape, true);
                                    horizontalFlipSet = true;
                                    System.Diagnostics.Debug.WriteLine("使用FlipHorizontal属性设置水平翻转");
                                }
                                else
                                {
                                    // 尝试备选属性名
                                    var flipXProperty = shape.GetType().GetProperty("FlipX");
                                    if (flipXProperty != null && flipXProperty.CanWrite)
                                    {
                                        flipXProperty.SetValue(shape, true);
                                        horizontalFlipSet = true;
                                        System.Diagnostics.Debug.WriteLine("使用FlipX属性设置水平翻转");
                                    }
                                }
                            }

                            // 尝试垂直翻转
                            if (FlipVertical)
                            {
                                var flipVerticalProperty = shape.GetType().GetProperty("FlipVertical");
                                if (flipVerticalProperty != null && flipVerticalProperty.CanWrite)
                                {
                                    flipVerticalProperty.SetValue(shape, true);
                                    verticalFlipSet = true;
                                    System.Diagnostics.Debug.WriteLine("使用FlipVertical属性设置垂直翻转");
                                }
                                else
                                {
                                    // 尝试备选属性名
                                    var flipYProperty = shape.GetType().GetProperty("FlipY");
                                    if (flipYProperty != null && flipYProperty.CanWrite)
                                    {
                                        flipYProperty.SetValue(shape, true);
                                        verticalFlipSet = true;
                                        System.Diagnostics.Debug.WriteLine("使用FlipY属性设置垂直翻转");
                                    }
                                }
                            }

                            // 方法2：如果标准属性不可用，记录警告信息
                            if ((FlipHorizontal && !horizontalFlipSet) || (FlipVertical && !verticalFlipSet))
                            {
                                if (FlipHorizontal && !horizontalFlipSet)
                                {
                                    System.Diagnostics.Debug.WriteLine("警告：水平翻转功能在当前Aspose.Words版本中不可用");
                                }
                                if (FlipVertical && !verticalFlipSet)
                                {
                                    System.Diagnostics.Debug.WriteLine("警告：垂直翻转功能在当前Aspose.Words版本中不可用");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置图片翻转时出错: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误但继续处理
                    System.Diagnostics.Debug.WriteLine($"应用图片旋转和翻转设置时出错: {ex.Message}");
                }
            }

            // 应用超链接设置
            if (EnableImageHyperlink)
            {
                try
                {
                    // 获取文档对象
                    var doc = shape.Document;

                    // 使用更简单的方式设置超链接
                    // 由于直接在图片上设置超链接比较复杂且可能依赖于Aspose.Words的特定版本
                    // 我们在这里只记录超链接的意图，实际的链接功能可能需要根据具体的Aspose.Words版本来实现
                    string linkTarget = string.Empty;

                    switch (HyperlinkType)
                    {
                        case HyperlinkType.Url: // URL链接
                            if (!string.IsNullOrEmpty(HyperlinkUrl))
                            {
                                linkTarget = $"URL: {HyperlinkUrl}";
                                System.Diagnostics.Debug.WriteLine($"图片设置了URL超链接: {HyperlinkUrl}");

                                // 尝试通过反射设置超链接属性（如果存在）
                                TrySetShapeHyperlinkProperty(shape, HyperlinkUrl, HyperlinkToolTip, OpenHyperlinkInNewWindow);
                            }
                            break;

                        case HyperlinkType.Bookmark: // 文档内部书签链接
                            if (!string.IsNullOrEmpty(BookmarkName))
                            {
                                linkTarget = $"Bookmark: {BookmarkName}";
                                System.Diagnostics.Debug.WriteLine($"图片设置了书签超链接: {BookmarkName}");

                                // 尝试通过反射设置超链接属性（如果存在）
                                TrySetShapeHyperlinkProperty(shape, $"#{BookmarkName}", HyperlinkToolTip, false);
                            }
                            break;

                        case HyperlinkType.File: // 文件链接
                            if (!string.IsNullOrEmpty(HyperlinkUrl))
                            {
                                linkTarget = $"File: {HyperlinkUrl}";
                                System.Diagnostics.Debug.WriteLine($"图片设置了文件超链接: {HyperlinkUrl}");

                                // 尝试通过反射设置超链接属性（如果存在）
                                TrySetShapeHyperlinkProperty(shape, HyperlinkUrl, HyperlinkToolTip, OpenHyperlinkInNewWindow);
                            }
                            break;
                        case HyperlinkType.Email: // 电子邮件链接
                            if (!string.IsNullOrEmpty(HyperlinkUrl))
                            {
                                linkTarget = $"Email: {HyperlinkUrl}";
                                System.Diagnostics.Debug.WriteLine($"图片设置了电子邮件超链接: {HyperlinkUrl}");

                                // 尝试通过反射设置超链接属性（如果存在）
                                TrySetShapeHyperlinkProperty(shape, HyperlinkUrl, HyperlinkToolTip, OpenHyperlinkInNewWindow);
                            }
                            break;
                    }

                    // 如果图片支持设置标题，可以将链接信息添加到标题中
                    if (!string.IsNullOrEmpty(linkTarget))
                    {
                        var titleProp = shape.GetType().GetProperty("Title");
                        if (titleProp != null)
                        {
                            var currentTitle = titleProp.GetValue(shape) as string ?? "";
                            titleProp.SetValue(shape, $"{currentTitle} [{linkTarget}]");
                        }

                        // 如果图片支持设置ALT文本，将链接信息添加到ALT文本中
                        var altTextProp = shape.GetType().GetProperty("AlternativeText");
                        if (altTextProp != null)
                        {
                            var currentAltText = altTextProp.GetValue(shape) as string ?? "";
                            altTextProp.SetValue(shape, $"{currentAltText} [{linkTarget}]");
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误但继续处理
                    System.Diagnostics.Debug.WriteLine($"应用图片超链接设置时出错: {ex.Message}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"成功应用图片格式设置到图形对象: {shape.Name}");

            // 应用图片锁定设置
            if (EnableImageLocking)
            {
                try
                {
                    // 应用锁定设置 - 直接使用Aspose.Words API
                    if (LockPosition)
                    {
                        // 锁定图片位置
                        try
                        {
                            var lockAnchorProperty = shape.GetType().GetProperty("LockAnchor");
                            if (lockAnchorProperty != null)
                            {
                                lockAnchorProperty.SetValue(shape, true);
                                System.Diagnostics.Debug.WriteLine("已锁定图片位置");
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"锁定图片位置时出错: {ex.Message}");
                        }
                    }

                    if (LockAspectRatio)
                    {
                        // 锁定图片纵横比 - 使用标准API
                        try
                        {
                            // 方法1：尝试使用标准的AspectRatioLocked属性
                            var aspectRatioLockedProperty = shape.GetType().GetProperty("AspectRatioLocked");
                            if (aspectRatioLockedProperty != null && aspectRatioLockedProperty.CanWrite)
                            {
                                aspectRatioLockedProperty.SetValue(shape, true);
                                System.Diagnostics.Debug.WriteLine("使用AspectRatioLocked属性锁定图片纵横比");
                            }
                            else
                            {
                                // 方法2：尝试使用LockAspectRatio属性
                                var lockAspectRatioProperty = shape.GetType().GetProperty("LockAspectRatio");
                                if (lockAspectRatioProperty != null && lockAspectRatioProperty.CanWrite)
                                {
                                    lockAspectRatioProperty.SetValue(shape, true);
                                    System.Diagnostics.Debug.WriteLine("使用LockAspectRatio属性锁定图片纵横比");
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine("警告：当前Aspose.Words版本不支持锁定图片纵横比功能");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"锁定图片纵横比时出错: {ex.Message}");
                        }
                    }

                    if (LockFormatting)
                    {
                        // 锁定图片格式 - 使用更可靠的方法
                        try
                        {
                            bool lockingApplied = false;

                            // 方法1：尝试使用标准的锁定属性
                            var isLockedProperty = shape.GetType().GetProperty("IsLocked");
                            if (isLockedProperty != null && isLockedProperty.CanWrite)
                            {
                                isLockedProperty.SetValue(shape, true);
                                lockingApplied = true;
                                System.Diagnostics.Debug.WriteLine("使用IsLocked属性锁定图片");
                            }

                            // 方法2：尝试禁止选择
                            var noSelectProperty = shape.GetType().GetProperty("NoSelect");
                            if (noSelectProperty != null && noSelectProperty.CanWrite)
                            {
                                noSelectProperty.SetValue(shape, true);
                                lockingApplied = true;
                                System.Diagnostics.Debug.WriteLine("使用NoSelect属性禁止选择图片");
                            }

                            // 方法3：尝试使用AllowInCell属性（如果存在）
                            var allowInCellProperty = shape.GetType().GetProperty("AllowInCell");
                            if (allowInCellProperty != null && allowInCellProperty.CanWrite)
                            {
                                allowInCellProperty.SetValue(shape, false);
                                lockingApplied = true;
                                System.Diagnostics.Debug.WriteLine("使用AllowInCell属性限制图片移动");
                            }

                            if (lockingApplied)
                            {
                                System.Diagnostics.Debug.WriteLine("已应用图片锁定设置");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("警告：当前Aspose.Words版本不支持图片锁定功能");
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"锁定图片格式时出错: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"应用图片锁定设置时出错: {ex.Message}");
                }
            }
        }

        // 设置图片超链接属性 - 使用标准API
        private void TrySetShapeHyperlinkProperty(Aspose.Words.Drawing.Shape shape, string url, string toolTip, bool openInNewWindow)
        {
            try
            {
                var doc = shape.Document;
                if (doc == null)
                {
                    System.Diagnostics.Debug.WriteLine("无法获取文档对象，跳过超链接设置");
                    return;
                }

                // 方法1：尝试使用标准的超链接API
                try
                {
                    // 获取包含图片的段落
                    var paragraph = shape.ParentParagraph;
                    if (paragraph != null)
                    {
                        // 使用DocumentBuilder在图片位置插入超链接
                        var builder = new AW.DocumentBuilder(doc as AW.Document);
                        builder.MoveTo(paragraph);

                        // 创建超链接字段
                        var field = builder.InsertField($"HYPERLINK \"{url}\"", "");
                        if (field != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"使用HYPERLINK字段创建超链接: {url}");

                            // 设置工具提示（如果支持）
                            if (!string.IsNullOrEmpty(toolTip))
                            {
                                try
                                {
                                    var hyperlinkField = field as AW.Fields.FieldHyperlink;
                                    if (hyperlinkField != null)
                                    {
                                        hyperlinkField.ScreenTip = toolTip;
                                        System.Diagnostics.Debug.WriteLine($"设置超链接工具提示: {toolTip}");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"设置超链接工具提示时出错: {ex.Message}");
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"使用标准API创建超链接时出错: {ex.Message}");

                    // 方法2：备选方案 - 使用反射（兼容性）
                    try
                    {
                        var hyperlinkProperty = shape.GetType().GetProperty("Hyperlink");
                        if (hyperlinkProperty != null)
                        {
                            var hyperlink = hyperlinkProperty.GetValue(shape);
                            if (hyperlink != null)
                            {
                                // 设置超链接URL
                                var urlProperty = hyperlink.GetType().GetProperty("Url");
                                if (urlProperty != null && urlProperty.CanWrite)
                                {
                                    urlProperty.SetValue(hyperlink, url);
                                    System.Diagnostics.Debug.WriteLine($"使用反射设置超链接URL: {url}");
                                }

                                // 设置工具提示
                                if (!string.IsNullOrEmpty(toolTip))
                                {
                                    var toolTipProperty = hyperlink.GetType().GetProperty("ToolTip") ??
                                                         hyperlink.GetType().GetProperty("ScreenTip");
                                    if (toolTipProperty != null && toolTipProperty.CanWrite)
                                    {
                                        toolTipProperty.SetValue(hyperlink, toolTip);
                                        System.Diagnostics.Debug.WriteLine($"使用反射设置超链接工具提示: {toolTip}");
                                    }
                                }

                                // 设置是否在新窗口打开
                                if (openInNewWindow)
                                {
                                    var targetProperty = hyperlink.GetType().GetProperty("Target");
                                    if (targetProperty != null && targetProperty.CanWrite)
                                    {
                                        targetProperty.SetValue(hyperlink, "_blank");
                                        System.Diagnostics.Debug.WriteLine("设置超链接在新窗口打开");
                                    }
                                }
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("图片的Hyperlink属性为null，无法设置超链接");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("图片不支持Hyperlink属性，超链接功能不可用");
                        }
                    }
                    catch (Exception reflectionEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"使用反射设置超链接时出错: {reflectionEx.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置图片超链接时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用表格格式到文档中的所有表格
        /// 只有勾选的子功能才会被处理，未勾选的功能会被跳过
        /// </summary>
        /// <param name="doc">要处理的文档</param>
        public void ApplyTableFormatToDocument(AW.Document doc)
        {
            // 检查是否有任何表格格式功能被启用
            if (!EnableTableAutoFit && !EnableTableLayout && !EnableTableStyle && !EnableCellFormat)
                return;

            try
            {
                // 获取文档中的所有表格
                var tables = doc.GetChildNodes(AW.NodeType.Table, true).Cast<AW.Tables.Table>().ToList();

                if (tables.Count == 0)
                    return;

                foreach (var table in tables)
                {
                    try
                    {
                        // 应用表格宽度自适应设置（只有启用时才处理）
                        if (EnableTableAutoFit)
                        {
                            ApplyTableAutoFitSettings(table);
                        }

                        // 应用表格布局设置（只有启用时才处理）
                        if (EnableTableLayout)
                        {
                            ApplyTableLayoutSettings(table);
                        }

                        // 应用表格样式设置（只有启用时才处理）
                        if (EnableTableStyle)
                        {
                            ApplyTableStyleSettings(table);
                        }

                        // 应用单元格格式设置（只有启用时才处理）
                        if (EnableCellFormat)
                        {
                            ApplyCellFormatSettings(table);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"应用表格格式时出错: {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"已应用表格格式到 {tables.Count} 个表格");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用表格格式到文档时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用表格宽度自适应设置
        /// </summary>
        /// <param name="table">要处理的表格</param>
        private void ApplyTableAutoFitSettings(AW.Tables.Table table)
        {
            try
            {
                if (AutoFitToContents)
                {
                    table.AutoFit(AW.Tables.AutoFitBehavior.AutoFitToContents);
                }
                else if (AutoFitToWindow)
                {
                    table.AutoFit(AW.Tables.AutoFitBehavior.AutoFitToWindow);
                }
                else if (UseFixedColumnWidth)
                {
                    table.AutoFit(AW.Tables.AutoFitBehavior.FixedColumnWidths);
                    if (PreferredTableWidth > 0)
                    {
                        table.PreferredWidth = AW.Tables.PreferredWidth.FromPercent(PreferredTableWidth);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用表格自适应设置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用表格布局设置
        /// </summary>
        /// <param name="table">要处理的表格</param>
        private void ApplyTableLayoutSettings(AW.Tables.Table table)
        {
            try
            {
                // 设置表格对齐方式
                table.Alignment = TableAlignment;

                // 设置表格缩进
                table.LeftIndent = TableLeftIndent;

                // 设置文本环绕
                table.TextWrapping = TableTextWrapping;

                // 设置是否允许自动调整
                table.AllowAutoFit = TableAllowAutoFit;

                // 设置默认单元格间距
                if (TableDefaultCellSpacing > 0)
                {
                    table.CellSpacing = TableDefaultCellSpacing;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用表格布局设置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用表格样式设置
        /// </summary>
        /// <param name="table">要处理的表格</param>
        private void ApplyTableStyleSettings(AW.Tables.Table table)
        {
            try
            {
                // 应用表格边框设置
                if (TableHasBorders)
                {
                    table.SetBorders(TableBorderStyle, TableBorderWidth, TableBorderColor);
                }
                else
                {
                    table.ClearBorders();
                }

                // 应用表格网格线设置
                if (TableHasGridlines)
                {
                    // 设置内部边框（网格线）
                    table.SetBorder(AW.BorderType.Horizontal, TableGridlineStyle, TableGridlineWidth, TableGridlineColor, true);
                    table.SetBorder(AW.BorderType.Vertical, TableGridlineStyle, TableGridlineWidth, TableGridlineColor, true);
                }

                // 应用表格底纹设置
                if (TableHasShading)
                {
                    table.SetShading(TableShadingPattern, TableShadingColor, Color.Empty);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用表格样式设置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用单元格格式设置
        /// </summary>
        /// <param name="table">要处理的表格</param>
        private void ApplyCellFormatSettings(AW.Tables.Table table)
        {
            try
            {
                foreach (AW.Tables.Row row in table.Rows)
                {
                    foreach (AW.Tables.Cell cell in row.Cells)
                    {
                        // 设置单元格垂直对齐
                        cell.CellFormat.VerticalAlignment = CellVerticalAlignment;

                        // 设置单元格边距
                        cell.CellFormat.TopPadding = CellMarginTop;
                        cell.CellFormat.BottomPadding = CellMarginBottom;
                        cell.CellFormat.LeftPadding = CellMarginLeft;
                        cell.CellFormat.RightPadding = CellMarginRight;

                        // 应用单元格边框设置
                        if (CellHasBorders)
                        {
                            cell.CellFormat.Borders.LineStyle = CellBorderStyle;
                            cell.CellFormat.Borders.LineWidth = CellBorderWidth;
                            cell.CellFormat.Borders.Color = CellBorderColor;
                        }
                        else
                        {
                            cell.CellFormat.Borders.ClearFormatting();
                        }

                        // 应用单元格底纹设置
                        if (CellHasShading)
                        {
                            cell.CellFormat.Shading.BackgroundPatternColor = CellShadingColor;
                            cell.CellFormat.Shading.Texture = CellShadingPattern;
                        }
                        else
                        {
                            cell.CellFormat.Shading.ClearFormatting();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用单元格格式设置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用水印到文档
        /// 只有勾选的子功能才会被处理，未勾选的功能会被跳过
        /// </summary>
        /// <param name="doc">要处理的文档</param>
        public void ApplyWatermarkToDocument(AW.Document doc)
        {
            if (!EnableWatermark && !EnableImageWatermark)
                return;

            try
            {
                // 应用文本水印（只有启用文本水印时才处理）
                if (EnableWatermark)
                {
                    // 使用Aspose.Words创建文本水印
                    // 由于API版本差异，使用更通用的方法
                    try
                    {
                        // 创建水印形状
                        AW.Drawing.Shape watermark = new AW.Drawing.Shape(doc, AW.Drawing.ShapeType.TextBox);
                        watermark.TextPath.Text = WatermarkText;
                        watermark.TextPath.FontFamily = WatermarkFontFamily;
                        watermark.TextPath.Size = WatermarkFontSize;

                        // 设置颜色和透明度
                        watermark.Fill.Color = System.Drawing.Color.FromArgb(
                            (int)(255 * (1 - WatermarkOpacity)),
                            WatermarkColor.R,
                            WatermarkColor.G,
                            WatermarkColor.B);

                        // 设置水印位置和大小
                        watermark.Width = 500;
                        watermark.Height = 100;

                        // 应用水印位置设置（只有启用水印位置功能时才处理）
                        if (EnableWatermarkPosition)
                        {
                            if (WatermarkAtPageCenter)
                            {
                                watermark.RelativeHorizontalPosition = AW.Drawing.RelativeHorizontalPosition.Page;
                                watermark.RelativeVerticalPosition = AW.Drawing.RelativeVerticalPosition.Page;
                                watermark.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Center;
                                watermark.VerticalAlignment = AW.Drawing.VerticalAlignment.Center;
                            }
                            else
                            {
                                watermark.RelativeHorizontalPosition = AW.Drawing.RelativeHorizontalPosition.Page;
                                watermark.RelativeVerticalPosition = AW.Drawing.RelativeVerticalPosition.Page;
                                watermark.Left = WatermarkHorizontalPosition;
                                watermark.Top = WatermarkVerticalPosition;
                            }
                        }
                        else
                        {
                            // 默认居中
                            watermark.RelativeHorizontalPosition = AW.Drawing.RelativeHorizontalPosition.Page;
                            watermark.RelativeVerticalPosition = AW.Drawing.RelativeVerticalPosition.Page;
                            watermark.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Center;
                            watermark.VerticalAlignment = AW.Drawing.VerticalAlignment.Center;
                        }

                        // 应用水印格式设置（只有启用水印格式功能时才处理）
                        if (EnableWatermarkFormat)
                        {
                            watermark.WrapType = WatermarkWrapType;

                            // 设置水印边框
                            if (WatermarkHasBorder)
                            {
                                watermark.Stroked = true;
                                watermark.StrokeWeight = WatermarkBorderWidth;
                                watermark.StrokeColor = WatermarkBorderColor;
                            }
                            else
                            {
                                watermark.Stroked = false;
                            }
                        }
                        else
                        {
                            // 默认无环绕
                            watermark.WrapType = AW.Drawing.WrapType.None;
                        }

                        // 根据水印布局设置旋转
                        if (ImageWatermarkLayout != AW.WatermarkLayout.Horizontal)
                        {
                            watermark.Rotation = WatermarkRotationAngle; // 使用配置的旋转角度
                        }

                        // 将水印添加到文档的每个节
                        foreach (AW.Section section in doc.Sections.Cast<AW.Section>())
                        {
                            try
                            {
                                // 获取页眉
                                var header = section.HeadersFooters.FirstOrDefault();
                                if (header != null)
                                {
                                    // 复制水印
                                    AW.Drawing.Shape sectionWatermark = (AW.Drawing.Shape)watermark.Clone(true);

                                    // 使用反射添加到页眉
                                    var appendChildMethod = header.GetType().GetMethod("AppendChild");
                                    if (appendChildMethod != null)
                                    {
                                        appendChildMethod.Invoke(header, new object[] { sectionWatermark });
                                    }
                                    else
                                    {
                                        // 备选方法：使用DocumentBuilder
                                        var builder = new AW.DocumentBuilder(doc);
                                        builder.MoveToHeaderFooter(AW.HeaderFooterType.HeaderPrimary);
                                        builder.InsertNode(sectionWatermark);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"添加水印到节时出错: {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"应用文本水印时出错: {ex.Message}");
                    }
                }

                // 应用图片水印（只有启用图片水印时才处理）
                if (EnableImageWatermark && !string.IsNullOrEmpty(WatermarkImagePath) && System.IO.File.Exists(WatermarkImagePath))
                {
                    try
                    {
                        // 使用DocumentBuilder插入图片水印
                        var builder = new AW.DocumentBuilder(doc);

                        // 创建图片水印形状
                        AW.Drawing.Shape imageWatermark = builder.InsertImage(WatermarkImagePath);

                        // 设置水印尺寸
                        if (PreserveImageWatermarkAspectRatio)
                        {
                            // 保持纵横比，按宽度缩放
                            imageWatermark.Width = ImageWatermarkWidth;
                            imageWatermark.AspectRatioLocked = true;
                        }
                        else
                        {
                            imageWatermark.Width = ImageWatermarkWidth;
                            imageWatermark.Height = ImageWatermarkHeight;
                        }

                        // 应用水印位置设置（只有启用水印位置功能时才处理）
                        if (EnableWatermarkPosition)
                        {
                            if (WatermarkAtPageCenter)
                            {
                                imageWatermark.RelativeHorizontalPosition = AW.Drawing.RelativeHorizontalPosition.Page;
                                imageWatermark.RelativeVerticalPosition = AW.Drawing.RelativeVerticalPosition.Page;
                                imageWatermark.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Center;
                                imageWatermark.VerticalAlignment = AW.Drawing.VerticalAlignment.Center;
                            }
                            else
                            {
                                imageWatermark.RelativeHorizontalPosition = AW.Drawing.RelativeHorizontalPosition.Page;
                                imageWatermark.RelativeVerticalPosition = AW.Drawing.RelativeVerticalPosition.Page;
                                imageWatermark.Left = WatermarkHorizontalPosition;
                                imageWatermark.Top = WatermarkVerticalPosition;
                            }
                        }
                        else
                        {
                            // 默认居中
                            imageWatermark.RelativeHorizontalPosition = AW.Drawing.RelativeHorizontalPosition.Page;
                            imageWatermark.RelativeVerticalPosition = AW.Drawing.RelativeVerticalPosition.Page;
                            imageWatermark.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Center;
                            imageWatermark.VerticalAlignment = AW.Drawing.VerticalAlignment.Center;
                        }

                        // 应用水印格式设置（只有启用水印格式功能时才处理）
                        if (EnableWatermarkFormat)
                        {
                            imageWatermark.WrapType = WatermarkWrapType;
                        }
                        else
                        {
                            // 默认无环绕
                            imageWatermark.WrapType = AW.Drawing.WrapType.None;
                        }

                        // 设置透明度
                        if (ImageWatermarkOpacity < 1.0)
                        {
                            imageWatermark.Fill.Opacity = ImageWatermarkOpacity;
                        }

                        // 设置颜色模式
                        if (ImageWatermarkColorMode == ImageColorMode.Grayscale)
                        {
                            // 尝试设置为灰度
                            try
                            {
                                var colorModeProp = imageWatermark.ImageData.GetType().GetProperty("ColorMode");
                                if (colorModeProp != null)
                                {
                                    // 假设1代表灰度
                                    colorModeProp.SetValue(imageWatermark.ImageData, 1);
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"设置图片水印灰度模式时出错: {ex.Message}");
                            }
                        }
                        else if (ImageWatermarkColorMode == ImageColorMode.BlackWhite)
                        {
                            // 尝试设置为黑白
                            try
                            {
                                var colorModeProp = imageWatermark.ImageData.GetType().GetProperty("ColorMode");
                                if (colorModeProp != null)
                                {
                                    // 假设2代表黑白
                                    colorModeProp.SetValue(imageWatermark.ImageData, 2);
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"设置图片水印黑白模式时出错: {ex.Message}");
                            }
                        }

                        // 设置旋转
                        if (ImageWatermarkLayout != AW.WatermarkLayout.Horizontal)
                        {
                            imageWatermark.Rotation = -45; // 对角线布局
                        }

                        // 将水印移动到所有页面的背景
                        // 这需要将水印复制到每个节的页眉中
                        AW.Drawing.Shape firstWatermark = imageWatermark;
                        foreach (AW.Section section in doc.Sections.Cast<AW.Section>())
                        {
                            if (section != doc.FirstSection)
                            {
                                // 为其他节创建水印副本
                                builder.MoveToSection(section.Document.IndexOf(section));
                                AW.Drawing.Shape sectionWatermark = builder.InsertImage(WatermarkImagePath);

                                // 复制属性
                                sectionWatermark.Width = firstWatermark.Width;
                                sectionWatermark.Height = firstWatermark.Height;
                                sectionWatermark.RelativeHorizontalPosition = firstWatermark.RelativeHorizontalPosition;
                                sectionWatermark.RelativeVerticalPosition = firstWatermark.RelativeVerticalPosition;
                                sectionWatermark.WrapType = firstWatermark.WrapType;
                                sectionWatermark.HorizontalAlignment = firstWatermark.HorizontalAlignment;
                                sectionWatermark.VerticalAlignment = firstWatermark.VerticalAlignment;
                                sectionWatermark.Fill.Opacity = firstWatermark.Fill.Opacity;
                                sectionWatermark.Rotation = firstWatermark.Rotation;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"设置图片水印时出错: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"应用水印时出错: {ex.Message}");
            }
        }

        // 这些方法已被移除，因为我们现在使用Aspose.Words的Watermark API
        // 不再需要手动配置水印和加载水印图片

        // 添加条件组合相关方法
        public void ClearConditions()
        {
            ConditionItems.Clear();
        }

        public void AddStartWithPattern(string pattern, string logic)
        {
            if (string.IsNullOrWhiteSpace(pattern)) return;
            ConditionItems.Add(new ConditionItem
            {
                Type = ConditionType.StartsWith,
                Value = pattern,
                Logic = logic == "OR" ? LogicOperator.Or : LogicOperator.And
            });
        }

        public void AddContainsPattern(string pattern, string logic)
        {
            if (string.IsNullOrWhiteSpace(pattern)) return;
            ConditionItems.Add(new ConditionItem
            {
                Type = ConditionType.Contains,
                Value = pattern,
                Logic = logic == "OR" ? LogicOperator.Or : LogicOperator.And
            });
        }

        public void AddEndWithPattern(string pattern, string logic)
        {
            if (string.IsNullOrWhiteSpace(pattern)) return;
            ConditionItems.Add(new ConditionItem
            {
                Type = ConditionType.EndsWith,
                Value = pattern,
                Logic = logic == "OR" ? LogicOperator.Or : LogicOperator.And
            });
        }

        public void AddRegexPattern(string pattern, string logic)
        {
            if (string.IsNullOrWhiteSpace(pattern)) return;
            ConditionItems.Add(new ConditionItem
            {
                Type = ConditionType.Regex,
                Value = pattern,
                Logic = logic == "OR" ? LogicOperator.Or : LogicOperator.And
            });
        }

        public void AddParagraphPositionPattern(int position, string logic)
        {
            if (position <= 0) return;
            ConditionItems.Add(new ConditionItem
            {
                Type = ConditionType.ParagraphPosition,
                Value = position.ToString(),
                Logic = logic == "OR" ? LogicOperator.Or : LogicOperator.And
            });
        }

        // 原始匹配方法的重载，使用条件组合进行匹配
        public bool Match(Aspose.Words.Paragraph paragraph)
        {
            // 首先检查是否启用
            if (!IsEnabled) return false;

            string text = paragraph.GetText();

            // 使用IsMatch方法进行匹配，确保逻辑一致
            return IsMatch(text);
        }

        // 新增的匹配方法，支持段落位置信息
        public bool Match(Aspose.Words.Paragraph paragraph, int paragraphPosition)
        {
            // 首先检查是否启用
            if (!IsEnabled) return false;

            string text = paragraph.GetText();

            // 使用IsMatch方法进行匹配，传入段落位置信息
            return IsMatch(text, paragraphPosition);
        }

        private bool MatchWithSingleCondition(string text)
        {
            string compareText = CaseSensitive ? text : text.ToLower();

            // 检查开头匹配
            if (StartWithPatterns.Count > 0)
            {
                bool startMatch = false;
                foreach (var pattern in StartWithPatterns)
                {
                    if (StartsWithVisibleText(text, pattern))
                    {
                        startMatch = true;
                        break;
                    }
                }
                if (!startMatch) return false;
            }

            // 检查包含匹配
            if (ContainsPatterns.Count > 0)
            {
                bool containsMatch = false;
                foreach (var pattern in ContainsPatterns)
                {
                    string comparePattern = CaseSensitive ? pattern : pattern.ToLower();
                    if (compareText.Contains(comparePattern))
                    {
                        containsMatch = true;
                        break;
                    }
                }
                if (!containsMatch) return false;
            }

            // 检查结尾匹配
            if (EndWithPatterns.Count > 0)
            {
                bool endMatch = false;
                foreach (var pattern in EndWithPatterns)
                {
                    string comparePattern = CaseSensitive ? pattern : pattern.ToLower();
                    if (compareText.EndsWith(comparePattern))
                    {
                        endMatch = true;
                        break;
                    }
                }
                if (!endMatch) return false;
            }

            // 检查正则表达式匹配
            if (!string.IsNullOrEmpty(Pattern))
            {
                var regex = new System.Text.RegularExpressions.Regex(
                    Pattern,
                    CaseSensitive ? System.Text.RegularExpressions.RegexOptions.None : System.Text.RegularExpressions.RegexOptions.IgnoreCase
                );
                if (!regex.IsMatch(text)) return false;
            }

            return true;
        }

        private bool MatchWithCompoundConditions(string text, int paragraphPosition = -1)
        {
            if (ConditionItems.Count == 0)
                return false;

            // 使用两阶段评估方法，先处理AND操作，再处理OR操作
            // 这样可以确保逻辑运算的优先级：AND优先于OR

            // 第一阶段：将条件分组，每组内的条件用AND连接
            List<List<ConditionItem>> conditionGroups = new List<List<ConditionItem>>();
            List<ConditionItem> currentGroup = new List<ConditionItem>();

            // 第一个条件总是开始一个新组
            currentGroup.Add(ConditionItems[0]);

            // 遍历剩余条件，根据逻辑运算符分组
            for (int i = 1; i < ConditionItems.Count; i++)
            {
                var item = ConditionItems[i];

                if (item.Logic == LogicOperator.Or)
                {
                    // OR操作符开始一个新组
                    conditionGroups.Add(currentGroup);
                    currentGroup = new List<ConditionItem>();
                }

                // 将当前条件添加到当前组
                currentGroup.Add(item);
            }

            // 添加最后一个组
            conditionGroups.Add(currentGroup);

            // 第二阶段：评估每个组，组内条件用AND连接，组间用OR连接
            bool finalResult = false;

            foreach (var group in conditionGroups)
            {
                // 评估当前组内的所有条件（AND连接）
                bool groupResult = true;

                foreach (var item in group)
                {
                    bool itemResult = EvaluateConditionItem(text, item, paragraphPosition);
                    groupResult = groupResult && itemResult;

                    // 如果组内有一个条件不满足，整个组就不满足
                    if (!groupResult)
                        break;
                }

                // 如果任何一个组满足条件，最终结果就是满足
                finalResult = finalResult || groupResult;

                // 如果已经找到一个满足的组，可以提前返回
                if (finalResult)
                    break;
            }

            return finalResult;
        }

        private bool EvaluateConditionItem(string text, ConditionItem item, int paragraphPosition = -1)
        {
            if (string.IsNullOrEmpty(item.Value))
                return false;

            // 对于段落位置匹配，不需要检查文本内容
            if (item.Type == ConditionType.ParagraphPosition)
            {
                if (paragraphPosition <= 0) return false;
                if (int.TryParse(item.Value, out int targetPosition))
                {
                    return paragraphPosition == targetPosition;
                }
                return false;
            }

            // 对于其他类型的匹配，需要检查文本内容
            if (string.IsNullOrEmpty(text))
                return false;

            try
            {
                string compareText = CaseSensitive ? text : text.ToLower();

                switch (item.Type)
                {
                    case ConditionType.StartsWith:
                        // 处理可能包含多个模式的情况（使用===分隔）
                        string[] startPatterns = item.Value.Split(new[] { "===" }, StringSplitOptions.RemoveEmptyEntries);
                        if (startPatterns.Length == 0) return false;

                        foreach (var pattern in startPatterns)
                        {
                            string trimmedPattern = pattern.Trim();
                            if (!string.IsNullOrEmpty(trimmedPattern) && StartsWithVisibleText(text, trimmedPattern))
                                return true;
                        }
                        return false;

                    case ConditionType.Contains:
                        // 处理可能包含多个模式的情况（使用===分隔）
                        string[] containsPatterns = item.Value.Split(new[] { "===" }, StringSplitOptions.RemoveEmptyEntries);
                        if (containsPatterns.Length == 0) return false;

                        foreach (var pattern in containsPatterns)
                        {
                            string comparePattern = CaseSensitive ? pattern.Trim() : pattern.Trim().ToLower();
                            if (!string.IsNullOrEmpty(comparePattern) && compareText.Contains(comparePattern))
                                return true;
                        }
                        return false;

                    case ConditionType.EndsWith:
                        // 处理可能包含多个模式的情况（使用===分隔）
                        string[] endPatterns = item.Value.Split(new[] { "===" }, StringSplitOptions.RemoveEmptyEntries);
                        if (endPatterns.Length == 0) return false;

                        foreach (var pattern in endPatterns)
                        {
                            string comparePattern = CaseSensitive ? pattern.Trim() : pattern.Trim().ToLower();
                            if (!string.IsNullOrEmpty(comparePattern) && compareText.EndsWith(comparePattern))
                                return true;
                        }
                        return false;

                    case ConditionType.Regex:
                        // 验证正则表达式的有效性
                        if (!IsValidRegex(item.Value))
                        {
                            System.Diagnostics.Debug.WriteLine($"无效的正则表达式: {item.Value}");
                            return false;
                        }

                        try
                        {
                            var regex = new System.Text.RegularExpressions.Regex(
                                item.Value,
                                CaseSensitive ? System.Text.RegularExpressions.RegexOptions.None : System.Text.RegularExpressions.RegexOptions.IgnoreCase
                            );
                            return regex.IsMatch(text);
                        }
                        catch (System.ArgumentException ex)
                        {
                            // 正则表达式无效
                            System.Diagnostics.Debug.WriteLine($"无效的正则表达式: {item.Value}, 错误: {ex.Message}");
                            return false;
                        }

                    case ConditionType.ParagraphPosition:
                        // 段落位置匹配已在方法开头处理
                        return false;

                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"评估条件项时出错: {ex.Message}");
                return false;
            }
        }

        // 验证正则表达式的有效性
        private bool IsValidRegex(string pattern)
        {
            if (string.IsNullOrEmpty(pattern))
                return false;

            try
            {
                // 尝试创建正则表达式对象，如果成功则表示正则表达式有效
                var regex = new System.Text.RegularExpressions.Regex(pattern);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"正则表达式验证失败: {ex.Message}");
                return false;
            }
        }

        // 检查文本是否以指定模式开头（忽略开头的空格、Tab、缩进等不可见字符）
        private bool StartsWithVisibleText(string text, string pattern)
        {
            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(pattern))
                return false;

            // 找到第一个可见字符的位置
            int firstVisibleCharIndex = 0;
            while (firstVisibleCharIndex < text.Length &&
                   (char.IsWhiteSpace(text[firstVisibleCharIndex]) ||
                    text[firstVisibleCharIndex] == '\t' ||
                    text[firstVisibleCharIndex] == '\u00A0')) // 包括不间断空格
            {
                firstVisibleCharIndex++;
            }

            // 如果整个文本都是空白字符，则不匹配
            if (firstVisibleCharIndex >= text.Length)
                return false;

            // 从第一个可见字符开始检查是否匹配模式
            string visibleText = text.Substring(firstVisibleCharIndex);

            return CaseSensitive ?
                visibleText.StartsWith(pattern) :
                visibleText.StartsWith(pattern, StringComparison.OrdinalIgnoreCase);
        }

        // 获取第一个可见字符的索引位置
        private int GetFirstVisibleCharIndex(string text)
        {
            if (string.IsNullOrEmpty(text))
                return 0;

            int firstVisibleCharIndex = 0;
            while (firstVisibleCharIndex < text.Length &&
                   (char.IsWhiteSpace(text[firstVisibleCharIndex]) ||
                    text[firstVisibleCharIndex] == '\t' ||
                    text[firstVisibleCharIndex] == '\u00A0')) // 包括不间断空格
            {
                firstVisibleCharIndex++;
            }

            return firstVisibleCharIndex;
        }
    }

    /// <summary>
    /// 制表位设置
    /// </summary>
    public class TabStop
    {
        public double Position { get; set; } = 0;
        public AW.TabAlignment Alignment { get; set; } = AW.TabAlignment.Left;
        public AW.TabLeader Leader { get; set; } = AW.TabLeader.None;

        public TabStop() { }

        public TabStop(double position, AW.TabAlignment alignment = AW.TabAlignment.Left, AW.TabLeader leader = AW.TabLeader.None)
        {
            Position = position;
            Alignment = alignment;
            Leader = leader;
        }
    }

    // 新增条件项类型枚举
    public enum ConditionType
    {
        StartsWith,
        Contains,
        EndsWith,
        Regex,
        ParagraphPosition
    }

    // 新增逻辑运算符枚举
    public enum LogicOperator
    {
        None,   // 第一个条件不需要逻辑运算符
        And,
        Or
    }

    // 新增条件项类
    public class ConditionItem
    {
        public required ConditionType Type { get; set; }
        public required string Value { get; set; } = string.Empty;
        public required LogicOperator Logic { get; set; }
    }
}
