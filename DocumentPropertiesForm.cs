/*
 * ========================================
 * 文件名: DocumentPropertiesForm.cs
 * 功能描述: 文档属性配置窗体
 * ========================================
 *
 * 主要功能:
 * 1. 文档基本属性设置（标题、作者、主题等）
 * 2. 文档扩展属性配置（内容类型、状态等）
 * 3. 文档统计信息设置（字数、页数等）
 * 4. 自定义属性管理
 * 5. 属性的启用/禁用控制
 * 6. 文件名作为标题的选项
 *
 * 界面结构:
 * - 基本信息标签页：标题、作者、主题、公司等基础属性
 * - 扩展信息标签页：内容类型、状态、模板、时间等扩展属性
 * - 统计信息标签页：字数、页数、段落数等统计数据
 * - 自定义信息标签页：用户自定义的文档属性
 *
 * 基本属性功能:
 * - 标题：支持多行文本，可选择使用文件名作为标题
 * - 主题：文档主题描述
 * - 作者：文档作者信息
 * - 管理者：文档管理者
 * - 公司：所属公司或组织
 * - 类别：文档分类
 * - 标记：关键词标签
 * - 备注：文档备注信息
 *
 * 扩展属性功能:
 * - 内容类型：文档内容的类型分类
 * - 内容状态：文档的当前状态
 * - 超链接基址：文档中相对链接的基础地址
 * - 模板：使用的文档模板
 * - 创建时间：文档创建时间
 * - 最后保存时间：文档最后保存时间
 * - 最后打印时间：文档最后打印时间
 * - 修订号：文档修订版本号
 * - 编辑时间：总编辑时间
 * - 版本：文档版本信息
 *
 * 统计信息功能:
 * - 页数：文档总页数
 * - 字数：文档总字数
 * - 字符数：文档总字符数
 * - 段落数：文档段落总数
 * - 行数：文档总行数
 * - 字节数：文档大小（字节）
 *
 * 自定义属性功能:
 * - 自定义属性1-6：用户定义的额外属性
 * - 支持文本类型的自定义属性
 * - 每个自定义属性都有独立的启用开关
 *
 * 启用控制特性:
 * - 每个属性都有对应的启用复选框
 * - 启用状态控制属性是否应用到文档
 * - 禁用的属性不会修改文档内容
 * - 支持选择性应用属性设置
 *
 * 数据管理:
 * - 与DocumentProperties模型深度集成
 * - 支持所有属性的实时数据绑定
 * - 自动保存用户输入的内容
 * - 支持时间类型属性的日期时间选择
 *
 * 注意事项:
 * - 支持Aspose.Words的所有文档属性
 * - 包含完整的数据验证机制
 * - 实现了用户友好的分标签页布局
 * - 支持多种数据类型的属性设置
 */

using System;
using System.Windows.Forms;
using System.Drawing;
using AW = Aspose.Words;
using AsposeWordFormatter.Models;
using System.Collections.Generic;

namespace AsposeWordFormatter
{
    public partial class DocumentPropertiesForm : Form
    {
        public DocumentProperties DocumentProperties { get; private set; }
        private TextBox titleTextBox = null!;
        private TextBox subjectTextBox = null!;
        private TextBox authorTextBox = null!;
        private TextBox managerTextBox = null!;
        private TextBox companyTextBox = null!;
        private TextBox categoryTextBox = null!;
        private TextBox keywordsTextBox = null!;
        private TextBox commentsTextBox = null!;
        private CheckBox useFilenameAsTitle = null!;
        private Panel titleTextBoxPanel = null!;
        private CheckBox titleEnableCheckBox = null!;
        private Dictionary<string, CheckBox> enableCheckBoxes = new Dictionary<string, CheckBox>();
        private TabControl tabControl = null!;
        private TabPage basicPropertiesTab = null!;
        private TabPage extendedPropertiesTab = null!;
        private TabPage statisticsTab = null!;
        private TextBox custom1TextBox = null!;
        private TextBox custom2TextBox = null!;
        private TextBox custom3TextBox = null!;
        private TextBox custom4TextBox = null!;
        private TextBox custom5TextBox = null!;
        private TextBox custom6TextBox = null!;
        private TabPage customPropertiesTab = null!;

        public DocumentPropertiesForm(DocumentProperties? documentProperties = null)
        {
            InitializeComponent();

            DocumentProperties = documentProperties ?? new DocumentProperties();
            enableCheckBoxes = new Dictionary<string, CheckBox>();

            // 创建各个标签页内容
            CreateBasicPropertiesTab();
            CreateCustomPropertiesTab();
            CreateExtendedPropertiesTab();
            CreateStatisticsTab();
        }

        private void InitializeComponent()
        {
            this.Text = "文档属性";
            this.Size = new System.Drawing.Size(550, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // 创建标签页控件
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Margin = new Padding(10)
            };

            // 添加标签页
            basicPropertiesTab = new TabPage("基本信息");
            customPropertiesTab = new TabPage("自定义信息");
            extendedPropertiesTab = new TabPage("扩展信息");
            statisticsTab = new TabPage("统计信息");

            tabControl.Controls.Add(basicPropertiesTab);
            tabControl.Controls.Add(extendedPropertiesTab);
            tabControl.Controls.Add(statisticsTab);
            tabControl.Controls.Add(customPropertiesTab);

            // 创建按钮面板
            var buttonPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 50,
                Padding = new Padding(5)
            };

            // 创建取消按钮
            var cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                Location = new Point(buttonPanel.Width - 85, 10),
                Size = new Size(75, 30)
            };

            // 创建确定按钮
            var okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                Location = new Point(buttonPanel.Width - 165, 10),
                Size = new Size(75, 30)
            };

            // 创建取消全选按钮
            var unselectAllButton = new Button
            {
                Text = "取消全选",
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                Location = new Point(buttonPanel.Width - 255, 10),
                Size = new Size(85, 30)
            };
            unselectAllButton.Click += UnselectAllButton_Click;

            // 创建全选按钮
            var selectAllButton = new Button
            {
                Text = "全选",
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                Location = new Point(buttonPanel.Width - 335, 10),
                Size = new Size(75, 30)
            };
            selectAllButton.Click += SelectAllButton_Click;

            buttonPanel.Controls.Add(cancelButton);
            buttonPanel.Controls.Add(okButton);
            buttonPanel.Controls.Add(unselectAllButton);
            buttonPanel.Controls.Add(selectAllButton);

            this.Controls.Add(tabControl);
            this.Controls.Add(buttonPanel);
            this.AcceptButton = okButton;
            this.CancelButton = cancelButton;
        }

        // 创建基本属性标签页内容
        private void CreateBasicPropertiesTab()
        {
            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 9,
                Padding = new Padding(10),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 设置列宽比例
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 24F)); // 复选框列
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));  // 标签列
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));  // 输入框列

            // 设置行高 - 所有行统一高度
            for (int i = 0; i < mainLayout.RowCount; i++)
            {
                mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            }

            // 创建标签样式
            Func<string, Label> createLabel = (text) => new Label
            {
                Text = text,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(SystemFonts.DefaultFont.FontFamily, 9F)
            };

            // 创建复选框样式
            Func<string, bool, CheckBox> createEnableCheckBox = (propertyName, isChecked) =>
            {
                var checkBox = new CheckBox
                {
                    Checked = isChecked,
                    Dock = DockStyle.Fill,
                    AutoSize = true
                };
                enableCheckBoxes[propertyName] = checkBox;
                return checkBox;
            };

            // 创建输入控件容器样式
            Func<Control, Panel> createInputPanel = (control) =>
            {
                var panel = new Panel
                {
                    Dock = DockStyle.Fill,
                    Padding = new Padding(0, 3, 0, 3)
                };

                control.Dock = DockStyle.Fill;
                panel.Controls.Add(control);
                return panel;
            };

            // 标题
            titleEnableCheckBox = createEnableCheckBox("Title", DocumentProperties.EnableTitle);
            var titleLabel = createLabel("标题:");

            // 标题文本框
            var titleInput = new TextBox
            {
                Text = DocumentProperties.Title,
                Multiline = true,
                Height = 80,
                ScrollBars = ScrollBars.Vertical,
                Enabled = DocumentProperties.EnableTitle && !DocumentProperties.UseFilenameAsTitle
            };
            titleTextBox = titleInput;
            titleTextBox.TextChanged += (s, e) => DocumentProperties.Title = titleTextBox.Text;

            var titlePanel = createInputPanel(titleInput);
            titleTextBoxPanel = titlePanel;

            // 将文件名作为标题的复选框
            useFilenameAsTitle = new CheckBox
            {
                Text = "将文件名作为标题",
                Checked = DocumentProperties.UseFilenameAsTitle,
                AutoSize = true,
                Dock = DockStyle.Fill
            };

            useFilenameAsTitle.CheckedChanged += (s, e) => {
                DocumentProperties.UseFilenameAsTitle = useFilenameAsTitle.Checked;
                titleTextBox.Enabled = !useFilenameAsTitle.Checked && titleEnableCheckBox.Checked;
            };

            titleEnableCheckBox.CheckedChanged += (s, e) => {
                DocumentProperties.EnableTitle = titleEnableCheckBox.Checked;
                titleTextBox.Enabled = titleEnableCheckBox.Checked && !useFilenameAsTitle.Checked;
            };

            // 其他基本字段定义
            var fields = new (string Name, string Label, TextBox Control, string PropertyName, bool Enabled)[]
            {
                (
                    "Subject",
                    "主题:",
                    new TextBox {
                        Text = DocumentProperties.Subject,
                        Enabled = DocumentProperties.EnableSubject
                    },
                    "Subject",
                    DocumentProperties.EnableSubject
                ),
                (
                    "Author",
                    "作者:",
                    new TextBox {
                        Text = DocumentProperties.Author,
                        Enabled = DocumentProperties.EnableAuthor
                    },
                    "Author",
                    DocumentProperties.EnableAuthor
                ),
                (
                    "Manager",
                    "管理者:",
                    new TextBox {
                        Text = DocumentProperties.Manager,
                        Enabled = DocumentProperties.EnableManager
                    },
                    "Manager",
                    DocumentProperties.EnableManager
                ),
                (
                    "Company",
                    "公司:",
                    new TextBox {
                        Text = DocumentProperties.Company,
                        Enabled = DocumentProperties.EnableCompany
                    },
                    "Company",
                    DocumentProperties.EnableCompany
                ),
                (
                    "Category",
                    "类别:",
                    new TextBox {
                        Text = DocumentProperties.Category,
                        Enabled = DocumentProperties.EnableCategory
                    },
                    "Category",
                    DocumentProperties.EnableCategory
                ),
                (
                    "Keywords",
                    "标记:",
                    new TextBox {
                        Text = DocumentProperties.Keywords,
                        Enabled = DocumentProperties.EnableKeywords
                    },
                    "Keywords",
                    DocumentProperties.EnableKeywords
                ),
                (
                    "Comments",
                    "备注:",
                    new TextBox {
                        Text = DocumentProperties.Comments,
                        Multiline = true,
                        Height = 80,
                        ScrollBars = ScrollBars.Vertical,
                        Enabled = DocumentProperties.EnableComments
                    },
                    "Comments",
                    DocumentProperties.EnableComments
                )
            };

            // 存储文本框引用以便外部使用
            subjectTextBox = fields[0].Control;
            authorTextBox = fields[1].Control;
            managerTextBox = fields[2].Control;
            companyTextBox = fields[3].Control;
            categoryTextBox = fields[4].Control;
            keywordsTextBox = fields[5].Control;
            commentsTextBox = fields[6].Control;

            // 添加标题行
            mainLayout.Controls.Add(titleEnableCheckBox, 0, 0);
            mainLayout.Controls.Add(titleLabel, 1, 0);
            mainLayout.Controls.Add(titlePanel, 2, 0);

            // 添加文件名作为标题复选框
            var filenamePanel = createInputPanel(useFilenameAsTitle);
            mainLayout.Controls.Add(new Panel(), 0, 1);
            mainLayout.Controls.Add(new Panel(), 1, 1);
            mainLayout.Controls.Add(filenamePanel, 2, 1);

            // 添加其他字段
            for (int i = 0; i < fields.Length; i++)
            {
                var field = fields[i];

                // 创建复选框
                var checkBox = createEnableCheckBox(field.PropertyName, field.Enabled);

                // 创建标签
                var label = createLabel(field.Label);

                // 创建输入控件和容器
                var inputPanel = createInputPanel(field.Control);

                // 添加事件处理 - 复选框改变时启用/禁用控件
                checkBox.CheckedChanged += (s, e) => {
                    // 设置对应的启用属性
                    var propInfo = typeof(DocumentProperties).GetProperty("Enable" + field.PropertyName);
                    if (propInfo != null)
                        propInfo.SetValue(DocumentProperties, checkBox.Checked);

                    // 启用/禁用控件
                    field.Control.Enabled = checkBox.Checked;
                };

                // 添加事件处理 - 控件值变化时更新属性
                field.Control.TextChanged += (s, e) => {
                    var propInfo = typeof(DocumentProperties).GetProperty(field.PropertyName);
                    if (propInfo != null)
                        propInfo.SetValue(DocumentProperties, field.Control.Text);
                };

                // 添加到布局中
                mainLayout.Controls.Add(checkBox, 0, i + 2); // +2 因为前两行已被占用
                mainLayout.Controls.Add(label, 1, i + 2);
                mainLayout.Controls.Add(inputPanel, 2, i + 2);
            }

            // 调整行高，为标题和备注(多行文本框)增加高度
            mainLayout.RowStyles.Clear();

            // 标题行(高度增加)
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 90F));
            // 文件名作为标题复选框行
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));

            // 其他字段行
            for (int i = 0; i < fields.Length - 1; i++) // 除了最后一个(备注)
            {
                mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            }
            // 备注行(高度增加)
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 90F));

            basicPropertiesTab.Controls.Add(mainLayout);
        }

        // 创建扩展属性标签页内容
        private void CreateExtendedPropertiesTab()
        {
            // 使用固定行高，确保精确控制
            const int ROW_HEIGHT = 30;
            const int CHECKBOX_LEFT = 20; // 增加左边距
            const int LABEL_LEFT = 45;    // 增加左边距
            const int CONTROL_LEFT = 165;  // 相应调整控件左边距
            const int CONTROL_WIDTH = 350;

            // 主容器面板
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(15) // 增加内边距
            };

            // 创建标签样式
            Func<string, int, Label> createLabel = (text, top) => new Label
            {
                Text = text,
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(SystemFonts.DefaultFont.FontFamily, 9F),
                Location = new Point(LABEL_LEFT, top),
                Size = new Size(115, ROW_HEIGHT)
            };

            // 创建复选框样式
            Func<string, bool, int, CheckBox> createCheckBox = (propertyName, isChecked, top) =>
            {
                var checkBox = new CheckBox
                {
                    Checked = isChecked,
                    AutoSize = true,
                    Location = new Point(CHECKBOX_LEFT, top + 5)
                };
                enableCheckBoxes[propertyName] = checkBox;
                return checkBox;
            };

            // 定义扩展属性信息 - 基本属性
            var items = new object[]
            {
                new {
                    PropertyName = "ContentType",
                    Label = "内容类型:",
                    CreateControl = new Func<Control>(() => new TextBox {
                        Text = DocumentProperties.ContentType,
                        Enabled = DocumentProperties.EnableContentType,
                        Location = new Point(CONTROL_LEFT, 0),
                        Size = new Size(CONTROL_WIDTH, 23),
                        Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
                    }),
                    Enabled = DocumentProperties.EnableContentType,
                    UpdateProperty = new Action<object>((value) => DocumentProperties.ContentType = (string)value),
                    GetValue = new Func<object>(() => DocumentProperties.ContentType ?? string.Empty)
                },

                new {
                    PropertyName = "ContentStatus",
                    Label = "内容状态:",
                    CreateControl = new Func<Control>(() => new TextBox {
                        Text = DocumentProperties.ContentStatus,
                        Enabled = DocumentProperties.EnableContentStatus,
                        Location = new Point(CONTROL_LEFT, 0),
                        Size = new Size(CONTROL_WIDTH, 23),
                        Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
                    }),
                    Enabled = DocumentProperties.EnableContentStatus,
                    UpdateProperty = new Action<object>((value) => DocumentProperties.ContentStatus = (string)value),
                    GetValue = new Func<object>(() => DocumentProperties.ContentStatus ?? string.Empty)
                },

                new {
                    PropertyName = "HyperlinkBase",
                    Label = "超链接基址:",
                    CreateControl = new Func<Control>(() => new TextBox {
                        Text = DocumentProperties.HyperlinkBase,
                        Enabled = DocumentProperties.EnableHyperlinkBase,
                        Location = new Point(CONTROL_LEFT, 0),
                        Size = new Size(CONTROL_WIDTH, 23),
                        Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
                    }),
                    Enabled = DocumentProperties.EnableHyperlinkBase,
                    UpdateProperty = new Action<object>((value) => DocumentProperties.HyperlinkBase = (string)value),
                    GetValue = new Func<object>(() => DocumentProperties.HyperlinkBase ?? string.Empty)
                },

                new {
                    PropertyName = "Template",
                    Label = "模板:",
                    CreateControl = new Func<Control>(() => new TextBox {
                        Text = DocumentProperties.Template,
                        Enabled = DocumentProperties.EnableTemplate,
                        Location = new Point(CONTROL_LEFT, 0),
                        Size = new Size(CONTROL_WIDTH, 23),
                        Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
                    }),
                    Enabled = DocumentProperties.EnableTemplate,
                    UpdateProperty = new Action<object>((value) => DocumentProperties.Template = (string)value),
                    GetValue = new Func<object>(() => DocumentProperties.Template ?? string.Empty)
                },

                new {
                    PropertyName = "CreatedTime",
                    Label = "创建时间:",
                    CreateControl = new Func<Control>(() => new DateTimePicker {
                        Format = DateTimePickerFormat.Custom,
                        CustomFormat = "yyyy-MM-dd HH:mm:ss",
                        Value = DocumentProperties.CreatedTime.HasValue ? DocumentProperties.CreatedTime.Value : DateTime.Now,
                        Enabled = DocumentProperties.EnableCreatedTime,
                        Location = new Point(CONTROL_LEFT, 0),
                        Size = new Size(CONTROL_WIDTH, 23),
                        ShowUpDown = true,
                        Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
                    }),
                    Enabled = DocumentProperties.EnableCreatedTime,
                    UpdateProperty = new Action<object>((value) => DocumentProperties.CreatedTime = (DateTime)value),
                    GetValue = new Func<object>(() => DocumentProperties.CreatedTime.HasValue ? DocumentProperties.CreatedTime.Value : DateTime.Now)
                },

                new {
                    PropertyName = "LastSavedTime",
                    Label = "最后保存时间:",
                    CreateControl = new Func<Control>(() => new DateTimePicker {
                        Format = DateTimePickerFormat.Custom,
                        CustomFormat = "yyyy-MM-dd HH:mm:ss",
                        Value = DocumentProperties.LastSavedTime.HasValue ? DocumentProperties.LastSavedTime.Value : DateTime.Now,
                        Enabled = DocumentProperties.EnableLastSavedTime,
                        Location = new Point(CONTROL_LEFT, 0),
                        Size = new Size(CONTROL_WIDTH, 23),
                        ShowUpDown = true,
                        Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
                    }),
                    Enabled = DocumentProperties.EnableLastSavedTime,
                    UpdateProperty = new Action<object>((value) => DocumentProperties.LastSavedTime = (DateTime)value),
                    GetValue = new Func<object>(() => DocumentProperties.LastSavedTime.HasValue ? DocumentProperties.LastSavedTime.Value : DateTime.Now)
                },

                new {
                    PropertyName = "LastSavedBy",
                    Label = "最后保存者:",
                    CreateControl = new Func<Control>(() => new TextBox {
                        Text = DocumentProperties.LastSavedBy,
                        Enabled = DocumentProperties.EnableLastSavedBy,
                        Location = new Point(CONTROL_LEFT, 0),
                        Size = new Size(CONTROL_WIDTH, 23),
                        Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
                    }),
                    Enabled = DocumentProperties.EnableLastSavedBy,
                    UpdateProperty = new Action<object>((value) => DocumentProperties.LastSavedBy = (string)value),
                    GetValue = new Func<object>(() => DocumentProperties.LastSavedBy ?? string.Empty)
                },

                new {
                    PropertyName = "LastPrinted",
                    Label = "最后打印时间:",
                    CreateControl = new Func<Control>(() => new DateTimePicker {
                        Format = DateTimePickerFormat.Custom,
                        CustomFormat = "yyyy-MM-dd HH:mm:ss",
                        Value = DocumentProperties.LastPrinted.HasValue ? DocumentProperties.LastPrinted.Value : DateTime.Now,
                        Enabled = DocumentProperties.EnableLastPrinted,
                        Location = new Point(CONTROL_LEFT, 0),
                        Size = new Size(CONTROL_WIDTH, 23),
                        ShowUpDown = true,
                        Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
                    }),
                    Enabled = DocumentProperties.EnableLastPrinted,
                    UpdateProperty = new Action<object>((value) => DocumentProperties.LastPrinted = (DateTime)value),
                    GetValue = new Func<object>(() => DocumentProperties.LastPrinted.HasValue ? DocumentProperties.LastPrinted.Value : DateTime.Now)
                },

                new {
                    PropertyName = "RevisionNumber",
                    Label = "修订号:",
                    CreateControl = new Func<Control>(() => new NumericUpDown {
                        Minimum = 0,
                        Maximum = int.MaxValue,
                        Value = DocumentProperties.RevisionNumber.HasValue ? DocumentProperties.RevisionNumber.Value : 0,
                        Enabled = DocumentProperties.EnableRevisionNumber,
                        Location = new Point(CONTROL_LEFT, 4),
                        Size = new Size(150, 23),
                        Anchor = AnchorStyles.Left | AnchorStyles.Top
                    }),
                    Enabled = DocumentProperties.EnableRevisionNumber,
                    UpdateProperty = new Action<object>((value) => DocumentProperties.RevisionNumber = Convert.ToInt32(value)),
                    GetValue = new Func<object>(() => DocumentProperties.RevisionNumber.HasValue ? DocumentProperties.RevisionNumber.Value : 0)
                }
            };

            // 动态创建并添加所有控件
            for (int i = 0; i < items.Length; i++)
            {
                dynamic item = items[i];
                int topPosition = i * ROW_HEIGHT;

                // 创建复选框
                CheckBox checkBox = createCheckBox(item.PropertyName, item.Enabled, topPosition);

                // 创建标签
                Label label = createLabel(item.Label, topPosition);

                // 创建控件
                Control control = item.CreateControl();
                // 调整控件位置到当前行
                control.Location = new Point(control.Location.X, topPosition + (control is NumericUpDown ? 4 : 3));

                // 添加事件处理 - 复选框改变时启用/禁用控件
                string propertyName = item.PropertyName; // 捕获变量以在Lambda中使用
                checkBox.CheckedChanged += (s, e) => {
                    // 设置对应的启用属性
                    var propInfo = typeof(DocumentProperties).GetProperty("Enable" + propertyName);
                    if (propInfo != null)
                        propInfo.SetValue(DocumentProperties, checkBox.Checked);

                    // 启用/禁用控件
                    control.Enabled = checkBox.Checked;
                };

                // 添加事件处理 - 根据控件类型添加相应的值变更事件
                if (control is TextBox textBox)
                {
                    var updateAction = item.UpdateProperty;
                    textBox.TextChanged += (s, e) => updateAction(textBox.Text);
                }
                else if (control is DateTimePicker datePicker)
                {
                    var updateAction = item.UpdateProperty;
                    datePicker.ValueChanged += (s, e) => updateAction(datePicker.Value);
                }
                else if (control is NumericUpDown numericUpDown)
                {
                    var updateAction = item.UpdateProperty;
                    numericUpDown.ValueChanged += (s, e) => updateAction(numericUpDown.Value);
                }

                // 添加控件到面板
                mainPanel.Controls.Add(checkBox);
                mainPanel.Controls.Add(label);
                mainPanel.Controls.Add(control);
            }

            // 将面板添加到标签页
            extendedPropertiesTab.Controls.Add(mainPanel);
        }

        // 创建统计信息标签页内容
        private void CreateStatisticsTab()
        {
            // 使用固定行高，确保精确控制
            const int ROW_HEIGHT = 26;
            const int TOTAL_ROWS = 7;
            const int LEFT_MARGIN = 20; // 增加左边距

            // 主容器面板
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(15) // 增加内边距
            };

            // 创建复选框样式
            Func<bool, int, CheckBox> createCheckBox = (isChecked, top) => new CheckBox
            {
                Checked = isChecked,
                AutoSize = true,
                Location = new Point(LEFT_MARGIN, top + 3)
            };

            // 创建标签样式
            Func<string, int, Label> createLabel = (text, top) => new Label
            {
                Text = text,
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(SystemFonts.DefaultFont.FontFamily, 9F),
                Location = new Point(LEFT_MARGIN + 24, top),
                Size = new Size(150, ROW_HEIGHT)
            };

            // 创建值标签样式
            Func<string, int, Label> createValueLabel = (text, top) => new Label
            {
                Text = text,
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(SystemFonts.DefaultFont.FontFamily, 9F, FontStyle.Bold),
                Location = new Point(LEFT_MARGIN + 180, top),
                Size = new Size(150, ROW_HEIGHT)
            };

            // 统计信息的顺序和类型
            var statsInfo = new[]
            {
                new {
                    Name = "页数",
                    GetValue = new Func<string>(() => DocumentProperties.PageCount.ToString()),
                    ShowProperty = DocumentProperties.ShowPageCount,
                    SetShowProperty = new Action<bool>((v) => DocumentProperties.ShowPageCount = v)
                },

                new {
                    Name = "字数",
                    GetValue = new Func<string>(() => DocumentProperties.WordCount.ToString()),
                    ShowProperty = DocumentProperties.ShowWordCount,
                    SetShowProperty = new Action<bool>((v) => DocumentProperties.ShowWordCount = v)
                },

                new {
                    Name = "字符数(不含空格)",
                    GetValue = new Func<string>(() => DocumentProperties.CharacterCount.ToString()),
                    ShowProperty = DocumentProperties.ShowCharacterCount,
                    SetShowProperty = new Action<bool>((v) => DocumentProperties.ShowCharacterCount = v)
                },

                new {
                    Name = "字符数(含空格)",
                    GetValue = new Func<string>(() => DocumentProperties.CharacterCountWithSpaces.ToString()),
                    ShowProperty = DocumentProperties.ShowCharacterCountWithSpaces,
                    SetShowProperty = new Action<bool>((v) => DocumentProperties.ShowCharacterCountWithSpaces = v)
                },

                new {
                    Name = "段落数",
                    GetValue = new Func<string>(() => DocumentProperties.ParagraphCount.ToString()),
                    ShowProperty = DocumentProperties.ShowParagraphCount,
                    SetShowProperty = new Action<bool>((v) => DocumentProperties.ShowParagraphCount = v)
                },

                // 注意这里行数和字节数已经紧挨着了，确保它们在UI中紧挨着显示
                new {
                    Name = "行数",
                    GetValue = new Func<string>(() => DocumentProperties.LineCount.ToString()),
                    ShowProperty = DocumentProperties.ShowLineCount,
                    SetShowProperty = new Action<bool>((v) => DocumentProperties.ShowLineCount = v)
                },

                new {
                    Name = "字节数",
                    GetValue = new Func<string>(() => DocumentProperties.ByteCount.ToString()),
                    ShowProperty = DocumentProperties.ShowByteCount,
                    SetShowProperty = new Action<bool>((v) => DocumentProperties.ShowByteCount = v)
                }
            };

            // 添加所有统计行
            for (int i = 0; i < TOTAL_ROWS; i++)
            {
                var info = statsInfo[i];
                int topPosition = i * ROW_HEIGHT;

                var checkBox = createCheckBox(info.ShowProperty, topPosition);
                var label = createLabel(info.Name, topPosition);
                var valueLabel = createValueLabel(info.GetValue(), topPosition);

                // 为每个复选框绑定事件
                int capturedIndex = i; // 捕获循环变量
                checkBox.CheckedChanged += (s, e) => {
                    statsInfo[capturedIndex].SetShowProperty(checkBox.Checked);
                };

                mainPanel.Controls.Add(checkBox);
                mainPanel.Controls.Add(label);
                mainPanel.Controls.Add(valueLabel);
            }

            // 添加说明标签
            var notePanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 35,
                Padding = new Padding(10, 10, 10, 0)
            };

            var noteLabel = new Label
            {
                Text = "注意: 统计信息仅在保存文档后更新，以上是最后一次读取文档时的数据。",
                Dock = DockStyle.Fill,
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(SystemFonts.DefaultFont.FontFamily, 8F, FontStyle.Italic),
                ForeColor = Color.Gray
            };

            notePanel.Controls.Add(noteLabel);

            // 添加至标签页
            statisticsTab.Controls.Add(mainPanel);
            statisticsTab.Controls.Add(notePanel);
        }

        // 创建自定义属性标签页内容
        private void CreateCustomPropertiesTab()
        {
            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 7,
                Padding = new Padding(15), // 增加内边距
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 设置列宽比例
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 24F)); // 复选框列
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));  // 标签列
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));  // 输入框列

            // 设置行高 - 为最后一行(Custom6多行文本框)增加高度
            for (int i = 0; i < mainLayout.RowCount - 1; i++)
            {
                mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            }
            // 最后一行高度增加以适应多行文本框
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 90F));

            // 创建标签样式
            Func<string, Label> createLabel = (text) => new Label
            {
                Text = text,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(SystemFonts.DefaultFont.FontFamily, 9F)
            };

            // 创建复选框样式
            Func<string, bool, CheckBox> createEnableCheckBox = (propertyName, isChecked) =>
            {
                var checkBox = new CheckBox
                {
                    Checked = isChecked,
                    Dock = DockStyle.Fill,
                    AutoSize = true
                };
                enableCheckBoxes[propertyName] = checkBox;
                return checkBox;
            };

            // 创建输入控件容器样式
            Func<Control, Panel> createInputPanel = (control) =>
            {
                var panel = new Panel
                {
                    Dock = DockStyle.Fill,
                    Padding = new Padding(0, 3, 0, 3)
                };

                control.Dock = DockStyle.Fill;
                panel.Controls.Add(control);
                return panel;
            };

            // 修改自定义属性定义以允许自定义标签
            var fields = new[]
            {
                (PropertyName: "Custom1", LabelTextBox: new TextBox { Text = DocumentProperties.Custom1Label, Dock = DockStyle.Fill },
                 ValueControl: new TextBox { Text = DocumentProperties.Custom1, Enabled = DocumentProperties.EnableCustom1 },
                 Enabled: DocumentProperties.EnableCustom1),

                (PropertyName: "Custom2", LabelTextBox: new TextBox { Text = DocumentProperties.Custom2Label, Dock = DockStyle.Fill },
                 ValueControl: new TextBox { Text = DocumentProperties.Custom2, Enabled = DocumentProperties.EnableCustom2 },
                 Enabled: DocumentProperties.EnableCustom2),

                (PropertyName: "Custom3", LabelTextBox: new TextBox { Text = DocumentProperties.Custom3Label, Dock = DockStyle.Fill },
                 ValueControl: new TextBox { Text = DocumentProperties.Custom3, Enabled = DocumentProperties.EnableCustom3 },
                 Enabled: DocumentProperties.EnableCustom3),

                (PropertyName: "Custom4", LabelTextBox: new TextBox { Text = DocumentProperties.Custom4Label, Dock = DockStyle.Fill },
                 ValueControl: new TextBox { Text = DocumentProperties.Custom4, Enabled = DocumentProperties.EnableCustom4 },
                 Enabled: DocumentProperties.EnableCustom4),

                (PropertyName: "Custom5", LabelTextBox: new TextBox { Text = DocumentProperties.Custom5Label, Dock = DockStyle.Fill },
                 ValueControl: new TextBox { Text = DocumentProperties.Custom5, Enabled = DocumentProperties.EnableCustom5 },
                 Enabled: DocumentProperties.EnableCustom5),

                (PropertyName: "Custom6", LabelTextBox: new TextBox { Text = DocumentProperties.Custom6Label, Dock = DockStyle.Fill },
                 ValueControl: new TextBox {
                     Text = DocumentProperties.Custom6,
                     Multiline = true,
                     Height = 80, // 增加高度从60到80
                     ScrollBars = ScrollBars.Vertical, // 添加垂直滚动条
                     Enabled = DocumentProperties.EnableCustom6
                 },
                 Enabled: DocumentProperties.EnableCustom6)
            };

            // 存储文本框引用以便外部使用
            custom1TextBox = (TextBox)fields[0].ValueControl;
            custom2TextBox = (TextBox)fields[1].ValueControl;
            custom3TextBox = (TextBox)fields[2].ValueControl;
            custom4TextBox = (TextBox)fields[3].ValueControl;
            custom5TextBox = (TextBox)fields[4].ValueControl;
            custom6TextBox = (TextBox)fields[5].ValueControl;

            // 添加所有字段
            for (int i = 0; i < fields.Length; i++)
            {
                var field = fields[i];

                // 创建复选框
                var checkBox = createEnableCheckBox(field.PropertyName, field.Enabled);

                // 使用可编辑的标签
                var labelPanel = createInputPanel(field.LabelTextBox);

                // 创建输入控件和容器
                var inputPanel = createInputPanel(field.ValueControl);

                // 添加事件处理 - 复选框改变时启用/禁用控件
                TextBox valueTextBox = (TextBox)field.ValueControl;
                TextBox labelTextBox = field.LabelTextBox;

                checkBox.CheckedChanged += (s, e) => {
                    // 设置对应的启用属性
                    var propInfo = typeof(DocumentProperties).GetProperty("Enable" + field.PropertyName);
                    if (propInfo != null)
                        propInfo.SetValue(DocumentProperties, checkBox.Checked);

                    // 启用/禁用控件
                    valueTextBox.Enabled = checkBox.Checked;
                };

                // 添加事件处理 - 控件值变化时更新属性
                valueTextBox.TextChanged += (s, e) => {
                    var propInfo = typeof(DocumentProperties).GetProperty(field.PropertyName);
                    if (propInfo != null)
                        propInfo.SetValue(DocumentProperties, valueTextBox.Text);
                };

                // 添加事件处理 - 标签文本框值变化时更新属性
                labelTextBox.TextChanged += (s, e) => {
                    var propInfo = typeof(DocumentProperties).GetProperty(field.PropertyName + "Label");
                    if (propInfo != null)
                        propInfo.SetValue(DocumentProperties, labelTextBox.Text);
                };

                // 添加到布局中
                mainLayout.Controls.Add(checkBox, 0, i);
                mainLayout.Controls.Add(labelPanel, 1, i);
                mainLayout.Controls.Add(inputPanel, 2, i);
            }

            // 说明面板
            var notePanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 30,
                Padding = new Padding(15, 5, 5, 5) // 增加左边距
            };

            var noteLabel = new Label
            {
                Text = "自定义属性可用于存储您的文档特定信息，左侧标签可自行修改",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font(SystemFonts.DefaultFont.FontFamily, 8F, FontStyle.Italic)
            };

            notePanel.Controls.Add(noteLabel);

            // 将说明面板添加到标签页
            customPropertiesTab.Controls.Add(notePanel);
            customPropertiesTab.Controls.Add(mainLayout);
        }

        // 应用属性到文档
        public void ApplyTo(AW.Document doc)
        {
            if (doc == null)
                throw new ArgumentNullException(nameof(doc));

            DocumentProperties.ApplyTo(doc);
        }

        // 全选按钮点击事件处理
        private void SelectAllButton_Click(object sender, EventArgs e)
        {
            SelectAllInCurrentTab();
        }

        // 取消全选按钮点击事件处理
        private void UnselectAllButton_Click(object sender, EventArgs e)
        {
            UnselectAllInCurrentTab();
        }

        // 根据当前标签页选择所有复选框
        private void SelectAllInCurrentTab()
        {
            var selectedTab = tabControl.SelectedTab;

            if (selectedTab == basicPropertiesTab)
            {
                // 基本信息标签页 - 选择所有基本属性
                SetBasicPropertiesEnabled(true);
            }
            else if (selectedTab == extendedPropertiesTab)
            {
                // 扩展信息标签页 - 选择所有扩展属性
                SetExtendedPropertiesEnabled(true);
            }
            else if (selectedTab == statisticsTab)
            {
                // 统计信息标签页 - 选择所有统计信息显示
                SetStatisticsEnabled(true);
            }
            else if (selectedTab == customPropertiesTab)
            {
                // 自定义信息标签页 - 选择所有自定义属性
                SetCustomPropertiesEnabled(true);
            }
        }

        // 根据当前标签页取消选择所有复选框
        private void UnselectAllInCurrentTab()
        {
            var selectedTab = tabControl.SelectedTab;

            if (selectedTab == basicPropertiesTab)
            {
                // 基本信息标签页 - 取消选择所有基本属性
                SetBasicPropertiesEnabled(false);
            }
            else if (selectedTab == extendedPropertiesTab)
            {
                // 扩展信息标签页 - 取消选择所有扩展属性
                SetExtendedPropertiesEnabled(false);
            }
            else if (selectedTab == statisticsTab)
            {
                // 统计信息标签页 - 取消选择所有统计信息显示
                SetStatisticsEnabled(false);
            }
            else if (selectedTab == customPropertiesTab)
            {
                // 自定义信息标签页 - 取消选择所有自定义属性
                SetCustomPropertiesEnabled(false);
            }
        }

        // 设置基本属性的启用状态
        private void SetBasicPropertiesEnabled(bool enabled)
        {
            // 设置标题复选框
            if (titleEnableCheckBox != null)
            {
                titleEnableCheckBox.Checked = enabled;
            }

            // 设置其他基本属性复选框
            var basicPropertyNames = new[] { "Subject", "Author", "Manager", "Company", "Category", "Keywords", "Comments" };
            foreach (var propertyName in basicPropertyNames)
            {
                if (enableCheckBoxes.ContainsKey(propertyName))
                {
                    enableCheckBoxes[propertyName].Checked = enabled;
                }
            }
        }

        // 设置扩展属性的启用状态
        private void SetExtendedPropertiesEnabled(bool enabled)
        {
            var extendedPropertyNames = new[] {
                "ContentType", "ContentStatus", "HyperlinkBase", "Template",
                "CreatedTime", "LastSavedTime", "LastSavedBy", "LastPrinted", "RevisionNumber"
            };

            foreach (var propertyName in extendedPropertyNames)
            {
                if (enableCheckBoxes.ContainsKey(propertyName))
                {
                    enableCheckBoxes[propertyName].Checked = enabled;
                }
            }
        }

        // 设置统计信息的显示状态
        private void SetStatisticsEnabled(bool enabled)
        {
            // 直接设置DocumentProperties中的显示属性
            DocumentProperties.ShowPageCount = enabled;
            DocumentProperties.ShowWordCount = enabled;
            DocumentProperties.ShowCharacterCount = enabled;
            DocumentProperties.ShowCharacterCountWithSpaces = enabled;
            DocumentProperties.ShowLineCount = enabled;
            DocumentProperties.ShowParagraphCount = enabled;
            DocumentProperties.ShowByteCount = enabled;

            // 查找并更新统计信息标签页中的复选框
            UpdateStatisticsCheckBoxes(enabled);
        }

        // 更新统计信息标签页中的复选框状态
        private void UpdateStatisticsCheckBoxes(bool enabled)
        {
            if (statisticsTab?.Controls.Count > 0)
            {
                var mainPanel = statisticsTab.Controls[0] as Panel;
                if (mainPanel != null)
                {
                    foreach (Control control in mainPanel.Controls)
                    {
                        if (control is CheckBox checkBox)
                        {
                            checkBox.Checked = enabled;
                        }
                    }
                }
            }
        }

        // 设置自定义属性的启用状态
        private void SetCustomPropertiesEnabled(bool enabled)
        {
            var customPropertyNames = new[] { "Custom1", "Custom2", "Custom3", "Custom4", "Custom5", "Custom6" };

            foreach (var propertyName in customPropertyNames)
            {
                if (enableCheckBoxes.ContainsKey(propertyName))
                {
                    enableCheckBoxes[propertyName].Checked = enabled;
                }
            }
        }
    }
}