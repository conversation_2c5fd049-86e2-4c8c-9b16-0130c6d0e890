当前项目以前是用Aspose.Words批量处理word文件的软件，我现在要将其改造成使用Aspose.Slides批量处理ppt软件。
Aspose.Words与Aspose.Slides是不是用很多共同的地方，有很多不同的地方。
帮我规划下要按怎样的流程进行改造，哪些功能删掉（因为Aspose.Slides不支持），哪些功能需要添加（因为Aspose.Slides支持）
项目根目录我已经放入Aspose.Slides.dll和说明使用说明文档Aspose.Slides.xml

PPT批量处理软件改造规划
基于对当前项目的深入分析，我为您制定了以下详细的改造方案：
📋 第一阶段：核心架构调整
1.1 项目重命名和基础配置
项目名称：AsposeWordFormatter → AsposeSlidesFormatter
程序集名称：WordFormatter → SlidesFormatter
命名空间：AsposeWordFormatter → AsposeSlidesFormatter
主窗体标题：Word文档格式化工具 → PPT演示文稿批量处理工具
1.2 依赖库替换
移除：Aspose.Words.dll 和 Aspose.Words.xml
添加：使用已有的 Aspose.Slides.dll 和 Aspose.Slides.xml
更新项目文件：修改 .csproj 中的引用
1.3 核心引擎重构
重命名：WordFormatter.cs → SlidesFormatter.cs
命名空间替换：所有 using Aspose.Words → using Aspose.Presentation
API适配：将Word文档操作API替换为Slides演示文稿API
📋 第二阶段：功能模块分析与调整
2.1 保留的功能模块 ✅
文件处理与管理
✅ 路径管理（源目录、输出目录）
✅ 子目录处理和目录结构保持
✅ 冲突处理策略（覆盖、跳过、重命名）
✅ 多线程处理和批处理
✅ 重试机制和内存优化
内容替换与修改
✅ 文本替换功能（在幻灯片文本框中）
✅ 正则表达式替换
✅ 批量规则管理
✅ 替换范围控制（标题、内容、备注等）
文档属性管理
✅ 基本文档属性（标题、作者、主题等）
✅ 自定义属性管理
✅ 属性控制和验证
文件名处理
✅ 文件名替换规则
✅ 模式匹配和批量规则
✅ 冲突处理和重命名策略
定时处理系统
✅ 定时模式（一次性、定期、倒计时）
✅ 执行限制和任务监控
✅ 定时任务历史记录
2.2 需要删除的功能模块 ❌
Word特有功能
❌ 页面设置（页边距、纸张大小等）- PPT没有页面概念
❌ 页眉页脚管理 - PPT没有页眉页脚
❌ 段落格式化 - PPT的文本格式化方式不同
❌ 段落匹配规则 - PPT使用文本框而非段落
❌ 制表位设置 - PPT不使用制表位
❌ Word转PDF功能 - 将改为PPT转PDF
删除的配置文件
❌ PageSetup.json
❌ HeaderFooter.json
❌ GlobalParagraphFormat.json
❌ ParagraphMatchRules.json
删除的窗体文件
❌ PageSetupForm.cs
❌ HeaderFooterForm.cs
❌ GlobalParagraphFormatForm.cs
❌ ParagraphMatchForm.cs
❌ ParagraphPresetRuleForm.cs
❌ TabStopForm.cs
删除的模型类
❌ PageSetupFixed.cs
❌ HeaderFooterSettings.cs
❌ ParagraphFormat.cs
❌ ParagraphMatchRule.cs
❌ TabStopSettings.cs
2.3 需要添加的PPT特有功能 ➕
幻灯片管理
➕ 幻灯片添加、删除、复制
➕ 幻灯片顺序调整
➕ 批量幻灯片操作
➕ 空白幻灯片清理
母版和布局管理
➕ 幻灯片母版设置
➕ 布局模板应用
➕ 母版背景设置
➕ 统一样式应用
文本框和形状处理
➕ 文本框格式化
➕ 形状样式设置
➕ 文本框位置调整
➕ 形状删除和替换
图片和媒体处理
➕ 图片压缩和优化
➕ 图片格式转换
➕ 媒体文件处理
➕ 图片水印添加
动画和切换效果
➕ 动画效果管理
➕ 幻灯片切换效果
➕ 动画时间设置
➕ 批量动画应用
演示文稿设置
➕ 演示文稿尺寸设置（4:3、16:9等）
➕ 演示文稿主题应用
➕ 字体替换和嵌入
➕ 演示文稿保护设置
📋 第三阶段：文档格式支持调整
3.1 更新支持的文件格式
{
  // PPT演示文稿格式（默认启用）
  ".ppt": true,     // Microsoft PowerPoint 97-2003 演示文稿
  ".pptx": true,    // Microsoft PowerPoint 2007+ 演示文稿
  ".pptm": true,    // Microsoft PowerPoint 启用宏的演示文稿
  ".pot": true,     // Microsoft PowerPoint 97-2003 模板

📋 第四阶段：具体实施步骤
4.1 第一步：基础架构调整
重命名项目和命名空间
更新项目文件引用
替换Aspose库引用
更新主窗体标题和图标
4.2 第二步：核心引擎改造
重构 WordFormatter.cs → SlidesFormatter.cs
替换所有Aspose.Words API为Aspose.Slides API
更新文件处理逻辑
适配PPT文档结构
4.3 第三步：删除不适用功能
删除Word特有的窗体文件
删除相关的模型类
删除对应的配置文件
更新主窗体界面，移除相关按钮
4.4 第四步：添加PPT特有功能
创建新的PPT功能窗体
添加对应的模型类
创建新的配置文件
在主窗体中添加新功能按钮
4.5 第五步：界面调整
更新主窗体布局
调整功能按钮排列
更新帮助文档和说明
测试所有功能模块
📋 第五阶段：新增功能详细设计
5.1 幻灯片管理模块
public class SlideManagementSettings
{
    public bool RemoveEmptySlides { get; set; } = true;
    public bool RemoveDuplicateSlides { get; set; } = false;
    public bool ReorderSlides { get; set; } = false;
    public List<int> SlideOrder { get; set; } = new List<int>();

5.2 演示文稿设置模块
public class PresentationSettings
{
    public SlideSize SlideSize { get; set; } = SlideSize.OnScreen16x9;
    public string ThemeName { get; set; } = "";
    public bool EmbedFonts { get; set; } = true;
    public bool CompressImages { get; set; } = true;

5.3 文本和形状处理模块
public class TextShapeSettings
{
    public bool StandardizeTextBoxes { get; set; } = true;
    public string DefaultFontName { get; set; } = "微软雅黑";
    public int DefaultFontSize { get; set; } = 18;
    public bool RemoveAnimations { get; set; } = false;

📋 第六阶段：测试和优化
6.1 功能测试
测试所有保留功能的正常运行
测试新增PPT功能的完整性
测试多线程处理的稳定性
测试大批量文件处理的性能
6.2 兼容性测试
测试不同版本PPT文件的支持
测试各种PPT模板的处理
测试复杂演示文稿的处理能力
6.3 性能优化
优化内存使用
提升处理速度
改进错误处理机制
这个改造方案充分考虑了Aspose.Words和Aspose.Slides的差异，保留了通用的文件处理功能，删除了Word特有的功能，并添加了PPT演示文稿特有的功能。整个改造过程将分阶段进行，确保每个阶段都能正常运行和测试。
您希望我开始实施哪个阶段的改造工作？我建议从第一阶段的基础架构调整开始。