/*
 * ========================================
 * 文件名: FileNameReplaceRuleForm.cs
 * 功能描述: 文件名替换规则编辑窗体
 * ========================================
 *
 * 主要功能:
 * 1. 单个文件名替换规则的详细编辑
 * 2. 原值和新值的输入和编辑
 * 3. 正则表达式选项配置
 * 4. 大小写敏感性设置
 * 5. 文件扩展名包含选项
 * 6. 规则参数的实时验证
 *
 * 界面结构:
 * - 原值输入框：输入要替换的原始文本或正则表达式
 * - 新值输入框：输入替换后的目标文本
 * - 选项复选框：正则表达式、区分大小写、包含扩展名
 * - 操作按钮：确定、取消
 *
 * 核心功能:
 * - 支持普通文本和正则表达式两种模式
 * - 灵活的大小写处理选项
 * - 文件扩展名的独立处理控制
 * - 实时数据绑定和更新
 * - 简洁直观的用户界面
 *
 * 正则表达式支持:
 * - 标准正则表达式语法
 * - 支持捕获组和反向引用
 * - 复杂模式匹配功能
 * - 与普通文本模式的无缝切换
 *
 * 文件名处理选项:
 * - 区分大小写：控制匹配时是否考虑字母大小写
 * - 包含扩展名：控制是否对文件扩展名进行替换
 * - 支持文件名和扩展名的分别处理
 *
 * 数据绑定:
 * - OldValue: 要替换的原始文本或正则表达式
 * - NewValue: 替换后的新文本
 * - IsRegex: 是否使用正则表达式
 * - IsCaseSensitive: 是否区分大小写
 * - IncludeExtension: 是否包含文件扩展名
 *
 * 界面特性:
 * - 固定对话框样式
 * - 表格布局管理器
 * - 自动调整控件大小
 * - 标准的确定取消按钮
 * - 实时数据更新机制
 *
 * 注意事项:
 * - 正则表达式语法的正确性
 * - 文件名的合法性检查
 * - 扩展名处理的逻辑
 * - 替换结果的预期效果
 */

using System;
using System.Windows.Forms;
using System.Drawing;
using AsposeWordFormatter.Models;

namespace AsposeWordFormatter
{
    public partial class FileNameReplaceRuleForm : Form
    {
        private readonly FileNameReplaceRule rule;

        public FileNameReplaceRuleForm(FileNameReplaceRule rule)
        {
            this.rule = rule;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "文件名替换规则";
            this.Size = new Size(400, 230);  // 增加高度以容纳新的控件
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 6,  // 增加行数以容纳新的控件
                Padding = new Padding(10)
            };

            // 添加列样式
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100F));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

            // 添加行样式
            for (int i = 0; i < 6; i++)  // 更新循环以匹配新的行数
            {
                layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            }

            // 添加原值控件
            layout.Controls.Add(new Label { Text = "原值:" }, 0, 0);
            var oldValueTextBox = new TextBox
            {
                Text = rule.OldValue
            };
            oldValueTextBox.TextChanged += (s, e) => rule.OldValue = oldValueTextBox.Text;
            layout.Controls.Add(oldValueTextBox, 1, 0);

            // 添加新值控件
            layout.Controls.Add(new Label { Text = "新值:" }, 0, 1);
            var newValueTextBox = new TextBox
            {
                Text = rule.NewValue
            };
            newValueTextBox.TextChanged += (s, e) => rule.NewValue = newValueTextBox.Text;
            layout.Controls.Add(newValueTextBox, 1, 1);

            // 添加使用正则表达式控件
            var useRegexCheck = new CheckBox
            {
                Text = "使用正则表达式",
                Checked = rule.IsRegex
            };
            useRegexCheck.CheckedChanged += (s, e) => rule.IsRegex = useRegexCheck.Checked;
            layout.Controls.Add(useRegexCheck, 0, 2);
            layout.SetColumnSpan(useRegexCheck, 2);

            // 添加区分大小写控件
            var caseSensitiveCheck = new CheckBox
            {
                Text = "区分大小写",
                Checked = rule.IsCaseSensitive
            };
            caseSensitiveCheck.CheckedChanged += (s, e) => rule.IsCaseSensitive = caseSensitiveCheck.Checked;
            layout.Controls.Add(caseSensitiveCheck, 0, 3);
            layout.SetColumnSpan(caseSensitiveCheck, 2);

            // 添加包含扩展名控件
            var includeExtensionCheck = new CheckBox
            {
                Text = "包含扩展名",
                Checked = rule.IncludeExtension
            };
            includeExtensionCheck.CheckedChanged += (s, e) => rule.IncludeExtension = includeExtensionCheck.Checked;
            layout.Controls.Add(includeExtensionCheck, 0, 4);
            layout.SetColumnSpan(includeExtensionCheck, 2);

            // 添加按钮面板
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft
            };

            var okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK
            };

            var cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel
            };

            buttonPanel.Controls.Add(okButton);
            buttonPanel.Controls.Add(cancelButton);

            layout.Controls.Add(buttonPanel, 0, 5);  // 更新按钮面板的行索引
            layout.SetColumnSpan(buttonPanel, 2);

            this.Controls.Add(layout);
        }
    }
}