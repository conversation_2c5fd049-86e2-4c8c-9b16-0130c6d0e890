/*
 * ========================================
 * 文件名: DeleteSettings.cs
 * 功能描述: 文档内容删除设置数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义文档内容删除的完整配置选项
 * 2. 支持多种类型内容的删除设置
 * 3. 提供图片、文本、格式等的清理配置
 * 4. 包含文档结构和元素的删除选项
 * 5. 支持高级内容清理和保护移除
 *
 * 删除功能分类:
 *
 * 图片删除设置:
 * - DeleteSpecificImages: 删除指定路径的图片
 * - DeleteSizedImages: 删除指定尺寸范围的图片
 * - DeleteAllImages: 删除所有图片
 * - DeleteTrailingImages: 删除尾部图片
 * - DeleteBackgroundImages: 删除背景图片
 * - MinImageWidth/Height: 最小图片尺寸限制
 * - MaxImageWidth/Height: 最大图片尺寸限制
 * - SpecificImagePaths: 指定删除的图片路径列表
 *
 * 联系信息删除设置:
 * - DeleteMobileNumbers: 删除手机号码
 * - DeletePhoneNumbers: 删除电话号码
 * - DeleteEmails: 删除邮箱地址
 * - DeleteUrls: 删除网址链接
 * - DeleteHyperlinks: 删除超链接
 *
 * 文本内容删除设置:
 * - DeleteParagraphsWithText: 删除包含指定文本的段落
 * - DeleteTextBoxesWithText: 删除包含指定文本的文本框
 * - DeleteTablesWithText: 删除包含指定文本的表格
 * - SpecificTextList: 指定删除的文本列表
 * - DeleteParagraphsWithKeywords: 删除包含关键词的段落
 * - ParagraphKeywords: 段落关键词列表
 *
 * 空白内容删除设置:
 * - DeleteEmptyPages: 删除空白页面
 * - DeleteEmptyParagraphs: 删除空段落
 * - DeleteEmptyLines: 删除空行
 * - DeleteParagraphLeadingSpaces: 删除段落开头空格
 * - DeleteParagraphTrailingSpaces: 删除段落结尾空格
 * - DeleteParagraphLeadingInvisibleChars: 删除段落开头不可见字符
 * - DeleteParagraphTrailingInvisibleChars: 删除段落结尾不可见字符
 *
 * 格式删除设置:
 * - DeleteFormats: 删除所有格式
 * - DeleteFontFormats: 删除字体格式
 * - DeleteParagraphFormats: 删除段落格式
 * - DeleteTableFormats: 删除表格格式
 * - DeleteListFormats: 删除列表格式
 * - DeleteHeaderFooterFormats: 删除页眉页脚格式
 * - PreserveBold/Italic/Underline: 保留特定格式
 * - PreserveHyperlinks: 保留超链接格式
 * - DefaultFontName/Size: 默认字体设置
 *
 * 换行符和段落标记设置:
 * - DeleteLineBreaks: 删除换行符
 * - DeleteParagraphMarks: 删除段落标记
 * - DeletePageBreaks: 删除分页符
 * - DeleteSectionBreaks: 删除分节符
 * - DeleteComments: 删除批注
 * - DeleteFootnotes: 删除脚注
 * - ReplaceLineBreaksWithParagraphMarks: 换行符转段落标记
 * - ReplaceParagraphMarksWithLineBreaks: 段落标记转换行符
 * - MergeMultipleLineBreaks: 合并多个换行符
 * - MergeMultipleParagraphMarks: 合并多个段落标记
 *
 * 文档保护移除设置:
 * - ForceRemoveEditingPassword: 强制移除编辑密码
 * - ForceRemoveContentProtection: 强制移除内容保护
 * - ForceAcceptAllRevisions: 强制接受所有修订
 * - ForceRemoveDigitalSignatures: 强制移除数字签名
 *
 * 文档元素删除设置:
 * - DeleteBookmarks: 删除书签
 * - DeleteFields: 删除域
 * - DeleteTableOfContents: 删除目录
 * - DeleteIndex: 删除索引
 * - DeleteCrossReferences: 删除交叉引用
 * - DeleteFormFields: 删除表单域
 * - DeleteSmartArt: 删除SmartArt图形
 * - DeleteCharts: 删除图表
 * - DeleteOleObjects: 删除OLE对象
 * - DeleteActiveXControls: 删除ActiveX控件
 *
 * 高级格式删除设置:
 * - DeleteCharacterSpacing: 删除字符间距
 * - DeleteCharacterScaling: 删除字符缩放
 * - DeleteCharacterPosition: 删除字符位置
 * - DeleteTextEffects: 删除文本效果
 * - DeleteParagraphBorders: 删除段落边框
 * - DeleteParagraphShading: 删除段落底纹
 * - DeleteCellBorders: 删除单元格边框
 * - DeleteCellShading: 删除单元格底纹
 * - DeleteCellMerging: 删除单元格合并
 * - DeleteCellSplitting: 删除单元格拆分
 *
 * 文档结构删除设置:
 * - DeleteColumns: 删除分栏
 * - DeleteTextBoxes: 删除文本框
 * - DeleteShapes: 删除形状
 * - DeleteWordArt: 删除艺术字
 * - DeleteMargins: 删除页边距设置
 * - DeletePageBorders: 删除页面边框
 * - DeletePageBackground: 删除页面背景
 * - DeleteHeadersFooters: 删除页眉页脚
 *
 * 文档属性删除设置:
 * - DeleteDocumentProperties: 删除文档属性
 * - DeleteCustomProperties: 删除自定义属性
 * - DeleteDocumentVariables: 删除文档变量
 * - DeleteDocumentStatistics: 删除文档统计信息
 *
 * 其他内容删除设置:
 * - DeleteWatermarks: 删除水印
 * - DeleteHiddenText: 删除隐藏文本
 * - DeleteRevisionMarks: 删除修订标记
 * - DeleteCompareResults: 删除比较结果
 * - DeleteDocumentVersions: 删除文档版本
 * - DeleteDocumentTheme: 删除文档主题
 * - DeleteDocumentStyles: 删除文档样式
 *
 * 应用场景:
 * - 文档内容的批量清理
 * - 敏感信息的自动删除
 * - 文档格式的标准化处理
 * - 文档大小的优化压缩
 * - 文档保护的强制移除
 *
 * 注意事项:
 * - 支持Aspose.Words的所有删除操作
 * - 包含完整的内容清理选项
 * - 实现了灵活的删除规则配置
 * - 提供了安全的保护移除功能
 */

using System;
using System.Collections.Generic;
using System.Drawing;

namespace AsposeSlidesFormatter.Models
{
    /// <summary>
    /// 文档删除功能的设置类
    /// </summary>
    public class DeleteSettings
    {
        // 删除图片相关设置
        public bool DeleteSpecificImages { get; set; } = false;
        public bool DeleteSizedImages { get; set; } = false;
        public bool DeleteAllImages { get; set; } = false;
        public bool DeleteTrailingImages { get; set; } = false;
        public bool DeleteBackgroundImages { get; set; } = false;
        public int MinImageWidth { get; set; } = 0;
        public int MinImageHeight { get; set; } = 0;
        public int MaxImageWidth { get; set; } = 0;
        public int MaxImageHeight { get; set; } = 0;
        public List<string> SpecificImagePaths { get; set; } = new List<string>();

        // 删除手机邮箱网址相关设置
        public bool DeleteMobileNumbers { get; set; } = false;
        public bool DeletePhoneNumbers { get; set; } = false;
        public bool DeleteEmails { get; set; } = false;
        public bool DeleteUrls { get; set; } = false;
        public bool DeleteHyperlinks { get; set; } = false;

        // 删除包含指定字符区域设置
        public bool DeleteParagraphsWithText { get; set; } = false;
        public bool DeleteTextBoxesWithText { get; set; } = false;
        public bool DeleteTablesWithText { get; set; } = false;
        public List<string> SpecificTextList { get; set; } = new List<string>();

        // 删除空白内容设置
        public bool DeleteEmptyPages { get; set; } = false;
        public bool DeleteEmptyParagraphs { get; set; } = false;
        public bool DeleteEmptyLines { get; set; } = false;

        // 新增段落空格和不可见字符设置
        public bool DeleteParagraphLeadingSpaces { get; set; } = false;
        public bool DeleteParagraphTrailingSpaces { get; set; } = false;
        public bool DeleteParagraphLeadingInvisibleChars { get; set; } = false;
        public bool DeleteParagraphTrailingInvisibleChars { get; set; } = false;

        // 删除水印设置
        public bool DeleteWatermarks { get; set; } = false;
        public bool DeleteHeaderWatermarks { get; set; } = false;
        public bool DeleteFooterWatermarks { get; set; } = false;

        // 删除格式设置
        public bool DeleteFormats { get; set; } = false;
        public bool DeleteFontFormats { get; set; } = false;
        public bool DeleteParagraphFormats { get; set; } = false;
        public bool DeleteTableFormats { get; set; } = false;
        public bool DeleteListFormats { get; set; } = false;
        public bool DeleteHeaderFooterFormats { get; set; } = false;
        public bool PreserveBold { get; set; } = false;
        public bool PreserveItalic { get; set; } = false;
        public bool PreserveUnderline { get; set; } = false;
        public bool PreserveHyperlinks { get; set; } = false;
        public string DefaultFontName { get; set; } = "宋体";
        public double DefaultFontSize { get; set; } = 12.0;

        // 换行符和段落标记相关设置
        public bool DeleteLineBreaks { get; set; } = false;
        public bool DeleteParagraphMarks { get; set; } = false;
        public bool DeletePageBreaks { get; set; } = false;
        public bool DeleteSectionBreaks { get; set; } = false;
        public bool DeleteComments { get; set; } = false;
        public bool DeleteFootnotes { get; set; } = false;
        public bool ReplaceLineBreaksWithParagraphMarks { get; set; } = false;
        public bool MergeMultipleLineBreaks { get; set; } = false;
        public bool MergeMultipleParagraphMarks { get; set; } = false;

        // 文档保护相关设置
        public bool ForceRemoveEditingPassword { get; set; } = false;
        public bool ForceRemoveContentProtection { get; set; } = false;
        public bool ForceAcceptAllRevisions { get; set; } = false;
        public bool ForceRemoveDigitalSignatures { get; set; } = false;

        // 文档元素删除设置
        public bool DeleteBookmarks { get; set; } = false;
        public bool DeleteFields { get; set; } = false;
        public bool DeleteTableOfContents { get; set; } = false;
        public bool DeleteIndex { get; set; } = false;
        public bool DeleteCrossReferences { get; set; } = false;
        public bool DeleteFormFields { get; set; } = false;
        public bool DeleteSmartArt { get; set; } = false;
        public bool DeleteCharts { get; set; } = false;
        public bool DeleteOleObjects { get; set; } = false;
        public bool DeleteActiveXControls { get; set; } = false;

        // 高级格式删除设置
        public bool DeleteCharacterSpacing { get; set; } = false;
        public bool DeleteCharacterScaling { get; set; } = false;
        public bool DeleteCharacterPosition { get; set; } = false;
        public bool DeleteTextEffects { get; set; } = false;
        public bool DeleteParagraphBorders { get; set; } = false;
        public bool DeleteParagraphShading { get; set; } = false;
        public bool DeleteCellBorders { get; set; } = false;
        public bool DeleteCellShading { get; set; } = false;
        public bool DeleteCellMerging { get; set; } = false;
        public bool DeleteCellSplitting { get; set; } = false;

        // 文档结构删除设置
        public bool DeleteColumns { get; set; } = false;
        public bool DeleteTextBoxes { get; set; } = false;
        public bool DeleteShapes { get; set; } = false;
        public bool DeleteWordArt { get; set; } = false;
        public bool DeleteMargins { get; set; } = false;
        public bool DeletePageBorders { get; set; } = false;
        public bool DeletePageBackground { get; set; } = false;

        // 文档属性删除设置
        public bool DeleteDocumentProperties { get; set; } = false;
        public bool DeleteCustomProperties { get; set; } = false;
        public bool DeleteDocumentVariables { get; set; } = false;
        public bool DeleteDocumentStatistics { get; set; } = false;

        // 其他内容删除设置
        public bool DeleteHiddenText { get; set; } = false;
        public bool DeleteRevisionMarks { get; set; } = false;
        public bool DeleteCompareResults { get; set; } = false;
        public bool DeleteDocumentVersions { get; set; } = false;
        public bool DeleteDocumentTheme { get; set; } = false;
        public bool DeleteDocumentStyles { get; set; } = false;

        // 删除文档相关设置
        public bool EnableDocumentDeletion { get; set; } = false; // 启用删除文档功能

        // 1. 文件名长度判断
        public bool CheckFileNameLength { get; set; } = false;
        public int MinFileNameLength { get; set; } = 1;
        public int MaxFileNameLength { get; set; } = 255;

        // 2. 文档大小判断
        public bool CheckDocumentSize { get; set; } = false;
        public int MinDocumentSize { get; set; } = 0;
        public int MaxDocumentSize { get; set; } = 1024;
        public string MinDocumentSizeUnit { get; set; } = "KB"; // B, KB, MB
        public string MaxDocumentSizeUnit { get; set; } = "KB"; // B, KB, MB

        // 3. 文档内容字符总数判断
        public bool CheckContentCharacterCount { get; set; } = false;
        public int MinContentCharacterCount { get; set; } = 0;
        public int MaxContentCharacterCount { get; set; } = 10000;

        // 4. 文档页数判断
        public bool CheckDocumentPageCount { get; set; } = false;
        public int MinDocumentPageCount { get; set; } = 2;
        public int MaxDocumentPageCount { get; set; } = 200;

        // 5. 文件名非法词判断
        public bool CheckFileNameIllegalWords { get; set; } = false;
        public List<string> FileNameIllegalWords { get; set; } = new List<string>();

        // 6. 文档内容非法词判断
        public bool CheckContentIllegalWords { get; set; } = false;
        public List<string> ContentIllegalWords { get; set; } = new List<string>();
    }
}