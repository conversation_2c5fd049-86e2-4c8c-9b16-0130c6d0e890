/*
 * ========================================
 * 文件名: WordFormatter.cs
 * 功能描述: 核心文档处理引擎
 * ========================================
 *
 * 主要功能:
 * 1. Word文档的批量处理和格式化
 * 2. 多种文档操作功能的统一调度
 * 3. 文件处理进度监控和状态报告
 * 4. 异步处理和取消操作支持
 * 5. 错误处理和重试机制
 *
 * 核心处理模块:
 * - 页面设置: 纸张大小、边距、方向等
 * - 内容删除: 空白内容、特定元素、格式清理
 * - 内容替换: 文本替换、正则表达式匹配
 * - 段落格式: 全局段落格式化和匹配规则
 * - 页眉页脚: 页眉页脚内容和格式设置
 * - 文档属性: 元数据和属性管理
 * - 文件名处理: 文件重命名规则
 * - PDF转换: Word文档转PDF功能
 *
 * 技术特性:
 * - 支持多线程并发处理
 * - 实现了完整的进度报告机制
 * - 包含详细的错误处理和日志记录
 * - 支持处理取消和暂停功能
 * - 优化了内存使用和性能
 *
 * 依赖关系:
 * - Aspose.Words: 核心文档处理库
 * - Logger: 日志记录系统
 * - Models: 各种配置数据模型
 * - ConvertToPdf: PDF转换模块
 *
 * 事件系统:
 * - ProgressChanged: 处理进度更新
 * - StatusChanged: 状态变化通知
 * - ErrorOccurred: 错误事件报告
 *
 * 注意事项:
 * - 所有文档操作都是线程安全的
 * - 支持大批量文件的高效处理
 * - 包含完整的异常恢复机制
 * - 实现了资源的自动释放和清理
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Aspose.Words;
using Aspose.Words.Replacing;
using Aspose.Words.Tables;
using Aspose.Words.Fields;
using Aspose.Words.Drawing;
using Aspose.Words.Layout;
using Aspose.Words.Themes;
using Aspose.Words.Settings;
using Aspose.Words.Properties;
using Aspose.Words.DigitalSignatures;
using AsposeWordFormatter.Models;
using AW = Aspose.Words;
using System.Drawing;
using System.Collections.Concurrent;
using System.Text;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace AsposeWordFormatter
{
    public class ProgressEventArgs : EventArgs
    {
        public double Progress { get; }
        public int TotalFiles { get; }
        public int CompletedFiles { get; }
        public int FailedFiles { get; }
        public int RetriedFiles { get; }

        public ProgressEventArgs(double progress, int totalFiles, int completedFiles, int failedFiles, int retriedFiles)
        {
            Progress = progress;
            TotalFiles = totalFiles;
            CompletedFiles = completedFiles;
            FailedFiles = failedFiles;
            RetriedFiles = retriedFiles;
        }
    }

    public class ErrorEventArgs : EventArgs
    {
        public string FilePath { get; }
        public Exception Exception { get; }

        public ErrorEventArgs(string filePath, Exception exception)
        {
            FilePath = filePath;
            Exception = exception;
        }
    }

    public class WordFormatter : IDisposable
    {
        private Models.Settings settings;
        private readonly Logger logger;
        private string sourceDirectory;
        private string outputDirectory;
        private CancellationTokenSource? cancellationTokenSource;
        private bool _isStopped;
        private int _totalFiles;
        private int _completedFiles;
        private int _failedFiles;
        private int _retriedFiles;
        private ConcurrentDictionary<string, int> _retryAttempts = new ConcurrentDictionary<string, int>();
        private ManualResetEvent pauseEvent = new ManualResetEvent(true);

        // 用于保存进度的变量
        private List<string> _remainingFiles = new List<string>();
        private string _progressFile;
        private MemoryMonitor _memoryMonitor;
        private bool _isResuming = false;

        // 公开统计属性
        public int TotalFiles => _totalFiles;
        public int CompletedFiles => _completedFiles;
        public int FailedFiles => _failedFiles;
        public int RetriedFiles => _retriedFiles;

        public event EventHandler<ProgressEventArgs>? ProgressChanged;
        public event EventHandler<string>? StatusChanged;
        public event EventHandler<ErrorEventArgs>? ErrorOccurred;

        public WordFormatter(Models.Settings settings, Logger logger, string sourceDirectory, string outputDirectory)
        {
            this.settings = settings ?? throw new ArgumentNullException(nameof(settings));
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
            this.sourceDirectory = sourceDirectory ?? throw new ArgumentNullException(nameof(sourceDirectory));
            this.outputDirectory = outputDirectory ?? throw new ArgumentNullException(nameof(outputDirectory));

            // 设置进度文件路径
            _progressFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "progress.dat");

            // 初始化内存监控器
            _memoryMonitor = new MemoryMonitor(logger);
        }

        public void StartProcessing(string sourceDirectory, string outputDirectory)
        {
            try
            {
                this.sourceDirectory = sourceDirectory;
                this.outputDirectory = outputDirectory;

                // 记录开始处理的日志
                logger.Log($"开始处理文件：源目录 {sourceDirectory}, 目标目录 {outputDirectory}");

                cancellationTokenSource?.Dispose();
                cancellationTokenSource = new CancellationTokenSource();

                // 设置处理状态
                _isStopped = false;

                // 重置所有统计计数器
                _totalFiles = 0;
                _completedFiles = 0;
                _failedFiles = 0;
                _retriedFiles = 0;
                _retryAttempts.Clear();
                _remainingFiles.Clear();

                // 强制删除旧的进度文件，确保总是从头开始新的处理任务
                DeleteProgressFile();
                _isResuming = false;

                // 启动异步处理，并捕获任何异常
                Task.Run(async () =>
                {
                    try
                    {
                        await StartAsync(cancellationTokenSource.Token);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("文件处理过程中发生错误", ex);
                        OnStatusChanged($"处理过程中发生错误: {ex.Message}");
                        _isStopped = true;
                    }
                });

                // 记录处理已启动的日志
                logger.Log("文件处理已启动，正在搜索文件...");
            }
            catch (Exception ex)
            {
                logger.LogError("启动处理时发生错误", ex);
                OnStatusChanged($"启动处理时发生错误: {ex.Message}");
                _isStopped = true;
                throw;
            }
        }

        public void StopProcessing()
        {
            _isStopped = true;
            pauseEvent.Set();  // 释放所有等待的线程
            cancellationTokenSource?.Cancel();

            // 保存当前进度
            SaveProgress();

            // 通知调用者处理已停止
            OnStatusChanged("处理已停止");
        }

        // 添加一个批处理方法
        private async Task StartBatchAsync(List<string> fileBatch, CancellationToken cancellationToken)
        {
            try
            {
                // 检查是否有文件需要处理
                if (fileBatch.Count == 0)
                    return;

                // 获取系统处理器数量
                int processorCount = Environment.ProcessorCount;

                // 计算最优线程数
                int optimalThreads;

                if (settings.EnableSmartThreading)
                {
                    // 智能线程管理：根据系统负载动态计算最优线程数
                    double cpuUsage = GetCurrentCpuUsage();
                    logger.Log($"当前CPU使用率: {cpuUsage:F1}%");

                    if (cpuUsage > 80)
                    {
                        // CPU负载高，减少线程数
                        optimalThreads = Math.Max(1, processorCount / 2);
                    }
                    else if (cpuUsage > 60)
                    {
                        // CPU负载中等，使用适中的线程数
                        optimalThreads = Math.Max(1, processorCount * 3 / 4);
                    }
                    else
                    {
                        // CPU负载低，可以使用更多线程
                        optimalThreads = processorCount;
                    }

                    // 如果用户设置了最大线程数，则不超过该值
                    if (settings.MaxThreads > 0)
                    {
                        optimalThreads = Math.Min(optimalThreads, settings.MaxThreads);
                    }
                }
                else if (settings.MaxThreads <= 0)
                {
                    // 用户设置为自动，使用处理器核心数作为线程数
                    optimalThreads = processorCount;
                }
                else
                {
                    // 使用用户设置的线程数，但不超过处理器核心数的2倍
                    optimalThreads = Math.Min(settings.MaxThreads, processorCount * 2);
                }

                // 确保线程数至少为1
                optimalThreads = Math.Max(1, optimalThreads);

                bool useParallel = optimalThreads > 1;

                // 计算最优批处理大小
                int optimalBatchSize;

                if (settings.BatchSize <= 0)
                {
                    // 用户设置为自动，根据线程数和可用内存计算最优批处理大小
                    long availableMemoryMB = GetAvailableMemoryMB();
                    logger.Log($"可用内存: {availableMemoryMB} MB");

                    // 根据可用内存和线程数计算批处理大小
                    // 假设每个文件处理平均需要5MB内存，并保留50%内存给系统和其他进程
                    long estimatedFilesPerBatch = Math.Max(10, availableMemoryMB / 10 / optimalThreads);

                    // 限制批处理大小在合理范围内
                    optimalBatchSize = (int)Math.Min(1000, estimatedFilesPerBatch);
                }
                else
                {
                    // 使用用户设置的批处理大小，但确保每个线程至少有2个任务
                    optimalBatchSize = Math.Max(settings.BatchSize, optimalThreads * 2);
                }

                logger.Log($"使用 {(useParallel ? "并行" : "串行")} 处理模式，" +
                           $"线程数: {optimalThreads}, " +
                           $"批处理大小: {optimalBatchSize}, " +
                           $"处理器核心数: {processorCount}");

                if (useParallel)
                {
                    var parallelOptions = new ParallelOptions
                    {
                        MaxDegreeOfParallelism = optimalThreads,
                        CancellationToken = cancellationToken
                    };

                    // 使用 SemaphoreSlim 来控制并发任务数
                    using var semaphore = new SemaphoreSlim(optimalThreads);
                    var tasks = new List<Task>();

                    foreach (var filePath in fileBatch)
                    {
                        if (_isStopped || cancellationToken.IsCancellationRequested)
                            break;

                        await semaphore.WaitAsync(cancellationToken);

                        var task = Task.Run(async () =>
                        {
                            try
                            {
                                pauseEvent.WaitOne();
                                if (!_isStopped && !cancellationToken.IsCancellationRequested)
                                {
                                    await ProcessFileWithRetryAsync(filePath, cancellationToken);
                                    OnProgressChanged();
                                }
                            }
                            catch (OperationCanceledException)
                            {
                                // 取消操作，不记录错误
                                throw;
                            }
                            catch (Exception ex)
                            {
                                logger.LogError($"处理文件时出错: {filePath}", ex);
                                OnErrorOccurred(filePath, ex);
                            }
                            finally
                            {
                                semaphore.Release();
                            }
                        }, cancellationToken);

                        tasks.Add(task);
                    }

                    // 等待所有任务完成
                    await Task.WhenAll(tasks);
                }
                else
                {
                    // 串行处理
                    foreach (var filePath in fileBatch)
                    {
                        try
                        {
                            pauseEvent.WaitOne();
                            if (_isStopped || cancellationToken.IsCancellationRequested)
                                break;

                            await ProcessFileWithRetryAsync(filePath, cancellationToken);
                            OnProgressChanged();
                        }
                        catch (OperationCanceledException)
                        {
                            throw;
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"处理文件时出错: {filePath}", ex);
                            OnErrorOccurred(filePath, ex);
                            // 继续处理下一个文件
                        }
                    }
                }

                // 从待处理列表中移除已处理的文件
                foreach (var file in fileBatch)
                {
                    _remainingFiles.Remove(file);
                }

                // 保存进度
                SaveProgress();

                // 记录内存使用情况
                _memoryMonitor.LogMemoryUsage($"批处理完成，已处理 {fileBatch.Count} 个文件");
            }
            catch (OperationCanceledException)
            {
                logger.Log("批处理已取消");
                throw;
            }
            catch (Exception ex)
            {
                logger.LogError($"批处理过程中出错: {ex.Message}", ex);
                throw;
            }
        }

        private async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                _isStopped = false;

                // 如果不是恢复处理，获取文件列表
                if (!_isResuming)
                {
                    // 注意：计数器已在StartProcessing中重置，这里不再重复重置

                    var files = await Task.Run(() => GetWordFiles(sourceDirectory), cancellationToken);
                    _remainingFiles.AddRange(files);
                    _totalFiles = files.Count;
                    OnStatusChanged($"找到 {_totalFiles} 个Word文件");
                }
                else
                {
                    // 恢复处理，使用保存的文件列表
                    _totalFiles = _completedFiles + _remainingFiles.Count;
                    OnStatusChanged($"恢复处理：已完成 {_completedFiles}/{_totalFiles} 个文件，剩余 {_remainingFiles.Count} 个文件");

                    // 启动时记录内存使用情况
                    _memoryMonitor.LogMemoryUsage("恢复处理开始");
                }

                // 检查是否有文件需要处理
                if (_remainingFiles.Count == 0)
                {
                    OnStatusChanged("没有找到需要处理的文件");
                    _isStopped = true;
                    return;
                }

                // 确定处理模式（并行或串行）
                int optimalThreads;

                if (settings.EnableSmartThreading)
                {
                    // 智能线程管理：根据系统负载动态计算最优线程数
                    double cpuUsage = GetCurrentCpuUsage();
                    logger.Log($"当前CPU使用率: {cpuUsage:F1}%");

                    if (cpuUsage > 80)
                    {
                        // CPU负载高，减少线程数
                        optimalThreads = Math.Max(1, Environment.ProcessorCount / 2);
                    }
                    else if (cpuUsage > 60)
                    {
                        // CPU负载中等，使用适中的线程数
                        optimalThreads = Math.Max(1, Environment.ProcessorCount * 3 / 4);
                    }
                    else
                    {
                        // CPU负载低，可以使用更多线程
                        optimalThreads = Environment.ProcessorCount;
                    }

                    // 如果用户设置了最大线程数，则不超过该值
                    if (settings.MaxThreads > 0)
                    {
                        optimalThreads = Math.Min(optimalThreads, settings.MaxThreads);
                    }

                    logger.Log($"智能线程管理已启用，根据系统负载选择线程数: {optimalThreads}");
                }
                else if (settings.MaxThreads <= 0)
                {
                    // 用户设置为自动，使用处理器核心数作为线程数
                    optimalThreads = Environment.ProcessorCount;
                    logger.Log($"自动选择线程数: {optimalThreads} (基于处理器核心数)");
                }
                else
                {
                    // 使用用户设置的线程数
                    optimalThreads = settings.MaxThreads;
                    logger.Log($"使用用户设置的线程数: {optimalThreads}");
                }

                bool useParallel = optimalThreads > 1;

                // 日志记录处理模式
                if (useParallel)
                    logger.Log($"使用并行处理模式，最大线程数: {optimalThreads}");
                else
                    logger.Log("使用串行处理模式");

                // 计算最优批处理大小
                int batchSize;

                if (settings.BatchSize <= 0)
                {
                    // 用户设置为自动，根据线程数和可用内存计算最优批处理大小
                    long availableMemoryMB = GetAvailableMemoryMB();
                    logger.Log($"可用内存: {availableMemoryMB} MB");

                    // 根据可用内存和线程数计算批处理大小
                    // 假设每个文件处理平均需要5MB内存，并保留50%内存给系统和其他进程
                    long estimatedFilesPerBatch = Math.Max(10, availableMemoryMB / 10 / optimalThreads);

                    // 限制批处理大小在合理范围内
                    batchSize = (int)Math.Min(1000, estimatedFilesPerBatch);
                    logger.Log($"自动计算的批处理大小: {batchSize}");
                }
                else
                {
                    // 使用用户设置的批处理大小
                    batchSize = settings.BatchSize;
                    logger.Log($"使用用户设置的批处理大小: {batchSize}");
                }

                // 处理所有文件
                while (_remainingFiles.Count > 0 && !_isStopped && !cancellationToken.IsCancellationRequested)
                {
                    // 获取当前批次要处理的文件
                    var currentBatch = _remainingFiles.Take(Math.Min(batchSize, _remainingFiles.Count)).ToList();

                    try
                    {
                        // 处理当前批次
                        await StartBatchAsync(currentBatch, cancellationToken);

                        // 检查是否应该进行内存整理
                        if (settings.RetrySettings != null && settings.RetrySettings.EnableSmartMemoryManagement)
                        {
                            // 智能内存管理：根据内存使用情况决定是否进行垃圾回收
                            long availableMemoryMB = GetAvailableMemoryMB();
                            int memoryThreshold = settings.RetrySettings.MemoryThresholdMB;

                            // 如果可用内存低于阈值，执行垃圾回收
                            if (availableMemoryMB < memoryThreshold)
                            {
                                logger.Log($"可用内存 ({availableMemoryMB}MB) 低于阈值 ({memoryThreshold}MB)，执行垃圾回收");
                                await Task.Run(() =>
                                {
                                    _memoryMonitor.LogMemoryUsage("执行垃圾回收前");
                                    GC.Collect();
                                    GC.WaitForPendingFinalizers();
                                    _memoryMonitor.LogMemoryUsage("执行垃圾回收后");
                                }, cancellationToken);
                            }
                        }
                        else if ((_completedFiles + _failedFiles) % 20 == 0)
                        {
                            // 传统方式：每处理20个文件进行一次GC，减少内存压力
                            await Task.Run(() =>
                            {
                                _memoryMonitor.LogMemoryUsage("执行垃圾回收前");
                                GC.Collect();
                                GC.WaitForPendingFinalizers();
                                _memoryMonitor.LogMemoryUsage("执行垃圾回收后");
                            }, cancellationToken);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        logger.Log("处理已取消");
                        _isStopped = true;
                        break;
                    }
                }

                // 确保所有文件处理完成后，总是触发状态更新
                if ((_remainingFiles.Count == 0 || _completedFiles + _failedFiles >= _totalFiles) && !_isStopped && !cancellationToken.IsCancellationRequested)
                {
                    // 保存进度
                    SaveProgress();

                    // 删除进度文件
                    DeleteProgressFile();

                    // 最后进行一次内存清理
                    await Task.Run(() =>
                    {
                        _memoryMonitor.LogMemoryUsage("最终内存清理前");
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                        _memoryMonitor.LogMemoryUsage("最终内存清理后");
                    }, cancellationToken);

                    // 无论是全部成功还是部分失败，都表示处理已完成
                    _isStopped = true;

                    // 清理完成后再通知状态变更
                    OnStatusChanged("处理已完成");
                    logger.Log($"处理已完成。共 {_totalFiles} 个文件，成功 {_completedFiles} 个，失败 {_failedFiles} 个，重试 {_retriedFiles} 个");
                }
                // 处理取消和停止的情况
                else if (_isStopped)
                {
                    // 避免重复发送"处理已停止"状态
                    // 只记录日志，不再触发状态变更事件
                    logger.Log($"处理已停止。共 {_totalFiles} 个文件，已处理 {_completedFiles + _failedFiles} 个，" +
                               $"成功 {_completedFiles} 个，失败 {_failedFiles} 个，重试 {_retriedFiles} 个");
                }
                else if (cancellationToken.IsCancellationRequested)
                {
                    OnStatusChanged("处理已取消");
                    logger.Log($"处理已取消。共 {_totalFiles} 个文件，已处理 {_completedFiles + _failedFiles} 个，" +
                               $"成功 {_completedFiles} 个，失败 {_failedFiles} 个，重试 {_retriedFiles} 个");
                }

                // 不再等待最后的内存清理，确保方法能够及时返回
            }
            catch (Exception ex)
            {
                logger.LogError($"处理过程中发生错误: {ex.Message}", ex);
                OnStatusChanged($"处理过程中发生错误: {ex.Message}");
                _isStopped = true;
            }
        }

        /// <summary>
        /// 保存当前处理进度到文件
        /// </summary>
        private void SaveProgress()
        {
            try
            {
                // 创建进度数据对象
                var progressData = new ProcessingProgress
                {
                    CompletedFiles = _completedFiles,
                    FailedFiles = _failedFiles,
                    RetriedFiles = _retriedFiles,
                    RemainingFiles = _remainingFiles.ToList(),
                    SourceDirectory = sourceDirectory,
                    OutputDirectory = outputDirectory,
                    TimeStamp = DateTime.Now
                };

                // 序列化进度数据
                var json = System.Text.Json.JsonSerializer.Serialize(progressData);
                File.WriteAllText(_progressFile, json);

                logger.LogDebug($"已保存处理进度：已完成 {_completedFiles} 个文件，剩余 {_remainingFiles.Count} 个文件");
            }
            catch (Exception ex)
            {
                logger.LogError($"保存处理进度失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 尝试从文件加载之前的处理进度
        /// </summary>
        /// <returns>如果有可恢复的进度则返回true，否则返回false</returns>
        private bool TryLoadProgress()
        {
            try
            {
                if (File.Exists(_progressFile))
                {
                    var json = File.ReadAllText(_progressFile);
                    var progressData = System.Text.Json.JsonSerializer.Deserialize<ProcessingProgress>(json);

                    // 验证恢复的进度数据是否有效
                    if (progressData != null &&
                        progressData.RemainingFiles != null &&
                        progressData.RemainingFiles.Count > 0 &&
                        progressData.SourceDirectory == sourceDirectory &&
                        progressData.OutputDirectory == outputDirectory)
                    {
                        _completedFiles = progressData.CompletedFiles;
                        _failedFiles = progressData.FailedFiles;
                        _retriedFiles = progressData.RetriedFiles;
                        _remainingFiles = progressData.RemainingFiles;

                        // 检查剩余文件是否仍然存在
                        _remainingFiles = _remainingFiles.Where(File.Exists).ToList();

                        logger.Log($"已加载处理进度：已完成 {_completedFiles} 个文件，剩余 {_remainingFiles.Count} 个文件");
                        return _remainingFiles.Count > 0; // 只有当还有剩余文件时才有意义恢复
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                logger.LogError($"加载处理进度失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 删除进度文件
        /// </summary>
        private void DeleteProgressFile()
        {
            try
            {
                if (File.Exists(_progressFile))
                {
                    File.Delete(_progressFile);
                    logger.LogDebug("已删除处理进度文件");
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"删除进度文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 通用的重试操作处理方法
        /// </summary>
        private async Task<(bool Success, T? Result)> RetryOperationAsync<T>(
            Func<Task<T>> operation,
            string operationName,
            CancellationToken cancellationToken,
            bool isFileLockCheck = false)
        {
            // 获取重试参数
            var retrySettings = settings.RetrySettings ?? new Models.RetrySettings();

            // 根据操作类型选择合适的重试参数
            int maxRetries = isFileLockCheck && retrySettings != null
                ? retrySettings.FileLockCheckMaxAttempts
                : settings.MaxRetryCount;

            int baseDelay = retrySettings.BaseDelayMs;
            float multiplier = retrySettings.DelayMultiplier;
            int maxDelay = retrySettings.MaxDelayMs;
            Exception? lastException = null;
            bool isFileLocked = false;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    if (attempt > 1)
                    {
                        logger.Log($"重试{operationName} ({attempt}/{maxRetries})");
                        _retryAttempts.AddOrUpdate(operationName, 1, (key, oldValue) => oldValue + 1);
                    }

                    var result = await operation();

                    if (attempt > 1)
                    {
                        Interlocked.Increment(ref _retriedFiles);
                        OnProgressChanged();
                    }

                    return (true, result);
                }
                catch (IOException ioEx) when (ioEx.Message.Contains("being used by another process"))
                {
                    lastException = ioEx;
                    isFileLocked = true;

                    // 对文件锁定错误使用特殊的重试策略
                    if (attempt < maxRetries)
                    {
                        // 使用文件锁定特定的延迟参数
                        int fileLockBaseDelay = retrySettings.FileLockBaseDelayMs;
                        int delay = Math.Min(
                            (int)(fileLockBaseDelay * Math.Pow(multiplier, attempt - 1)),
                            maxDelay
                        );

                        logger.Log($"文件被占用，等待 {delay}ms 后重试...");
                        await Task.Delay(delay, cancellationToken);
                        continue;
                    }
                    logger.Log($"{operationName}失败：文件被占用，已达到最大重试次数 ({maxRetries})");
                    break;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    if (attempt < maxRetries)
                    {
                        // 计算指数退避延迟
                        int delay = Math.Min(
                            (int)(baseDelay * Math.Pow(multiplier, attempt - 1)),
                            maxDelay
                        );

                        logger.Log($"操作失败，等待 {delay}ms 后重试...");
                        await Task.Delay(delay, cancellationToken);
                        continue;
                    }
                    logger.LogError($"{operationName}失败：{ex.Message}", ex);
                    break;
                }
            }

            if (lastException != null)
            {
                if (isFileLocked)
                {
                    logger.LogError($"{operationName}失败，文件被占用，已重试{maxRetries}次：{lastException.Message}", lastException);
                }
                else
                {
                    logger.LogError($"{operationName}失败，已重试{maxRetries}次：{lastException.Message}", lastException);
                }
            }

            return (false, default);
        }

        /// <summary>
        /// 检查文件是否被锁定
        /// </summary>
        private async Task<bool> WaitForTemporaryFileRelease(string filePath)
        {
            try
            {
                await Task.Run(() =>
                {
                    using (var stream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.None))
                    {
                        return true;
                    }
                });
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 使用重试机制处理单个文件
        /// </summary>
        private async Task<bool> ProcessFileWithRetryAsync(string filePath, CancellationToken cancellationToken)
        {
            var result = await RetryOperationAsync<bool>(
                async () => await ProcessFileAsync(filePath, cancellationToken),
                $"处理文件: {Path.GetFileName(filePath)}",
                cancellationToken
            );

            // 如果重试失败，需要增加失败计数器
            // 但要注意：ProcessFileAsync中某些情况已经计数了（如TXT转换失败），避免重复计数
            if (!result.Success)
            {
                logger.LogError($"文件处理失败（已重试）: {filePath}", new Exception("文件处理重试失败"));
                // 只有当ProcessFileAsync没有计数时才计数
                // 由于ProcessFileAsync中的异常会被重试机制捕获，这里统一计数
                Interlocked.Increment(ref _failedFiles);
            }

            return result.Success;
        }

        /// <summary>
        /// 使用重试机制复制文件
        /// </summary>
        private async Task<bool> CopyFileWithRetryAsync(string sourceFile, string targetFile, CancellationToken cancellationToken)
        {
            try
            {
                await Task.Run(() => File.Copy(sourceFile, targetFile, true), cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                logger.LogError($"复制文件失败: {sourceFile} -> {targetFile}", ex);
                return false;
            }
        }

        /// <summary>
        /// 使用重试机制保存文档
        /// </summary>
        private async Task<bool> SaveDocumentWithRetryAsync(AW.Document doc, string filePath, AW.SaveFormat saveFormat, CancellationToken cancellationToken)
        {
            var result = await RetryOperationAsync<bool>(
                async () =>
                {
                    await Task.Run(() => doc.Save(filePath, saveFormat), cancellationToken);
                    return true;
                },
                $"保存文档: {Path.GetFileName(filePath)}",
                cancellationToken
            );

            return result.Success;
        }

        private async Task<bool> ProcessFileAsync(string filePath, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                logger.LogWarning("文件路径为空");
                return false;
            }

            // 检查是否是临时文件
            if (IsOfficeTemporaryFile(filePath))
            {
                logger.LogWarning($"跳过Office临时文件: {filePath}");
                // 跳过临时文件应该计为成功，因为这是预期行为
                Interlocked.Increment(ref _completedFiles);
                return true;
            }

            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory))
            {
                // 清理同目录下的临时文件
                CleanupOfficeTemporaryFiles(directory);
            }

            try
            {
                // 等待文件释放
                if (!await WaitForTemporaryFileRelease(filePath))
                {
                    logger.LogError($"文件被Office程序锁定，无法处理: {filePath}", new IOException("文件被其他程序占用"));
                    // 文件锁定应该抛出异常，让重试机制处理
                    throw new IOException($"文件被其他程序占用: {filePath}");
                }

                var fileName = Path.GetFileName(filePath);
                OnStatusChanged($"正在处理: {fileName}");

                // 开始计时
                var stopwatch = Stopwatch.StartNew();

                // 记录处理开始时的内存使用情况
                _memoryMonitor.LogMemoryUsage($"处理文件 {fileName} 开始");

                cancellationToken.ThrowIfCancellationRequested();

                // 添加对暂停状态的检查
                pauseEvent.WaitOne();

                // 检查是否取消
                if (cancellationToken.IsCancellationRequested)
                {
                    return false;
                }

                // 保存原始文件路径，用于移动模式下的删除操作
                string originalFilePath = filePath;
                bool isConvertedFromTxt = false; // 标记是否从TXT转换而来

                // 检查文件是否为TXT文件，如果是则先转换为Word文件
                string extension = Path.GetExtension(filePath).ToLower();
                if (extension == ".txt")
                {
                    logger.Log($"检测到TXT文件: {filePath}");

                    // 确定输出目录
                    string outputDir;
                    if (settings.ProcessOriginalFiles)
                    {
                        // 如果处理原始文件，使用原始文件所在目录
                        outputDir = Path.GetDirectoryName(filePath);
                    }
                    else
                    {
                        // 对于移动模式，TXT转换应该直接在源目录进行，然后再移动到目标目录
                        // 这样可以避免路径计算错误
                        if (settings.MoveFiles)
                        {
                            // 移动模式：先在源目录转换，后续会移动到目标目录
                            outputDir = Path.GetDirectoryName(filePath);
                        }
                        else
                        {
                            // 复制模式：直接转换到目标目录
                            var relativePath = Path.GetRelativePath(sourceDirectory, filePath);
                            outputDir = settings.KeepDirectoryStructure ?
                                Path.Combine(outputDirectory, Path.GetDirectoryName(relativePath)) :
                                outputDirectory;
                        }
                    }

                    // 转换TXT文件为Word文件
                    string convertedFilePath = ConvertTxtToWord(filePath, outputDir, cancellationToken);

                    // 如果转换成功且路径不同（说明确实进行了转换），则使用转换后的文件路径继续处理
                    if (!string.IsNullOrEmpty(convertedFilePath) && convertedFilePath != filePath)
                    {
                        logger.Log($"TXT文件已转换为Word文件: {convertedFilePath}");

                        // TXT转换成功，但不在这里增加计数器
                        // 因为我们需要等到整个处理流程完成后再计数
                        // 这样用户看到的是TXT文件被完整处理了，而不是仅仅转换了
                        logger.Log($"TXT转换完成，文件: {Path.GetFileName(filePath)}");

                        // 删除原始TXT文件的条件：
                        // 1. 启用了TXT转Word且不保留原始文件
                        // 2. 移动模式且不处理原始文件
                        bool shouldDeleteOriginalTxt =
                            (settings.TxtSettings?.ConvertToWordBeforeProcessing == true && !settings.BackupOriginalFiles) ||
                            (settings.MoveFiles && !settings.ProcessOriginalFiles);

                        if (shouldDeleteOriginalTxt)
                        {
                            try
                            {
                                File.Delete(filePath);
                                logger.Log($"已删除原始TXT文件: {filePath}");
                            }
                            catch (Exception ex)
                            {
                                logger.LogWarning($"删除原始TXT文件失败: {filePath}, 错误: {ex.Message}");
                            }
                        }

                        // 使用转换后的文件路径继续处理
                        filePath = convertedFilePath;
                        fileName = Path.GetFileName(filePath);
                        isConvertedFromTxt = true; // 标记为从TXT转换而来
                        logger.Log($"继续处理转换后的文件: {filePath}");
                    }
                    else if (string.IsNullOrEmpty(convertedFilePath) || convertedFilePath == filePath)
                    {
                        // 转换失败或未进行转换
                        if (settings.TxtSettings?.ConvertToWordBeforeProcessing == true)
                        {
                            logger.LogError($"TXT文件转换失败，无法继续处理: {filePath}", new Exception("TXT转Word转换失败"));
                            // 转换失败，抛出异常让重试机制处理
                            throw new Exception("TXT转Word转换失败");
                        }
                        else
                        {
                            logger.LogError($"TXT转Word功能未启用，无法处理TXT文件: {filePath}", new Exception("TXT文件需要转换为Word格式才能处理"));
                            // 未启用转换功能，抛出异常让重试机制处理
                            throw new Exception("TXT文件需要转换为Word格式才能处理");
                        }
                    }
                }

                // 确定输出路径
                string outputFile;
                bool savingAsPdf = settings.EnableWordToPdf;

                // 如果设置了直接处理原文件
                if (settings.ProcessOriginalFiles)
                {
                    // 处理原文件时，如果需要转换为PDF，则输出到同一目录
                    if (savingAsPdf)
                    {
                        // 处理原文档并在同目录输出PDF
                        var fileDirectory = Path.GetDirectoryName(filePath);
                        var fileNameWithoutExt = Path.GetFileNameWithoutExtension(filePath);
                        outputFile = Path.Combine(fileDirectory, fileNameWithoutExt + ".pdf");
                    }
                    else
                    {
                        // 直接处理原文件
                        outputFile = filePath;
                    }
                }
                else
                {
                    // 创建输出目录
                    var relativePath = Path.GetRelativePath(sourceDirectory, filePath);
                    var targetPath = settings.KeepDirectoryStructure ?
                        Path.Combine(outputDirectory, Path.GetDirectoryName(relativePath)) :
                        outputDirectory;

                    await Task.Run(() => Directory.CreateDirectory(targetPath), cancellationToken);

                    // 应用文件名替换规则
                    var newFileName = settings.EnableFileNameReplace ?
                        ApplyFileNameRules(fileName) :
                        fileName;

                    // 如果启用了PDF转换，强制使用.pdf扩展名
                    if (savingAsPdf)
                    {
                        newFileName = Path.GetFileNameWithoutExtension(newFileName) + ".pdf";
                    }

                    outputFile = Path.Combine(targetPath, newFileName);

                    // 检查文件是否存在 - 将文件检查移到后台线程
                    bool fileExists = await Task.Run(() => File.Exists(outputFile), cancellationToken);
                    if (fileExists)
                    {
                        switch (settings.ConflictHandling.ToLower())
                        {
                            case "跳过":
                                logger.Log($"跳过已存在的文件: {outputFile}");

                                // 如果是移动模式，即使跳过处理也要删除源文件
                                if (settings.MoveFiles)
                                {
                                    try
                                    {
                                        File.Delete(originalFilePath);
                                        logger.Log($"移动模式：已删除源文件 {originalFilePath}");
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogWarning($"移动模式：删除源文件失败 {originalFilePath}, 错误: {ex.Message}");
                                    }
                                }

                                // 跳过文件应该计为成功，因为这是用户选择的行为
                                Interlocked.Increment(ref _completedFiles);
                                return true;
                            case "重命名":
                                outputFile = await Task.Run(() => GetUniqueFileName(outputFile), cancellationToken);
                                break;
                            // 默认覆盖
                        }
                    }

                    // 如果选择移动文件，先复制到目标位置
                    if (settings.MoveFiles)
                    {
                        if (!await CopyFileWithRetryAsync(filePath, outputFile, cancellationToken))
                        {
                            logger.LogError($"复制文件失败: {filePath} -> {outputFile}", new IOException($"无法复制文件从 {filePath} 到 {outputFile}"));
                            // 复制失败应该抛出异常，让重试机制处理
                            throw new IOException($"无法复制文件从 {filePath} 到 {outputFile}");
                        }
                        logger.Log($"已复制文件到目标位置: {outputFile}");
                    }
                }

                AW.Document? doc = null;
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // 在后台线程加载文档，添加文件访问错误的处理逻辑
                    try
                    {
                        doc = await Task.Run(() => new AW.Document(filePath), cancellationToken);
                    }
                    catch (IOException ioEx) when (ioEx.Message.Contains("being used by another process"))
                    {
                        // 文件被占用，由上层的 ProcessFileWithRetryAsync 处理重试
                        logger.LogError($"文件被其他程序占用，无法打开: {filePath}", ioEx);
                        throw; // 向上层抛出异常，由ProcessFileWithRetryAsync处理重试
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"打开文件失败: {filePath}, 错误: {ex.Message}", ex);
                        throw;
                    }

                    // 检查是否应该删除文档（在所有处理之前进行）
                    if (settings.EnableDeleteContent && settings.DeleteSettings != null)
                    {
                        if (ShouldDeleteDocument(filePath, doc, settings.DeleteSettings))
                        {
                            // 根据文件处理模式删除文档
                            if (settings.ProcessOriginalFiles)
                            {
                                // 直接删除原文件
                                File.Delete(filePath);
                                logger.Log($"已删除原文档: {filePath}");
                            }
                            else
                            {
                                // 在复制或移动模式下，不创建输出文件，相当于"删除"
                                logger.Log($"文档不符合条件，跳过处理: {filePath}");

                                // 如果是移动模式，还需要删除源文件
                                if (settings.MoveFiles)
                                {
                                    try
                                    {
                                        File.Delete(originalFilePath);
                                        logger.Log($"移动模式：已删除源文件 {originalFilePath}");
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogWarning($"移动模式：删除源文件失败 {originalFilePath}, 错误: {ex.Message}");
                                    }
                                }
                            }

                            // 记录处理时间
                            stopwatch.Stop();
                            logger.Log($"文档删除完成，耗时: {stopwatch.ElapsedMilliseconds}ms");

                            // 处理成功，增加计数器
                            Interlocked.Increment(ref _completedFiles);

                            return true; // 返回成功，因为这是预期的行为
                        }
                    }

                    // 1. 应用页面设置 (第一步 - TXT转Word已在前面完成)
                    if (settings.EnablePageSetup && settings.PageSetup != null)
                    {
                        // 检查是否有任何页面设置标签页的功能被启用
                        if (HasAnyPageSetupTabEnabled(settings.PageSetup))
                        {
                            logger.Log("开始应用页面设置...");

                            // 应用到所有section，在后台线程中处理，使用并行处理提高性能
                            await Task.Run(() =>
                            {
                                try
                                {
                                    // 获取所有节
                                    var sections = doc.Sections.Cast<AW.Section>().ToList();

                                    // 使用并行处理多个节
                                    Parallel.ForEach(sections, section =>
                                    {
                                        try
                                        {
                                            // 应用页面设置
                                            settings.PageSetup.ApplyTo(section.PageSetup);

                                            // 处理背景颜色设置
                                            if (settings.PageSetup.EnableBackgroundColor &&
                                                settings.PageSetup.BackgroundColor != Color.Transparent &&
                                                settings.PageSetup.BackgroundColor != Color.Black)
                                            {
                                                try
                                                {
                                                    // 尝试使用反射设置PageColor属性（如果支持）
                                                    var pageColorProp = typeof(AW.PageSetup).GetProperty("PageColor");
                                                    if (pageColorProp != null)
                                                    {
                                                        pageColorProp.SetValue(section.PageSetup, settings.PageSetup.BackgroundColor);
                                                        logger.Log($"已设置页面背景颜色: {settings.PageSetup.BackgroundColor}");
                                                    }
                                                    else
                                                    {
                                                        // 如果不支持PageColor，记录警告但不设置段落底纹
                                                        logger.LogWarning("当前Aspose.Words版本不支持页面背景颜色设置，已跳过背景色应用");
                                                    }
                                                }
                                                catch (Exception bgEx)
                                                {
                                                    logger.LogError($"设置页面背景颜色时出错: {bgEx.Message}", bgEx);
                                                }
                                            }
                                            else if (settings.PageSetup.EnableBackgroundColor &&
                                                     settings.PageSetup.BackgroundColor == Color.Black)
                                            {
                                                logger.LogWarning("检测到黑色背景设置，为避免文档背景变黑已跳过背景色应用");
                                            }

                                            // 处理背景图片设置
                                            if (settings.PageSetup.EnableBackgroundImage &&
                                                !string.IsNullOrEmpty(settings.PageSetup.BackgroundImage))
                                            {
                                                try
                                                {
                                                    // 创建DocumentBuilder
                                                    var builder = new AW.DocumentBuilder(doc);

                                                    // 移动到节的开始
                                                    builder.MoveTo(section.Body.FirstParagraph);

                                                    // 插入图片作为背景
                                                    var shape = builder.InsertImage(settings.PageSetup.BackgroundImage);

                                                    // 设置图片属性
                                                    shape.BehindText = true;
                                                    shape.RelativeHorizontalPosition = AW.Drawing.RelativeHorizontalPosition.Page;
                                                    shape.RelativeVerticalPosition = AW.Drawing.RelativeVerticalPosition.Page;

                                                    // 根据显示模式设置图片属性
                                                    switch (settings.PageSetup.BackgroundImageDisplayMode)
                                                    {
                                                        case PageSetupFixed.DisplayMode.Centered:
                                                            // 居中显示
                                                            shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Center;
                                                            shape.VerticalAlignment = AW.Drawing.VerticalAlignment.Center;
                                                            break;

                                                        case PageSetupFixed.DisplayMode.Stretched:
                                                            // 拉伸显示 - 设置为页面大小
                                                            shape.Width = section.PageSetup.PageWidth;
                                                            shape.Height = section.PageSetup.PageHeight;
                                                            shape.Left = 0;
                                                            shape.Top = 0;
                                                            break;

                                                        case PageSetupFixed.DisplayMode.Tiled:
                                                        default:
                                                            // 平铺显示 - 这里简化为单个图片
                                                            shape.Left = 0;
                                                            shape.Top = 0;
                                                            break;
                                                    }
                                                }
                                                catch (Exception imgEx)
                                                {
                                                    logger.LogError($"设置页面背景图片时出错: {imgEx.Message}", imgEx);
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            logger.LogError($"应用页面设置到节时出错: {ex.Message}", ex);
                                        }
                                    });

                                    logger.Log($"已应用页面设置到 {sections.Count} 个节");
                                }
                                catch (Exception ex)
                                {
                                    logger.LogError($"应用页面设置时出错: {ex.Message}", ex);
                                }
                            }, cancellationToken);
                        }
                        else
                        {
                            logger.Log("没有启用任何页面设置标签页的功能，跳过处理");
                        }
                    }

                    cancellationToken.ThrowIfCancellationRequested();

                    // 2. 应用删除内容设置 (第二步)
                    if (settings.EnableDeleteContent && settings.DeleteSettings != null)
                    {
                        await ApplyDeleteSettingsAsync(doc, settings.DeleteSettings, cancellationToken);
                    }

                    // 3. 应用内容替换规则 (第三步) - 检查是否所有子功能都未勾选
                    if (settings.EnableContentReplace && settings.ContentReplaceRules != null && settings.ContentReplaceRules.Count > 0)
                    {
                        // 检查是否所有范围都未勾选
                        bool allRangesDisabled = true;
                        foreach (var rule in settings.ContentReplaceRules.Where(r => r.IsEnabled))
                        {
                            if (rule.SearchInMainText || rule.SearchInHeaders || rule.SearchInFooters ||
                                rule.SearchInTextBoxes || rule.SearchInFootnotes || rule.SearchInComments)
                            {
                                allRangesDisabled = false;
                                break;
                            }
                        }

                        if (!allRangesDisabled)
                        {
                            await ApplyContentReplaceRulesAsync(doc, settings.ContentReplaceRules, cancellationToken);
                        }
                        else
                        {
                            logger.Log("跳过内容替换规则应用，因为所有启用的规则都没有选择任何应用范围");
                        }
                    }

                    cancellationToken.ThrowIfCancellationRequested();

                    // 4. 应用全局段落格式 (第四步) - 将耗时处理移至后台线程
                    if (settings.EnableGlobalParagraphFormat && settings.GlobalParagraphFormat != null)
                    {
                        await Task.Run(() =>
                        {
                            // 获取文档中的所有段落
                            var paragraphs = doc.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();

                            // 遍历所有段落应用格式
                            foreach (var paragraph in paragraphs)
                            {
                                // 应用段落格式
                                settings.GlobalParagraphFormat.ApplyToParagraph(paragraph);
                            }

                            logger.Log($"已应用全局段落格式到 {paragraphs.Count} 个段落");

                            // 处理全局段落格式中的图片格式和水印
                            if (settings.GlobalParagraphFormat.EnableImageFormat || settings.GlobalParagraphFormat.EnableWatermark)
                            {
                                ApplyImageFormat(doc, settings.GlobalParagraphFormat);
                            }

                            // 处理全局段落格式中的表格格式
                            if (settings.GlobalParagraphFormat.EnableTableAutoFit || settings.GlobalParagraphFormat.EnableTableLayout ||
                                settings.GlobalParagraphFormat.EnableTableStyle || settings.GlobalParagraphFormat.EnableCellFormat)
                            {
                                settings.GlobalParagraphFormat.ApplyTableFormatToDocument(doc);
                            }
                        }, cancellationToken);
                    }

                    cancellationToken.ThrowIfCancellationRequested();

                    // 5. 应用段落匹配规则 (第五步) - 将耗时处理移至后台线程
                    if (settings.EnableParagraphMatch && settings.ParagraphMatchRules != null && settings.ParagraphMatchRules.Count > 0)
                    {
                        await Task.Run(() =>
                        {
                            // 处理文档中的所有节点，应用段落匹配规则
                            ProcessParagraphs(doc, settings);

                            logger.Log($"已应用段落匹配规则到文档");
                        }, cancellationToken);
                    }
                    else if (settings.ParagraphMatchRules != null && settings.ParagraphMatchRules.Count > 0)
                    {
                        logger.Log("段落匹配规则功能未启用，跳过应用段落匹配规则");
                    }

                    cancellationToken.ThrowIfCancellationRequested();

                    // 6. 应用页眉页脚设置 (第六步) - 使用异步方法
                    if (settings.EnableHeaderFooter && settings.HeaderFooterSettings != null)
                    {
                        await ApplyHeaderFooterSettingsAsync(doc, settings.HeaderFooterSettings, cancellationToken);
                    }

                    cancellationToken.ThrowIfCancellationRequested();

                    // 7. 应用文档属性 (第七步)
                    if (settings.EnableDocumentProperties && settings.DocumentProperties != null)
                    {
                        // 只有当有属性被启用时才应用文档属性
                        if (settings.DocumentProperties.HasAnyEnabledProperty())
                        {
                            logger.Log("开始应用文档属性...");

                            // 记录自定义属性的启用状态
                            if (settings.DocumentProperties.HasEnabledCustomProperties())
                            {
                                logger.Log("检测到启用的自定义属性，将应用到文档");
                            }

                            await Task.Run(() => settings.DocumentProperties.ApplyTo(doc), cancellationToken);

                            logger.Log("文档属性应用完成");
                        }
                        else
                        {
                            logger.Log("没有启用任何文档属性，跳过应用");
                        }
                    }

                    // 8. 保存文档 (第八步 - 文件名替换在此步骤中应用) - 在后台线程执行
                    if (savingAsPdf)
                    {
                        try
                        {
                            // 首先保存处理后的Word文档到目标位置
                            // 获取Word文档的目标路径
                            string wordOutputFile;
                            if (settings.ProcessOriginalFiles)
                            {
                                // 使用原始文件名，但保存到输出目录
                                wordOutputFile = Path.Combine(Path.GetDirectoryName(outputFile), Path.GetFileName(filePath));
                            }
                            else
                            {
                                // 使用目标文件名，但更改扩展名为原始扩展名
                                string originalExtension = Path.GetExtension(filePath);
                                string outputFileWithoutExt = Path.Combine(
                                    Path.GetDirectoryName(outputFile),
                                    Path.GetFileNameWithoutExtension(outputFile));
                                wordOutputFile = outputFileWithoutExt + originalExtension;
                            }

                            // 检查Word输出文件是否存在
                            if (File.Exists(wordOutputFile))
                            {
                                switch (settings.ConflictHandling.ToLower())
                                {
                                    case "跳过":
                                        logger.Log($"跳过已存在的Word文件: {wordOutputFile}");
                                        break;
                                    case "重命名":
                                        wordOutputFile = await Task.Run(() => GetUniqueFileName(wordOutputFile), cancellationToken);
                                        break;
                                    default:
                                        // 覆盖模式，删除现有文件
                                        try
                                        {
                                            await Task.Run(() => File.Delete(wordOutputFile), cancellationToken);
                                        }
                                        catch (Exception ex)
                                        {
                                            logger.LogWarning($"无法删除已存在的文件: {wordOutputFile}, {ex.Message}");
                                        }
                                        break;
                                }
                            }

                            // 保存处理后的Word文档
                            var docSaveFormat = GetSaveFormat(wordOutputFile);

                            if (!await SaveDocumentWithRetryAsync(doc, wordOutputFile, docSaveFormat, cancellationToken))
                            {
                                var ex = new Exception($"保存Word文档失败: {wordOutputFile}");
                                logger.LogError($"保存Word文档失败: {wordOutputFile}", ex);
                                return false;
                            }
                            logger.Log($"已保存处理后的Word文档: {wordOutputFile}");

                            // 9. Word转PDF (第九步 - 最后步骤) - 使用PDF转换方法
                            await ConvertToPdf(filePath, Path.GetDirectoryName(outputFile), cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"保存Word文档副本时出错: {ex.Message}", ex);
                            // 继续PDF转换，不因Word副本保存失败而中断
                        }
                    }
                    else
                    {
                        // 保存为Word格式
                        await Task.Run(() =>
                        {
                            // 根据文件扩展名确定保存格式
                            string extension = Path.GetExtension(outputFile).ToLower();
                            AW.SaveFormat saveFormat;

                            switch (extension)
                            {
                                case ".doc":
                                    saveFormat = AW.SaveFormat.Doc;
                                    break;
                                case ".rtf":
                                    saveFormat = AW.SaveFormat.Rtf;
                                    break;
                                case ".txt":
                                    saveFormat = AW.SaveFormat.Text;
                                    break;
                                case ".html":
                                case ".htm":
                                    saveFormat = AW.SaveFormat.Html;
                                    break;
                                case ".odt":
                                    saveFormat = AW.SaveFormat.Odt;
                                    break;
                                case ".pdf":
                                    saveFormat = AW.SaveFormat.Pdf;
                                    break;
                                case ".docx":
                                default:
                                    saveFormat = AW.SaveFormat.Docx;
                                    break;
                            }

                            // 根据保存格式选择合适的保存选项
                            if (saveFormat == AW.SaveFormat.Pdf)
                            {
                                // PDF格式需要使用PdfSaveOptions
                                var pdfOptions = new AW.Saving.PdfSaveOptions();
                                doc.Save(outputFile, pdfOptions);
                            }
                            else if (saveFormat == AW.SaveFormat.Html || saveFormat == AW.SaveFormat.Mhtml)
                            {
                                // HTML格式需要使用HtmlSaveOptions
                                var htmlOptions = new AW.Saving.HtmlSaveOptions();
                                doc.Save(outputFile, htmlOptions);
                            }
                            else if (saveFormat == AW.SaveFormat.Text)
                            {
                                // 文本格式需要使用TxtSaveOptions
                                var txtOptions = new AW.Saving.TxtSaveOptions();
                                doc.Save(outputFile, txtOptions);
                            }
                            else
                            {
                                // 其他Word文档格式使用DocSaveOptions
                                try
                                {
                                    // 尝试使用DocSaveOptions
                                    var saveOptions = new AW.Saving.DocSaveOptions(saveFormat);
                                    doc.Save(outputFile, saveOptions);
                                }
                                catch (ArgumentException)
                                {
                                    // 如果DocSaveOptions不支持，退回到通用保存方法
                                    logger.Log($"无法使用DocSaveOptions保存为{saveFormat}格式，使用默认保存方法");
                                    doc.Save(outputFile, saveFormat);
                                }
                            }
                        }, cancellationToken);

                        logger.Log($"文档已保存: {outputFile}");
                    }

                    // 记录处理完成时的内存使用情况
                    _memoryMonitor.LogMemoryUsage($"处理文件 {fileName} 完成");

                    // 记录处理时间
                    stopwatch.Stop();
                    logger.Log($"文件处理完成，耗时: {stopwatch.ElapsedMilliseconds}ms");

                    // 处理成功，增加计数器
                    // 无论是TXT转换的文件还是普通文件，都应该计数
                    // 因为对用户来说，一个文件（无论是TXT还是Word）被完整处理了
                    Interlocked.Increment(ref _completedFiles);

                    if (isConvertedFromTxt)
                    {
                        logger.Log($"TXT文件转换并处理完成: {originalFilePath} -> {fileName}");
                    }
                    else
                    {
                        logger.Log($"文件处理完成: {fileName}");
                    }

                    // 如果是移动模式且不是处理原文件，删除源文件
                    if (settings.MoveFiles && !settings.ProcessOriginalFiles)
                    {
                        try
                        {
                            // 如果是从TXT转换来的文件，原始TXT文件在转换时已经被删除了
                            // 这里需要删除的是转换后的临时Word文件（如果它不在目标目录中）
                            if (isConvertedFromTxt)
                            {
                                // 对于从TXT转换来的文件，检查转换后的文件是否需要删除
                                // 如果转换后的文件在源目录中（临时文件），则删除它
                                if (filePath != originalFilePath && File.Exists(filePath) &&
                                    !filePath.StartsWith(outputDirectory, StringComparison.OrdinalIgnoreCase))
                                {
                                    File.Delete(filePath);
                                    logger.Log($"移动模式：已删除TXT转换后的临时Word文件 {filePath}");
                                }
                            }
                            else
                            {
                                // 对于普通文件，删除原始源文件
                                File.Delete(originalFilePath);
                                logger.Log($"移动模式：已删除源文件 {originalFilePath}");
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogWarning($"移动模式：删除源文件失败 {originalFilePath}, 错误: {ex.Message}");
                        }
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    logger.LogError($"处理文件 {filePath} 出错: {ex.Message}", ex);
                    LogDetailedError(filePath, ex);
                    OnErrorOccurred(filePath, ex);

                    // 记录内存使用情况
                    _memoryMonitor.LogMemoryUsage($"处理文件 {fileName} 出错");

                    // 处理失败，增加失败计数器
                    Interlocked.Increment(ref _failedFiles);

                    return false;
                }
                finally
                {
                    // 确保正确释放文档资源
                    if (doc != null)
                    {
                        await Task.Run(() =>
                        {
                            try
                            {
                                // 释放资源前清理内存
                                GC.Collect();
                                GC.WaitForPendingFinalizers();

                                // 手动清理doc避免资源泄漏
                                doc = null;
                            }
                            catch (Exception ex)
                            {
                                logger.LogWarning($"释放文档资源出错: {ex.Message}");
                            }
                        });
                    }

                    // 强制进行垃圾回收
                    if (_completedFiles % 10 == 0) // 每处理10个文件进行一次垃圾回收
                    {
                        await Task.Run(() =>
                        {
                            GC.Collect();
                            GC.WaitForPendingFinalizers();
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"处理文件 {filePath} 出错: {ex.Message}", ex);
                OnErrorOccurred(filePath, ex);
                return false;
            }
        }

        private void ProcessParagraphs(AW.Node node, Models.Settings settings)
        {
            if (node == null)
                return;

            // 确保段落匹配规则功能已启用
            if (!settings.EnableParagraphMatch)
                return;

            // 如果是文档节点，需要特殊处理以支持段落位置匹配
            if (node.NodeType == AW.NodeType.Document)
            {
                var doc = (AW.Document)node;
                ProcessDocumentParagraphs(doc, settings);
                return;
            }

            if (node is AW.Paragraph paragraph)
            {
                foreach (var rule in settings.ParagraphMatchRules)
                {
                    // 跳过禁用的规则
                    if (!rule.IsEnabled)
                        continue;

                    // 跳过使用段落位置匹配的规则，这些规则在ProcessDocumentParagraphs中处理
                    if (rule.UseParagraphPosition)
                        continue;

                    if (rule.IsMatch(paragraph.GetText()))
                    {
                        // 应用样式到段落，ApplyToParagraph方法会根据格式应用范围设置来决定如何应用格式
                        rule.ApplyToParagraph(paragraph);
                    }
                }
            }

            // 递归处理所有子节点
            if (node is AW.CompositeNode composite)
            {
                // 创建子节点的副本以防止集合在迭代时发生修改
                var childNodes = composite.GetChildNodes(AW.NodeType.Any, false).ToArray();
                foreach (var childNode in childNodes)
                {
                    ProcessParagraphs(childNode, settings);
                }
            }
        }

        private void ProcessDocumentParagraphs(AW.Document doc, Models.Settings settings)
        {
            // 获取文档中所有的段落
            var allParagraphs = doc.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();

            logger.Log($"文档共有 {allParagraphs.Count} 个段落");

            // 处理段落匹配规则
            foreach (var rule in settings.ParagraphMatchRules)
            {
                // 跳过禁用的规则
                if (!rule.IsEnabled)
                    continue;

                // 检查规则是否包含段落位置匹配条件
                bool hasPositionCondition = rule.ConditionItems.Any(item => item.Type == Models.ConditionType.ParagraphPosition) || rule.UseParagraphPosition;

                if (hasPositionCondition)
                {
                    // 如果规则包含段落位置匹配，需要遍历所有段落并传入位置信息
                    for (int i = 0; i < allParagraphs.Count; i++)
                    {
                        var paragraph = allParagraphs[i];
                        int paragraphPosition = i + 1; // 从1开始计数

                        bool isMatch = false;

                        // 如果使用旧的段落位置匹配方式
                        if (rule.UseParagraphPosition && rule.ConditionItems.Count == 0)
                        {
                            isMatch = (paragraphPosition == rule.ParagraphPosition);
                        }
                        else
                        {
                            // 使用新的条件组合匹配方式
                            isMatch = rule.Match(paragraph, paragraphPosition);
                        }

                        if (isMatch)
                        {
                            logger.Log($"应用规则 '{rule.Name}' 到第 {paragraphPosition} 段");
                            rule.ApplyToParagraph(paragraph);
                        }
                    }
                }
                else
                {
                    // 处理基于内容的匹配规则（不包含段落位置条件）
                    foreach (var paragraph in allParagraphs)
                    {
                        if (rule.IsMatch(paragraph.GetText()))
                        {
                            rule.ApplyToParagraph(paragraph);
                        }
                    }
                }

                // 处理图片格式和水印
                if (rule.EnableImageFormat || rule.EnableWatermark)
                {
                    ApplyImageFormat(doc, rule);
                }

                // 处理表格格式
                if (rule.EnableTableAutoFit || rule.EnableTableLayout || rule.EnableTableStyle || rule.EnableCellFormat)
                {
                    rule.ApplyTableFormatToDocument(doc);
                }
            }
        }

        private string ApplyFileNameRules(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return fileName;

            var result = fileName;
            foreach (var rule in settings.FileNameReplaceRules.Where(r => r.IsEnabled))
            {
                try
                {
                    result = rule.ApplyTo(result);
                }
                catch (Exception ex)
                {
                    logger.LogError($"应用文件名替换规则时出错: {ex.Message}", ex);
                }
            }

            // 确保文件名非空且有效
            if (string.IsNullOrWhiteSpace(result))
            {
                logger.LogWarning("替换后的文件名为空，使用原始文件名");
                return fileName;
            }

            return result;
        }

        private string GetUniqueFileName(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new ArgumentNullException(nameof(filePath));
            }

            var directory = Path.GetDirectoryName(filePath) ?? throw new InvalidOperationException("Unable to get directory name from file path");
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            var extension = Path.GetExtension(filePath);
            var counter = 1;

            do
            {
                var newFileName = $"{fileName}_{counter}{extension}";
                filePath = Path.Combine(directory, newFileName);
                counter++;
            } while (File.Exists(filePath));

            return filePath;
        }

        private List<string> GetWordFiles(string directory)
        {
            if (string.IsNullOrEmpty(directory))
            {
                throw new ArgumentNullException(nameof(directory));
            }

            logger.Log($"开始搜索文件：目录 {directory}，包含子目录: {settings.IncludeSubdirectories}");

            var files = new List<string>();
            var searchOption = settings.IncludeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;

            try
            {
                // 检查目录是否存在
                if (!Directory.Exists(directory))
                {
                    logger.LogError($"目录不存在: {directory}", new Exception("目录不存在"));
                    return files;
                }

                // 获取启用的文档格式
                var documentFormats = SettingsManager.GetDocumentFormats();
                var enabledFormats = documentFormats
                    .Where(kv => kv.Value) // 只选择启用的格式
                    .Select(kv => "*" + kv.Key) // 转换为搜索模式，如 "*.docx"
                    .ToList();

                if (enabledFormats.Count == 0)
                {
                    logger.Log("没有启用的文档格式，使用默认格式: *.docx;*.doc");
                    enabledFormats.Add("*.docx");
                    enabledFormats.Add("*.doc");
                }

                logger.Log($"使用启用的文档格式: {string.Join(";", enabledFormats)}");

                // 存储所有找到的文件，包括临时文件
                var allFiles = new List<string>();

                foreach (var pattern in enabledFormats)
                {
                    if (!string.IsNullOrWhiteSpace(pattern))
                    {
                        var trimmedPattern = pattern.Trim();
                        logger.Log($"搜索模式: {trimmedPattern}");

                        try
                        {
                            var foundFiles = Directory.GetFiles(directory, trimmedPattern, searchOption);
                            allFiles.AddRange(foundFiles);
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"搜索文件时发生错误，模式: {trimmedPattern}", ex);
                        }
                    }
                }

                // 过滤掉以~$开头的临时文件
                var filteredFiles = allFiles.Where(f => !Path.GetFileName(f).StartsWith("~$")).ToList();

                int tempFilesCount = allFiles.Count - filteredFiles.Count;
                logger.Log($"总共找到 {allFiles.Count} 个文件，过滤掉 {tempFilesCount} 个临时文件，剩余 {filteredFiles.Count} 个文件");

                // 如果没有找到文件，记录所有目录内容以便于调试
                if (filteredFiles.Count == 0)
                {
                    logger.Log("未找到有效文件，列出目录内容：");
                    try
                    {
                        var directoryFiles = Directory.GetFiles(directory, "*.*", searchOption);
                        foreach (var file in directoryFiles.Take(20)) // 最多显示20个文件
                        {
                            logger.Log($"  - {file}");
                        }

                        if (directoryFiles.Length > 20)
                        {
                            logger.Log($"  ... 以及其他 {directoryFiles.Length - 20} 个文件");
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("列出目录内容时发生错误", ex);
                    }
                }

                return filteredFiles;
            }
            catch (Exception ex)
            {
                logger.LogError($"搜索文件过程中发生异常: {ex.Message}", ex);
                return new List<string>();
            }
        }

        protected virtual void OnProgressChanged()
        {
            try
            {
                var completedFiles = _completedFiles;
                var failedFiles = _failedFiles;
                var retriedFiles = _retriedFiles;
                var totalFiles = _totalFiles;
                var progress = totalFiles > 0 ? (double)completedFiles / totalFiles * 100 : 0;

                logger.LogDebug($"进度更新: {completedFiles}/{totalFiles} ({progress:F1}%)");

                // 触发进度变更事件
                ProgressChanged?.Invoke(this, new ProgressEventArgs(progress, totalFiles, completedFiles, failedFiles, retriedFiles));
            }
            catch (Exception ex)
            {
                logger.LogError("触发进度变更事件时发生错误", ex);
            }
        }

        protected virtual void OnStatusChanged(string status)
        {
            try
            {
                logger.Log($"状态变更: {status}");

                // 触发状态变更事件
                StatusChanged?.Invoke(this, status);
            }
            catch (Exception ex)
            {
                logger.LogError($"触发状态变更事件时发生错误: {ex.Message}", ex);
            }
        }

        protected virtual void OnErrorOccurred(string filePath, Exception exception)
        {
            try
            {
                logger.LogError($"处理文件出错: {filePath}", exception);

                // 触发错误事件
                ErrorOccurred?.Invoke(this, new ErrorEventArgs(filePath, exception));
            }
            catch (Exception ex)
            {
                logger.LogError($"触发错误事件时发生错误: {ex.Message}", ex);
            }
        }

        private async Task ApplyHeaderFooterSettingsAsync(AW.Document doc, HeaderFooterSettings settings, CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                logger.Log("开始应用页眉页脚设置...");

                // 检查是否有任何页眉页脚设置被启用
                if (!HasAnyHeaderFooterSettingsEnabled(settings))
                {
                    logger.Log("没有启用任何页眉页脚设置，跳过处理");
                    return;
                }

                try
                {
                    // 创建文档构建器
                    AW.DocumentBuilder builder = new AW.DocumentBuilder(doc);

                    // 如果需要清除页眉或页脚，先处理所有节的页眉页脚
                    if (settings.RemoveHeader || settings.RemoveFooter || settings.RemoveHeaderFooter)
                    {
                        // 记录清除操作的日志
                        if (settings.RemoveHeaderFooter)
                            logger.Log("清除所有节的页眉页脚...");
                        else if (settings.RemoveHeader && settings.RemoveFooter)
                            logger.Log("清除所有节的页眉和页脚...");
                        else if (settings.RemoveHeader)
                            logger.Log("清除所有节的页眉...");
                        else if (settings.RemoveFooter)
                            logger.Log("清除所有节的页脚...");

                        try
                        {
                            // 遍历所有节
                            foreach (AW.Section section in doc.Sections)
                            {
                                int sectionIndex = doc.Sections.IndexOf(section) + 1;

                                // 清除页眉 - 只有在RemoveHeader或RemoveHeaderFooter为true时才清除页眉
                                if (settings.RemoveHeader || settings.RemoveHeaderFooter)
                                {
                                    logger.Log($"正在清除节 {sectionIndex} 的页眉...");
                                    try
                                    {
                                        // 获取该节的所有页眉
                                        var headersToRemove = new List<AW.HeaderFooter>();

                                        // 尝试获取各种类型的页眉
                                        try
                                        {
                                            var headerFirst = section.HeadersFooters[AW.HeaderFooterType.HeaderFirst];
                                            if (headerFirst != null)
                                                headersToRemove.Add(headerFirst);
                                        }
                                        catch (Exception ex)
                                        {
                                            logger.LogError($"获取首页页眉时出错: {ex.Message}", ex);
                                        }

                                        try
                                        {
                                            var headerPrimary = section.HeadersFooters[AW.HeaderFooterType.HeaderPrimary];
                                            if (headerPrimary != null)
                                                headersToRemove.Add(headerPrimary);
                                        }
                                        catch (Exception ex)
                                        {
                                            logger.LogError($"获取主页眉时出错: {ex.Message}", ex);
                                        }

                                        try
                                        {
                                            var headerEven = section.HeadersFooters[AW.HeaderFooterType.HeaderEven];
                                            if (headerEven != null)
                                                headersToRemove.Add(headerEven);
                                        }
                                        catch (Exception ex)
                                        {
                                            logger.LogError($"获取偶数页页眉时出错: {ex.Message}", ex);
                                        }

                                        // 移除所有页眉
                                        foreach (var header in headersToRemove)
                                        {
                                            try
                                            {
                                                string headerType = header.HeaderFooterType.ToString();
                                                logger.Log($"移除节 {sectionIndex} 的 {headerType}");
                                                header.Remove();
                                                logger.Log($"已成功移除节 {sectionIndex} 的 {headerType}");
                                            }
                                            catch (Exception ex)
                                            {
                                                logger.LogError($"移除页眉时出错: {ex.Message}", ex);
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogError($"清除节 {sectionIndex} 的页眉时出错: {ex.Message}", ex);
                                    }
                                }

                                // 清除页脚 - 只有在RemoveFooter或RemoveHeaderFooter为true时才清除页脚
                                if (settings.RemoveFooter || settings.RemoveHeaderFooter)
                                {
                                    logger.Log($"正在清除节 {sectionIndex} 的页脚...");
                                    try
                                    {
                                        // 获取该节的所有页脚
                                        var footersToRemove = new List<AW.HeaderFooter>();

                                        // 尝试获取各种类型的页脚
                                        try
                                        {
                                            var footerFirst = section.HeadersFooters[AW.HeaderFooterType.FooterFirst];
                                            if (footerFirst != null)
                                                footersToRemove.Add(footerFirst);
                                        }
                                        catch (Exception ex)
                                        {
                                            logger.LogError($"获取首页页脚时出错: {ex.Message}", ex);
                                        }

                                        try
                                        {
                                            var footerPrimary = section.HeadersFooters[AW.HeaderFooterType.FooterPrimary];
                                            if (footerPrimary != null)
                                                footersToRemove.Add(footerPrimary);
                                        }
                                        catch (Exception ex)
                                        {
                                            logger.LogError($"获取主页脚时出错: {ex.Message}", ex);
                                        }

                                        try
                                        {
                                            var footerEven = section.HeadersFooters[AW.HeaderFooterType.FooterEven];
                                            if (footerEven != null)
                                                footersToRemove.Add(footerEven);
                                        }
                                        catch (Exception ex)
                                        {
                                            logger.LogError($"获取偶数页页脚时出错: {ex.Message}", ex);
                                        }

                                        // 移除所有页脚
                                        foreach (var footer in footersToRemove)
                                        {
                                            try
                                            {
                                                string footerType = footer.HeaderFooterType.ToString();
                                                logger.Log($"移除节 {sectionIndex} 的 {footerType}");
                                                footer.Remove();
                                                logger.Log($"已成功移除节 {sectionIndex} 的 {footerType}");
                                            }
                                            catch (Exception ex)
                                            {
                                                logger.LogError($"移除页脚时出错: {ex.Message}", ex);
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogError($"清除节 {sectionIndex} 的页脚时出错: {ex.Message}", ex);
                                    }
                                }
                            }

                            // 记录清除操作完成的日志
                            if (settings.RemoveHeaderFooter)
                                logger.Log("已清除所有节的页眉页脚");
                            else if (settings.RemoveHeader && settings.RemoveFooter)
                                logger.Log("已清除所有节的页眉和页脚");
                            else if (settings.RemoveHeader)
                                logger.Log("已清除所有节的页眉");
                            else if (settings.RemoveFooter)
                                logger.Log("已清除所有节的页脚");
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"清除页眉页脚时出错: {ex.Message}", ex);
                        }

                        // 如果只是清除页眉页脚，不需要添加新的页眉页脚，则直接返回
                        // 只有在以下情况下才提前返回：
                        // 1. 同时清除页眉和页脚（RemoveHeaderFooter为true）
                        // 2. 同时勾选了清除页眉和清除页脚
                        // 3. 勾选了清除页眉，并且没有启用页眉设置
                        // 4. 勾选了清除页脚，并且没有启用页脚设置
                        if (settings.RemoveHeaderFooter ||
                            (settings.RemoveHeader && settings.RemoveFooter) ||
                            (settings.RemoveHeader && !settings.EnableHeader && !settings.EnableFooter) ||
                            (settings.RemoveFooter && !settings.EnableFooter && !settings.EnableHeader))
                        {
                            logger.Log("仅清除页眉页脚，不添加新内容");
                            return;
                        }

                        // 如果勾选了清除页眉但启用了页脚设置，或者勾选了清除页脚但启用了页眉设置，则继续执行
                    }

                    // 设置首页不同
                    foreach (AW.Section section in doc.Sections)
                    {
                        section.PageSetup.DifferentFirstPageHeaderFooter = settings.DifferentFirstPage;
                    }

                    // 在应用新的页眉页脚设置前，先清除要应用设置的页眉页脚
                    // 这样可以确保新内容完全覆盖旧内容
                    foreach (AW.Section section in doc.Sections)
                    {
                        try
                        {
                            // 如果启用了页眉设置，先清除原有页眉
                            if (settings.EnableHeader && !settings.RemoveHeader)
                            {
                                logger.Log($"清除节 {doc.Sections.IndexOf(section) + 1} 的原有页眉，准备应用新页眉设置");

                                // 清除主页眉
                                try
                                {
                                    var headerPrimary = section.HeadersFooters[AW.HeaderFooterType.HeaderPrimary];
                                    if (headerPrimary != null)
                                    {
                                        headerPrimary.RemoveAllChildren();
                                        logger.Log("已清除主页眉，准备应用新内容");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    logger.LogError($"清除主页眉时出错: {ex.Message}", ex);
                                }

                                // 只有当同时满足以下条件时，才清除首页页眉：
                                // 1. 设置了首页不同
                                // 2. 启用了页眉设置
                                // 3. 有页眉内容（文本、图片或页码）
                                if (settings.DifferentFirstPage && settings.EnableHeader &&
                                    (settings.HasHeader || settings.HasHeaderImage || settings.HasHeaderPageNumber))
                                {
                                    try
                                    {
                                        var headerFirst = section.HeadersFooters[AW.HeaderFooterType.HeaderFirst];
                                        if (headerFirst != null)
                                        {
                                            headerFirst.RemoveAllChildren();
                                            logger.Log("已清除首页页眉，准备应用新内容");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogError($"清除首页页眉时出错: {ex.Message}", ex);
                                    }
                                }
                            }

                            // 如果启用了页脚设置，先清除原有页脚
                            if (settings.EnableFooter && !settings.RemoveFooter)
                            {
                                logger.Log($"清除节 {doc.Sections.IndexOf(section) + 1} 的原有页脚，准备应用新页脚设置");

                                // 清除主页脚
                                try
                                {
                                    var footerPrimary = section.HeadersFooters[AW.HeaderFooterType.FooterPrimary];
                                    if (footerPrimary != null)
                                    {
                                        footerPrimary.RemoveAllChildren();
                                        logger.Log("已清除主页脚，准备应用新内容");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    logger.LogError($"清除主页脚时出错: {ex.Message}", ex);
                                }

                                // 只有当同时满足以下条件时，才清除首页页脚：
                                // 1. 设置了首页不同
                                // 2. 启用了页脚设置
                                // 3. 有页脚内容（文本、图片或页码）
                                if (settings.DifferentFirstPage && settings.EnableFooter &&
                                    (settings.HasFooter || settings.HasFooterImage || settings.HasFooterPageNumber))
                                {
                                    try
                                    {
                                        var footerFirst = section.HeadersFooters[AW.HeaderFooterType.FooterFirst];
                                        if (footerFirst != null)
                                        {
                                            footerFirst.RemoveAllChildren();
                                            logger.Log("已清除首页页脚，准备应用新内容");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogError($"清除首页页脚时出错: {ex.Message}", ex);
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"清除原有页眉页脚时出错: {ex.Message}", ex);
                        }
                    }

                    // 应用首页页眉 - 只有当同时满足以下条件时才应用首页页眉设置：
                    // 1. 设置了首页不同
                    // 2. 启用了页眉设置
                    // 3. 没有勾选清除页眉
                    // 4. 有页眉内容（文本、图片或页码）
                    if (settings.DifferentFirstPage && settings.EnableHeader && !settings.RemoveHeader &&
                        (settings.HasHeader || settings.HasHeaderImage || settings.HasHeaderPageNumber))
                    {
                        logger.Log("应用首页页眉设置");
                        ProcessFirstPageHeader(builder, settings);
                    }

                    // 应用首页页脚 - 只有当同时满足以下条件时才应用首页页脚设置：
                    // 1. 设置了首页不同
                    // 2. 启用了页脚设置
                    // 3. 没有勾选清除页脚
                    // 4. 有页脚内容（文本、图片或页码）
                    if (settings.DifferentFirstPage && settings.EnableFooter && !settings.RemoveFooter &&
                        (settings.HasFooter || settings.HasFooterImage || settings.HasFooterPageNumber))
                    {
                        logger.Log("应用首页页脚设置");
                        ProcessFirstPageFooter(builder, settings);
                    }

                    // 应用主页眉 - 只要启用了页眉设置并且没有勾选清除页眉，就应用页眉设置
                    if (settings.EnableHeader && !settings.RemoveHeader)
                    {
                        logger.Log("应用主页眉设置");
                        ProcessPrimaryHeader(builder, settings);
                    }

                    // 应用主页脚 - 只要启用了页脚设置并且没有勾选清除页脚，就应用页脚设置
                    if (settings.EnableFooter && !settings.RemoveFooter)
                    {
                        logger.Log("应用主页脚设置");
                        ProcessPrimaryFooter(builder, settings);
                    }

                    // 更新字段
                    doc.UpdateFields();

                    logger.Log("页眉页脚设置已应用");
                }
                catch (Exception ex)
                {
                    logger.LogError($"应用页眉页脚设置时出错: {ex.Message}", ex);
                }
            }, cancellationToken);
        }

        // 新增方法：处理主页面页眉
        private void ProcessPrimaryHeader(AW.DocumentBuilder builder, HeaderFooterSettings settings)
        {
            try
            {

                // 添加主页面页眉
                if (settings.EnableHeader &&
                   (settings.HasHeader || settings.HasHeaderImage || settings.HasHeaderPageNumber))
                {
                    builder.MoveToHeaderFooter(AW.HeaderFooterType.HeaderPrimary);

                    // 设置对齐方式
                    builder.ParagraphFormat.Alignment = settings.HeaderAlignment;

                    // 设置字体
                    builder.Font.Name = settings.FontName;
                    builder.Font.Size = settings.FontSize;
                    builder.Font.Bold = settings.IsBold;
                    builder.Font.Italic = settings.IsItalic;
                    builder.Font.Color = System.Drawing.Color.FromArgb(
                        settings.FontColor.A,
                        settings.FontColor.R,
                        settings.FontColor.G,
                        settings.FontColor.B);

                    // 设置文字背景色
                    if (settings.HeaderBackgroundColor.HasValue && settings.HeaderBackgroundColor.Value != System.Drawing.Color.Transparent)
                    {
                        builder.Font.Shading.BackgroundPatternColor = System.Drawing.Color.FromArgb(
                            settings.HeaderBackgroundColor.Value.A,
                            settings.HeaderBackgroundColor.Value.R,
                            settings.HeaderBackgroundColor.Value.G,
                            settings.HeaderBackgroundColor.Value.B);
                    }
                    else
                    {
                        builder.Font.Shading.ClearFormatting();
                    }

                    if (settings.HasHeaderImage && settings.HeaderImageData != null)
                    {
                        try
                        {
                            // 从字节数组创建图片并插入
                            using (var ms = new System.IO.MemoryStream(settings.HeaderImageData))
                            {
                                // 使用Shape对象插入图片，以便更好地控制图片属性
                                AW.Drawing.Shape shape = builder.InsertImage(ms);

                                // 设置图片大小限制
                                double maxWidth = 450; // 最大宽度(点)
                                double maxHeight = 80; // 最大高度(点)

                                // 保持宽高比例调整大小
                                if (shape.Width > maxWidth || shape.Height > maxHeight)
                                {
                                    double widthRatio = maxWidth / shape.Width;
                                    double heightRatio = maxHeight / shape.Height;
                                    double ratio = Math.Min(widthRatio, heightRatio);

                                    shape.Width = shape.Width * ratio;
                                    shape.Height = shape.Height * ratio;
                                }

                                // 设置图片位置
                                shape.WrapType = AW.Drawing.WrapType.Inline;

                                // 根据对齐方式设置水平位置
                                switch (settings.HeaderAlignment)
                                {
                                    case AW.ParagraphAlignment.Left:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Left;
                                        break;
                                    case AW.ParagraphAlignment.Right:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Right;
                                        break;
                                    default:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Center;
                                        break;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"插入页眉图片时出错: {ex.Message}", ex);
                        }
                    }
                    else if (settings.HasHeader)
                    {
                        // 插入文本
                        builder.Write(settings.HeaderText);
                    }
                    else if (settings.HasHeaderPageNumber)
                    {
                        try
                        {
                            // 插入页码
                            // 设置页码格式
                            string numFormat = "";
                            switch (settings.HeaderPageNumberFormat)
                            {
                                case 1: // 罗马数字大写
                                    numFormat = "I, II, III, ...";
                                    break;
                                case 2: // 罗马数字小写
                                    numFormat = "i, ii, iii, ...";
                                    break;
                                case 3: // 字母大写
                                    numFormat = "A, B, C, ...";
                                    break;
                                case 4: // 字母小写
                                    numFormat = "a, b, c, ...";
                                    break;
                                default: // 数字
                                    numFormat = "1, 2, 3, ...";
                                    break;
                            }

                            // 插入前缀
                            if (!string.IsNullOrEmpty(settings.HeaderPageNumberPrefix))
                            {
                                builder.Write(settings.HeaderPageNumberPrefix);
                            }

                            // 插入页码字段并设置格式
                            AW.Fields.Field pageField = builder.InsertField("PAGE", "");

                            // 如果需要包含总页数
                            if (settings.HeaderIncludePageCount)
                            {
                                builder.Write(" / ");
                                AW.Fields.Field numPagesField = builder.InsertField("NUMPAGES", "");
                            }

                            // 插入后缀
                            if (!string.IsNullOrEmpty(settings.HeaderPageNumberSuffix))
                            {
                                builder.Write(settings.HeaderPageNumberSuffix);
                            }

                            // 设置页码格式
                            if (pageField != null && pageField.Start != null)
                            {
                                AW.Fields.FieldStart fieldStart = pageField.Start;
                                if (fieldStart.FieldType == AW.Fields.FieldType.FieldPage)
                                {
                                    // 设置数字格式
                                    AW.Run fieldCodeRun = (AW.Run)fieldStart.NextSibling;
                                    if (fieldCodeRun != null)
                                    {
                                        fieldCodeRun.Text = "PAGE \\* " + numFormat;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"插入页眉页码时出错: {ex.Message}", ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"处理主页面页眉时出错: {ex.Message}", ex);
            }
        }

        // 新增方法：处理首页页眉
        private void ProcessFirstPageHeader(AW.DocumentBuilder builder, HeaderFooterSettings settings)
        {
            try
            {

                // 添加首页页眉 - 只有当启用了页眉设置并且有内容时才添加
                if (settings.EnableHeader && settings.DifferentFirstPage &&
                   (settings.HasHeader || settings.HasHeaderImage || settings.HasHeaderPageNumber))
                {
                    builder.MoveToHeaderFooter(AW.HeaderFooterType.HeaderFirst);

                    // 设置对齐方式
                    builder.ParagraphFormat.Alignment = settings.HeaderAlignment;

                    // 设置字体
                    builder.Font.Name = settings.FontName;
                    builder.Font.Size = settings.FontSize;
                    builder.Font.Bold = settings.IsBold;
                    builder.Font.Italic = settings.IsItalic;
                    builder.Font.Color = System.Drawing.Color.FromArgb(
                        settings.FontColor.A,
                        settings.FontColor.R,
                        settings.FontColor.G,
                        settings.FontColor.B);

                    // 设置文字背景色
                    if (settings.HeaderBackgroundColor.HasValue && settings.HeaderBackgroundColor.Value != System.Drawing.Color.Transparent)
                    {
                        builder.Font.Shading.BackgroundPatternColor = System.Drawing.Color.FromArgb(
                            settings.HeaderBackgroundColor.Value.A,
                            settings.HeaderBackgroundColor.Value.R,
                            settings.HeaderBackgroundColor.Value.G,
                            settings.HeaderBackgroundColor.Value.B);
                    }
                    else
                    {
                        builder.Font.Shading.ClearFormatting();
                    }

                    if (settings.HasHeaderImage && settings.HeaderImageData != null)
                    {
                        try
                        {
                            // 从字节数组创建图片并插入
                            using (var ms = new System.IO.MemoryStream(settings.HeaderImageData))
                            {
                                // 使用Shape对象插入图片，以便更好地控制图片属性
                                AW.Drawing.Shape shape = builder.InsertImage(ms);

                                // 设置图片大小限制
                                double maxWidth = 450; // 最大宽度(点)
                                double maxHeight = 80; // 最大高度(点)

                                // 保持宽高比例调整大小
                                if (shape.Width > maxWidth || shape.Height > maxHeight)
                                {
                                    double widthRatio = maxWidth / shape.Width;
                                    double heightRatio = maxHeight / shape.Height;
                                    double ratio = Math.Min(widthRatio, heightRatio);

                                    shape.Width = shape.Width * ratio;
                                    shape.Height = shape.Height * ratio;
                                }

                                // 设置图片位置
                                shape.WrapType = AW.Drawing.WrapType.Inline;

                                // 根据对齐方式设置水平位置
                                switch (settings.HeaderAlignment)
                                {
                                    case AW.ParagraphAlignment.Left:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Left;
                                        break;
                                    case AW.ParagraphAlignment.Right:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Right;
                                        break;
                                    default:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Center;
                                        break;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"插入首页页眉图片时出错: {ex.Message}", ex);
                        }
                    }
                    else if (settings.HasHeader)
                    {
                        // 插入文本
                        builder.Write(settings.HeaderText);
                    }
                    else if (settings.HasHeaderPageNumber)
                    {
                        try
                        {
                            // 首页页码可以选择不插入或使用特殊格式
                            // 设置页码格式
                            string numFormat = "";
                            switch (settings.HeaderPageNumberFormat)
                            {
                                case 1: // 罗马数字大写
                                    numFormat = "I, II, III, ...";
                                    break;
                                case 2: // 罗马数字小写
                                    numFormat = "i, ii, iii, ...";
                                    break;
                                case 3: // 字母大写
                                    numFormat = "A, B, C, ...";
                                    break;
                                case 4: // 字母小写
                                    numFormat = "a, b, c, ...";
                                    break;
                                default: // 数字
                                    numFormat = "1, 2, 3, ...";
                                    break;
                            }

                            // 插入前缀
                            if (!string.IsNullOrEmpty(settings.HeaderPageNumberPrefix))
                            {
                                builder.Write(settings.HeaderPageNumberPrefix);
                            }

                            // 插入页码字段并设置格式
                            AW.Fields.Field pageField = builder.InsertField("PAGE", "");

                            // 如果需要包含总页数
                            if (settings.HeaderIncludePageCount)
                            {
                                builder.Write(" / ");
                                AW.Fields.Field numPagesField = builder.InsertField("NUMPAGES", "");
                            }

                            // 插入后缀
                            if (!string.IsNullOrEmpty(settings.HeaderPageNumberSuffix))
                            {
                                builder.Write(settings.HeaderPageNumberSuffix);
                            }

                            // 设置页码格式
                            if (pageField != null && pageField.Start != null)
                            {
                                AW.Fields.FieldStart fieldStart = pageField.Start;
                                if (fieldStart.FieldType == AW.Fields.FieldType.FieldPage)
                                {
                                    // 设置数字格式
                                    AW.Run fieldCodeRun = (AW.Run)fieldStart.NextSibling;
                                    if (fieldCodeRun != null)
                                    {
                                        fieldCodeRun.Text = "PAGE \\* " + numFormat;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"插入首页页眉页码时出错: {ex.Message}", ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"处理首页页眉时出错: {ex.Message}", ex);
            }
        }

        // 新增方法：处理主页面页脚
        private void ProcessPrimaryFooter(AW.DocumentBuilder builder, HeaderFooterSettings settings)
        {
            try
            {

                // 添加主页面页脚
                if (settings.EnableFooter &&
                   (settings.HasFooter || settings.HasFooterImage || settings.HasFooterPageNumber))
                {
                    builder.MoveToHeaderFooter(AW.HeaderFooterType.FooterPrimary);

                    // 设置对齐方式
                    builder.ParagraphFormat.Alignment = settings.FooterAlignment;

                    // 设置字体
                    builder.Font.Name = settings.FooterFontName;
                    builder.Font.Size = settings.FooterFontSize;
                    builder.Font.Bold = settings.FooterIsBold;
                    builder.Font.Italic = settings.FooterIsItalic;
                    builder.Font.Color = System.Drawing.Color.FromArgb(
                        settings.FooterFontColor.A,
                        settings.FooterFontColor.R,
                        settings.FooterFontColor.G,
                        settings.FooterFontColor.B);

                    // 设置文字背景色
                    if (settings.FooterBackgroundColor.HasValue && settings.FooterBackgroundColor.Value != System.Drawing.Color.Transparent)
                    {
                        builder.Font.Shading.BackgroundPatternColor = System.Drawing.Color.FromArgb(
                            settings.FooterBackgroundColor.Value.A,
                            settings.FooterBackgroundColor.Value.R,
                            settings.FooterBackgroundColor.Value.G,
                            settings.FooterBackgroundColor.Value.B);
                    }
                    else
                    {
                        builder.Font.Shading.ClearFormatting();
                    }

                    if (settings.HasFooterImage && settings.FooterImageData != null)
                    {
                        try
                        {
                            // 从字节数组创建图片并插入
                            using (var ms = new System.IO.MemoryStream(settings.FooterImageData))
                            {
                                // 使用Shape对象插入图片，以便更好地控制图片属性
                                AW.Drawing.Shape shape = builder.InsertImage(ms);

                                // 设置图片大小限制
                                double maxWidth = 450; // 最大宽度(点)
                                double maxHeight = 80; // 最大高度(点)

                                // 保持宽高比例调整大小
                                if (shape.Width > maxWidth || shape.Height > maxHeight)
                                {
                                    double widthRatio = maxWidth / shape.Width;
                                    double heightRatio = maxHeight / shape.Height;
                                    double ratio = Math.Min(widthRatio, heightRatio);

                                    shape.Width = shape.Width * ratio;
                                    shape.Height = shape.Height * ratio;
                                }

                                // 设置图片位置
                                shape.WrapType = AW.Drawing.WrapType.Inline;

                                // 根据对齐方式设置水平位置
                                switch (settings.FooterAlignment)
                                {
                                    case AW.ParagraphAlignment.Left:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Left;
                                        break;
                                    case AW.ParagraphAlignment.Right:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Right;
                                        break;
                                    default:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Center;
                                        break;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"插入页脚图片时出错: {ex.Message}", ex);
                        }
                    }
                    else if (settings.HasFooter)
                    {
                        // 插入文本
                        builder.Write(settings.FooterText);
                    }
                    else if (settings.HasFooterPageNumber)
                    {
                        try
                        {
                            // 插入页码
                            // 设置页码格式
                            string numFormat = "";
                            switch (settings.FooterPageNumberFormat)
                            {
                                case 1: // 罗马数字大写
                                    numFormat = "I, II, III, ...";
                                    break;
                                case 2: // 罗马数字小写
                                    numFormat = "i, ii, iii, ...";
                                    break;
                                case 3: // 字母大写
                                    numFormat = "A, B, C, ...";
                                    break;
                                case 4: // 字母小写
                                    numFormat = "a, b, c, ...";
                                    break;
                                default: // 数字
                                    numFormat = "1, 2, 3, ...";
                                    break;
                            }

                            // 插入前缀
                            if (!string.IsNullOrEmpty(settings.FooterPageNumberPrefix))
                            {
                                builder.Write(settings.FooterPageNumberPrefix);
                            }

                            // 插入页码字段并设置格式
                            AW.Fields.Field pageField = builder.InsertField("PAGE", "");

                            // 如果需要包含总页数
                            if (settings.FooterIncludePageCount)
                            {
                                builder.Write(" / ");
                                AW.Fields.Field numPagesField = builder.InsertField("NUMPAGES", "");
                            }

                            // 插入后缀
                            if (!string.IsNullOrEmpty(settings.FooterPageNumberSuffix))
                            {
                                builder.Write(settings.FooterPageNumberSuffix);
                            }

                            // 设置页码格式
                            if (pageField != null && pageField.Start != null)
                            {
                                AW.Fields.FieldStart fieldStart = pageField.Start;
                                if (fieldStart.FieldType == AW.Fields.FieldType.FieldPage)
                                {
                                    // 设置数字格式
                                    AW.Run fieldCodeRun = (AW.Run)fieldStart.NextSibling;
                                    if (fieldCodeRun != null)
                                    {
                                        fieldCodeRun.Text = "PAGE \\* " + numFormat;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"插入页脚页码时出错: {ex.Message}", ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"处理主页面页脚时出错: {ex.Message}", ex);
            }
        }

        // 新增方法：处理首页页脚
        private void ProcessFirstPageFooter(AW.DocumentBuilder builder, HeaderFooterSettings settings)
        {
            try
            {

                // 添加首页页脚 - 只有当启用了页脚设置并且有内容时才添加
                if (settings.EnableFooter && settings.DifferentFirstPage &&
                   (settings.HasFooter || settings.HasFooterImage || settings.HasFooterPageNumber))
                {
                    builder.MoveToHeaderFooter(AW.HeaderFooterType.FooterFirst);

                    // 设置对齐方式
                    builder.ParagraphFormat.Alignment = settings.FooterAlignment;

                    // 设置字体
                    builder.Font.Name = settings.FooterFontName;
                    builder.Font.Size = settings.FooterFontSize;
                    builder.Font.Bold = settings.FooterIsBold;
                    builder.Font.Italic = settings.FooterIsItalic;
                    builder.Font.Color = System.Drawing.Color.FromArgb(
                        settings.FooterFontColor.A,
                        settings.FooterFontColor.R,
                        settings.FooterFontColor.G,
                        settings.FooterFontColor.B);

                    // 设置文字背景色
                    if (settings.FooterBackgroundColor.HasValue && settings.FooterBackgroundColor.Value != System.Drawing.Color.Transparent)
                    {
                        builder.Font.Shading.BackgroundPatternColor = System.Drawing.Color.FromArgb(
                            settings.FooterBackgroundColor.Value.A,
                            settings.FooterBackgroundColor.Value.R,
                            settings.FooterBackgroundColor.Value.G,
                            settings.FooterBackgroundColor.Value.B);
                    }
                    else
                    {
                        builder.Font.Shading.ClearFormatting();
                    }

                    if (settings.HasFooterImage && settings.FooterImageData != null)
                    {
                        try
                        {
                            // 从字节数组创建图片并插入
                            using (var ms = new System.IO.MemoryStream(settings.FooterImageData))
                            {
                                // 使用Shape对象插入图片，以便更好地控制图片属性
                                AW.Drawing.Shape shape = builder.InsertImage(ms);

                                // 设置图片大小限制
                                double maxWidth = 450; // 最大宽度(点)
                                double maxHeight = 80; // 最大高度(点)

                                // 保持宽高比例调整大小
                                if (shape.Width > maxWidth || shape.Height > maxHeight)
                                {
                                    double widthRatio = maxWidth / shape.Width;
                                    double heightRatio = maxHeight / shape.Height;
                                    double ratio = Math.Min(widthRatio, heightRatio);

                                    shape.Width = shape.Width * ratio;
                                    shape.Height = shape.Height * ratio;
                                }

                                // 设置图片位置
                                shape.WrapType = AW.Drawing.WrapType.Inline;

                                // 根据对齐方式设置水平位置
                                switch (settings.FooterAlignment)
                                {
                                    case AW.ParagraphAlignment.Left:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Left;
                                        break;
                                    case AW.ParagraphAlignment.Right:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Right;
                                        break;
                                    default:
                                        shape.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Center;
                                        break;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"插入首页页脚图片时出错: {ex.Message}", ex);
                        }
                    }
                    else if (settings.HasFooter)
                    {
                        // 插入文本
                        builder.Write(settings.FooterText);
                    }
                    else if (settings.HasFooterPageNumber)
                    {
                        try
                        {
                            // 首页页码可以选择不插入或使用特殊格式
                            // 设置页码格式
                            string numFormat = "";
                            switch (settings.FooterPageNumberFormat)
                            {
                                case 1: // 罗马数字大写
                                    numFormat = "I, II, III, ...";
                                    break;
                                case 2: // 罗马数字小写
                                    numFormat = "i, ii, iii, ...";
                                    break;
                                case 3: // 字母大写
                                    numFormat = "A, B, C, ...";
                                    break;
                                case 4: // 字母小写
                                    numFormat = "a, b, c, ...";
                                    break;
                                default: // 数字
                                    numFormat = "1, 2, 3, ...";
                                    break;
                            }

                            // 插入前缀
                            if (!string.IsNullOrEmpty(settings.FooterPageNumberPrefix))
                            {
                                builder.Write(settings.FooterPageNumberPrefix);
                            }

                            // 插入页码字段并设置格式
                            AW.Fields.Field pageField = builder.InsertField("PAGE", "");

                            // 如果需要包含总页数
                            if (settings.FooterIncludePageCount)
                            {
                                builder.Write(" / ");
                                AW.Fields.Field numPagesField = builder.InsertField("NUMPAGES", "");
                            }

                            // 插入后缀
                            if (!string.IsNullOrEmpty(settings.FooterPageNumberSuffix))
                            {
                                builder.Write(settings.FooterPageNumberSuffix);
                            }

                            // 设置页码格式
                            if (pageField != null && pageField.Start != null)
                            {
                                AW.Fields.FieldStart fieldStart = pageField.Start;
                                if (fieldStart.FieldType == AW.Fields.FieldType.FieldPage)
                                {
                                    // 设置数字格式
                                    AW.Run fieldCodeRun = (AW.Run)fieldStart.NextSibling;
                                    if (fieldCodeRun != null)
                                    {
                                        fieldCodeRun.Text = "PAGE \\* " + numFormat;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"插入首页页脚页码时出错: {ex.Message}", ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"处理首页页脚时出错: {ex.Message}", ex);
            }
        }

        private async Task ApplyDeleteSettingsAsync(AW.Document doc, DeleteSettings settings, CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                logger.Log("开始应用删除内容设置...");

                // 检查是否有任何删除内容设置被启用
                if (!HasAnyDeleteSettingsEnabled(settings))
                {
                    logger.Log("没有启用任何删除内容设置，跳过处理");
                    return;
                }

                // 删除文档保护 - 检查是否有任何文档保护设置被启用
                if (HasDocumentProtectionSettingsEnabled(settings))
                {
                    RemoveDocumentProtection(doc, settings);
                }

                // 处理段落空格和不可见字符 - 检查是否有任何相关设置被启用
                if (HasParagraphSpacesSettingsEnabled(settings))
                {
                    HandleParagraphSpacesAndInvisibleChars(doc, settings);
                }

                // 处理断行和段落标记 - 检查是否有任何相关设置被启用
                if (HasLineBreakSettingsEnabled(settings))
                {
                    HandleLineBreaksAndParagraphMarks(doc, settings);
                }

                // 检查图片删除标签页是否有任何设置被启用
                if (HasImageDeletionSettingsEnabled(settings))
                {
                    DeleteImages(doc, settings);
                }

                // 删除表格 - 仅当启用了相关设置时
                if (settings.DeleteTablesWithText && settings.SpecificTextList.Count > 0)
                {
                    var tables = doc.GetChildNodes(AW.NodeType.Table, true);
                    foreach (AW.Tables.Table table in tables)
                    {
                        table.Remove();
                    }
                    logger.Log("已删除表格");
                }

                // 删除图形对象
                if (settings.DeleteShapes)
                {
                    var shapes = doc.GetChildNodes(AW.NodeType.Shape, true);
                    foreach (AW.Drawing.Shape shape in shapes)
                    {
                        shape.Remove();
                    }
                    logger.Log("已删除图形对象");
                }

                // 删除SmartArt
                if (settings.DeleteSmartArt)
                {
                    var smartArts = doc.GetChildNodes(AW.NodeType.SmartTag, true);
                    foreach (AW.Node smartArt in smartArts)
                    {
                        smartArt.Remove();
                    }
                    logger.Log("已删除SmartArt");
                }

                // 删除评论
                if (settings.DeleteComments)
                {
                    // 正确处理评论集合
                    foreach (var comment in doc.GetChildNodes(AW.NodeType.Comment, true))
                    {
                        comment.Remove();
                    }
                    logger.Log("已删除评论");
                }

                // 删除书签
                if (settings.DeleteBookmarks)
                {
                    doc.Range.Bookmarks.Clear();
                    logger.Log("已删除书签");
                }

                // 删除超链接
                if (settings.DeleteHyperlinks)
                {
                    DeleteHyperlinks(doc);
                }

                // 删除水印
                if (settings.DeleteWatermarks)
                {
                    DeleteWatermarks(doc);
                }

                // 检查联系方式删除标签页是否有任何设置被启用
                if (HasContactInfoDeletionSettingsEnabled(settings))
                {
                    // 删除联系方式相关内容
                    if (settings.DeleteMobileNumbers)
                    {
                        DeleteMobileNumbers(doc);
                    }

                    if (settings.DeletePhoneNumbers)
                    {
                        DeletePhoneNumbers(doc);
                    }

                    if (settings.DeleteEmails)
                    {
                        DeleteEmails(doc);
                    }

                    if (settings.DeleteUrls)
                    {
                        DeleteUrls(doc);
                    }
                }

                // 检查特定文本删除标签页是否有任何设置被启用
                if (HasSpecificTextDeletionSettingsEnabled(settings))
                {
                    DeleteContentWithText(doc, settings);
                }

                // 检查空白内容删除标签页是否有任何设置被启用
                if (HasEmptyContentDeletionSettingsEnabled(settings))
                {
                    DeleteEmptyContent(doc, settings);
                }

                // 检查格式删除标签页是否有任何设置被启用
                if (settings.DeleteFormats)
                {
                    DeleteFormats(doc, settings);
                }

                // 删除文档元素 - 方法内部已有标签页级别检查
                DeleteDocumentElements(doc, settings);

                // 删除高级格式 - 方法内部已有标签页级别检查
                DeleteAdvancedFormats(doc, settings);

                // 删除文档结构 - 方法内部已有标签页级别检查
                DeleteDocumentStructure(doc, settings);

                // 检查文档属性删除标签页是否有任何设置被启用
                if (HasDocumentPropertiesDeletionSettingsEnabled(settings))
                {
                    DeleteDocumentProperties(doc, settings);
                }

                logger.Log("删除内容设置已应用");
            }, cancellationToken);
        }

        // 检查是否有任何删除内容设置被启用
        private bool HasAnyDeleteSettingsEnabled(DeleteSettings settings)
        {
            return HasDocumentDeletionSettingsEnabled(settings) ||
                   HasDocumentProtectionSettingsEnabled(settings) ||
                   HasParagraphSpacesSettingsEnabled(settings) ||
                   HasLineBreakSettingsEnabled(settings) ||
                   HasImageDeletionSettingsEnabled(settings) ||
                   (settings.DeleteTablesWithText && settings.SpecificTextList.Count > 0) ||
                   settings.DeleteShapes ||
                   settings.DeleteSmartArt ||
                   settings.DeleteComments ||
                   settings.DeleteBookmarks ||
                   settings.DeleteHyperlinks ||
                   settings.DeleteWatermarks ||
                   HasContactInfoDeletionSettingsEnabled(settings) ||
                   HasSpecificTextDeletionSettingsEnabled(settings) ||
                   HasEmptyContentDeletionSettingsEnabled(settings) ||
                   settings.DeleteFormats ||
                   HasDocumentElementsDeletionSettingsEnabled(settings) ||
                   HasAdvancedFormatsDeletionSettingsEnabled(settings) ||
                   HasDocumentStructureDeletionSettingsEnabled(settings) ||
                   HasDocumentPropertiesDeletionSettingsEnabled(settings);
        }

        // 检查删除文档标签页是否有任何设置被启用
        private bool HasDocumentDeletionSettingsEnabled(DeleteSettings settings)
        {
            return settings.EnableDocumentDeletion &&
                   (settings.CheckFileNameLength ||
                    settings.CheckDocumentSize ||
                    settings.CheckContentCharacterCount ||
                    settings.CheckDocumentPageCount ||
                    (settings.CheckFileNameIllegalWords && settings.FileNameIllegalWords.Count > 0) ||
                    (settings.CheckContentIllegalWords && settings.ContentIllegalWords.Count > 0));
        }

        // 检查文档保护标签页是否有任何设置被启用
        private bool HasDocumentProtectionSettingsEnabled(DeleteSettings settings)
        {
            return settings.ForceRemoveEditingPassword ||
                   settings.ForceRemoveContentProtection ||
                   settings.ForceAcceptAllRevisions ||
                   settings.ForceRemoveDigitalSignatures;
        }

        // 检查段落空格标签页是否有任何设置被启用
        private bool HasParagraphSpacesSettingsEnabled(DeleteSettings settings)
        {
            return settings.DeleteParagraphLeadingSpaces ||
                   settings.DeleteParagraphTrailingSpaces ||
                   settings.DeleteParagraphLeadingInvisibleChars ||
                   settings.DeleteParagraphTrailingInvisibleChars;
        }

        // 检查换行符和段落标记标签页是否有任何设置被启用
        private bool HasLineBreakSettingsEnabled(DeleteSettings settings)
        {
            return settings.DeleteLineBreaks ||
                   settings.DeleteParagraphMarks ||
                   settings.DeletePageBreaks ||
                   settings.DeleteSectionBreaks ||
                   settings.DeleteComments ||
                   settings.DeleteFootnotes ||
                   settings.ReplaceLineBreaksWithParagraphMarks ||
                   settings.MergeMultipleLineBreaks ||
                   settings.MergeMultipleParagraphMarks;
        }

        // 检查图片删除标签页是否有任何设置被启用
        private bool HasImageDeletionSettingsEnabled(DeleteSettings settings)
        {
            return settings.DeleteAllImages ||
                   (settings.DeleteSpecificImages && settings.SpecificImagePaths.Count > 0) ||
                   settings.DeleteSizedImages ||
                   settings.DeleteTrailingImages ||
                   settings.DeleteBackgroundImages;
        }

        // 检查联系方式删除标签页是否有任何设置被启用
        private bool HasContactInfoDeletionSettingsEnabled(DeleteSettings settings)
        {
            return settings.DeleteMobileNumbers ||
                   settings.DeletePhoneNumbers ||
                   settings.DeleteEmails ||
                   settings.DeleteUrls ||
                   settings.DeleteHyperlinks;
        }

        // 检查特定文本删除标签页是否有任何设置被启用
        private bool HasSpecificTextDeletionSettingsEnabled(DeleteSettings settings)
        {
            return (settings.DeleteParagraphsWithText ||
                   settings.DeleteTextBoxesWithText ||
                   settings.DeleteTablesWithText) &&
                   settings.SpecificTextList.Count > 0;
        }

        // 检查空白内容删除标签页是否有任何设置被启用
        private bool HasEmptyContentDeletionSettingsEnabled(DeleteSettings settings)
        {
            return settings.DeleteEmptyPages ||
                   settings.DeleteEmptyParagraphs ||
                   settings.DeleteEmptyLines;
        }

        // 检查文档元素删除标签页是否有任何设置被启用
        private bool HasDocumentElementsDeletionSettingsEnabled(DeleteSettings settings)
        {
            return settings.DeleteBookmarks ||
                   settings.DeleteFields ||
                   settings.DeleteTableOfContents ||
                   settings.DeleteIndex ||
                   settings.DeleteCrossReferences ||
                   settings.DeleteFormFields ||
                   settings.DeleteSmartArt ||
                   settings.DeleteCharts ||
                   settings.DeleteOleObjects ||
                   settings.DeleteActiveXControls;
        }

        // 检查高级格式删除标签页是否有任何设置被启用
        private bool HasAdvancedFormatsDeletionSettingsEnabled(DeleteSettings settings)
        {
            return settings.DeleteCharacterSpacing ||
                   settings.DeleteCharacterScaling ||
                   settings.DeleteCharacterPosition ||
                   settings.DeleteTextEffects ||
                   settings.DeleteParagraphBorders ||
                   settings.DeleteParagraphShading ||
                   settings.DeleteCellBorders ||
                   settings.DeleteCellShading ||
                   settings.DeleteCellMerging ||
                   settings.DeleteCellSplitting;
        }

        // 检查文档结构删除标签页是否有任何设置被启用
        private bool HasDocumentStructureDeletionSettingsEnabled(DeleteSettings settings)
        {
            return settings.DeleteColumns ||
                   settings.DeleteTextBoxes ||
                   settings.DeleteShapes ||
                   settings.DeleteWordArt ||
                   settings.DeleteMargins ||
                   settings.DeletePageBorders ||
                   settings.DeletePageBackground;
        }

        // 检查文档属性删除标签页是否有任何设置被启用
        private bool HasDocumentPropertiesDeletionSettingsEnabled(DeleteSettings settings)
        {
            return settings.DeleteDocumentProperties ||
                   settings.DeleteCustomProperties ||
                   settings.DeleteDocumentVariables ||
                   settings.DeleteDocumentStatistics ||
                   settings.DeleteHiddenText ||
                   settings.DeleteRevisionMarks ||
                   settings.DeleteCompareResults ||
                   settings.DeleteDocumentVersions ||
                   settings.DeleteDocumentTheme ||
                   settings.DeleteDocumentStyles;
        }

        /// <summary>
        /// 检查文档是否应该被删除
        /// </summary>
        private bool ShouldDeleteDocument(string filePath, AW.Document doc, DeleteSettings settings)
        {
            if (!settings.EnableDocumentDeletion)
                return false;

            try
            {
                string fileName = Path.GetFileNameWithoutExtension(filePath);

                // 1. 检查文件名长度
                if (settings.CheckFileNameLength)
                {
                    int fileNameLength = fileName.Length;
                    if (fileNameLength < settings.MinFileNameLength || fileNameLength > settings.MaxFileNameLength)
                    {
                        logger.Log($"文档 {Path.GetFileName(filePath)} 文件名长度 {fileNameLength} 不符合要求 ({settings.MinFileNameLength}-{settings.MaxFileNameLength})，将被删除");
                        return true;
                    }
                }

                // 2. 检查文档大小
                if (settings.CheckDocumentSize)
                {
                    long fileSizeBytes = new FileInfo(filePath).Length;
                    long minSizeBytes = ConvertToBytes(settings.MinDocumentSize, settings.MinDocumentSizeUnit);
                    long maxSizeBytes = ConvertToBytes(settings.MaxDocumentSize, settings.MaxDocumentSizeUnit);

                    if (fileSizeBytes < minSizeBytes || fileSizeBytes > maxSizeBytes)
                    {
                        logger.Log($"文档 {Path.GetFileName(filePath)} 大小 {FormatFileSize(fileSizeBytes)} 不符合要求 ({FormatFileSize(minSizeBytes)}-{FormatFileSize(maxSizeBytes)})，将被删除");
                        return true;
                    }
                }

                // 3. 检查文档内容字符总数
                if (settings.CheckContentCharacterCount)
                {
                    string documentText = doc.GetText();
                    int characterCount = documentText.Length;

                    if (characterCount < settings.MinContentCharacterCount || characterCount > settings.MaxContentCharacterCount)
                    {
                        logger.Log($"文档 {Path.GetFileName(filePath)} 字符数 {characterCount} 不符合要求 ({settings.MinContentCharacterCount}-{settings.MaxContentCharacterCount})，将被删除");
                        return true;
                    }
                }

                // 4. 检查文档页数
                if (settings.CheckDocumentPageCount)
                {
                    int pageCount = doc.PageCount;

                    if (pageCount < settings.MinDocumentPageCount || pageCount > settings.MaxDocumentPageCount)
                    {
                        logger.Log($"文档 {Path.GetFileName(filePath)} 页数 {pageCount} 不符合要求 ({settings.MinDocumentPageCount}-{settings.MaxDocumentPageCount})，将被删除");
                        return true;
                    }
                }

                // 5. 检查文件名是否包含非法词
                if (settings.CheckFileNameIllegalWords && settings.FileNameIllegalWords.Count > 0)
                {
                    foreach (string illegalWord in settings.FileNameIllegalWords)
                    {
                        if (!string.IsNullOrEmpty(illegalWord) && fileName.IndexOf(illegalWord, StringComparison.OrdinalIgnoreCase) >= 0)
                        {
                            logger.Log($"文档 {Path.GetFileName(filePath)} 文件名包含非法词 '{illegalWord}'，将被删除");
                            return true;
                        }
                    }
                }

                // 6. 检查文档内容是否包含非法词
                if (settings.CheckContentIllegalWords && settings.ContentIllegalWords.Count > 0)
                {
                    string documentText = doc.GetText();
                    foreach (string illegalWord in settings.ContentIllegalWords)
                    {
                        if (!string.IsNullOrEmpty(illegalWord) && documentText.IndexOf(illegalWord, StringComparison.OrdinalIgnoreCase) >= 0)
                        {
                            logger.Log($"文档 {Path.GetFileName(filePath)} 内容包含非法词 '{illegalWord}'，将被删除");
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                logger.LogError($"检查文档删除条件时出错: {filePath}, 错误: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 将大小值转换为字节
        /// </summary>
        private long ConvertToBytes(int size, string unit)
        {
            return unit.ToUpper() switch
            {
                "B" => size,
                "KB" => size * 1024L,
                "MB" => size * 1024L * 1024L,
                _ => size * 1024L // 默认为KB
            };
        }

        /// <summary>
        /// 格式化文件大小显示
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024)
                return $"{bytes} B";
            else if (bytes < 1024 * 1024)
                return $"{bytes / 1024.0:F1} KB";
            else
                return $"{bytes / (1024.0 * 1024.0):F1} MB";
        }

        /// <summary>
        /// 检查是否有任何页面设置标签页的功能被启用
        /// </summary>
        private bool HasAnyPageSetupTabEnabled(PageSetupFixed pageSetup)
        {
            if (pageSetup == null)
                return false;

            // 检查"基础设置"标签页
            bool basicSettingsEnabled = HasBasicSettingsTabEnabled(pageSetup);

            // 检查"页面背景"标签页
            bool backgroundTabEnabled = HasBackgroundTabEnabled(pageSetup);

            // 检查"边框样式"标签页
            bool borderTabEnabled = HasBorderTabEnabled(pageSetup);

            // 检查"页面网格"标签页
            bool gridTabEnabled = HasGridTabEnabled(pageSetup);

            // 检查"页面布局增强"标签页
            bool enhancedLayoutTabEnabled = HasEnhancedLayoutTabEnabled(pageSetup);

            // 检查"文档视图设置"标签页
            bool viewTabEnabled = HasViewTabEnabled(pageSetup);

            // 如果任何标签页有功能被启用，则返回true
            return basicSettingsEnabled ||
                   backgroundTabEnabled ||
                   borderTabEnabled ||
                   gridTabEnabled ||
                   enhancedLayoutTabEnabled ||
                   viewTabEnabled;
        }

        /// <summary>
        /// 检查"基础设置"标签页是否有任何功能被启用
        /// </summary>
        private bool HasBasicSettingsTabEnabled(PageSetupFixed pageSetup)
        {
            return pageSetup.EnableMargins ||
                   pageSetup.EnableOrientation ||
                   pageSetup.EnablePaperSize ||
                   pageSetup.EnableGutter ||
                   pageSetup.LineNumberingIsActive;
        }

        /// <summary>
        /// 检查"页面背景"标签页是否有任何功能被启用
        /// </summary>
        private bool HasBackgroundTabEnabled(PageSetupFixed pageSetup)
        {
            return pageSetup.EnableBackgroundColor ||
                   pageSetup.EnableBackgroundImage;
        }

        /// <summary>
        /// 检查"边框样式"标签页是否有任何功能被启用
        /// </summary>
        private bool HasBorderTabEnabled(PageSetupFixed pageSetup)
        {
            return pageSetup.EnableBorderStyle ||
                   pageSetup.EnableBorderColor ||
                   pageSetup.EnableBorderWidth ||
                   pageSetup.EnableBorderArt;
        }

        /// <summary>
        /// 检查"页面网格"标签页是否有任何功能被启用
        /// </summary>
        private bool HasGridTabEnabled(PageSetupFixed pageSetup)
        {
            return pageSetup.EnableGridColor ||
                   pageSetup.EnableGridSpacing ||
                   pageSetup.EnableSnapToGrid;
        }

        /// <summary>
        /// 检查"页面布局增强"标签页是否有任何功能被启用
        /// </summary>
        private bool HasEnhancedLayoutTabEnabled(PageSetupFixed pageSetup)
        {
            return pageSetup.EnableColumns ||
                   pageSetup.EnableScaling ||
                   pageSetup.EnableTextOrientation ||
                   pageSetup.EnableMirrorMargins ||
                   pageSetup.EnableBidi;
        }

        /// <summary>
        /// 检查"文档视图设置"标签页是否有任何功能被启用
        /// </summary>
        private bool HasViewTabEnabled(PageSetupFixed pageSetup)
        {
            return pageSetup.EnableViewType ||
                   pageSetup.EnableZoom ||
                   pageSetup.EnableFormatMarks;
        }

        /// <summary>
        /// 检查是否有任何页眉页脚设置被启用
        /// </summary>
        private bool HasAnyHeaderFooterSettingsEnabled(HeaderFooterSettings settings)
        {
            // 如果启用了删除页眉页脚，也算作有功能被启用
            if (settings.RemoveHeaderFooter)
                return true;

            // 如果只启用了删除页眉，也算作有功能被启用
            if (settings.RemoveHeader)
                return true;

            // 如果只启用了删除页脚，也算作有功能被启用
            if (settings.RemoveFooter)
                return true;

            // 检查页眉设置
            bool headerEnabled = settings.EnableHeader &&
                (settings.HasHeader || settings.HasHeaderImage || settings.HasHeaderPageNumber);

            // 检查页脚设置
            bool footerEnabled = settings.EnableFooter &&
                (settings.HasFooter || settings.HasFooterImage || settings.HasFooterPageNumber);

            return headerEnabled || footerEnabled;
        }

        private void RemoveDocumentProtection(AW.Document doc, DeleteSettings settings)
        {
            try
            {
                logger.Log("开始移除文档保护...");

                if (settings.ForceRemoveEditingPassword)
                {
                    // 移除编辑密码
                    doc.Protect(AW.ProtectionType.NoProtection);
                    logger.Log("已移除文档编辑密码");
                }

                if (settings.ForceRemoveContentProtection)
                {
                    // 移除内容保护
                    doc.Unprotect();
                    logger.Log("已移除文档内容保护");
                }

                if (settings.ForceRemoveDigitalSignatures)
                {
                    // 移除数字签名
                    if (doc.DigitalSignatures != null && doc.DigitalSignatures.Count > 0)
                    {
                        // 在Aspose.Words中，我们需要重新保存文档来移除数字签名
                        try
                        {
                            // 创建临时内存流
                            using (var stream = new MemoryStream())
                            {
                                // 保存到内存流
                                doc.Save(stream, AW.SaveFormat.Docx);
                                // 重新加载文档
                                stream.Position = 0;
                                doc.Save(stream, AW.SaveFormat.Docx);
                            }
                            logger.Log("已移除文档数字签名");
                        }
                        catch (Exception ex)
                        {
                            logger.LogWarning($"移除数字签名时出错：{ex.Message}");
                        }
                    }
                }

                if (settings.ForceAcceptAllRevisions)
                {
                    // 接受所有修订
                    doc.AcceptAllRevisions();
                    logger.Log("已接受所有修订");
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"移除文档保护时出错: {ex.Message}", ex);
            }
        }

        private void HandleLineBreaksAndParagraphMarks(AW.Document doc, DeleteSettings settings)
        {
            try
            {
                logger.Log("开始处理换行符和段落标记...");

                // 先刷新文档，确保所有内容已经更新
                doc.UpdateFields();

                // 先执行文档级别的合并操作，处理可能跨Run的段落标记
                if (settings.DeleteParagraphMarks ||
                    settings.MergeMultipleParagraphMarks)
                {
                    try
                    {
                        doc.JoinRunsWithSameFormatting();
                        logger.Log("执行了Run合并操作，合并相同格式的文本以便更好地处理段落标记");
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning($"Run合并操作时出错: {ex.Message}，将尝试其他方法处理");
                    }
                }

                // 获取所有段落和Run
                var paragraphs = doc.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();
                var allRuns = doc.GetChildNodes(AW.NodeType.Run, true).Cast<AW.Run>().ToList();

                // 删除批注
                if (settings.DeleteComments)
                {
                    var comments = doc.GetChildNodes(AW.NodeType.Comment, true).ToList();
                    foreach (var comment in comments)
                    {
                        comment.Remove();
                    }
                    logger.Log($"已删除 {comments.Count} 个批注");
                }

                // 删除尾注
                if (settings.DeleteFootnotes)
                {
                    var footnotes = doc.GetChildNodes(AW.NodeType.Footnote, true).ToList();
                    foreach (var footnote in footnotes)
                    {
                        footnote.Remove();
                    }
                    logger.Log($"已删除 {footnotes.Count} 个尾注");
                }

                // 删除分页符
                int pageBreaksCount = 0;
                if (settings.DeletePageBreaks)
                {
                    foreach (var run in allRuns)
                    {
                        try
                        {
                            string text = run.GetText();
                            if (text.Contains(AW.ControlChar.PageBreak))
                            {
                                string newText = text.Replace(AW.ControlChar.PageBreak, "");
                                pageBreaksCount += text.Length - newText.Length;
                                run.Text = newText;
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogWarning($"处理Run中的分页符时出错: {ex.Message}");
                        }
                    }
                    logger.Log($"已删除 {pageBreaksCount} 个分页符");
                }

                // 删除分节符，通过合并Section对象来实现
                if (settings.DeleteSectionBreaks)
                {
                    try
                    {
                        var sections = doc.GetChildNodes(AW.NodeType.Section, true).Cast<AW.Section>().ToList();
                        int sectionCount = sections.Count;

                        if (sectionCount > 1)
                        {
                            int removedSections = 0;
                            // 从最后一个节开始向前处理，保留第一个节
                            for (int i = sectionCount - 1; i > 0; i--)
                            {
                                try
                                {
                                    AW.Section currentSection = sections[i];
                                    AW.Section targetSection = sections[i - 1];

                                    // 将当前节的所有Body节点移动到目标节
                                    while (currentSection.Body.HasChildNodes)
                                    {
                                        var node = currentSection.Body.FirstChild;
                                        node.Remove();
                                        targetSection.Body.AppendChild(node);
                                    }

                                    // 移除当前节
                                    currentSection.Remove();
                                    removedSections++;
                                }
                                catch (Exception ex)
                                {
                                    logger.LogWarning($"合并节 {i} 时出错: {ex.Message}");
                                }
                            }
                            logger.Log($"已删除 {removedSections} 个分节符，将多个节合并为一个");
                        }
                        else
                        {
                            logger.Log("文档只有一个节，无需处理分节符");
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"处理分节符时发生错误: {ex.Message}", ex);
                    }
                }

                // 处理换行符和段落标记
                int lineBreaksProcessed = 0;
                int paragraphMarksProcessed = 0;

                // 删除段落标记的特殊处理 - 需要合并段落
                if (settings.DeleteParagraphMarks)
                {
                    try
                    {
                        for (int i = paragraphs.Count - 1; i > 0; i--)
                        {
                            try
                            {
                                // 获取当前段落和前一个段落
                                var currentPara = paragraphs[i];
                                var prevPara = paragraphs[i - 1];

                                // 段落内容合并到前一个段落
                                foreach (var node in currentPara.GetChildNodes(AW.NodeType.Any, false).ToArray())
                                {
                                    node.Remove();
                                    prevPara.AppendChild(node.Clone(true));
                                }

                                // 删除当前段落
                                currentPara.Remove();
                                paragraphMarksProcessed++;
                            }
                            catch (Exception ex)
                            {
                                logger.LogWarning($"合并段落 {i} 时出错: {ex.Message}");
                            }
                        }
                        logger.Log($"删除段落标记: 合并了 {paragraphMarksProcessed} 个段落");
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"处理段落标记(合并段落)时出错: {ex.Message}", ex);
                    }
                }

                // 重新获取段落和Run（因为前面的处理可能更改了文档结构）
                paragraphs = doc.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();
                allRuns = doc.GetChildNodes(AW.NodeType.Run, true).Cast<AW.Run>().ToList();

                // 处理段落内的换行符和段落标记
                foreach (var run in allRuns)
                {
                    try
                    {
                        string text = run.GetText();
                        bool modified = false;

                        // 删除手动换行符
                        if (settings.DeleteLineBreaks && text.Contains(AW.ControlChar.LineBreak))
                        {
                            string newText = text.Replace(AW.ControlChar.LineBreak, "");
                            int count = text.Length - newText.Length;
                            lineBreaksProcessed += count;
                            text = newText;
                            modified = true;
                            logger.LogDebug($"删除了 {count} 个手动换行符");
                        }

                        // 手动换行符替换为段落标记
                        if (settings.ReplaceLineBreaksWithParagraphMarks && text.Contains(AW.ControlChar.LineBreak))
                        {
                            int count = text.Split(new[] { AW.ControlChar.LineBreak }, StringSplitOptions.None).Length - 1;
                            text = text.Replace(AW.ControlChar.LineBreak, AW.ControlChar.ParagraphBreak);
                            modified = true;
                            logger.LogDebug($"将 {count} 个手动换行符替换为段落标记");
                        }

                        // 合并多个连续的手动换行符
                        if (settings.MergeMultipleLineBreaks)
                        {
                            try
                            {
                                logger.Log("开始合并多个连续的手动换行符...");
                                int mergedCount = 0;

                                // 首先合并Run对象以确保跨Run的换行符也能被处理
                                doc.JoinRunsWithSameFormatting();

                                // 处理文档中所有Run对象
                                var mergeAllRuns = doc.GetChildNodes(AW.NodeType.Run, true).Cast<AW.Run>().ToList();
                                foreach (var currentRun in mergeAllRuns)
                                {
                                    try
                                    {
                                        string runText = currentRun.GetText();
                                        if (runText.Contains(AW.ControlChar.LineBreak))
                                        {
                                            // 创建双换行符字符串
                                            string doubleLineBreak = AW.ControlChar.LineBreak + AW.ControlChar.LineBreak;
                                            string originalText = runText;

                                            // 替换所有连续的换行符为单个换行符
                                            while (runText.Contains(doubleLineBreak))
                                            {
                                                runText = runText.Replace(doubleLineBreak, AW.ControlChar.LineBreak);
                                            }

                                            // 如果文本被修改，更新Run
                                            if (originalText != runText)
                                            {
                                                currentRun.Text = runText;
                                                mergedCount++;
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogWarning($"合并Run中的连续换行符时出错: {ex.Message}");
                                    }
                                }

                                // 使用全局替换确保没有遗漏的连续换行符
                                string pattern = AW.ControlChar.LineBreak + "{2,}";
                                var regex = new System.Text.RegularExpressions.Regex(pattern);
                                string replacement = AW.ControlChar.LineBreak;

                                foreach (AW.Paragraph para in doc.GetChildNodes(AW.NodeType.Paragraph, true))
                                {
                                    foreach (AW.Run paraRun in para.GetChildNodes(AW.NodeType.Run, true))
                                    {
                                        string paraRunText = paraRun.GetText();
                                        if (regex.IsMatch(paraRunText))
                                        {
                                            string newText = regex.Replace(paraRunText, replacement);
                                            if (paraRunText != newText)
                                            {
                                                paraRun.Text = newText;
                                                mergedCount++;
                                            }
                                        }
                                    }
                                }

                                logger.Log($"已合并 {mergedCount} 处连续的手动换行符");
                            }
                            catch (Exception ex)
                            {
                                logger.LogError($"合并连续手动换行符时发生错误: {ex.Message}", ex);
                            }
                        }

                        // 合并多个连续的段落标记
                        if (settings.MergeMultipleParagraphMarks && text.Contains(AW.ControlChar.ParagraphBreak))
                        {
                            string doubleParagraphBreak = AW.ControlChar.ParagraphBreak + AW.ControlChar.ParagraphBreak;
                            string singleParagraphBreak = AW.ControlChar.ParagraphBreak;
                            string originalText = text;

                            while (text.Contains(doubleParagraphBreak))
                            {
                                text = text.Replace(doubleParagraphBreak, singleParagraphBreak);
                            }

                            if (originalText != text)
                            {
                                modified = true;
                                logger.LogDebug("合并了连续的段落标记");
                            }
                        }

                        if (modified)
                        {
                            run.Text = text;
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning($"处理Run时出错: {ex.Message}");
                    }
                }

                // 合并多个连续的段落 - 如果需要合并连续的段落
                if (settings.MergeMultipleParagraphMarks)
                {
                    try
                    {
                        int mergedCount = 0;

                        // 查找并合并空段落
                        var paragraphsToMerge = doc.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();
                        for (int i = paragraphsToMerge.Count - 1; i > 0; i--)
                        {
                            try
                            {
                                var para = paragraphsToMerge[i];

                                // 检查是否是空段落
                                if (string.IsNullOrWhiteSpace(para.GetText().Trim()))
                                {
                                    para.Remove();
                                    mergedCount++;
                                }
                            }
                            catch (Exception ex)
                            {
                                logger.LogWarning($"合并空段落 {i} 时出错: {ex.Message}");
                            }
                        }

                        logger.Log($"合并了 {mergedCount} 个空段落");
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"合并连续段落时出错: {ex.Message}", ex);
                    }
                }

                // 最后进行一次格式清理
                try
                {
                    logger.Log("标准化了文档内容");
                }
                catch (Exception ex)
                {
                    logger.LogWarning($"文档标准化时出错: {ex.Message}");
                }

                logger.Log($"换行符和段落标记处理完成，处理了 {lineBreaksProcessed} 个换行符和 {paragraphMarksProcessed} 个段落标记");
            }
            catch (Exception ex)
            {
                logger.LogError($"处理换行符和段落标记时出错: {ex.Message}", ex);
            }
        }

        private void DeleteImages(AW.Document doc, DeleteSettings settings)
        {
            // 如果没有启用任何图片删除功能，直接返回
            if (!HasImageDeletionSettingsEnabled(settings))
            {
                return;
            }

            try
            {
                // 获取所有图片节点 - 只获取一次，避免多次遍历文档
                var shapes = doc.GetChildNodes(AW.NodeType.Shape, true)
                    .Cast<AW.Drawing.Shape>()
                    .Where(s => s.HasImage)
                    .ToList();

                if (shapes.Count == 0)
                {
                    logger.Log("文档中没有找到图片");
                    return;
                }

                // 创建一个HashSet用于高效查找要删除的图片
                HashSet<AW.Drawing.Shape> imagesToDelete = new HashSet<AW.Drawing.Shape>();

                // 如果删除所有图片，直接添加所有图片到删除列表
                if (settings.DeleteAllImages)
                {
                    foreach (var shape in shapes)
                    {
                        imagesToDelete.Add(shape);
                    }
                    logger.Log($"标记删除所有图片: {shapes.Count} 个");
                }
                else
                {
                    // 预加载特定图片数据，避免在循环中重复读取
                    Dictionary<string, byte[]> specificImageDataCache = new Dictionary<string, byte[]>();
                    if (settings.DeleteSpecificImages && settings.SpecificImagePaths.Count > 0)
                    {
                        foreach (var imagePath in settings.SpecificImagePaths)
                        {
                            if (File.Exists(imagePath))
                            {
                                try
                                {
                                    specificImageDataCache[imagePath] = File.ReadAllBytes(imagePath);
                                }
                                catch (Exception ex)
                                {
                                    logger.LogError($"读取指定图片失败: {imagePath}, {ex.Message}", ex);
                                }
                            }
                        }
                    }

                    // 处理尾部图片 - 只在需要时执行一次
                    List<AW.Drawing.Shape> trailingImages = new List<AW.Drawing.Shape>();
                    if (settings.DeleteTrailingImages)
                    {
                        try
                        {
                            logger.Log("开始处理文档结尾处的图片...");

                            // 获取文档中所有节点，包括段落、表格、图片等
                            var allNodes = doc.GetChildNodes(AW.NodeType.Any, true).ToList();
                            bool foundNonImageContent = false;

                            // 从末尾向前遍历节点
                            for (int i = allNodes.Count - 1; i >= 0; i--)
                            {
                                var node = allNodes[i];

                                // 如果是图片形状
                                if (node is AW.Drawing.Shape imageShape && imageShape.HasImage)
                                {
                                    // 如果还没有找到非图片内容，则将其添加到要删除的列表中
                                    if (!foundNonImageContent)
                                    {
                                        trailingImages.Add(imageShape);
                                    }
                                }
                                // 如果是文本内容
                                else if (node is AW.Run run)
                                {
                                    // 检查是否包含实际文本内容（不仅仅是空格或换行符）
                                    string text = run.GetText().Trim();
                                    if (!string.IsNullOrEmpty(text))
                                    {
                                        foundNonImageContent = true;
                                    }
                                }
                                // 如果是表格或其他非图片内容
                                else if (node is AW.Tables.Table || node is AW.Paragraph)
                                {
                                    // 检查如果是段落，是否包含实际文本内容
                                    if (node is AW.Paragraph paragraph)
                                    {
                                        string paraText = paragraph.GetText().Trim();
                                        if (!string.IsNullOrEmpty(paraText))
                                        {
                                            foundNonImageContent = true;
                                        }
                                    }
                                    else
                                    {
                                        // 表格和其他复杂元素，直接标记为非图片内容
                                        foundNonImageContent = true;
                                    }
                                }

                                // 如果已经找到非图片内容，不需要继续向前搜索图片
                                if (foundNonImageContent && trailingImages.Count > 0)
                                {
                                    break;
                                }
                            }

                            if (trailingImages.Count > 0)
                            {
                                logger.Log($"找到 {trailingImages.Count} 个文档末尾的图片，将被删除");
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"处理尾部图片时出错: {ex.Message}", ex);
                        }
                    }

                    // 并行处理图片，提高性能
                    Parallel.ForEach(shapes, shape =>
                    {
                        // 如果已经标记为删除，跳过后续检查
                        if (imagesToDelete.Contains(shape))
                        {
                            return;
                        }

                        // 删除特定图片 - 基于图片哈希值与已选图片的相似度比较
                        if (settings.DeleteSpecificImages && specificImageDataCache.Count > 0)
                        {
                            try
                            {
                                // 从Shape获取图片数据
                                byte[] shapeImageData = shape.ImageData.ImageBytes;

                                // 比较图片数据与指定图片列表
                                foreach (var entry in specificImageDataCache)
                                {
                                    bool isSimilar = IsImageSimilar(shapeImageData, entry.Value);
                                    if (isSimilar)
                                    {
                                        lock (imagesToDelete)
                                        {
                                            imagesToDelete.Add(shape);
                                        }
                                        logger.Log($"找到匹配的图片: {Path.GetFileName(entry.Key)}");
                                        break;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                logger.LogError($"比较图片时出错: {ex.Message}", ex);
                            }
                        }

                        // 如果已经标记为删除，跳过后续检查
                        if (imagesToDelete.Contains(shape))
                        {
                            return;
                        }

                        // 删除特定大小的图片
                        if (settings.DeleteSizedImages)
                        {
                            bool matchesSize = true;

                            if (settings.MinImageWidth > 0 && shape.Width < settings.MinImageWidth)
                                matchesSize = false;

                            if (settings.MinImageHeight > 0 && shape.Height < settings.MinImageHeight)
                                matchesSize = false;

                            if (settings.MaxImageWidth > 0 && shape.Width > settings.MaxImageWidth)
                                matchesSize = false;

                            if (settings.MaxImageHeight > 0 && shape.Height > settings.MaxImageHeight)
                                matchesSize = false;

                            if (matchesSize)
                            {
                                lock (imagesToDelete)
                                {
                                    imagesToDelete.Add(shape);
                                }
                                return;
                            }
                        }

                        // 处理尾部图片 - 使用预先计算的结果
                        if (settings.DeleteTrailingImages && trailingImages.Contains(shape))
                        {
                            lock (imagesToDelete)
                            {
                                imagesToDelete.Add(shape);
                            }
                            return;
                        }

                        // 处理背景图片
                        if (settings.DeleteBackgroundImages)
                        {
                            // 检查是否是背景图片
                            if (shape.BehindText || shape.RelativeHorizontalPosition == AW.Drawing.RelativeHorizontalPosition.Page)
                            {
                                lock (imagesToDelete)
                                {
                                    imagesToDelete.Add(shape);
                                }
                                return;
                            }
                        }
                    });
                }

                // 删除所有标记的图片
                int deletedCount = 0;
                foreach (var image in imagesToDelete)
                {
                    if (image.ParentNode != null)
                    {
                        image.Remove();
                        deletedCount++;
                    }
                }

                // 如果需要删除背景图片，还需要处理页面背景
                if (settings.DeleteBackgroundImages)
                {
                    foreach (AW.Section section in doc.Sections)
                    {
                        try
                        {
                            // 清除页面背景 - 只获取背景图片，避免重复遍历
                            var backgroundShapes = section.GetChildNodes(AW.NodeType.Shape, true)
                                .Cast<AW.Drawing.Shape>()
                                .Where(s => s.BehindText || s.RelativeHorizontalPosition == AW.Drawing.RelativeHorizontalPosition.Page)
                                .ToList();

                            foreach (var shape in backgroundShapes)
                            {
                                shape.Remove();
                                deletedCount++;
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"清除页面背景时出错: {ex.Message}", ex);
                        }
                    }
                }

                logger.Log($"已删除 {deletedCount} 个图片");
            }
            catch (Exception ex)
            {
                logger.LogError($"删除图片时出错: {ex.Message}", ex);
            }
        }

        // 判断两个图片是否相似的优化方法
        private bool IsImageSimilar(byte[] imageData1, byte[] imageData2)
        {
            try
            {
                // 快速检查：如果任一图片数据为空，则不相似
                if (imageData1 == null || imageData2 == null || imageData1.Length == 0 || imageData2.Length == 0)
                    return false;

                // 快速检查：如果图片大小相差太多，认为不是同一图片 (差距超过50%)
                if (Math.Abs(imageData1.Length - imageData2.Length) > imageData1.Length * 0.5)
                    return false;

                // 使用System.Drawing进行图片比较 - 计算感知哈希
                using (var ms1 = new MemoryStream(imageData1))
                using (var ms2 = new MemoryStream(imageData2))
                using (var image1 = System.Drawing.Image.FromStream(ms1))
                using (var image2 = System.Drawing.Image.FromStream(ms2))
                {
                    // 快速检查：如果图片尺寸相差太大，认为不是同一图片
                    if (Math.Abs(image1.Width - image2.Width) > image1.Width * 0.5 ||
                        Math.Abs(image1.Height - image2.Height) > image1.Height * 0.5)
                        return false;

                    // 将图片缩小到8x8像素以便计算感知哈希
                    const int hashSize = 8;
                    using (var thumbnail1 = new Bitmap(hashSize, hashSize))
                    using (var thumbnail2 = new Bitmap(hashSize, hashSize))
                    using (var g1 = Graphics.FromImage(thumbnail1))
                    using (var g2 = Graphics.FromImage(thumbnail2))
                    {
                        // 使用低质量插值以提高性能，对于8x8的缩略图足够了
                        g1.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.Low;
                        g2.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.Low;
                        g1.DrawImage(image1, 0, 0, hashSize, hashSize);
                        g2.DrawImage(image2, 0, 0, hashSize, hashSize);

                        // 使用更高效的方式计算灰度值和哈希
                        ulong hash1 = 0, hash2 = 0;
                        int totalGray1 = 0, totalGray2 = 0;
                        int[] grayValues1 = new int[hashSize * hashSize];
                        int[] grayValues2 = new int[hashSize * hashSize];

                        // 第一次遍历：计算灰度值和总和
                        for (int y = 0; y < hashSize; y++)
                        {
                            for (int x = 0; x < hashSize; x++)
                            {
                                Color pixel1 = thumbnail1.GetPixel(x, y);
                                Color pixel2 = thumbnail2.GetPixel(x, y);

                                // 使用整数计算灰度值，避免浮点运算
                                int gray1 = (pixel1.R * 30 + pixel1.G * 59 + pixel1.B * 11) / 100;
                                int gray2 = (pixel2.R * 30 + pixel2.G * 59 + pixel2.B * 11) / 100;

                                int index = y * hashSize + x;
                                grayValues1[index] = gray1;
                                grayValues2[index] = gray2;

                                totalGray1 += gray1;
                                totalGray2 += gray2;
                            }
                        }

                        // 计算平均值
                        int avgGray1 = totalGray1 / (hashSize * hashSize);
                        int avgGray2 = totalGray2 / (hashSize * hashSize);

                        // 第二次遍历：计算哈希值
                        for (int i = 0; i < hashSize * hashSize; i++)
                        {
                            if (grayValues1[i] >= avgGray1)
                                hash1 |= 1UL << i;

                            if (grayValues2[i] >= avgGray2)
                                hash2 |= 1UL << i;
                        }

                        // 计算汉明距离 - 使用内置的BitOperations.PopCount方法（.NET Core 3.0+）
                        // 如果不可用，则使用查表法或其他优化方法
                        ulong xor = hash1 ^ hash2;
                        int hammingDistance = CountBits(xor);

                        // 相似度阈值，汉明距离越小则越相似
                        // 返回相似度结果：汉明距离小于10认为是相似图片
                        return hammingDistance <= 10;
                    }
                }
            }
            catch (Exception ex)
            {
                // 异常情况下，返回保守结果：不相似
                logger.LogError($"图片比较过程出错: {ex.Message}", ex);
                return false;
            }
        }

        // 计算一个64位整数中1的个数的优化方法
        private static int CountBits(ulong value)
        {
            // 使用查表法或位操作计算
            int count = 0;
            while (value != 0)
            {
                count++;
                value &= value - 1; // 清除最低位的1
            }
            return count;
        }

        // 预编译的正则表达式，提高性能
        private static readonly Regex MobileNumberRegex = new Regex(@"1[3-9]\d{9}|1[3-9][\s-]?\d{1}[\s-]?\d{4}[\s-]?\d{4}", RegexOptions.Compiled);

        private static readonly Regex[] PhoneNumberRegexes = new[]
        {
            new Regex(@"0\d{2,3}[-]?\d{7,8}", RegexOptions.Compiled),                   // 标准格式：区号-号码
            new Regex(@"\(\d{3,4}\)\s*\d{7,8}", RegexOptions.Compiled),                 // 括号格式：(区号)号码
            new Regex(@"\d{3,4}[-\s]?\d{7,8}", RegexOptions.Compiled),                  // 无0开头：区号号码
            new Regex(@"0\d{2,3}[-]?\d{7,8}[-]?\d{1,5}", RegexOptions.Compiled)         // 带分机号：区号-号码-分机
        };

        private static readonly Regex EmailRegex = new Regex(@"[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,6}", RegexOptions.Compiled);

        private static readonly Regex[] UrlRegexes = new[]
        {
            new Regex(@"https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(/[-\w%/.~!$&'()*+,;=:@]*)?", RegexOptions.Compiled),  // http/https链接
            new Regex(@"www\.(?:[-\w.]|(?:%[\da-fA-F]{2}))+(/[-\w%/.~!$&'()*+,;=:@]*)?", RegexOptions.Compiled),      // www开头链接
            new Regex(@"(?<![@/\w.-])(?:[\w-]+\.)+(?:com|net|org|edu|gov|mil|io|co|info|biz|cn|com\.cn|net\.cn|org\.cn|gov\.cn)(?![/\w.-])", RegexOptions.Compiled) // 特定域名链接
        };

        private void DeleteMobileNumbers(AW.Document doc)
        {
            try
            {
                var options = new FindReplaceOptions
                {
                    Direction = FindReplaceDirection.Forward,
                    MatchCase = false,
                    UseSubstitutions = false
                };

                doc.Range.Replace(MobileNumberRegex, "", options);
                logger.Log("已删除文档中的手机号码");
            }
            catch (Exception ex)
            {
                logger.LogError($"删除手机号码时出错: {ex.Message}", ex);
            }
        }

        private void DeletePhoneNumbers(AW.Document doc)
        {
            try
            {
                var options = new FindReplaceOptions
                {
                    Direction = FindReplaceDirection.Forward,
                    MatchCase = false,
                    UseSubstitutions = false
                };

                int count = 0;
                foreach (var regex in PhoneNumberRegexes)
                {
                    count += doc.Range.Replace(regex, "", options);
                }

                logger.Log($"已删除文档中的 {count} 个电话号码");
            }
            catch (Exception ex)
            {
                logger.LogError($"删除电话号码时出错: {ex.Message}", ex);
            }
        }

        private void DeleteEmails(AW.Document doc)
        {
            try
            {
                var options = new FindReplaceOptions
                {
                    Direction = FindReplaceDirection.Forward,
                    MatchCase = false,
                    UseSubstitutions = false,
                    FindWholeWordsOnly = false
                };

                int count = doc.Range.Replace(EmailRegex, "", options);
                logger.Log($"已删除文档中的 {count} 个邮箱地址");
            }
            catch (Exception ex)
            {
                logger.LogError($"删除邮箱地址时出错: {ex.Message}", ex);
            }
        }

        private void DeleteUrls(AW.Document doc)
        {
            try
            {
                var options = new FindReplaceOptions
                {
                    Direction = FindReplaceDirection.Forward,
                    MatchCase = false,
                    UseSubstitutions = false,
                    FindWholeWordsOnly = false
                };

                int count = 0;
                foreach (var regex in UrlRegexes)
                {
                    count += doc.Range.Replace(regex, "", options);
                }

                logger.Log($"已删除文档中的 {count} 个URL地址");
            }
            catch (Exception ex)
            {
                logger.LogError($"删除URL地址时出错: {ex.Message}", ex);
            }
        }

        private void DeleteContentWithText(AW.Document doc, DeleteSettings settings)
        {
            if (settings.SpecificTextList == null || settings.SpecificTextList.Count == 0)
                return;

            // 如果没有启用任何相关功能，直接返回
            if (!settings.DeleteParagraphsWithText && !settings.DeleteTextBoxesWithText && !settings.DeleteTablesWithText)
                return;

            try
            {
                // 过滤掉空字符串，并转换为HashSet以提高查找性能
                HashSet<string> specificTexts = new HashSet<string>(
                    settings.SpecificTextList.Where(text => !string.IsNullOrEmpty(text)),
                    StringComparer.OrdinalIgnoreCase); // 忽略大小写比较

                if (specificTexts.Count == 0)
                    return;

                // 创建节点收集器 - 使用并发集合以支持并行处理
                ConcurrentBag<AW.Node> nodesToRemove = new ConcurrentBag<AW.Node>();

                // 并行处理各种内容类型
                Parallel.Invoke(
                    // 处理段落
                    () => {
                        if (settings.DeleteParagraphsWithText)
                        {
                            var paragraphs = doc.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();
                            Parallel.ForEach(paragraphs, paragraph => {
                                string paragraphText = paragraph.GetText();
                                if (specificTexts.Any(text => paragraphText.IndexOf(text, StringComparison.OrdinalIgnoreCase) >= 0))
                                {
                                    nodesToRemove.Add(paragraph);
                                }
                            });
                        }
                    },

                    // 处理文本框
                    () => {
                        if (settings.DeleteTextBoxesWithText)
                        {
                            var textBoxes = doc.GetChildNodes(AW.NodeType.Shape, true)
                                .Cast<AW.Drawing.Shape>()
                                .Where(s => s.ShapeType == AW.Drawing.ShapeType.TextBox ||
                                           s.ShapeType == AW.Drawing.ShapeType.TextPlainText)
                                .ToList();

                            Parallel.ForEach(textBoxes, shape => {
                                string textBoxText = shape.GetText();
                                if (specificTexts.Any(text => textBoxText.IndexOf(text, StringComparison.OrdinalIgnoreCase) >= 0))
                                {
                                    nodesToRemove.Add(shape);
                                }
                            });
                        }
                    },

                    // 处理表格
                    () => {
                        if (settings.DeleteTablesWithText)
                        {
                            var tables = doc.GetChildNodes(AW.NodeType.Table, true).Cast<AW.Tables.Table>().ToList();
                            Parallel.ForEach(tables, table => {
                                string tableText = table.GetText();
                                if (specificTexts.Any(text => tableText.IndexOf(text, StringComparison.OrdinalIgnoreCase) >= 0))
                                {
                                    nodesToRemove.Add(table);
                                }
                            });
                        }
                    }
                );

                // 删除收集到的节点 - 必须按顺序删除以避免并发修改问题
                int removedCount = 0;
                foreach (var node in nodesToRemove)
                {
                    if (node.ParentNode != null)
                    {
                        try
                        {
                            node.Remove();
                            removedCount++;
                        }
                        catch (Exception ex)
                        {
                            logger.LogWarning($"删除节点时出错: {ex.Message}");
                        }
                    }
                }

                logger.Log($"已删除包含指定文本的 {removedCount} 个内容区域");
            }
            catch (Exception ex)
            {
                logger.LogError($"删除包含特定文本的内容时出错: {ex.Message}", ex);
            }
        }

        private void DeleteEmptyContent(AW.Document doc, DeleteSettings settings)
        {
            List<AW.Node> nodesToRemove = new List<AW.Node>();

            // 删除空段落
            if (settings.DeleteEmptyParagraphs)
            {
                var paragraphs = doc.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();
                foreach (var paragraph in paragraphs)
                {
                    string text = paragraph.GetText().Trim();
                    if (string.IsNullOrWhiteSpace(text))
                    {
                        // 如果是表格单元格中的唯一段落，则不要删除它
                        if (paragraph.ParentNode is AW.Tables.Cell &&
                            paragraph.ParentNode.GetChildNodes(AW.NodeType.Any, false).Count == 1)
                        {
                            continue;
                        }
                        nodesToRemove.Add(paragraph);
                    }
                }
            }

            // 删除空行
            if (settings.DeleteEmptyLines)
            {
                var paragraphs = doc.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();
                foreach (var paragraph in paragraphs)
                {
                    if (paragraph.Runs.Count == 0)
                    {
                        continue; // 已经由空段落处理
                    }

                    List<AW.Run> emptyRuns = new List<AW.Run>();
                    foreach (AW.Run run in paragraph.Runs)
                    {
                        string text = run.Text.Trim();
                        if (string.IsNullOrWhiteSpace(text))
                        {
                            emptyRuns.Add(run);
                        }
                    }

                    // 如果所有运行都是空的，且不是表格单元格中的唯一段落
                    if (emptyRuns.Count == paragraph.Runs.Count &&
                        !(paragraph.ParentNode is AW.Tables.Cell && paragraph.ParentNode.GetChildNodes(AW.NodeType.Any, false).Count == 1))
                    {
                        nodesToRemove.Add(paragraph);
                    }
                    else
                    {
                        // 删除空运行
                        foreach (var run in emptyRuns)
                        {
                            run.Remove();
                        }
                    }
                }
            }

            // 删除收集到的节点
            foreach (var node in nodesToRemove)
            {
                if (node.ParentNode != null)
                {
                    node.Remove();
                }
            }

            logger.Log($"已删除 {nodesToRemove.Count} 个空白内容");
        }

        private void DeleteEmptyPages(AW.Document doc)
        {
            // 获取所有节
            var sections = doc.Sections;

            // 最后一页不算空白页，不处理
            for (int i = 0; i < sections.Count - 1; i++)
            {
                var section = sections[i];

                // 检查这个节是否为空
                bool isEmpty = true;
                var nodes = section.GetChildNodes(AW.NodeType.Any, true);

                foreach (var node in nodes)
                {
                    // 忽略页眉页脚
                    if (node.GetAncestor(AW.NodeType.HeaderFooter) != null)
                        continue;

                    // 检查是否有实际内容
                    if (node is AW.Paragraph paragraph)
                    {
                        if (!string.IsNullOrWhiteSpace(paragraph.GetText()))
                        {
                            isEmpty = false;
                            break;
                        }
                    }
                    else if (node is AW.Tables.Table || node is AW.Drawing.Shape)
                    {
                        isEmpty = false;
                        break;
                    }
                }

                // 如果节为空，删除它
                if (isEmpty)
                {
                    section.Remove();
                    i--; // 调整索引，因为我们删除了一个节
                }
            }

            logger.Log("已删除空白页");
        }

        private void DeleteWatermarks(AW.Document doc)
        {
            try
            {
                logger.Log("开始删除文档水印...");

                // 使用Aspose.Words的内置水印删除API
                doc.Watermark.Remove();

                logger.Log("文档水印删除完成");
            }
            catch (Exception ex)
            {
                logger.LogError($"删除水印时出错: {ex.Message}", ex);
            }
        }

        private void DeleteFormats(AW.Document doc, DeleteSettings settings)
        {
            if (!settings.DeleteFormats) return;

            logger.Log("开始删除文档格式...");

            // 设置默认字体和字号
            string defaultFontName = settings.DefaultFontName ?? "宋体";
            double defaultFontSize = settings.DefaultFontSize > 0 ? settings.DefaultFontSize : 12;

            // 处理字体格式 - 应用默认字体
            if (settings.DeleteFontFormats)
            {
                // 处理所有Run元素
                var runs = doc.GetChildNodes(AW.NodeType.Run, true).Cast<AW.Run>().ToList();
                foreach (var run in runs)
                {
                    // 保存需要保留的格式属性
                    bool isBold = run.Font.Bold;
                    bool isItalic = run.Font.Italic;
                    bool isUnderline = run.Font.Underline != AW.Underline.None;

                    // 清除所有字体格式
                    run.Font.ClearFormatting();

                    // 设置默认字体和大小
                    run.Font.Name = defaultFontName;
                    run.Font.Size = defaultFontSize;

                    // 根据设置恢复需要保留的格式
                    if (settings.PreserveBold && isBold)
                        run.Font.Bold = true;

                    if (settings.PreserveItalic && isItalic)
                        run.Font.Italic = true;

                    if (settings.PreserveUnderline && isUnderline)
                        run.Font.Underline = AW.Underline.Single;
                }
                logger.Log("已删除字体格式并应用默认字体");
            }

            // 处理段落格式
            if (settings.DeleteParagraphFormats)
            {
                var paragraphs = doc.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();
                foreach (var paragraph in paragraphs)
                {
                    // 清除段落格式
                    paragraph.ParagraphFormat.ClearFormatting();
                }
                logger.Log("已删除段落格式");
            }

            // 处理表格格式
            if (settings.DeleteTableFormats)
            {
                var tables = doc.GetChildNodes(AW.NodeType.Table, true).Cast<AW.Tables.Table>().ToList();
                foreach (var table in tables)
                {
                    try
                    {
                        // 清除表格边框
                        table.SetBorders(AW.LineStyle.None, 0, Color.Empty);

                        // 清除单元格格式
                        for (int i = 0; i < table.Rows.Count; i++)
                        {
                            var row = table.Rows[i];
                            for (int j = 0; j < row.Cells.Count; j++)
                            {
                                var cell = row.Cells[j];
                                cell.CellFormat.Borders.ClearFormatting();
                                cell.CellFormat.Shading.ClearFormatting();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"处理表格格式时出错: {ex.Message}", ex);
                    }
                }
                logger.Log("已删除表格格式");
            }

            // 处理列表格式
            if (settings.DeleteListFormats)
            {
                try
                {
                    var paragraphs = doc.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();
                    foreach (var paragraph in paragraphs)
                    {
                        if (paragraph.IsListItem)
                        {
                            // 移除列表格式
                            paragraph.ListFormat.RemoveNumbers();
                        }
                    }
                    logger.Log("已删除列表格式");
                }
                catch (Exception ex)
                {
                    logger.LogError($"处理列表格式时出错: {ex.Message}", ex);
                }
            }

            // 处理页眉页脚格式
            if (settings.DeleteHeaderFooterFormats)
            {
                try
                {
                    foreach (AW.Section section in doc.Sections)
                    {
                        if (section.HeadersFooters != null)
                        {
                            foreach (AW.HeaderFooter headerFooter in section.HeadersFooters)
                            {
                                if (headerFooter != null)
                                {
                                    // 获取页眉页脚中的段落
                                    var headerParas = headerFooter.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();
                                    foreach (var para in headerParas)
                                    {
                                        if (settings.DeleteParagraphFormats)
                                        {
                                            para.ParagraphFormat.ClearFormatting();
                                        }

                                        if (settings.DeleteFontFormats)
                                        {
                                            foreach (AW.Run run in para.Runs)
                                            {
                                                bool isBold = run.Font.Bold;
                                                bool isItalic = run.Font.Italic;
                                                bool isUnderline = run.Font.Underline != AW.Underline.None;

                                                run.Font.ClearFormatting();
                                                run.Font.Name = defaultFontName;
                                                run.Font.Size = defaultFontSize;

                                                if (settings.PreserveBold && isBold)
                                                    run.Font.Bold = true;

                                                if (settings.PreserveItalic && isItalic)
                                                    run.Font.Italic = true;

                                                if (settings.PreserveUnderline && isUnderline)
                                                    run.Font.Underline = AW.Underline.Single;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    logger.Log("已删除页眉页脚格式");
                }
                catch (Exception ex)
                {
                    logger.LogError($"处理页眉页脚格式时出错: {ex.Message}", ex);
                }
            }

            // 处理超链接
            if (settings.DeleteHyperlinks)
            {
                DeleteHyperlinks(doc);
            }

            logger.Log("文档格式删除完成");
        }

        private void DeleteHyperlinks(AW.Document doc)
        {
            try
            {
                // 寻找所有超链接字段并删除
                foreach (AW.Fields.Field field in doc.Range.Fields)
                {
                    if (field.Type == AW.Fields.FieldType.FieldHyperlink)
                    {
                        // 提取超链接显示文本
                        string linkText = "";

                        // 提取超链接显示文本
                        if (field.DisplayResult != null)
                        {
                            linkText = field.DisplayResult.ToString().Trim();
                        }

                        // 如果提取不到显示文本，使用默认文本
                        if (string.IsNullOrEmpty(linkText))
                        {
                            linkText = "[链接]";
                        }

                        // 解除超链接，保留文本
                        field.Unlink();
                    }
                }
                logger.Log("已删除超链接格式");
            }
            catch (Exception ex)
            {
                logger.LogError($"处理超链接时出错: {ex.Message}", ex);
            }
        }

        public async Task<string> ConvertToPdf(string wordFilePath, string outputDirectory, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(wordFilePath))
            {
                throw new ArgumentNullException(nameof(wordFilePath));
            }

            if (string.IsNullOrEmpty(outputDirectory))
            {
                throw new ArgumentNullException(nameof(outputDirectory));
            }

            // 检查是否是临时文件（以~$开头）
            string fileName = Path.GetFileName(wordFilePath);
            if (fileName.StartsWith("~$"))
            {
                logger.LogWarning($"跳过Word临时文件: {wordFilePath}");
                throw new InvalidOperationException($"跳过Word临时文件: {wordFilePath}");
            }

            if (!File.Exists(wordFilePath))
            {
                throw new FileNotFoundException("Word文件不存在", wordFilePath);
            }

            var fileNameWithoutExt = Path.GetFileNameWithoutExtension(wordFilePath);
            var pdfOutputFilePath = Path.Combine(outputDirectory, $"{fileNameWithoutExt}.pdf");
            var wordOutputFilePath = Path.Combine(outputDirectory, fileName);

            try
            {
                // 确保输出目录存在（包括所有父目录）
                if (!Directory.Exists(outputDirectory))
                {
                    // 尝试获取父目录
                    string? parentDir = Path.GetDirectoryName(outputDirectory);

                    // 确保父目录存在后再创建目标目录
                    if (parentDir != null && !Directory.Exists(parentDir))
                    {
                        Directory.CreateDirectory(parentDir);
                        logger.Log($"已创建父目录: {parentDir}");
                    }

                    // 现在创建输出目录
                    Directory.CreateDirectory(outputDirectory);
                    logger.Log($"已创建输出目录: {outputDirectory}");
                }
            }
            catch (Exception ex)
            {
                throw new DirectoryNotFoundException($"无法创建输出目录: {outputDirectory}. 原因: {ex.Message}");
            }

            // 检查PDF文件是否存在
            if (File.Exists(pdfOutputFilePath))
            {
                switch (settings.ConflictHandling.ToLower())
                {
                    case "跳过":
                        logger.Log($"跳过已存在的文件: {pdfOutputFilePath}");
                        return pdfOutputFilePath;
                    case "重命名":
                        pdfOutputFilePath = GetUniqueFileName(pdfOutputFilePath);
                        // 同时修改Word输出文件路径，保持命名一致
                        string newFileName = Path.GetFileNameWithoutExtension(pdfOutputFilePath);
                        string wordExt = Path.GetExtension(wordFilePath);
                        wordOutputFilePath = Path.Combine(outputDirectory, $"{newFileName}{wordExt}");
                        break;
                    // 默认覆盖
                }
            }

            AW.Document? doc = null;
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 加载Word文档，添加文件访问错误的处理逻辑
                try
                {
                    doc = new AW.Document(wordFilePath);
                }
                catch (IOException ioEx) when (ioEx.Message.Contains("being used by another process"))
                {
                    // 文件被占用
                    logger.LogError($"PDF转换: 文件被其他程序占用，无法打开: {wordFilePath}", ioEx);
                    throw new IOException($"PDF转换: 文件被其他程序占用: {ioEx.Message}", ioEx);
                }
                catch (Exception ex)
                {
                    logger.LogError($"PDF转换: 打开文件失败: {wordFilePath}, 错误: {ex.Message}", ex);
                    throw;
                }

                // 首先保存Word文档的副本到目标目录
                try
                {
                    // 如果输出文件已存在并且选择了"跳过"选项，则不尝试保存
                    if (File.Exists(wordOutputFilePath) && settings.ConflictHandling.ToLower() == "跳过")
                    {
                        logger.Log($"跳过已存在的Word文件: {wordOutputFilePath}");
                    }
                    else
                    {
                        // 如果目标文件已存在，尝试删除它（覆盖模式）
                        if (File.Exists(wordOutputFilePath) && settings.ConflictHandling.ToLower() != "重命名")
                        {
                            try
                            {
                                File.Delete(wordOutputFilePath);
                            }
                            catch (Exception ex)
                            {
                                logger.LogWarning($"无法删除已存在的文件: {wordOutputFilePath}, {ex.Message}");
                            }
                        }

                        // 保存Word文档副本
                        await Task.Run(() => doc.Save(wordOutputFilePath), cancellationToken);
                        logger.Log($"已保存Word文档副本: {wordOutputFilePath}");
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError($"保存Word文档副本失败: {ex.Message}", ex);
                    // 继续处理PDF转换，不因Word副本保存失败而中断
                }

                // 创建PDF保存选项
                AW.Saving.PdfSaveOptions pdfSaveOptions;

                // 使用配置中的PDF设置，如果存在
                if (settings.PdfSettings != null)
                {
                    pdfSaveOptions = settings.PdfSettings.CreateSaveOptions();
                    logger.Log("应用自定义PDF转换设置");
                }
                else
                {
                    // 使用默认设置
                    pdfSaveOptions = new AW.Saving.PdfSaveOptions
                    {
                        SaveFormat = AW.SaveFormat.Pdf,
                        TextCompression = AW.Saving.PdfTextCompression.Flate,
                        JpegQuality = 90,
                        ImageCompression = AW.Saving.PdfImageCompression.Jpeg,
                        ExportDocumentStructure = true,
                        EmbedFullFonts = true,
                        CreateNoteHyperlinks = true,
                        Compliance = AW.Saving.PdfCompliance.Pdf17,
                        DmlEffectsRenderingMode = AW.Saving.DmlEffectsRenderingMode.Fine,
                        AdditionalTextPositioning = true,
                        DisplayDocTitle = true,
                        DownsampleOptions = new AW.Saving.DownsampleOptions
                        {
                            DownsampleImages = false
                        }
                    };
                    logger.Log("应用默认PDF转换设置");
                }

                // 保存为PDF文件
                await Task.Run(() => doc.Save(pdfOutputFilePath, pdfSaveOptions), cancellationToken);

                logger.Log($"成功将Word文件转换为PDF: {pdfOutputFilePath}");
                return pdfOutputFilePath;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(wordFilePath, ex);
                logger.LogError($"转换PDF时出错: {ex.Message}", ex);
                throw;
            }
            finally
            {
                // 释放资源
                doc = null;
            }
        }

        public void Dispose()
        {
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
            cancellationTokenSource = null;
        }

        // 添加图片格式化方法
        private void ApplyImageFormat(AW.Document doc, ParagraphMatchRule formatRule)
        {
            // 检查是否启用图片格式，并且至少有一个子功能被启用
            bool hasAnyImageFormatEnabled = formatRule.EnableImageFormat &&
                (formatRule.EnableImageSize || formatRule.EnableImageWrap ||
                 formatRule.EnableImageEffect || formatRule.EnableImageBorder ||
                 formatRule.EnableImagePosition || formatRule.EnableImageRotateFlip ||
                 formatRule.EnableImageHyperlink || formatRule.EnableImageLocking);

            if (hasAnyImageFormatEnabled)
            {
                var shapes = doc.GetChildNodes(AW.NodeType.Shape, true).Cast<AW.Drawing.Shape>().ToList();
                int imagesCount = 0;

                foreach (var shape in shapes)
                {
                    if (shape.HasImage)
                    {
                        formatRule.ApplyToShape(shape);
                        imagesCount++;
                    }
                }

                if (imagesCount > 0)
                {
                    logger.Log($"应用图片格式到 {imagesCount} 个图片");
                }
            }
            else if (formatRule.EnableImageFormat)
            {
                logger.Log("图片格式已启用，但没有勾选任何子功能，跳过图片格式处理");
            }

            // 应用水印
            if (formatRule.EnableWatermark)
            {
                ApplyWatermark(doc, formatRule);
            }
        }

        private void ApplyWatermark(AW.Document doc, ParagraphMatchRule formatRule)
        {
            try
            {
                if (formatRule.EnableWatermark)
                {
                    logger.Log($"正在应用文本水印：{formatRule.WatermarkText}");
                }

                if (formatRule.EnableImageWatermark && !string.IsNullOrEmpty(formatRule.WatermarkImagePath))
                {
                    logger.Log($"正在应用图片水印：{formatRule.WatermarkImagePath}");
                }

                // 使用ParagraphMatchRule中的方法应用水印
                // 该方法已经更新为使用Aspose.Words的Watermark API
                formatRule.ApplyWatermarkToDocument(doc);

                logger.Log("水印添加成功");
            }
            catch (Exception ex)
            {
                logger.LogError($"应用水印时出错: {ex.Message}", ex);
            }
        }

        private void LogDetailedError(string filePath, Exception ex)
        {
            var errorMessage = new StringBuilder();
            errorMessage.AppendLine($"处理文件失败：{filePath}");
            errorMessage.AppendLine($"错误类型：{ex.GetType().Name}");
            errorMessage.AppendLine($"错误消息：{ex.Message}");
            errorMessage.AppendLine($"堆栈跟踪：{ex.StackTrace}");

            if (ex.InnerException != null)
            {
                errorMessage.AppendLine("内部异常：");
                errorMessage.AppendLine($"类型：{ex.InnerException.GetType().Name}");
                errorMessage.AppendLine($"消息：{ex.InnerException.Message}");
                errorMessage.AppendLine($"堆栈跟踪：{ex.InnerException.StackTrace}");
            }

            logger.LogError(errorMessage.ToString(), ex);
        }

        public void UpdateSettings(Models.Settings newSettings)
        {
            if (newSettings == null)
                throw new ArgumentNullException(nameof(newSettings));

            settings = newSettings;
        }



        private void DeleteDocumentElements(AW.Document doc, DeleteSettings settings)
        {
            if (!settings.DeleteBookmarks && !settings.DeleteFields && !settings.DeleteTableOfContents &&
                !settings.DeleteIndex && !settings.DeleteCrossReferences && !settings.DeleteFormFields &&
                !settings.DeleteSmartArt && !settings.DeleteCharts && !settings.DeleteOleObjects &&
                !settings.DeleteActiveXControls)
            {
                return;
            }

            try
            {
                if (settings.DeleteBookmarks)
                {
                    doc.Range.Bookmarks.Clear();
                    logger.Log("已删除所有书签");
                }

                if (settings.DeleteFields)
                {
                    // 获取所有字段
                    var fields = doc.Range.Fields.ToList();
                    int count = 0;
                    foreach (var field in fields)
                    {
                        // 保留某些特殊字段（如页码等）
                        if (!field.Type.ToString().Contains("PAGE"))
                        {
                            field.Remove();
                            count++;
                        }
                    }
                    logger.Log($"已删除 {count} 个域");
                }

                if (settings.DeleteTableOfContents)
                {
                    // 删除目录字段
                    var fields = doc.Range.Fields.Where(f => f.Type.ToString().Contains("TOC")).ToList();
                    foreach (var field in fields)
                    {
                        field.Remove();
                    }
                    logger.Log($"已删除 {fields.Count} 个目录");
                }

                if (settings.DeleteIndex)
                {
                    var fields = doc.Range.Fields.Where(f => f.Type.ToString().Contains("INDEX")).ToList();
                    foreach (var field in fields)
                    {
                        field.Remove();
                    }
                    logger.Log($"已删除 {fields.Count} 个索引");
                }

                if (settings.DeleteCrossReferences)
                {
                    var fields = doc.Range.Fields.Where(f => f.Type.ToString().Contains("REF")).ToList();
                    foreach (var field in fields)
                    {
                        field.Remove();
                    }
                    logger.Log($"已删除 {fields.Count} 个交叉引用");
                }

                if (settings.DeleteFormFields)
                {
                    // 删除传统表单域
                    var formFields = doc.Range.FormFields;
                    int formFieldCount = formFields.Count;
                    formFields.Clear();

                    // 删除内容控件
                    var contentControls = doc.GetChildNodes(NodeType.StructuredDocumentTag, true).ToList();
                    foreach (var control in contentControls)
                    {
                        control.Remove();
                    }

                    logger.Log($"已删除 {formFieldCount + contentControls.Count} 个表单域");
                }

                if (settings.DeleteSmartArt)
                {
                    var smartArts = doc.GetChildNodes(NodeType.Shape, true)
                        .Cast<Shape>()
                        .Where(s => s.ShapeType == ShapeType.NonPrimitive)
                        .ToList();

                    foreach (var smartArt in smartArts)
                    {
                        smartArt.Remove();
                    }

                    logger.Log($"已删除 {smartArts.Count} 个SmartArt图形");
                }

                if (settings.DeleteCharts)
                {
                    var charts = doc.GetChildNodes(NodeType.Shape, true)
                        .Cast<Shape>()
                        .Where(s => s.HasChart)
                        .ToList();

                    foreach (var chart in charts)
                    {
                        chart.Remove();
                    }

                    logger.Log($"已删除 {charts.Count} 个图表");
                }

                if (settings.DeleteOleObjects)
                {
                    var oleObjects = doc.GetChildNodes(NodeType.Shape, true)
                        .Cast<Shape>()
                        .Where(s => s.OleFormat != null && !s.OleFormat.IsLink)
                        .ToList();

                    foreach (var oleObject in oleObjects)
                    {
                        oleObject.Remove();
                    }

                    logger.Log($"已删除 {oleObjects.Count} 个OLE对象");
                }

                if (settings.DeleteActiveXControls)
                {
                    var activeXControls = doc.GetChildNodes(NodeType.Shape, true)
                        .Cast<Shape>()
                        .Where(s => s.OleFormat != null && s.OleFormat.IsLink)
                        .ToList();

                    foreach (var control in activeXControls)
                    {
                        control.Remove();
                    }

                    logger.Log($"已删除 {activeXControls.Count} 个ActiveX控件");
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"删除文档元素时发生错误: {ex.Message}", ex);
            }
        }

        private void DeleteAdvancedFormats(Document doc, DeleteSettings settings)
        {
            if (!settings.DeleteCharacterSpacing && !settings.DeleteCharacterScaling &&
                !settings.DeleteCharacterPosition && !settings.DeleteTextEffects &&
                !settings.DeleteParagraphBorders && !settings.DeleteParagraphShading &&
                !settings.DeleteCellBorders && !settings.DeleteCellShading &&
                !settings.DeleteCellMerging && !settings.DeleteCellSplitting)
            {
                return;
            }

            try
            {
                foreach (Run run in doc.GetChildNodes(NodeType.Run, true))
                {
                    if (settings.DeleteCharacterSpacing)
                    {
                        run.Font.Spacing = 0;
                        run.Font.Kerning = 0;
                    }
                    if (settings.DeleteCharacterScaling)
                    {
                        run.Font.Scaling = 100;
                        run.Font.Spacing = 1;
                    }
                    if (settings.DeleteCharacterPosition)
                    {
                        run.Font.Position = 0;
                        run.Font.Subscript = false;
                        run.Font.Superscript = false;
                    }
                    if (settings.DeleteTextEffects)
                    {
                        run.Font.Emboss = false;
                        run.Font.Engrave = false;
                        run.Font.Shadow = false;
                        run.Font.Outline = false;
                        run.Font.SmallCaps = false;
                        run.Font.AllCaps = false;
                        run.Font.DoubleStrikeThrough = false;
                        run.Font.Hidden = false;
                        run.Font.SnapToGrid = true;
                        run.Font.Scaling = 100;
                        run.Font.Spacing = 0;
                        run.Font.Kerning = 0;
                    }
                }

                foreach (Paragraph para in doc.GetChildNodes(NodeType.Paragraph, true))
                {
                    if (settings.DeleteParagraphBorders)
                    {
                        para.ParagraphFormat.Borders.ClearFormatting();
                    }
                    if (settings.DeleteParagraphShading)
                    {
                        para.ParagraphFormat.Shading.ClearFormatting();
                    }
                }

                foreach (Cell cell in doc.GetChildNodes(NodeType.Cell, true))
                {
                    if (settings.DeleteCellBorders)
                    {
                        cell.CellFormat.Borders.ClearFormatting();
                    }
                    if (settings.DeleteCellShading)
                    {
                        cell.CellFormat.Shading.ClearFormatting();
                    }
                    if (settings.DeleteCellMerging || settings.DeleteCellSplitting)
                    {
                        // 获取单元格所在的表格
                        var table = cell.ParentRow.ParentTable;

                        // 如果是合并单元格，先拆分
                        if (cell.CellFormat.HorizontalMerge != CellMerge.None ||
                            cell.CellFormat.VerticalMerge != CellMerge.None)
                        {
                            try
                            {
                                // 拆分水平合并
                                if (cell.CellFormat.HorizontalMerge != CellMerge.None)
                                {
                                    cell.CellFormat.HorizontalMerge = CellMerge.None;
                                }

                                // 拆分垂直合并
                                if (cell.CellFormat.VerticalMerge != CellMerge.None)
                                {
                                    cell.CellFormat.VerticalMerge = CellMerge.None;
                                }
                            }
                            catch (Exception ex)
                            {
                                logger.LogError($"拆分单元格时出错: {ex.Message}", ex);
                            }
                        }
                    }
                }

                logger.Log("已删除高级格式");
            }
            catch (Exception ex)
            {
                logger.LogError($"删除高级格式时发生错误: {ex.Message}", ex);
            }
        }

        private void DeleteDocumentStructure(Document doc, DeleteSettings settings)
        {
            if (!settings.DeleteColumns && !settings.DeleteTextBoxes && !settings.DeleteShapes &&
                !settings.DeleteWordArt && !settings.DeleteMargins && !settings.DeletePageBorders &&
                !settings.DeletePageBackground)
            {
                return;
            }

            try
            {
                if (settings.DeleteColumns)
                {
                    foreach (Section section in doc.Sections)
                    {
                        section.PageSetup.TextColumns.SetCount(1);
                    }
                    logger.Log("已删除分栏");
                }

                if (settings.DeleteTextBoxes)
                {
                    var textBoxes = doc.GetChildNodes(NodeType.Shape, true)
                        .Cast<Shape>()
                        .Where(s => s.ShapeType == ShapeType.TextBox)
                        .ToList();

                    foreach (var textBox in textBoxes)
                    {
                        textBox.Remove();
                    }

                    logger.Log($"已删除 {textBoxes.Count} 个文本框");
                }

                if (settings.DeleteShapes)
                {
                    var shapes = doc.GetChildNodes(NodeType.Shape, true)
                        .Cast<Shape>()
                        .Where(s => !s.HasChart &&
                                  s.ShapeType != ShapeType.TextBox &&
                                  s.ShapeType != ShapeType.OleObject)
                        .ToList();

                    foreach (var shape in shapes)
                    {
                        shape.Remove();
                    }

                    logger.Log($"已删除 {shapes.Count} 个形状");
                }

                if (settings.DeleteWordArt)
                {
                    var wordArts = doc.GetChildNodes(NodeType.Shape, true)
                        .Cast<Shape>()
                        .Where(s => s.TextPath != null)
                        .ToList();

                    foreach (var wordArt in wordArts)
                    {
                        wordArt.Remove();
                    }

                    logger.Log($"已删除 {wordArts.Count} 个艺术字");
                }

                if (settings.DeleteMargins)
                {
                    foreach (Section section in doc.Sections)
                    {
                        // 设置为默认边距（1英寸 = 72点）
                        section.PageSetup.LeftMargin = 72;
                        section.PageSetup.RightMargin = 72;
                        section.PageSetup.TopMargin = 72;
                        section.PageSetup.BottomMargin = 72;
                        section.PageSetup.HeaderDistance = 36;
                        section.PageSetup.FooterDistance = 36;
                    }

                    logger.Log("已删除页边距");
                }

                if (settings.DeletePageBorders)
                {
                    foreach (Section section in doc.Sections)
                    {
                        section.PageSetup.Borders.ClearFormatting();
                    }

                    logger.Log("已删除页面边框");
                }

                if (settings.DeletePageBackground)
                {
                    foreach (Section section in doc.Sections)
                    {
                        // 清除页面背景色
                        section.PageSetup.Borders.ClearFormatting();

                        // 删除背景形状
                        var backgroundShapes = section.GetChildNodes(NodeType.Shape, true)
                            .Cast<Shape>()
                            .Where(s => s.BehindText &&
                                      s.RelativeHorizontalPosition == RelativeHorizontalPosition.Page &&
                                      s.RelativeVerticalPosition == RelativeVerticalPosition.Page)
                            .ToList();

                        foreach (var shape in backgroundShapes)
                        {
                            shape.Remove();
                        }
                    }

                    logger.Log("已删除页面背景");
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"删除文档结构时发生错误: {ex.Message}", ex);
            }
        }

        private void DeleteDocumentProperties(AW.Document doc, DeleteSettings settings)
        {
            try
            {
                logger.Log("开始删除文档属性...");

                if (settings.DeleteDocumentProperties)
                {
                    // 清除内置文档属性
                    doc.BuiltInDocumentProperties.Clear();
                    logger.Log("已删除内置文档属性");
                }

                if (settings.DeleteCustomProperties)
                {
                    // 清除自定义文档属性
                    doc.CustomDocumentProperties.Clear();
                    logger.Log("已删除自定义文档属性");
                }

                if (settings.DeleteDocumentVariables)
                {
                    doc.Variables.Clear();
                    logger.Log("已删除文档变量");
                }

                if (settings.DeleteDocumentStatistics)
                {
                    // 注意：文档统计信息是只读的，无法直接删除
                    logger.Log("文档统计信息是只读的，无法删除");
                }

                if (settings.DeleteHiddenText)
                {
                    var hiddenRuns = doc.GetChildNodes(AW.NodeType.Run, true).Cast<AW.Run>()
                        .Where(r => r.Font.Hidden)
                        .ToList();
                    foreach (var run in hiddenRuns)
                    {
                        run.Remove();
                    }
                    logger.Log($"已删除 {hiddenRuns.Count} 处隐藏文本");
                }

                if (settings.DeleteRevisionMarks)
                {
                    doc.AcceptAllRevisions();
                    logger.Log("已删除修订标记");
                }

                if (settings.DeleteCompareResults)
                {
                    // 注意：比较结果通常以修订标记的形式存在，通过删除修订标记来清除
                    doc.AcceptAllRevisions();
                    logger.Log("已删除比较结果");
                }

                if (settings.DeleteDocumentVersions)
                {
                    // 注意：文档版本信息通常存储在文档属性中，通过删除文档属性来清除
                    doc.BuiltInDocumentProperties.Clear();
                    logger.Log("已删除文档版本");
                }

                if (settings.DeleteDocumentTheme)
                {
                    // 清除主题相关的所有值
                    foreach (var style in doc.Styles)
                    {
                        if (style.Type == AW.StyleType.Paragraph)
                        {
                            // 设置字体和颜色为明确的值
                            style.Font.Name = settings.DefaultFontName;
                            style.Font.Color = Color.FromArgb(0, 0, 0);
                        }
                    }
                    logger.Log("已删除文档主题");
                }

                if (settings.DeleteDocumentStyles)
                {
                    foreach (var style in doc.Styles)
                    {
                        if (!style.BuiltIn)
                        {
                            style.Remove();
                        }
                    }
                    logger.Log("已删除文档样式");
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"删除文档属性时发生错误: {ex.Message}", ex);
            }
        }

        // 处理TXT文件转换为Word
        private string ConvertTxtToWord(string txtFilePath, string outputDirectory, CancellationToken cancellationToken)
        {
            try
            {
                // 检查是否启用了TXT转Word
                if (settings.TxtSettings?.ConvertToWordBeforeProcessing != true)
                {
                    // 如果未启用，返回原文件路径表示未转换
                    logger.Log($"TXT转Word功能未启用，跳过转换: {txtFilePath}");
                    return txtFilePath;
                }

                logger.Log($"准备将TXT文件转换为Word: {txtFilePath}");

                // 确定输出文件路径
                string fileName = Path.GetFileNameWithoutExtension(txtFilePath);
                string outputFormat = GetOutputFormatExtension();
                string outputFilePath = Path.Combine(outputDirectory, $"{fileName}.{outputFormat}");

                // 确保输出目录存在
                Directory.CreateDirectory(outputDirectory);

                // 检查输出文件是否已存在
                if (File.Exists(outputFilePath))
                {
                    switch (settings.ConflictHandling.ToLower())
                    {
                        case "跳过":
                            logger.Log($"输出文件已存在，跳过转换: {outputFilePath}");
                            return string.Empty; // 返回空字符串，表示转换被跳过
                        case "重命名":
                            outputFilePath = GetUniqueFileName(outputFilePath);
                            logger.Log($"输出文件已存在，重命名为: {outputFilePath}");
                            break;
                        default:
                            // 覆盖模式，继续处理
                            break;
                    }
                }

                // 读取TXT文件内容
                string encoding = settings.TxtSettings.DefaultEncoding;
                string content;

                // 根据设置选择编码读取文件
                if (settings.TxtSettings.EnableEncodingDetection)
                {
                    // 这里可以添加更复杂的编码检测逻辑
                    // 为简单起见，目前使用默认编码
                    content = File.ReadAllText(txtFilePath, System.Text.Encoding.GetEncoding(encoding));
                }
                else
                {
                    content = File.ReadAllText(txtFilePath, System.Text.Encoding.GetEncoding(encoding));
                }

                // 创建新的Word文档
                var doc = new Document();
                var builder = new DocumentBuilder(doc);

                // 设置默认字体和格式
                if (settings.TxtSettings.EnableTextStyling)
                {
                    builder.Font.Name = settings.TxtSettings.DefaultFontName;
                    builder.Font.Size = settings.TxtSettings.DefaultFontSize;
                }

                // 处理内容
                ProcessTxtContent(content, builder);

                // 保存文档
                SaveFormat saveFormat = settings.TxtSettings.ConvertToFormat;
                doc.Save(outputFilePath, saveFormat);

                logger.Log($"TXT文件已成功转换为Word: {outputFilePath}");
                return outputFilePath;
            }
            catch (Exception ex)
            {
                logger.LogError($"转换TXT文件时出错: {txtFilePath}", ex);
                // 出错时返回空字符串，表示转换失败
                return string.Empty;
            }
        }

        // 获取输出格式的文件扩展名
        private string GetOutputFormatExtension()
        {
            switch (settings.TxtSettings?.ConvertToFormat ?? SaveFormat.Docx)
            {
                case SaveFormat.Doc:
                    return "doc";
                case SaveFormat.Rtf:
                    return "rtf";
                case SaveFormat.Docx:
                default:
                    return "docx";
            }
        }

        // 处理TXT内容
        private void ProcessTxtContent(string content, DocumentBuilder builder)
        {
            var txtSettings = settings.TxtSettings;
            if (txtSettings == null)
                return;

            // 按行分割内容
            string[] lines = content.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.None);

            // 段落处理
            if (txtSettings.EnableParagraphProcessing)
            {
                ProcessParagraphsInTxt(lines, builder);
            }
            else
            {
                // 简单处理：每行作为一个段落
                foreach (string line in lines)
                {
                    builder.Writeln(line);
                }
            }
        }

        // 处理TXT中的段落
        private void ProcessParagraphsInTxt(string[] lines, DocumentBuilder builder)
        {
            var txtSettings = settings.TxtSettings;
            if (txtSettings == null)
                return;

            int consecutiveBlankLines = 0;
            System.Text.StringBuilder paragraphText = new System.Text.StringBuilder();

            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i];

                // 检测空行
                if (string.IsNullOrWhiteSpace(line))
                {
                    consecutiveBlankLines++;

                    // 检查是否达到段落分隔阈值
                    if (txtSettings.TreatConsecutiveBlankLinesAsParagraph &&
                        consecutiveBlankLines >= txtSettings.BlankLinesThreshold)
                    {
                        // 写入累积的段落文本
                        if (paragraphText.Length > 0)
                        {
                            builder.Writeln(paragraphText.ToString());
                            paragraphText.Clear();
                        }

                        // 添加一个空行作为段落分隔
                        builder.Writeln();
                        consecutiveBlankLines = 0;
                    }
                }
                else
                {
                    // 检查是否需要处理表格
                    if (txtSettings.EnableTableDetection && IsTableRow(line, txtSettings.TableDelimiter))
                    {
                        // 如果有累积的段落文本，先写入
                        if (paragraphText.Length > 0)
                        {
                            builder.Writeln(paragraphText.ToString());
                            paragraphText.Clear();
                        }

                        // 处理表格行
                        ProcessTableRow(line, builder, txtSettings.TableDelimiter);
                    }
                    // 检查是否需要处理列表
                    else if (txtSettings.EnableListDetection &&
                            ((txtSettings.DetectNumberedLists && IsNumberedListItem(line)) ||
                             (txtSettings.DetectBulletedLists && IsBulletedListItem(line))))
                    {
                        // 如果有累积的段落文本，先写入
                        if (paragraphText.Length > 0)
                        {
                            builder.Writeln(paragraphText.ToString());
                            paragraphText.Clear();
                        }

                        // 处理列表项
                        ProcessListItem(line, builder);
                    }
                    // 检查是否需要处理标题
                    else if (txtSettings.EnableHeadingDetection && IsHeading(line, i, lines))
                    {
                        // 如果有累积的段落文本，先写入
                        if (paragraphText.Length > 0)
                        {
                            builder.Writeln(paragraphText.ToString());
                            paragraphText.Clear();
                        }

                        // 处理标题
                        ProcessHeading(line, builder);
                    }
                    else
                    {
                        // 普通文本行处理
                        if (txtSettings.PreserveLineBreaks)
                        {
                            // 保留换行符：每行作为单独的段落
                            if (paragraphText.Length > 0)
                            {
                                // 写入之前累积的段落文本
                                builder.Writeln(paragraphText.ToString());
                                paragraphText.Clear();
                            }
                            // 添加当前行
                            paragraphText.Append(line);
                        }
                        else
                        {
                            // 不保留换行符：将多行合并为一个段落
                            if (paragraphText.Length > 0)
                            {
                                paragraphText.Append(" ");
                            }
                            paragraphText.Append(line);
                        }
                        consecutiveBlankLines = 0;
                    }
                }
            }

            // 处理最后累积的段落文本
            if (paragraphText.Length > 0)
            {
                builder.Writeln(paragraphText.ToString());
            }
        }

        // 检查是否为表格行
        private bool IsTableRow(string line, string delimiter)
        {
            if (string.IsNullOrEmpty(line))
                return false;

            // 简单判断：包含分隔符且至少有两列
            string[] columns = line.Split(new[] { delimiter }, StringSplitOptions.None);
            return columns.Length >= 2;
        }

        // 处理表格行
        private void ProcessTableRow(string line, DocumentBuilder builder, string delimiter)
        {
            string[] cells = line.Split(new[] { delimiter }, StringSplitOptions.None);

            // 开始一个表格行
            builder.StartTable();

            foreach (string cell in cells)
            {
                builder.InsertCell();
                builder.Write(cell.Trim());
            }

            // 结束表格行
            builder.EndRow();
            builder.EndTable();
        }

        // 检查是否为编号列表项
        private bool IsNumberedListItem(string line)
        {
            // 简单判断：以数字和点或括号开头
            return System.Text.RegularExpressions.Regex.IsMatch(line.Trim(), @"^\d+[\.\)\]]");
        }

        // 检查是否为项目符号列表项
        private bool IsBulletedListItem(string line)
        {
            // 简单判断：以项目符号标记开头
            string trimmed = line.Trim();
            return trimmed.StartsWith("-") || trimmed.StartsWith("*") || trimmed.StartsWith("•");
        }

        // 处理列表项
        private void ProcessListItem(string line, DocumentBuilder builder)
        {
            // 简单处理：不实际创建Word列表，只写入文本
            builder.Writeln(line);
        }

        // 检查是否为标题
        private bool IsHeading(string line, int lineIndex, string[] allLines)
        {
            if (string.IsNullOrEmpty(line))
                return false;

            // 简单判断：短行且后面是空行
            bool isShortLine = line.Length < 80 && !line.EndsWith(".");
            bool followedByBlankLine = lineIndex < allLines.Length - 1 && string.IsNullOrWhiteSpace(allLines[lineIndex + 1]);

            return isShortLine && followedByBlankLine;
        }

        // 处理标题
        private void ProcessHeading(string line, DocumentBuilder builder)
        {
            // 简单处理：使用粗体
            builder.Font.Bold = true;
            builder.Font.Size += 2;
            builder.Writeln(line);
            builder.Font.Bold = false;
            builder.Font.Size -= 2;
        }

        /// <summary>
        /// 处理进度数据，用于保存和恢复处理状态
        /// </summary>
        public class ProcessingProgress
        {
            public int CompletedFiles { get; set; }
            public int FailedFiles { get; set; }
            public int RetriedFiles { get; set; }
            public List<string> RemainingFiles { get; set; } = new List<string>();
            public string SourceDirectory { get; set; } = string.Empty;
            public string OutputDirectory { get; set; } = string.Empty;
            public DateTime TimeStamp { get; set; }
        }

        /// <summary>
        /// 获取当前CPU使用率
        /// </summary>
        /// <returns>CPU使用率百分比</returns>
        private double GetCurrentCpuUsage()
        {
            try
            {
                using (var cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total", true))
                {
                    // 第一次读取通常返回0，需要等待一小段时间后再次读取
                    cpuCounter.NextValue();
                    Thread.Sleep(500);
                    return cpuCounter.NextValue();
                }
            }
            catch (Exception ex)
            {
                logger.LogError("获取CPU使用率时出错", ex);
                // 出错时返回默认值50%，避免影响处理
                return 50;
            }
        }

        /// <summary>
        /// 获取系统可用内存（MB）
        /// </summary>
        /// <returns>可用内存（MB）</returns>
        private long GetAvailableMemoryMB()
        {
            try
            {
                using (var memCounter = new PerformanceCounter("Memory", "Available MBytes", true))
                {
                    return (long)memCounter.NextValue();
                }
            }
            catch (Exception ex)
            {
                logger.LogError("获取可用内存时出错", ex);
                // 出错时返回默认值1024MB，避免影响处理
                return 1024;
            }
        }

        /// <summary>
        /// 内存监控类，用于监控和记录内存使用情况
        /// </summary>
        public class MemoryMonitor
        {
            private readonly Logger _logger;
            private long _lastMemoryUsage;
            private DateTime _lastCheckTime;

            public MemoryMonitor(Logger logger)
            {
                _logger = logger;
                _lastMemoryUsage = GC.GetTotalMemory(false);
                _lastCheckTime = DateTime.Now;
            }

            /// <summary>
            /// 记录当前内存使用情况
            /// </summary>
            /// <param name="context">上下文信息，用于日志记录</param>
            public void LogMemoryUsage(string context = "")
            {
                try
                {
                    long currentMemory = GC.GetTotalMemory(false);
                    long memoryDifference = currentMemory - _lastMemoryUsage;
                    TimeSpan timeSinceLastCheck = DateTime.Now - _lastCheckTime;

                    double currentMemoryMB = currentMemory / (1024.0 * 1024.0);
                    double memoryDifferenceMB = memoryDifference / (1024.0 * 1024.0);

                    string sign = memoryDifference >= 0 ? "+" : "";

                    _logger.LogDebug($"内存使用({context}): {currentMemoryMB:F2}MB ({sign}{memoryDifferenceMB:F2}MB), 距上次检查: {timeSinceLastCheck.TotalSeconds:F1}秒");

                    // 更新上次值
                    _lastMemoryUsage = currentMemory;
                    _lastCheckTime = DateTime.Now;

                    // 如果内存使用增加超过100MB，主动提示需要垃圾回收
                    if (memoryDifferenceMB > 100)
                    {
                        _logger.LogWarning($"内存使用增加过快: {memoryDifferenceMB:F2}MB，建议进行垃圾回收");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"记录内存使用情况失败: {ex.Message}");
                }
            }
        }

        // 新增方法：处理段落空格和不可见字符
        private void HandleParagraphSpacesAndInvisibleChars(AW.Document doc, DeleteSettings settings)
        {
            // 如果四个功能都未启用，直接返回
            if (!settings.DeleteParagraphLeadingSpaces &&
                !settings.DeleteParagraphTrailingSpaces &&
                !settings.DeleteParagraphLeadingInvisibleChars &&
                !settings.DeleteParagraphTrailingInvisibleChars)
            {
                return;
            }

            try
            {
                logger.Log("开始处理段落空格和不可见字符...");
                int leadingSpacesRemoved = 0;
                int trailingSpacesRemoved = 0;
                int leadingInvisibleCharsRemoved = 0;
                int trailingInvisibleCharsRemoved = 0;

                // 获取文档中的所有段落
                var paragraphs = doc.GetChildNodes(AW.NodeType.Paragraph, true).Cast<AW.Paragraph>().ToList();

                foreach (var paragraph in paragraphs)
                {
                    try
                    {
                        // 1. 处理段落开头的空格
                        if (settings.DeleteParagraphLeadingSpaces)
                        {
                            // 获取段落的第一个Run节点
                            if (paragraph.HasChildNodes)
                            {
                                // 获取第一个Run节点
                                AW.Run? firstRun = null;
                                foreach (var node in paragraph.GetChildNodes(AW.NodeType.Any, false))
                                {
                                    if (node is AW.Run run)
                                    {
                                        firstRun = run;
                                        break;
                                    }
                                }

                                if (firstRun != null)
                                {
                                    string text = firstRun.Text;
                                    if (!string.IsNullOrEmpty(text))
                                    {
                                        string trimmedText = text.TrimStart();
                                        if (text.Length != trimmedText.Length)
                                        {
                                            int removed = text.Length - trimmedText.Length;
                                            leadingSpacesRemoved += removed;
                                            firstRun.Text = trimmedText;
                                            logger.LogDebug($"从段落删除了 {removed} 个开头空格");
                                        }
                                    }
                                }
                            }
                        }

                        // 2. 处理段落结尾的空格
                        if (settings.DeleteParagraphTrailingSpaces)
                        {
                            // 获取段落的最后一个Run节点
                            if (paragraph.HasChildNodes)
                            {
                                // 获取最后一个Run节点
                                AW.Run? lastRun = null;
                                for (int i = paragraph.GetChildNodes(AW.NodeType.Any, false).Count - 1; i >= 0; i--)
                                {
                                    if (paragraph.GetChildNodes(AW.NodeType.Any, false)[i] is AW.Run run)
                                    {
                                        lastRun = run;
                                        break;
                                    }
                                }

                                if (lastRun != null)
                                {
                                    string text = lastRun.Text;
                                    if (!string.IsNullOrEmpty(text))
                                    {
                                        string trimmedText = text.TrimEnd();
                                        if (text.Length != trimmedText.Length)
                                        {
                                            int removed = text.Length - trimmedText.Length;
                                            trailingSpacesRemoved += removed;
                                            lastRun.Text = trimmedText;
                                            logger.LogDebug($"从段落删除了 {removed} 个结尾空格");
                                        }
                                    }
                                }
                            }
                        }

                        // 3. 处理段落中的不可见字符（包括开头和所有位置）
                        if (settings.DeleteParagraphLeadingInvisibleChars)
                        {
                            // 获取段落中的所有Run节点
                            var allRuns = paragraph.GetChildNodes(AW.NodeType.Run, true).Cast<AW.Run>().ToList();
                            bool isFirstVisibleCharFound = false; // 标记是否已找到段落中的第一个可见字符

                            foreach (var run in allRuns)
                            {
                                // 如果已找到第一个可见字符，则不再处理开头的不可见字符
                                if (isFirstVisibleCharFound)
                                    continue;

                                string text = run.Text;
                                if (!string.IsNullOrEmpty(text))
                                {
                                    int originalLength = text.Length;
                                    StringBuilder sb = new StringBuilder();
                                    bool foundVisibleCharInCurrentRun = false;

                                    foreach (char c in text)
                                    {
                                        // 如果已经找到了可见字符，或者当前字符是可见字符，则保留
                                        if (foundVisibleCharInCurrentRun || !IsInvisibleChar(c))
                                        {
                                            foundVisibleCharInCurrentRun = true;
                                            isFirstVisibleCharFound = true; // 标记已找到段落中的第一个可见字符
                                            sb.Append(c);
                                        }
                                    }

                                    string newText = sb.ToString();
                                    if (originalLength != newText.Length)
                                    {
                                        int removed = originalLength - newText.Length;
                                        leadingInvisibleCharsRemoved += removed;
                                        run.Text = newText;
                                        logger.LogDebug($"从段落删除了 {removed} 个不可见字符");
                                    }

                                    // 如果在当前Run中找到了可见字符，则停止处理段落开头的不可见字符
                                    if (foundVisibleCharInCurrentRun)
                                        break;
                                }
                            }
                        }

                        // 4. 处理段落结尾的不可见字符
                        if (settings.DeleteParagraphTrailingInvisibleChars)
                        {
                            // 获取段落中的所有Run节点，从后往前处理
                            var allRuns = paragraph.GetChildNodes(AW.NodeType.Run, true).Cast<AW.Run>().ToList();
                            bool isLastVisibleCharFound = false; // 标记是否已找到段落中的最后一个可见字符

                            // 从后往前遍历所有Run节点
                            for (int i = allRuns.Count - 1; i >= 0; i--)
                            {
                                // 如果已找到最后一个可见字符，则不再处理结尾的不可见字符
                                if (isLastVisibleCharFound)
                                    break;

                                var run = allRuns[i];
                                string text = run.Text;
                                if (!string.IsNullOrEmpty(text))
                                {
                                    int originalLength = text.Length;
                                    StringBuilder sb = new StringBuilder();

                                    // 从后往前找到第一个可见字符的位置
                                    int lastVisibleCharPos = -1;
                                    for (int j = text.Length - 1; j >= 0; j--)
                                    {
                                        if (!IsInvisibleChar(text[j]))
                                        {
                                            lastVisibleCharPos = j;
                                            isLastVisibleCharFound = true; // 标记已找到段落中的最后一个可见字符
                                            break;
                                        }
                                    }

                                    // 保留直到最后一个可见字符的文本
                                    if (lastVisibleCharPos >= 0)
                                    {
                                        string newText = text.Substring(0, lastVisibleCharPos + 1);
                                        int removed = originalLength - newText.Length;
                                        trailingInvisibleCharsRemoved += removed;
                                        run.Text = newText;
                                        logger.LogDebug($"从段落删除了 {removed} 个结尾不可见字符");
                                        break; // 找到最后一个可见字符后，不再处理其他Run
                                    }
                                    else
                                    {
                                        // 整个Run都是不可见字符，检查是否是段落的最后一个Run
                                        if (i == allRuns.Count - 1)
                                        {
                                            trailingInvisibleCharsRemoved += text.Length;
                                            run.Text = "";
                                            logger.LogDebug($"删除了整个不可见字符的Run ({text.Length} 个字符)");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning($"处理段落空格和不可见字符时出错: {ex.Message}");
                    }
                }

                logger.Log($"完成处理段落空格和不可见字符: 删除了 {leadingSpacesRemoved} 个开头空格, {trailingSpacesRemoved} 个结尾空格, {leadingInvisibleCharsRemoved} 个开头不可见字符, {trailingInvisibleCharsRemoved} 个结尾不可见字符");
            }
            catch (Exception ex)
            {
                logger.LogError($"处理段落空格和不可见字符时发生错误: {ex.Message}", ex);
            }
        }

        // 判断字符是否为不可见字符
        private bool IsInvisibleChar(char c)
        {
            // 空格和制表符(Tab)也算作是空白字符但不算入不可见字符
            if (c == ' ' || c == '\t')
                return false;

            // Unicode控制字符范围
            if (c <= 0x001F || (c >= 0x007F && c <= 0x009F))
                return true;

            // 零宽度空格和其他不可见Unicode字符
            switch (c)
            {
                case '\u200B': // 零宽度空格
                case '\u200C': // 零宽度非连接符
                case '\u200D': // 零宽度连接符
                case '\u200E': // 从左到右标记
                case '\u200F': // 从右到左标记
                case '\u202A': // 从左到右嵌入
                case '\u202B': // 从右到左嵌入
                case '\u202C': // 弹出方向格式
                case '\u202D': // 从左到右覆盖
                case '\u202E': // 从右到左覆盖
                case '\u2060': // 单词连接符
                case '\u2061': // 函数应用
                case '\u2062': // 不可见乘号
                case '\u2063': // 不可见分隔符
                case '\u2064': // 不可见加号
                case '\u2066': // 从左到右隔离
                case '\u2067': // 从右到左隔离
                case '\u2068': // 第一强方向隔离
                case '\u2069': // 弹出方向隔离
                case '\u206A': // 抑制对称交换
                case '\u206B': // 激活对称交换
                case '\u206C': // 抑制阿拉伯形式整形
                case '\u206D': // 激活阿拉伯形式整形
                case '\u206E': // 国家数字整形
                case '\u206F': // 名义数字整形
                case '\uFEFF': // 零宽度非断空格 (BOM)
                    return true;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 清理指定目录下的Office临时文件
        /// </summary>
        /// <param name="directory">要清理的目录</param>
        private void CleanupOfficeTemporaryFiles(string directory)
        {
            try
            {
                var tempFiles = Directory.GetFiles(directory, "~$*", SearchOption.TopDirectoryOnly);
                foreach (var tempFile in tempFiles)
                {
                    try
                    {
                        File.Delete(tempFile);
                        logger.Log($"已删除Office临时文件: {tempFile}");
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning($"无法删除Office临时文件 {tempFile}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning($"清理Office临时文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查文件是否是Office临时文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否是临时文件</returns>
        private bool IsOfficeTemporaryFile(string filePath)
        {
            return Path.GetFileName(filePath).StartsWith("~$");
        }

        /// <summary>
        /// 根据文件扩展名获取保存格式
        /// </summary>
        private AW.SaveFormat GetSaveFormat(string filePath)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            switch (extension)
            {
                case ".doc":
                    return AW.SaveFormat.Doc;
                case ".rtf":
                    return AW.SaveFormat.Rtf;
                case ".txt":
                    return AW.SaveFormat.Text;
                case ".html":
                case ".htm":
                    return AW.SaveFormat.Html;
                case ".odt":
                    return AW.SaveFormat.Odt;
                case ".pdf":
                    return AW.SaveFormat.Pdf;
                case ".docx":
                default:
                    return AW.SaveFormat.Docx;
            }
        }

        // 添加内容替换规则的应用方法
        private async Task ApplyContentReplaceRulesAsync(AW.Document doc, List<ContentReplaceRule> rules, CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                int appliedRulesCount = 0;
                logger.Log("开始应用内容替换规则...");

                // 性能优化：预先获取所有需要处理的节点集合
                Dictionary<string, object> nodeCollections = new Dictionary<string, object>();

                // 只有当有规则需要处理特定类型的节点时，才获取该类型的节点集合
                bool needHeaders = rules.Any(r => r.IsEnabled && r.SearchInHeaders);
                bool needFooters = rules.Any(r => r.IsEnabled && r.SearchInFooters);
                bool needTextBoxes = rules.Any(r => r.IsEnabled && r.SearchInTextBoxes);
                bool needComments = rules.Any(r => r.IsEnabled && r.SearchInComments);
                bool needFootnotes = rules.Any(r => r.IsEnabled && r.SearchInFootnotes);

                // 预先获取所有节点集合，避免重复查询
                if (needHeaders || needFooters)
                {
                    nodeCollections["sections"] = doc.Sections;
                }

                if (needTextBoxes)
                {
                    nodeCollections["textBoxes"] = doc.GetChildNodes(AW.NodeType.Shape, true)
                        .Cast<AW.Drawing.Shape>()
                        .Where(s => s.ShapeType == AW.Drawing.ShapeType.TextBox)
                        .ToList();
                }

                if (needComments)
                {
                    nodeCollections["comments"] = doc.GetChildNodes(AW.NodeType.Comment, true);
                }

                if (needFootnotes)
                {
                    nodeCollections["footnotes"] = doc.GetChildNodes(AW.NodeType.Footnote, true);
                }

                foreach (var rule in rules)
                {
                    try
                    {
                        // 跳过禁用的规则
                        if (!rule.IsEnabled)
                            continue;

                        // 创建查找替换选项
                        var options = new AW.Replacing.FindReplaceOptions
                        {
                            Direction = AW.Replacing.FindReplaceDirection.Forward,
                            MatchCase = rule.CaseSensitive,
                            FindWholeWordsOnly = rule.FindWholeWordsOnly,
                            ReplacingCallback = null
                        };

                        // 根据规则使用正则表达式或普通文本搜索
                        if (rule.UseRegex)
                        {
                            try
                            {
                                var regex = new System.Text.RegularExpressions.Regex(rule.Find);

                                // 处理主文本
                                if (rule.SearchInMainText)
                                {
                                    doc.Range.Replace(regex, rule.Replace, options);
                                }

                                // 处理页眉页脚
                                if (rule.SearchInHeaders || rule.SearchInFooters)
                                {
                                    var sections = nodeCollections.ContainsKey("sections")
                                        ? (AW.SectionCollection)nodeCollections["sections"]
                                        : doc.Sections;

                                    foreach (AW.Section section in sections)
                                    {
                                        if (rule.SearchInHeaders)
                                        {
                                            foreach (AW.HeaderFooter header in section.HeadersFooters)
                                            {
                                                if (header.HeaderFooterType == AW.HeaderFooterType.HeaderPrimary ||
                                                    header.HeaderFooterType == AW.HeaderFooterType.HeaderFirst ||
                                                    header.HeaderFooterType == AW.HeaderFooterType.HeaderEven)
                                                {
                                                    header.Range.Replace(regex, rule.Replace, options);
                                                }
                                            }
                                        }

                                        if (rule.SearchInFooters)
                                        {
                                            foreach (AW.HeaderFooter footer in section.HeadersFooters)
                                            {
                                                if (footer.HeaderFooterType == AW.HeaderFooterType.FooterPrimary ||
                                                    footer.HeaderFooterType == AW.HeaderFooterType.FooterFirst ||
                                                    footer.HeaderFooterType == AW.HeaderFooterType.FooterEven)
                                                {
                                                    footer.Range.Replace(regex, rule.Replace, options);
                                                }
                                            }
                                        }
                                    }
                                }

                                // 处理文本框
                                if (rule.SearchInTextBoxes)
                                {
                                    var textBoxes = nodeCollections.ContainsKey("textBoxes")
                                        ? (List<AW.Drawing.Shape>)nodeCollections["textBoxes"]
                                        : doc.GetChildNodes(AW.NodeType.Shape, true)
                                            .Cast<AW.Drawing.Shape>()
                                            .Where(s => s.ShapeType == AW.Drawing.ShapeType.TextBox)
                                            .ToList();

                                    foreach (var textBox in textBoxes)
                                    {
                                        textBox.Range.Replace(regex, rule.Replace, options);
                                    }
                                }

                                // 处理批注
                                if (rule.SearchInComments)
                                {
                                    var comments = nodeCollections.ContainsKey("comments")
                                        ? (AW.NodeCollection)nodeCollections["comments"]
                                        : doc.GetChildNodes(AW.NodeType.Comment, true);

                                    foreach (AW.Comment comment in comments)
                                    {
                                        comment.Range.Replace(regex, rule.Replace, options);
                                    }
                                }

                                // 处理脚注
                                if (rule.SearchInFootnotes)
                                {
                                    var footnotes = nodeCollections.ContainsKey("footnotes")
                                        ? (AW.NodeCollection)nodeCollections["footnotes"]
                                        : doc.GetChildNodes(AW.NodeType.Footnote, true);

                                    foreach (AW.Notes.Footnote footnote in footnotes)
                                    {
                                        footnote.Range.Replace(regex, rule.Replace, options);
                                    }
                                }
                            }
                            catch (System.ArgumentException regexEx)
                            {
                                logger.LogWarning($"正则表达式无效: {rule.Find}, 错误: {regexEx.Message}");
                                continue;
                            }
                        }
                        else
                        {
                            // 使用普通文本搜索

                            // 处理主文本
                            if (rule.SearchInMainText)
                            {
                                doc.Range.Replace(rule.Find, rule.Replace, options);
                            }

                            // 处理页眉页脚
                            if (rule.SearchInHeaders || rule.SearchInFooters)
                            {
                                var sections = nodeCollections.ContainsKey("sections")
                                    ? (AW.SectionCollection)nodeCollections["sections"]
                                    : doc.Sections;

                                foreach (AW.Section section in sections)
                                {
                                    if (rule.SearchInHeaders)
                                    {
                                        foreach (AW.HeaderFooter header in section.HeadersFooters)
                                        {
                                            if (header.HeaderFooterType == AW.HeaderFooterType.HeaderPrimary ||
                                                header.HeaderFooterType == AW.HeaderFooterType.HeaderFirst ||
                                                header.HeaderFooterType == AW.HeaderFooterType.HeaderEven)
                                            {
                                                header.Range.Replace(rule.Find, rule.Replace, options);
                                            }
                                        }
                                    }

                                    if (rule.SearchInFooters)
                                    {
                                        foreach (AW.HeaderFooter footer in section.HeadersFooters)
                                        {
                                            if (footer.HeaderFooterType == AW.HeaderFooterType.FooterPrimary ||
                                                footer.HeaderFooterType == AW.HeaderFooterType.FooterFirst ||
                                                footer.HeaderFooterType == AW.HeaderFooterType.FooterEven)
                                            {
                                                footer.Range.Replace(rule.Find, rule.Replace, options);
                                            }
                                        }
                                    }
                                }
                            }

                            // 处理文本框
                            if (rule.SearchInTextBoxes)
                            {
                                var textBoxes = nodeCollections.ContainsKey("textBoxes")
                                    ? (List<AW.Drawing.Shape>)nodeCollections["textBoxes"]
                                    : doc.GetChildNodes(AW.NodeType.Shape, true)
                                        .Cast<AW.Drawing.Shape>()
                                        .Where(s => s.ShapeType == AW.Drawing.ShapeType.TextBox)
                                        .ToList();

                                foreach (var textBox in textBoxes)
                                {
                                    textBox.Range.Replace(rule.Find, rule.Replace, options);
                                }
                            }

                            // 处理批注
                            if (rule.SearchInComments)
                            {
                                var comments = nodeCollections.ContainsKey("comments")
                                    ? (AW.NodeCollection)nodeCollections["comments"]
                                    : doc.GetChildNodes(AW.NodeType.Comment, true);

                                foreach (AW.Comment comment in comments)
                                {
                                    comment.Range.Replace(rule.Find, rule.Replace, options);
                                }
                            }

                            // 处理脚注
                            if (rule.SearchInFootnotes)
                            {
                                var footnotes = nodeCollections.ContainsKey("footnotes")
                                    ? (AW.NodeCollection)nodeCollections["footnotes"]
                                    : doc.GetChildNodes(AW.NodeType.Footnote, true);

                                foreach (AW.Notes.Footnote footnote in footnotes)
                                {
                                    footnote.Range.Replace(rule.Find, rule.Replace, options);
                                }
                            }
                        }

                        appliedRulesCount++;
                        logger.Log($"应用替换规则: \"{rule.Find}\" -> \"{rule.Replace}\"");
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning($"应用替换规则时出错: {rule.Find} -> {rule.Replace}, {ex.Message}");
                    }
                }

                // 记录性能统计信息
                string performanceInfo = "";
                if (nodeCollections.ContainsKey("sections"))
                    performanceInfo += $"节数: {((AW.SectionCollection)nodeCollections["sections"]).Count}, ";
                if (nodeCollections.ContainsKey("textBoxes"))
                    performanceInfo += $"文本框数: {((List<AW.Drawing.Shape>)nodeCollections["textBoxes"]).Count}, ";
                if (nodeCollections.ContainsKey("comments"))
                    performanceInfo += $"批注数: {((AW.NodeCollection)nodeCollections["comments"]).Count}, ";
                if (nodeCollections.ContainsKey("footnotes"))
                    performanceInfo += $"脚注数: {((AW.NodeCollection)nodeCollections["footnotes"]).Count}, ";

                logger.Log($"已完成内容替换规则应用，共应用了 {appliedRulesCount} 条规则。{performanceInfo}");
            }, cancellationToken);
        }
    }
}