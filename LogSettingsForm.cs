/*
 * ========================================
 * 文件名: LogSettingsForm.cs
 * 功能描述: 日志设置窗体
 * ========================================
 *
 * 主要功能:
 * 1. 提供日志系统的详细配置界面
 * 2. 支持各个日志级别的独立开关控制
 * 3. 提供日志总开关控制
 * 4. 支持日志文件存储选项配置
 * 5. 实时应用日志设置到Logger系统
 *
 * 界面组件:
 * - 日志总开关复选框
 * - 各日志级别开关复选框（Debug、Info、Warning、Error、Fatal）
 * - 日志文件存储选项复选框
 * - 确定和取消按钮
 * - 说明标签
 *
 * 功能特性:
 * - 支持实时预览日志设置效果
 * - 自动同步Logger系统状态
 * - 提供用户友好的界面布局
 * - 支持设置的保存和恢复
 */

using System;
using System.Drawing;
using System.Windows.Forms;
using AsposeWordFormatter.Models;

namespace AsposeWordFormatter
{
    public partial class LogSettingsForm : Form
    {
        private readonly LogSettings logSettings;
        private readonly Logger logger;

        // UI控件
        private CheckBox masterLogSwitch;
        private CheckBox debugLogSwitch;
        private CheckBox infoLogSwitch;
        private CheckBox warningLogSwitch;
        private CheckBox errorLogSwitch;
        private CheckBox fatalLogSwitch;
        private CheckBox separateFilesSwitch;
        private CheckBox keepAllLogFileSwitch;
        private Button selectAllButton;
        private Button unselectAllButton;
        private Button okButton;
        private Button cancelButton;

        public LogSettings LogSettings => logSettings;

        public LogSettingsForm(LogSettings settings)
        {
            logSettings = settings ?? throw new ArgumentNullException(nameof(settings));
            logger = Logger.Instance;

            InitializeComponent();
            LoadSettings();
        }

        private void InitializeComponent()
        {
            // 设置窗体属性
            this.Text = "日志设置";
            this.Size = new Size(450, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // 创建主布局面板
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(15)
            };

            // 设置行高比例
            mainPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 说明区域
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // 设置区域
            mainPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 按钮区域

            // 创建说明标签
            var descriptionLabel = new Label
            {
                Text = "配置日志记录系统的详细设置。您可以选择启用或禁用不同类型的日志记录。",
                AutoSize = false,
                Size = new Size(400, 40),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.DarkBlue,
                Margin = new Padding(0, 0, 0, 10)
            };

            // 创建设置区域
            var settingsPanel = CreateSettingsPanel();

            // 创建按钮区域
            var buttonPanel = CreateButtonPanel();

            // 添加到主布局
            mainPanel.Controls.Add(descriptionLabel, 0, 0);
            mainPanel.Controls.Add(settingsPanel, 0, 1);
            mainPanel.Controls.Add(buttonPanel, 0, 2);

            this.Controls.Add(mainPanel);
        }

        private Panel CreateSettingsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(10)
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 10,
                AutoSize = true
            };

            // 设置行高
            for (int i = 0; i < 10; i++)
            {
                layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 创建总开关
            masterLogSwitch = new CheckBox
            {
                Text = "启用日志记录系统",
                AutoSize = true,
                Font = new Font(this.Font, FontStyle.Bold),
                ForeColor = Color.DarkRed,
                Margin = new Padding(0, 5, 0, 15)
            };
            masterLogSwitch.CheckedChanged += MasterLogSwitch_CheckedChanged;

            // 创建分隔线
            var separator1 = new Label
            {
                Text = "日志级别设置：",
                AutoSize = true,
                Font = new Font(this.Font, FontStyle.Bold),
                Margin = new Padding(0, 5, 0, 5)
            };

            // 创建各级别开关
            debugLogSwitch = CreateLogLevelSwitch("Debug - 调试信息（开发时使用）", Color.Gray);
            infoLogSwitch = CreateLogLevelSwitch("Info - 一般信息（默认记录级别）", Color.Blue);
            warningLogSwitch = CreateLogLevelSwitch("Warning - 警告信息（潜在问题）", Color.Orange);
            errorLogSwitch = CreateLogLevelSwitch("Error - 错误信息（操作失败）", Color.Red);
            fatalLogSwitch = CreateLogLevelSwitch("Fatal - 致命错误（程序崩溃）", Color.DarkRed);

            // 创建文件设置分隔线
            var separator2 = new Label
            {
                Text = "文件存储设置：",
                AutoSize = true,
                Font = new Font(this.Font, FontStyle.Bold),
                Margin = new Padding(0, 15, 0, 5)
            };

            // 创建文件设置开关
            separateFilesSwitch = new CheckBox
            {
                Text = "使用分离的日志文件（按级别分别保存）",
                AutoSize = true,
                Margin = new Padding(20, 3, 0, 3)
            };

            keepAllLogFileSwitch = new CheckBox
            {
                Text = "保留总日志文件（包含所有启用级别的日志）",
                AutoSize = true,
                Margin = new Padding(20, 3, 0, 3)
            };

            // 添加到布局
            layout.Controls.Add(masterLogSwitch, 0, 0);
            layout.Controls.Add(separator1, 0, 1);
            layout.Controls.Add(debugLogSwitch, 0, 2);
            layout.Controls.Add(infoLogSwitch, 0, 3);
            layout.Controls.Add(warningLogSwitch, 0, 4);
            layout.Controls.Add(errorLogSwitch, 0, 5);
            layout.Controls.Add(fatalLogSwitch, 0, 6);
            layout.Controls.Add(separator2, 0, 7);
            layout.Controls.Add(separateFilesSwitch, 0, 8);
            layout.Controls.Add(keepAllLogFileSwitch, 0, 9);

            panel.Controls.Add(layout);
            return panel;
        }

        private CheckBox CreateLogLevelSwitch(string text, Color color)
        {
            return new CheckBox
            {
                Text = text,
                AutoSize = true,
                ForeColor = color,
                Margin = new Padding(20, 3, 0, 3)
            };
        }

        private Panel CreateButtonPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Height = 40
            };

            var layout = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = false,
                Margin = new Padding(0, 10, 0, 0),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
            };

            // 创建全选按钮
            selectAllButton = new Button
            {
                Text = "全选",
                Size = new Size(75, 30),
                Margin = new Padding(0, 0, 10, 0)
            };
            selectAllButton.Click += SelectAllButton_Click;

            // 创建取消全选按钮
            unselectAllButton = new Button
            {
                Text = "取消全选",
                Size = new Size(75, 30),
                Margin = new Padding(0, 0, 10, 0)
            };
            unselectAllButton.Click += UnselectAllButton_Click;

            // 创建弹簧控件，将确定取消按钮推到右边
            var spacer = new Panel
            {
                Width = 1,
                Height = 1,
                Margin = new Padding(0)
            };

            // 创建确定按钮
            okButton = new Button
            {
                Text = "确定",
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK,
                Margin = new Padding(0, 0, 10, 0),
                Anchor = AnchorStyles.Right
            };
            okButton.Click += OkButton_Click;

            // 创建取消按钮
            cancelButton = new Button
            {
                Text = "取消",
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel,
                Anchor = AnchorStyles.Right
            };

            // 使用TableLayoutPanel来更好地控制布局
            var tableLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 6,
                RowCount = 1,
                Margin = new Padding(0, 10, 0, 0)
            };

            // 设置列宽
            tableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 全选
            tableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 取消全选
            tableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100)); // 弹簧
            tableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 确定
            tableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 取消

            // 添加按钮到布局
            tableLayout.Controls.Add(selectAllButton, 0, 0);
            tableLayout.Controls.Add(unselectAllButton, 1, 0);
            tableLayout.Controls.Add(spacer, 2, 0);
            tableLayout.Controls.Add(okButton, 3, 0);
            tableLayout.Controls.Add(cancelButton, 4, 0);

            panel.Controls.Add(tableLayout);
            return panel;
        }

        private void LoadSettings()
        {
            // 加载设置到UI控件
            masterLogSwitch.Checked = logSettings.EnableLogging;
            debugLogSwitch.Checked = logSettings.EnableDebugLog;
            infoLogSwitch.Checked = logSettings.EnableInfoLog;
            warningLogSwitch.Checked = logSettings.EnableWarningLog;
            errorLogSwitch.Checked = logSettings.EnableErrorLog;
            fatalLogSwitch.Checked = logSettings.EnableFatalLog;
            separateFilesSwitch.Checked = logSettings.UseSeparateLogFiles;
            keepAllLogFileSwitch.Checked = logSettings.KeepAllLogFile;

            // 更新UI状态
            UpdateUIState();
        }

        private void MasterLogSwitch_CheckedChanged(object sender, EventArgs e)
        {
            UpdateUIState();
        }

        private void UpdateUIState()
        {
            bool masterEnabled = masterLogSwitch.Checked;

            // 根据总开关状态启用/禁用子选项
            debugLogSwitch.Enabled = masterEnabled;
            infoLogSwitch.Enabled = masterEnabled;
            warningLogSwitch.Enabled = masterEnabled;
            errorLogSwitch.Enabled = masterEnabled;
            fatalLogSwitch.Enabled = masterEnabled;
            separateFilesSwitch.Enabled = masterEnabled;
            keepAllLogFileSwitch.Enabled = masterEnabled;
        }

        private void SelectAllButton_Click(object sender, EventArgs e)
        {
            // 全选所有日志级别开关（不包括总开关和文件设置）
            debugLogSwitch.Checked = true;
            infoLogSwitch.Checked = true;
            warningLogSwitch.Checked = true;
            errorLogSwitch.Checked = true;
            fatalLogSwitch.Checked = true;
        }

        private void UnselectAllButton_Click(object sender, EventArgs e)
        {
            // 取消全选所有日志级别开关（不包括总开关和文件设置）
            debugLogSwitch.Checked = false;
            infoLogSwitch.Checked = false;
            warningLogSwitch.Checked = false;
            errorLogSwitch.Checked = false;
            fatalLogSwitch.Checked = false;
        }

        private void OkButton_Click(object sender, EventArgs e)
        {
            // 保存设置
            SaveSettings();

            // 应用设置到Logger系统
            ApplySettingsToLogger();
        }

        private void SaveSettings()
        {
            logSettings.EnableLogging = masterLogSwitch.Checked;
            logSettings.EnableDebugLog = debugLogSwitch.Checked;
            logSettings.EnableInfoLog = infoLogSwitch.Checked;
            logSettings.EnableWarningLog = warningLogSwitch.Checked;
            logSettings.EnableErrorLog = errorLogSwitch.Checked;
            logSettings.EnableFatalLog = fatalLogSwitch.Checked;
            logSettings.UseSeparateLogFiles = separateFilesSwitch.Checked;
            logSettings.KeepAllLogFile = keepAllLogFileSwitch.Checked;
        }

        private void ApplySettingsToLogger()
        {
            // 应用总开关
            logger.EnableLogging = logSettings.EnableLogging;

            // 应用各级别开关
            logger.SetLogLevelEnabled(LogLevel.Debug, logSettings.EnableDebugLog);
            logger.SetLogLevelEnabled(LogLevel.Info, logSettings.EnableInfoLog);
            logger.SetLogLevelEnabled(LogLevel.Warning, logSettings.EnableWarningLog);
            logger.SetLogLevelEnabled(LogLevel.Error, logSettings.EnableErrorLog);
            logger.SetLogLevelEnabled(LogLevel.Fatal, logSettings.EnableFatalLog);

            // 应用文件设置
            logger.UseSeparateLogFiles = logSettings.UseSeparateLogFiles;
            logger.KeepAllLogFile = logSettings.KeepAllLogFile;

            // 记录设置更改日志
            if (logSettings.EnableLogging)
            {
                logger.Log("日志设置已更新", LogLevel.Info);
            }
        }
    }
}
