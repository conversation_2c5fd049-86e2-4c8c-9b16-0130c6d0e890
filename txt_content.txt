# TxtSettings.json 配置文件说明

此配置文件用于控制TXT文本文件的导入/导出处理选项。

## 编码设置
- `Encoding`: 文本文件的默认编码，常用值包括"UTF-8"、"GB2312"、"GBK"等
- `DetectEncoding`: 是否自动检测文本文件编码，true为自动检测，false为使用指定编码

## 文本处理选项
- `PreserveLineBreaks`: 是否保留原始文本中的换行符
- `TrimTrailingSpaces`: 是否删除行尾空格
- `NormalizeWhitespace`: 是否规范化空白字符（如连续空格转为单个空格）
- `ConvertTabsToSpaces`: 是否将制表符转换为空格
- `TabSize`: 制表符对应的空格数

## 默认格式设置
- `DefaultFontName`: 导入TXT文件时应用的默认字体名称
- `DefaultFontSize`: 导入TXT文件时应用的默认字体大小（磅）
- `DefaultLineSpacing`: 导入TXT文件时应用的默认行距
- `DefaultFirstLineIndent`: 默认首行缩进（磅）
- `DefaultLeftIndent`: 默认左缩进（磅）
- `DefaultRightIndent`: 默认右缩进（磅）
- `DefaultSpaceBefore`: 默认段前间距（磅）
- `DefaultSpaceAfter`: 默认段后间距（磅）
- `ApplyDefaultFormatting`: 是否应用上述默认格式设置

## 段落识别选项
- `DetectParagraphs`: 是否自动检测段落
- `ParagraphSeparation`: 段落分隔方式，可选值："EmptyLine"（空行分隔）, "LineBreak"（换行分隔）
- `TreatConsecutiveLineBreaksAsParagraphs`: 是否将连续的换行符视为段落分隔
- `ConsecutiveLineBreaksThreshold`: 将多少个连续换行符视为段落分隔的阈值 
