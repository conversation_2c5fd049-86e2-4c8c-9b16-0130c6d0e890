/*
 * ========================================
 * 文件名: PageSetupForm.cs
 * 功能描述: 页面设置配置窗体
 * ========================================
 *
 * 主要功能:
 * 1. 页面边距设置（上下左右边距）
 * 2. 纸张方向设置（纵向/横向）
 * 3. 纸张大小设置（标准尺寸/自定义尺寸）
 * 4. 装订线和镜像边距设置
 * 5. 页面背景颜色和图片设置
 * 6. 页面边框样式和颜色设置
 * 7. 页面网格和对齐设置
 * 8. 分栏和页面布局增强功能
 *
 * 界面结构:
 * - 基础设置标签页：边距、方向、纸张大小、装订线
 * - 背景设置标签页：背景颜色、背景图片
 * - 边框设置标签页：边框样式、颜色、宽度、艺术效果
 * - 网格设置标签页：网格线、颜色、间距、对齐
 * - 布局增强标签页：分栏、缩放、文本方向、镜像
 * - 视图设置标签页：文档视图、缩放、格式标记
 * - 行号设置标签页：行号显示和格式设置
 *
 * 核心特性:
 * - 支持所有Aspose.Words页面设置选项
 * - 实时预览和验证
 * - 分组管理相关设置
 * - 智能启用/禁用控件
 * - 数据绑定和自动保存
 *
 * 数据模型:
 * - PageSetupFixed: 页面设置数据模型
 * - 支持所有页面属性的配置
 * - 包含启用/禁用开关
 *
 * 注意事项:
 * - 所有设置都有对应的启用开关
 * - 支持厘米和磅值的单位转换
 * - 包含完整的数据验证
 * - 实现了设置的实时应用
 */

using System;
using System.Windows.Forms;
using System.Drawing;
using AW = Aspose.Words;
using AsposeWordFormatter.Models;

namespace AsposeWordFormatter
{
    public partial class PageSetupForm : Form
    {
        private readonly Settings settings;

        // 边距设置控件
        private CheckBox? enableMarginsCheckBox;
        private NumericUpDown? topMarginNumeric;
        private NumericUpDown? bottomMarginNumeric;
        private NumericUpDown? leftMarginNumeric;
        private NumericUpDown? rightMarginNumeric;

        // 纸张方向设置控件
        private CheckBox? enableOrientationCheckBox;
        private RadioButton? portraitRadio;
        private RadioButton? landscapeRadio;

        // 纸张大小设置控件
        private CheckBox? enablePaperSizeCheckBox;
        private ComboBox? paperSizeComboBox;
        private NumericUpDown? customWidthNumeric;
        private NumericUpDown? customHeightNumeric;

        // 装订线设置控件
        private CheckBox? enableGutterCheckBox;
        private NumericUpDown? gutterNumeric;
        private CheckBox? mirrorMarginsCheckBox;
        private CheckBox? rtlGutterCheckBox;

        // 背景颜色设置控件
        private CheckBox? enableBackgroundColorCheckBox;
        private Button? colorPickerButton;
        private Button? clearColorButton;
        private Panel? colorPreviewPanel;

        // 背景图片设置控件
        private CheckBox? enableBackgroundImageCheckBox;
        private TextBox? imagePathTextBox;
        private Button? browseImageButton;
        private ComboBox? imageSourceTypeComboBox;
        private ComboBox? imageWrapTypeComboBox;

        // 边框样式设置控件
        private CheckBox? enableBorderStyleCheckBox;
        private ComboBox? borderStyleComboBox;
        private CheckBox? applyBorderTopCheckBox;
        private CheckBox? applyBorderLeftCheckBox;
        private CheckBox? applyBorderBottomCheckBox;
        private CheckBox? applyBorderRightCheckBox;

        // 边框颜色设置控件
        private CheckBox? enableBorderColorCheckBox;
        private Button? borderColorPickerButton;
        private Button? clearBorderColorButton;
        private Panel? borderColorPreviewPanel;

        // 边框宽度设置控件
        private CheckBox? enableBorderWidthCheckBox;
        private NumericUpDown? borderWidthNumeric;

        // 边框艺术效果设置控件
        private CheckBox? enableBorderArtCheckBox;
        private ComboBox? borderArtComboBox;
        private NumericUpDown? distanceFromTopNumeric;
        private NumericUpDown? distanceFromLeftNumeric;
        private NumericUpDown? distanceFromBottomNumeric;
        private NumericUpDown? distanceFromRightNumeric;

        // 边框高级属性控件（新增）
        private CheckBox? enableBorderAdvancedCheckBox;
        private CheckBox? borderAlwaysInFrontCheckBox;
        private ComboBox? borderAppliesToComboBox;
        private ComboBox? borderDistanceFromComboBox;
        private CheckBox? borderSurroundsHeaderCheckBox;
        private CheckBox? borderSurroundsFooterCheckBox;

        // 页面网格设置控件
        private CheckBox? enableGridLinesCheckBox;
        private CheckBox? enableGridColorCheckBox;
        private Button? gridColorPickerButton;
        private Button? clearGridColorButton;
        private Panel? gridColorPreviewPanel;
        private CheckBox? enableGridSpacingCheckBox;
        private NumericUpDown? horizontalSpacingNumeric;
        private NumericUpDown? verticalSpacingNumeric;
        private CheckBox? enableSnapToGridCheckBox;
        private CheckBox? snapToGridCheckBox;
        private NumericUpDown? snapToGridDivisionNumeric;

        // 页面布局增强控件
        // 分栏设置
        private CheckBox? enableColumnsCheckBox;
        private NumericUpDown? columnCountNumeric;
        private NumericUpDown? columnSpacingNumeric;
        private CheckBox? evenlySpacedCheckBox;

        // 页面大小缩放
        private CheckBox? enableScalingCheckBox;
        private NumericUpDown? scalePercentageNumeric;
        private CheckBox? scaleToFitCheckBox;
        private NumericUpDown? scaleToPagesNumeric;

        // 页面旋转
        private CheckBox? enableTextOrientationCheckBox;
        private ComboBox? textOrientationComboBox;

        // 页面镜像
        private CheckBox? enableMirrorMarginsCheckBox;
        private CheckBox? mirrorMarginsCheckBox2;  // 用于页面镜像标签页
        private CheckBox? enableBidiCheckBox;
        private RadioButton? leftToRightRadio;
        private RadioButton? rightToLeftRadio;

        // 对齐网格相关控件
        // 注意：以下字段暂未使用，但保留以备未来实现
        private CheckBox? showGridLinesCheckBox;

        // 文档网格布局控件（新增）
        private CheckBox? enableCharactersPerLineCheckBox;
        private NumericUpDown? charactersPerLineNumeric;
        private CheckBox? enableLinesPerPageCheckBox;
        private NumericUpDown? linesPerPageNumeric;
        private CheckBox? enableLayoutModeCheckBox;
        private ComboBox? layoutModeComboBox;

        // 文档视图设置相关控件
        private CheckBox? enableViewTypeCheckBox;
        private ComboBox? viewTypeComboBox;

        private CheckBox? enableZoomCheckBox;
        private ComboBox? zoomTypeComboBox;
        private NumericUpDown? zoomPercentNumeric;

        private CheckBox? enableFormatMarksCheckBox;
        private CheckBox? showFormatMarksCheckBox;
        private CheckBox? doNotDisplayPageBoundariesCheckBox;
        private CheckBox? displayBackgroundShapeCheckBox;

        // 打印设置控件（新增）
        private CheckBox? enablePrintSettingsCheckBox;
        private NumericUpDown? firstPageTrayNumeric;
        private NumericUpDown? otherPagesTrayNumeric;

        // 脚注选项控件（新增）
        private CheckBox? enableFootnoteOptionsCheckBox;
        private ComboBox? footnotePositionComboBox;
        private ComboBox? footnoteNumberStyleComboBox;
        private NumericUpDown? footnoteStartNumberNumeric;
        private ComboBox? footnoteRestartRuleComboBox;
        private NumericUpDown? footnoteColumnsNumeric;

        // 尾注选项控件（新增）
        private CheckBox? enableEndnoteOptionsCheckBox;
        private ComboBox? endnotePositionComboBox;
        private ComboBox? endnoteNumberStyleComboBox;
        private NumericUpDown? endnoteStartNumberNumeric;
        private ComboBox? endnoteRestartRuleComboBox;

        // 分栏高级设置控件（新增）
        private CheckBox? enableColumnAdvancedCheckBox;
        private CheckBox? lineBetweenCheckBox;
        private NumericUpDown? separatorLineWidthNumeric;
        private Button? separatorLineColorButton;
        private Panel? separatorLineColorPreview;

        // 节设置控件（新增）
        private CheckBox? enableSectionSettingsCheckBox;
        private ComboBox? sectionStartTypeComboBox;
        private CheckBox? differentFirstPageHeaderFooterCheckBox;
        private CheckBox? oddAndEvenPagesHeaderFooterCheckBox;

        // 垂直对齐控件（新增）
        private CheckBox? enableVerticalAlignmentCheckBox;
        private ComboBox? verticalAlignmentComboBox;

        // 多页设置控件（新增）
        private CheckBox? enableMultiplePagesCheckBox;
        private ComboBox? multiplePagesComboBox;
        private NumericUpDown? sheetsPerBookletNumeric;

        // 章节设置控件（新增）
        private CheckBox? enableChapterSettingsCheckBox;
        private NumericUpDown? headingLevelNumeric;
        private ComboBox? chapterSeparatorComboBox;

        // 双向文本设置控件（新增）
        private CheckBox? bidiCheckBox;

        // 行号相关控件
        private CheckBox? enableLineNumberingCheckBox;
        private NumericUpDown? lineNumberStartNumeric;
        private NumericUpDown? lineNumberCountByNumeric;
        private ComboBox? lineNumberRestartModeComboBox;
        private NumericUpDown? lineNumberDistanceNumeric;

        // 页面边距标签页相关控件
        private CheckBox? enableHeaderFooterDistanceCheckBox;
        private NumericUpDown? headerDistanceNumeric;
        private NumericUpDown? footerDistanceNumeric;
        private CheckBox? enableMarginPresetsCheckBox;
        private CheckBox? enableMarginValidationCheckBox;

        public PageSetupFixed PageSetup => settings.PageSetup ?? new PageSetupFixed();

        public PageSetupForm(Settings settings)
        {
            this.settings = settings ?? throw new ArgumentNullException(nameof(settings));
            if (settings.PageSetup == null)
            {
                settings.PageSetup = new PageSetupFixed();
            }
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "页面设置";
            this.AutoSize = false;
            this.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            this.MinimumSize = new System.Drawing.Size(480, 650);
            this.Size = new System.Drawing.Size(500, 700);
            this.Padding = new Padding(8);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // 创建主TabControl
            var mainTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Padding = new Point(3, 3),
                Multiline = false,
                SizeMode = TabSizeMode.Normal
            };

            // 创建"基础设置"标签页
            var basicSettingsTab = new TabPage("基础设置");
            basicSettingsTab.AutoScroll = true;
            basicSettingsTab.UseVisualStyleBackColor = true;

            // 创建"页面边距"标签页
            var marginsTab = new TabPage("页面边距");
            marginsTab.AutoScroll = true;
            marginsTab.UseVisualStyleBackColor = true;

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 3,
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 设置行样式，让每个部分都有足够的空间
            mainLayout.RowStyles.Clear();
            for (int i = 0; i < 3; i++)
            {
                mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }





            // 纸张方向设置组
            var orientationGroup = new GroupBox
            {
                Text = "纸张方向",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var orientationLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            orientationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            orientationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用纸张方向复选框
            enableOrientationCheckBox = new CheckBox
            {
                Text = "启用纸张方向设置",
                AutoSize = true,
                Checked = PageSetup.EnableOrientation,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 方向选择单选按钮
            var radioPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = false
            };

            portraitRadio = new RadioButton
            {
                Text = "纵向",
                Checked = PageSetup.Orientation == AW.Orientation.Portrait,
                AutoSize = true,
                Margin = new Padding(0, 0, 15, 0)
            };

            landscapeRadio = new RadioButton
            {
                Text = "横向",
                Checked = PageSetup.Orientation == AW.Orientation.Landscape,
                AutoSize = true
            };

            radioPanel.Controls.Add(portraitRadio);
            radioPanel.Controls.Add(landscapeRadio);

            // 添加事件处理
            enableOrientationCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableOrientationCheckBox.Checked;
                portraitRadio.Enabled = enabled;
                landscapeRadio.Enabled = enabled;
            };

            // 应用初始启用状态
            portraitRadio.Enabled = enableOrientationCheckBox.Checked;
            landscapeRadio.Enabled = enableOrientationCheckBox.Checked;

            // 添加控件到方向布局
            orientationLayout.Controls.Add(enableOrientationCheckBox, 0, 0);
            orientationLayout.SetColumnSpan(enableOrientationCheckBox, 2);

            orientationLayout.Controls.Add(radioPanel, 0, 1);
            orientationLayout.SetColumnSpan(radioPanel, 2);

            // 完成纸张方向设置组
            orientationGroup.Controls.Add(orientationLayout);
            mainLayout.Controls.Add(orientationGroup, 0, 0);

            // 纸张大小设置组
            var paperSizeGroup = new GroupBox
            {
                Text = "纸张大小",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var paperSizeLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            paperSizeLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            paperSizeLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用纸张大小复选框
            enablePaperSizeCheckBox = new CheckBox
            {
                Text = "启用纸张大小设置",
                AutoSize = true,
                // 直接使用EnablePaperSize属性
                Checked = PageSetup.EnablePaperSize,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 纸张大小选择行
            var paperSizeRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true
            };

            paperSizeRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            paperSizeRow.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            var paperSizeLabel = new Label
            {
                Text = "纸张大小:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            paperSizeComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            paperSizeComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            paperSizeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = paperSizeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加常用纸张大小（按照Word中的顺序和分组）
            paperSizeComboBox.Items.Add("A3 (297×420毫米)");
            paperSizeComboBox.Items.Add("A4 (210×297毫米)");
            paperSizeComboBox.Items.Add("A5 (148×210毫米)");
            paperSizeComboBox.Items.Add("B4 (250×353毫米)");
            paperSizeComboBox.Items.Add("B5 (176×250毫米)");
            paperSizeComboBox.Items.Add("JIS B4 (257×364毫米)");
            paperSizeComboBox.Items.Add("JIS B5 (182×257毫米)");
            paperSizeComboBox.Items.Add("Letter (8.5×11英寸)");
            paperSizeComboBox.Items.Add("Legal (8.5×14英寸)");
            paperSizeComboBox.Items.Add("Executive (7.25×10.5英寸)");
            paperSizeComboBox.Items.Add("Tabloid (11×17英寸)");
            paperSizeComboBox.Items.Add("Ledger (17×11英寸)");
            paperSizeComboBox.Items.Add("Folio (8.5×13英寸)");
            paperSizeComboBox.Items.Add("Quarto (8.47×10.83英寸)");
            paperSizeComboBox.Items.Add("Statement (8.5×5.5英寸)");
            paperSizeComboBox.Items.Add("10×14英寸");
            paperSizeComboBox.Items.Add("11×17英寸");
            paperSizeComboBox.Items.Add("信封DL (110×220毫米)");
            paperSizeComboBox.Items.Add("10号信封 (4.125×9.5英寸)");
            paperSizeComboBox.Items.Add("自定义");

            // 根据当前PageSetup设置选中相应项
            switch (PageSetup.PaperSize)
            {
                case AW.PaperSize.A3:
                    paperSizeComboBox.SelectedIndex = 0;
                    break;
                case AW.PaperSize.A4:
                    paperSizeComboBox.SelectedIndex = 1;
                    break;
                case AW.PaperSize.A5:
                    paperSizeComboBox.SelectedIndex = 2;
                    break;
                case AW.PaperSize.B4:
                    paperSizeComboBox.SelectedIndex = 3;
                    break;
                case AW.PaperSize.B5:
                    paperSizeComboBox.SelectedIndex = 4;
                    break;
                case AW.PaperSize.JisB4:
                    paperSizeComboBox.SelectedIndex = 5;
                    break;
                case AW.PaperSize.JisB5:
                    paperSizeComboBox.SelectedIndex = 6;
                    break;
                case AW.PaperSize.Letter:
                    paperSizeComboBox.SelectedIndex = 7;
                    break;
                case AW.PaperSize.Legal:
                    paperSizeComboBox.SelectedIndex = 8;
                    break;
                case AW.PaperSize.Executive:
                    paperSizeComboBox.SelectedIndex = 9;
                    break;
                case AW.PaperSize.Tabloid:
                    paperSizeComboBox.SelectedIndex = 10;
                    break;
                case AW.PaperSize.Ledger:
                    paperSizeComboBox.SelectedIndex = 11;
                    break;
                case AW.PaperSize.Folio:
                    paperSizeComboBox.SelectedIndex = 12;
                    break;
                case AW.PaperSize.Quarto:
                    paperSizeComboBox.SelectedIndex = 13;
                    break;
                case AW.PaperSize.Statement:
                    paperSizeComboBox.SelectedIndex = 14;
                    break;
                case AW.PaperSize.Paper10x14:
                    paperSizeComboBox.SelectedIndex = 15;
                    break;
                case AW.PaperSize.Paper11x17:
                    paperSizeComboBox.SelectedIndex = 16;
                    break;
                case AW.PaperSize.EnvelopeDL:
                    paperSizeComboBox.SelectedIndex = 17;
                    break;
                case AW.PaperSize.Number10Envelope:
                    paperSizeComboBox.SelectedIndex = 18;
                    break;
                case AW.PaperSize.Custom:
                    paperSizeComboBox.SelectedIndex = 19;
                    break;
                default:
                    paperSizeComboBox.SelectedIndex = 1; // 默认A4
                    break;
            }

            paperSizeRow.Controls.Add(paperSizeLabel, 0, 0);
            paperSizeRow.Controls.Add(paperSizeComboBox, 1, 0);

            // 自定义纸张尺寸面板
            var customSizePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Margin = new Padding(0, 5, 0, 0)
            };

            // 宽度行
            var widthRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            widthRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            widthRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            widthRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var widthLabel = new Label
            {
                Text = "宽度:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            customWidthNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                DecimalPlaces = 2,
                Increment = 0.1M,
                Minimum = 1,
                Maximum = 100,
                Value = (decimal)PageSetup.PaperWidth,
                TextAlign = HorizontalAlignment.Center
            };

            var widthUnitLabel = new Label
            {
                Text = "厘米",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            widthRow.Controls.Add(widthLabel, 0, 0);
            widthRow.Controls.Add(customWidthNumeric, 1, 0);
            widthRow.Controls.Add(widthUnitLabel, 2, 0);

            // 高度行
            var heightRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            heightRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            heightRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            heightRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var heightLabel = new Label
            {
                Text = "高度:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            customHeightNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                DecimalPlaces = 2,
                Increment = 0.1M,
                Minimum = 1,
                Maximum = 100,
                Value = (decimal)PageSetup.PaperHeight,
                TextAlign = HorizontalAlignment.Center
            };

            var heightUnitLabel = new Label
            {
                Text = "厘米",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            heightRow.Controls.Add(heightLabel, 0, 0);
            heightRow.Controls.Add(customHeightNumeric, 1, 0);
            heightRow.Controls.Add(heightUnitLabel, 2, 0);

            customSizePanel.Controls.Add(widthRow, 0, 0);
            customSizePanel.SetColumnSpan(widthRow, 2);

            customSizePanel.Controls.Add(heightRow, 0, 1);
            customSizePanel.SetColumnSpan(heightRow, 2);

            // 根据当前是否选择"自定义"设置自定义尺寸控件的可见性
            customSizePanel.Visible = paperSizeComboBox.SelectedIndex == 19;

            // 添加ComboBox选择改变事件处理
            paperSizeComboBox.SelectedIndexChanged += (s, e) =>
            {
                bool isCustom = paperSizeComboBox.SelectedIndex == 19;
                customSizePanel.Visible = isCustom;
                customWidthNumeric.Enabled = isCustom && enablePaperSizeCheckBox.Checked;
                customHeightNumeric.Enabled = isCustom && enablePaperSizeCheckBox.Checked;
            };

            enablePaperSizeCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enablePaperSizeCheckBox.Checked;
                paperSizeComboBox.Enabled = enabled;
                customWidthNumeric.Enabled = enabled && customSizePanel.Visible;
                customHeightNumeric.Enabled = enabled && customSizePanel.Visible;
            };

            // 应用初始启用状态
            paperSizeComboBox.Enabled = enablePaperSizeCheckBox.Checked;
            customWidthNumeric.Enabled = enablePaperSizeCheckBox.Checked && customSizePanel.Visible;
            customHeightNumeric.Enabled = enablePaperSizeCheckBox.Checked && customSizePanel.Visible;

            // 添加控件到纸张大小布局
            paperSizeLayout.Controls.Add(enablePaperSizeCheckBox, 0, 0);
            paperSizeLayout.SetColumnSpan(enablePaperSizeCheckBox, 2);

            paperSizeLayout.Controls.Add(paperSizeRow, 0, 1);
            paperSizeLayout.SetColumnSpan(paperSizeRow, 2);

            paperSizeLayout.Controls.Add(customSizePanel, 0, 2);
            paperSizeLayout.SetColumnSpan(customSizePanel, 2);

            // 完成纸张大小设置组
            paperSizeGroup.Controls.Add(paperSizeLayout);
            mainLayout.Controls.Add(paperSizeGroup, 0, 1);

            // 装订线设置组
            var gutterGroup = new GroupBox
            {
                Text = "装订线设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var gutterLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            gutterLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            gutterLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用装订线设置复选框
            enableGutterCheckBox = new CheckBox
            {
                Text = "启用装订线设置",
                AutoSize = true,
                Checked = PageSetup.EnableGutter,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 装订线宽度行
            var gutterWidthRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            gutterWidthRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            gutterWidthRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            gutterWidthRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var gutterLabel = new Label
            {
                Text = "装订线宽度:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            gutterNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                DecimalPlaces = 2,
                Increment = 0.1M,
                Minimum = 0,
                Maximum = 10,
                Value = (decimal)PageSetup.Gutter,
                TextAlign = HorizontalAlignment.Center
            };

            var gutterUnitLabel = new Label
            {
                Text = "厘米",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            gutterWidthRow.Controls.Add(gutterLabel, 0, 0);
            gutterWidthRow.Controls.Add(gutterNumeric, 1, 0);
            gutterWidthRow.Controls.Add(gutterUnitLabel, 2, 0);

            // 镜像边距复选框
            mirrorMarginsCheckBox = new CheckBox
            {
                Text = "镜像边距",
                AutoSize = true,
                Checked = PageSetup.MirrorMargins,
                Margin = new Padding(0, 5, 0, 5)
            };

            // 装订线位置复选框
            rtlGutterCheckBox = new CheckBox
            {
                Text = "装订线位于顶部",
                AutoSize = true,
                Checked = PageSetup.RtlGutter,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 添加事件处理
            enableGutterCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableGutterCheckBox.Checked;
                gutterNumeric.Enabled = enabled;
                mirrorMarginsCheckBox.Enabled = enabled;
                rtlGutterCheckBox.Enabled = enabled;
            };

            // 应用初始启用状态
            gutterNumeric.Enabled = enableGutterCheckBox.Checked;
            mirrorMarginsCheckBox.Enabled = enableGutterCheckBox.Checked;
            rtlGutterCheckBox.Enabled = enableGutterCheckBox.Checked;

            // 添加控件到装订线布局
            gutterLayout.Controls.Add(enableGutterCheckBox, 0, 0);
            gutterLayout.SetColumnSpan(enableGutterCheckBox, 2);

            gutterLayout.Controls.Add(gutterWidthRow, 0, 1);
            gutterLayout.SetColumnSpan(gutterWidthRow, 2);

            gutterLayout.Controls.Add(mirrorMarginsCheckBox, 0, 2);
            gutterLayout.SetColumnSpan(mirrorMarginsCheckBox, 2);

            gutterLayout.Controls.Add(rtlGutterCheckBox, 0, 3);
            gutterLayout.SetColumnSpan(rtlGutterCheckBox, 2);

            // 完成装订线设置组
            gutterGroup.Controls.Add(gutterLayout);
            mainLayout.Controls.Add(gutterGroup, 0, 2);

            // 行号设置组
            var lineNumberingGroup = new GroupBox
            {
                Text = "行号设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var lineNumberingLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 5,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            lineNumberingLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            lineNumberingLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用行号显示复选框
            enableLineNumberingCheckBox = new CheckBox
            {
                Text = "显示行号",
                AutoSize = true,
                Checked = PageSetup.LineNumberingIsActive,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 行号起始值
            var startValueRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            startValueRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            startValueRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            startValueRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var startValueLabel = new Label
            {
                Text = "起始值:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            lineNumberStartNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 1000,
                Value = PageSetup.LineNumberingStartValue,
                TextAlign = HorizontalAlignment.Center
            };

            var startValueUnitLabel = new Label
            {
                Text = "行",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            startValueRow.Controls.Add(startValueLabel, 0, 0);
            startValueRow.Controls.Add(lineNumberStartNumeric, 1, 0);
            startValueRow.Controls.Add(startValueUnitLabel, 2, 0);

            // 行号间隔
            var countByRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            countByRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            countByRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            countByRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var countByLabel = new Label
            {
                Text = "计数间隔:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            lineNumberCountByNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 100,
                Value = PageSetup.LineNumberingCountBy,
                TextAlign = HorizontalAlignment.Center
            };

            var countByUnitLabel = new Label
            {
                Text = "行",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            countByRow.Controls.Add(countByLabel, 0, 0);
            countByRow.Controls.Add(lineNumberCountByNumeric, 1, 0);
            countByRow.Controls.Add(countByUnitLabel, 2, 0);

            // 行号重置模式
            var restartModeRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true
            };

            restartModeRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            restartModeRow.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            var restartModeLabel = new Label
            {
                Text = "重置模式:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            lineNumberRestartModeComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            lineNumberRestartModeComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            lineNumberRestartModeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = lineNumberRestartModeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加重置模式选项
            lineNumberRestartModeComboBox.Items.Add("连续");
            lineNumberRestartModeComboBox.Items.Add("每页");
            lineNumberRestartModeComboBox.Items.Add("每节");

            // 选择当前重置模式
            int selectedIndex = 0;
            switch (PageSetup.LineNumberingRestartMode)
            {
                case AW.LineNumberRestartMode.Continuous:
                    selectedIndex = 0;
                    break;
                case AW.LineNumberRestartMode.RestartPage:
                    selectedIndex = 1;
                    break;
                case AW.LineNumberRestartMode.RestartSection:
                    selectedIndex = 2;
                    break;
            }
            lineNumberRestartModeComboBox.SelectedIndex = selectedIndex;

            restartModeRow.Controls.Add(restartModeLabel, 0, 0);
            restartModeRow.Controls.Add(lineNumberRestartModeComboBox, 1, 0);

            // 行号与文本距离
            var distanceRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            distanceRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            distanceRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            distanceRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var distanceLabel = new Label
            {
                Text = "距文本距离:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            lineNumberDistanceNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                DecimalPlaces = 2,
                Increment = 0.1M,
                Minimum = 0,
                Maximum = 100,
                Value = (decimal)PageSetup.LineNumberingDistance,
                TextAlign = HorizontalAlignment.Center
            };

            var distanceUnitLabel = new Label
            {
                Text = "厘米",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            distanceRow.Controls.Add(distanceLabel, 0, 0);
            distanceRow.Controls.Add(lineNumberDistanceNumeric, 1, 0);
            distanceRow.Controls.Add(distanceUnitLabel, 2, 0);

            // 添加事件处理
            enableLineNumberingCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableLineNumberingCheckBox.Checked;
                lineNumberStartNumeric.Enabled = enabled;
                lineNumberCountByNumeric.Enabled = enabled;
                lineNumberRestartModeComboBox.Enabled = enabled;
                lineNumberDistanceNumeric.Enabled = enabled;
            };

            // 应用初始启用状态
            lineNumberStartNumeric.Enabled = enableLineNumberingCheckBox.Checked;
            lineNumberCountByNumeric.Enabled = enableLineNumberingCheckBox.Checked;
            lineNumberRestartModeComboBox.Enabled = enableLineNumberingCheckBox.Checked;
            lineNumberDistanceNumeric.Enabled = enableLineNumberingCheckBox.Checked;

            // 添加控件到行号布局
            lineNumberingLayout.Controls.Add(enableLineNumberingCheckBox, 0, 0);
            lineNumberingLayout.SetColumnSpan(enableLineNumberingCheckBox, 2);

            lineNumberingLayout.Controls.Add(startValueRow, 0, 1);
            lineNumberingLayout.SetColumnSpan(startValueRow, 2);

            lineNumberingLayout.Controls.Add(countByRow, 0, 2);
            lineNumberingLayout.SetColumnSpan(countByRow, 2);

            lineNumberingLayout.Controls.Add(restartModeRow, 0, 3);
            lineNumberingLayout.SetColumnSpan(restartModeRow, 2);

            lineNumberingLayout.Controls.Add(distanceRow, 0, 4);
            lineNumberingLayout.SetColumnSpan(distanceRow, 2);

            // 完成行号设置组
            lineNumberingGroup.Controls.Add(lineNumberingLayout);
            mainLayout.Controls.Add(lineNumberingGroup, 0, 4);

            // 6. 章节设置组（新增）
            var chapterGroup = new GroupBox
            {
                Text = "章节设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var chapterLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            chapterLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            chapterLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用章节设置复选框
            enableChapterSettingsCheckBox = new CheckBox
            {
                Text = "启用章节设置",
                AutoSize = true,
                Checked = PageSetup.EnableChapterSettings,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 章节标题级别设置
            var headingLevelRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            headingLevelRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            headingLevelRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            headingLevelRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var headingLevelLabel = new Label
            {
                Text = "章节标题级别:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            headingLevelNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Value = PageSetup.HeadingLevelForChapter,
                Minimum = 0,
                Maximum = 9,
                TextAlign = HorizontalAlignment.Center
            };

            var headingLevelUnitLabel = new Label
            {
                Text = "级 (0=不使用)",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            headingLevelRow.Controls.Add(headingLevelLabel, 0, 0);
            headingLevelRow.Controls.Add(headingLevelNumeric, 1, 0);
            headingLevelRow.Controls.Add(headingLevelUnitLabel, 2, 0);

            // 章节页码分隔符设置
            var separatorRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            separatorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            separatorRow.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            var separatorLabel = new Label
            {
                Text = "页码分隔符:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            chapterSeparatorComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            chapterSeparatorComboBox.Items.Add("连字符 (-)");
            chapterSeparatorComboBox.Items.Add("句号 (.)");
            chapterSeparatorComboBox.Items.Add("冒号 (:)");
            chapterSeparatorComboBox.Items.Add("破折号 (—)");
            chapterSeparatorComboBox.Items.Add("长破折号 (——)");

            // 选择当前分隔符
            switch (PageSetup.ChapterPageSeparator)
            {
                case AW.ChapterPageSeparator.Hyphen:
                    chapterSeparatorComboBox.SelectedIndex = 0;
                    break;
                case AW.ChapterPageSeparator.Period:
                    chapterSeparatorComboBox.SelectedIndex = 1;
                    break;
                case AW.ChapterPageSeparator.Colon:
                    chapterSeparatorComboBox.SelectedIndex = 2;
                    break;
                case AW.ChapterPageSeparator.EmDash:
                    chapterSeparatorComboBox.SelectedIndex = 3;
                    break;
                case AW.ChapterPageSeparator.EnDash:
                    chapterSeparatorComboBox.SelectedIndex = 4;
                    break;
                default:
                    chapterSeparatorComboBox.SelectedIndex = 0;
                    break;
            }

            separatorRow.Controls.Add(separatorLabel, 0, 0);
            separatorRow.Controls.Add(chapterSeparatorComboBox, 1, 0);

            // 添加工具提示
            var chapterToolTip = new ToolTip();
            chapterToolTip.SetToolTip(headingLevelNumeric, "设置用于章节编号的标题级别（0-9），0表示不使用章节编号");
            chapterToolTip.SetToolTip(chapterSeparatorComboBox, "设置章节编号与页码之间的分隔符");

            // 添加事件处理
            enableChapterSettingsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableChapterSettingsCheckBox.Checked;
                headingLevelNumeric.Enabled = enabled;
                chapterSeparatorComboBox.Enabled = enabled;
            };

            // 应用初始启用状态
            headingLevelNumeric.Enabled = enableChapterSettingsCheckBox.Checked;
            chapterSeparatorComboBox.Enabled = enableChapterSettingsCheckBox.Checked;

            // 添加控件到章节布局
            chapterLayout.Controls.Add(enableChapterSettingsCheckBox, 0, 0);
            chapterLayout.SetColumnSpan(enableChapterSettingsCheckBox, 2);

            chapterLayout.Controls.Add(headingLevelRow, 0, 1);
            chapterLayout.SetColumnSpan(headingLevelRow, 2);

            chapterLayout.Controls.Add(separatorRow, 0, 2);
            chapterLayout.SetColumnSpan(separatorRow, 2);

            // 完成章节设置组
            chapterGroup.Controls.Add(chapterLayout);
            mainLayout.Controls.Add(chapterGroup, 0, 5);

            // 更新rowCount以容纳新的章节设置组
            mainLayout.RowCount = 6;

            // 将mainLayout添加到基础设置标签页
            basicSettingsTab.Controls.Add(mainLayout);
            mainTabControl.TabPages.Add(basicSettingsTab);

            // 创建"页面边距"标签页
            CreateMarginsTab(mainTabControl, marginsTab);

            // 创建"页面背景"标签页
            var backgroundTab = new TabPage("页面背景");
            backgroundTab.AutoScroll = true;
            backgroundTab.UseVisualStyleBackColor = true;

            var backgroundTabLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 设置行样式
            backgroundTabLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            backgroundTabLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            // 背景颜色设置组
            var backgroundColorGroup = new GroupBox
            {
                Text = "背景颜色",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var backgroundColorLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            backgroundColorLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            backgroundColorLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用背景颜色设置复选框
            enableBackgroundColorCheckBox = new CheckBox
            {
                Text = "启用背景颜色设置",
                AutoSize = true,
                Checked = PageSetup.EnableBackgroundColor,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 颜色选择行
            var colorRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 1,
                AutoSize = true
            };

            colorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 标签
            colorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 颜色展示框
            colorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 选择按钮
            colorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 清除按钮

            var colorLabel = new Label
            {
                Text = "页面背景颜色:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            colorPreviewPanel = new Panel
            {
                BorderStyle = BorderStyle.FixedSingle,
                Size = new Size(30, 20),
                BackColor = PageSetup.BackgroundColor != Color.Transparent ?
                    PageSetup.BackgroundColor : Color.Black,
                Margin = new Padding(0, 2, 10, 0)
            };

            colorPickerButton = new Button
            {
                Text = "选择颜色...",
                AutoSize = true,
                Margin = new Padding(0, 0, 10, 0)
            };

            clearColorButton = new Button
            {
                Text = "清除",
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 0)
            };

            colorRow.Controls.Add(colorLabel, 0, 0);
            colorRow.Controls.Add(colorPreviewPanel, 1, 0);
            colorRow.Controls.Add(colorPickerButton, 2, 0);
            colorRow.Controls.Add(clearColorButton, 3, 0);

            // 添加事件处理
            enableBackgroundColorCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBackgroundColorCheckBox.Checked;
                colorPickerButton.Enabled = enabled;
                colorPreviewPanel.Enabled = enabled;
                clearColorButton.Enabled = enabled;
            };

            colorPickerButton.Click += (sender, e) =>
            {
                using (ColorDialog colorDialog = new ColorDialog())
                {
                    colorDialog.Color = colorPreviewPanel.BackColor;
                    if (colorDialog.ShowDialog() == DialogResult.OK)
                    {
                        // 检查是否选择了黑色，避免文档背景变黑
                        if (colorDialog.Color.ToArgb() == Color.Black.ToArgb())
                        {
                            MessageBox.Show("不建议使用黑色作为背景颜色，这可能导致文档背景变黑。\n已自动调整为浅灰色。",
                                "背景颜色警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            colorPreviewPanel.BackColor = Color.LightGray;
                        }
                        else
                        {
                            colorPreviewPanel.BackColor = colorDialog.Color;
                        }
                    }
                }
            };

            clearColorButton.Click += (sender, e) =>
            {
                colorPreviewPanel.BackColor = Color.Transparent; // 修改为透明色而不是黑色
            };

            // 应用初始启用状态
            colorPickerButton.Enabled = enableBackgroundColorCheckBox.Checked;
            colorPreviewPanel.Enabled = enableBackgroundColorCheckBox.Checked;
            clearColorButton.Enabled = enableBackgroundColorCheckBox.Checked;

            // 添加控件到背景颜色布局
            backgroundColorLayout.Controls.Add(enableBackgroundColorCheckBox, 0, 0);
            backgroundColorLayout.SetColumnSpan(enableBackgroundColorCheckBox, 2);

            backgroundColorLayout.Controls.Add(colorRow, 0, 1);
            backgroundColorLayout.SetColumnSpan(colorRow, 2);

            // 完成背景颜色设置组
            backgroundColorGroup.Controls.Add(backgroundColorLayout);
            backgroundTabLayout.Controls.Add(backgroundColorGroup, 0, 0);

            // 背景图片设置组
            var backgroundImageGroup = new GroupBox
            {
                Text = "背景图片",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var backgroundImageLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            backgroundImageLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            backgroundImageLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用背景图片设置复选框
            enableBackgroundImageCheckBox = new CheckBox
            {
                Text = "启用背景图片设置",
                AutoSize = true,
                Checked = PageSetup.EnableBackgroundImage,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 图片路径行
            var imagePathRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            imagePathRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            imagePathRow.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            imagePathRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var imagePathLabel = new Label
            {
                Text = "图片路径:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            imagePathTextBox = new TextBox
            {
                Dock = DockStyle.Fill,
                Text = PageSetup.BackgroundImage ?? "",
                Margin = new Padding(0, 0, 10, 0),
                TextAlign = HorizontalAlignment.Center
            };

            browseImageButton = new Button
            {
                Text = "浏览...",
                AutoSize = true,
                Margin = new Padding(0)
            };

            imagePathRow.Controls.Add(imagePathLabel, 0, 0);
            imagePathRow.Controls.Add(imagePathTextBox, 1, 0);
            imagePathRow.Controls.Add(browseImageButton, 2, 0);

            // 图片源类型行
            var imageSourceTypeRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 5, 0, 0)
            };

            imageSourceTypeRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            imageSourceTypeRow.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            var imageSourceTypeLabel = new Label
            {
                Text = "图片源类型:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            imageSourceTypeComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            imageSourceTypeComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            imageSourceTypeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = imageSourceTypeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加图片源类型选项
            imageSourceTypeComboBox.Items.Add("文件");
            imageSourceTypeComboBox.Items.Add("网址");
            imageSourceTypeComboBox.SelectedIndex = 0; // 默认选择文件

            imageSourceTypeRow.Controls.Add(imageSourceTypeLabel, 0, 0);
            imageSourceTypeRow.Controls.Add(imageSourceTypeComboBox, 1, 0);

            // 图片显示方式行
            var imageWrapTypeRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 5, 0, 0)
            };

            imageWrapTypeRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            imageWrapTypeRow.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            var imageWrapTypeLabel = new Label
            {
                Text = "图片显示方式:",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            imageWrapTypeComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            imageWrapTypeComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            imageWrapTypeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = imageWrapTypeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加图片显示方式选项
            imageWrapTypeComboBox.Items.Add("平铺");
            imageWrapTypeComboBox.Items.Add("居中");
            imageWrapTypeComboBox.Items.Add("拉伸");

            // 根据当前设置选择相应项
            switch (PageSetup.BackgroundImageDisplayMode)
            {
                case PageSetupFixed.DisplayMode.Tiled:
                    imageWrapTypeComboBox.SelectedIndex = 0;
                    break;
                case PageSetupFixed.DisplayMode.Centered:
                    imageWrapTypeComboBox.SelectedIndex = 1;
                    break;
                case PageSetupFixed.DisplayMode.Stretched:
                    imageWrapTypeComboBox.SelectedIndex = 2;
                    break;
                default:
                    imageWrapTypeComboBox.SelectedIndex = 0; // 默认平铺
                    break;
            }

            imageWrapTypeRow.Controls.Add(imageWrapTypeLabel, 0, 0);
            imageWrapTypeRow.Controls.Add(imageWrapTypeComboBox, 1, 0);

            // 添加事件处理
            enableBackgroundImageCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBackgroundImageCheckBox.Checked;
                imagePathTextBox.Enabled = enabled;
                browseImageButton.Enabled = enabled;
                imageSourceTypeComboBox.Enabled = enabled;
                imageWrapTypeComboBox.Enabled = enabled;
            };

            browseImageButton.Click += (sender, e) =>
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "图片文件 (*.jpg, *.jpeg, *.png, *.bmp, *.gif)|*.jpg;*.jpeg;*.png;*.bmp;*.gif|所有文件 (*.*)|*.*";
                    openFileDialog.Title = "选择背景图片";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        imagePathTextBox.Text = openFileDialog.FileName;
                    }
                }
            };

            // 应用初始启用状态
            imagePathTextBox.Enabled = enableBackgroundImageCheckBox.Checked;
            browseImageButton.Enabled = enableBackgroundImageCheckBox.Checked;
            imageSourceTypeComboBox.Enabled = enableBackgroundImageCheckBox.Checked;
            imageWrapTypeComboBox.Enabled = enableBackgroundImageCheckBox.Checked;

            // 添加控件到背景图片布局
            backgroundImageLayout.Controls.Add(enableBackgroundImageCheckBox, 0, 0);
            backgroundImageLayout.SetColumnSpan(enableBackgroundImageCheckBox, 2);

            backgroundImageLayout.Controls.Add(imagePathRow, 0, 1);
            backgroundImageLayout.SetColumnSpan(imagePathRow, 2);

            backgroundImageLayout.Controls.Add(imageSourceTypeRow, 0, 2);
            backgroundImageLayout.SetColumnSpan(imageSourceTypeRow, 2);

            backgroundImageLayout.Controls.Add(imageWrapTypeRow, 0, 3);
            backgroundImageLayout.SetColumnSpan(imageWrapTypeRow, 2);

            // 完成背景图片设置组
            backgroundImageGroup.Controls.Add(backgroundImageLayout);
            backgroundTabLayout.Controls.Add(backgroundImageGroup, 0, 1);

            // 将背景布局添加到背景标签页
            backgroundTab.Controls.Add(backgroundTabLayout);
            mainTabControl.TabPages.Add(backgroundTab);

            // 创建"边框样式"标签页
            var borderTab = new TabPage("边框样式");
            borderTab.AutoScroll = true;
            borderTab.UseVisualStyleBackColor = true;

            var borderTabLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 4,  // 四个子区域：边框样式、边框应用位置、边框颜色、边框宽度
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 设置行样式
            for (int i = 0; i < 4; i++)
            {
                borderTabLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            var borderLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 边框样式设置组
            var borderStyleGroup = new GroupBox
            {
                Text = "边框样式",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var borderStyleLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            borderStyleLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            borderStyleLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用边框样式设置复选框
            enableBorderStyleCheckBox = new CheckBox
            {
                Text = "启用边框样式设置",
                AutoSize = true,
                Checked = PageSetup.EnableBorderStyle,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 边框样式选择
            var borderStyleLabel = new Label {
                Text = "边框样式:",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };
            borderStyleComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            borderStyleComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            borderStyleComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = borderStyleComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加边框样式选项
            borderStyleComboBox.Items.Add("无");
            borderStyleComboBox.Items.Add("单线");
            borderStyleComboBox.Items.Add("双线");
            borderStyleComboBox.Items.Add("三线");
            borderStyleComboBox.Items.Add("点线");
            borderStyleComboBox.Items.Add("虚线");
            borderStyleComboBox.Items.Add("双点线");
            borderStyleComboBox.Items.Add("双虚线");

            // 选择当前边框样式
            switch (PageSetup.BorderStyle)
            {
                case PageSetupFixed.BorderLineStyle.None:
                    borderStyleComboBox.SelectedIndex = 0;
                    break;
                case PageSetupFixed.BorderLineStyle.Single:
                    borderStyleComboBox.SelectedIndex = 1;
                    break;
                case PageSetupFixed.BorderLineStyle.Double:
                    borderStyleComboBox.SelectedIndex = 2;
                    break;
                case PageSetupFixed.BorderLineStyle.Triple:
                    borderStyleComboBox.SelectedIndex = 3;
                    break;
                case PageSetupFixed.BorderLineStyle.Dotted:
                    borderStyleComboBox.SelectedIndex = 4;
                    break;
                case PageSetupFixed.BorderLineStyle.Dashed:
                    borderStyleComboBox.SelectedIndex = 5;
                    break;
                case PageSetupFixed.BorderLineStyle.DotDash:
                    borderStyleComboBox.SelectedIndex = 6;
                    break;
                case PageSetupFixed.BorderLineStyle.DotDotDash:
                    borderStyleComboBox.SelectedIndex = 7;
                    break;
                default:
                    borderStyleComboBox.SelectedIndex = 0;
                    break;
            }

            // 添加事件处理
            enableBorderStyleCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBorderStyleCheckBox.Checked;
                borderStyleComboBox.Enabled = enabled;
            };

            // 应用初始启用状态
            borderStyleComboBox.Enabled = enableBorderStyleCheckBox.Checked;

            // 添加控件到边框样式布局
            borderStyleLayout.Controls.Add(enableBorderStyleCheckBox, 0, 0);
            borderStyleLayout.SetColumnSpan(enableBorderStyleCheckBox, 2);

            borderStyleLayout.Controls.Add(borderStyleLabel, 0, 1);
            borderStyleLayout.Controls.Add(borderStyleComboBox, 1, 1);

            borderStyleGroup.Controls.Add(borderStyleLayout);
            borderTabLayout.Controls.Add(borderStyleGroup, 0, 0);

            // 边框应用位置设置组
            var borderApplicationGroup = new GroupBox
            {
                Text = "边框应用位置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var borderApplicationLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            borderApplicationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            borderApplicationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 应用边框顶部
            applyBorderTopCheckBox = new CheckBox
            {
                Text = "应用边框顶部",
                AutoSize = true,
                Checked = PageSetup.ApplyBorderTop,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 应用边框左侧
            applyBorderLeftCheckBox = new CheckBox
            {
                Text = "应用边框左侧",
                AutoSize = true,
                Checked = PageSetup.ApplyBorderLeft,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 应用边框底部
            applyBorderBottomCheckBox = new CheckBox
            {
                Text = "应用边框底部",
                AutoSize = true,
                Checked = PageSetup.ApplyBorderBottom,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 应用边框右侧
            applyBorderRightCheckBox = new CheckBox
            {
                Text = "应用边框右侧",
                AutoSize = true,
                Checked = PageSetup.ApplyBorderRight,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 添加事件处理
            enableBorderStyleCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBorderStyleCheckBox.Checked;
                applyBorderTopCheckBox.Enabled = enabled;
                applyBorderLeftCheckBox.Enabled = enabled;
                applyBorderBottomCheckBox.Enabled = enabled;
                applyBorderRightCheckBox.Enabled = enabled;
            };

            // 应用初始启用状态
            applyBorderTopCheckBox.Enabled = enableBorderStyleCheckBox.Checked;
            applyBorderLeftCheckBox.Enabled = enableBorderStyleCheckBox.Checked;
            applyBorderBottomCheckBox.Enabled = enableBorderStyleCheckBox.Checked;
            applyBorderRightCheckBox.Enabled = enableBorderStyleCheckBox.Checked;

            // 添加控件到边框应用位置布局
            borderApplicationLayout.Controls.Add(applyBorderTopCheckBox, 0, 0);
            borderApplicationLayout.Controls.Add(applyBorderLeftCheckBox, 0, 1);
            borderApplicationLayout.Controls.Add(applyBorderBottomCheckBox, 0, 2);
            borderApplicationLayout.Controls.Add(applyBorderRightCheckBox, 0, 3);

            borderApplicationGroup.Controls.Add(borderApplicationLayout);
            borderTabLayout.Controls.Add(borderApplicationGroup, 0, 1);

            // 边框颜色设置组
            var borderColorGroup = new GroupBox
            {
                Text = "边框颜色",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var borderColorLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            borderColorLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            borderColorLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用边框颜色设置复选框
            enableBorderColorCheckBox = new CheckBox
            {
                Text = "启用边框颜色设置",
                AutoSize = true,
                Checked = PageSetup.EnableBorderColor,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 颜色选择行
            var borderColorRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 1,
                AutoSize = true
            };

            borderColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 标签
            borderColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 颜色展示框
            borderColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 选择按钮
            borderColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 清除按钮

            var borderColorLabel = new Label
            {
                Text = "页面边框颜色:",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            borderColorPreviewPanel = new Panel
            {
                BorderStyle = BorderStyle.FixedSingle,
                Size = new Size(30, 20),
                BackColor = PageSetup.BorderColor != Color.Transparent ?
                    PageSetup.BorderColor : Color.Black,
                Margin = new Padding(0, 2, 10, 0)
            };

            borderColorPickerButton = new Button
            {
                Text = "选择颜色...",
                AutoSize = true,
                Margin = new Padding(0, 0, 10, 0)
            };

            clearBorderColorButton = new Button
            {
                Text = "清除",
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 0)
            };

            borderColorRow.Controls.Add(borderColorLabel, 0, 0);
            borderColorRow.Controls.Add(borderColorPreviewPanel, 1, 0);
            borderColorRow.Controls.Add(borderColorPickerButton, 2, 0);
            borderColorRow.Controls.Add(clearBorderColorButton, 3, 0);

            // 添加事件处理
            enableBorderColorCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBorderColorCheckBox.Checked;
                borderColorPickerButton.Enabled = enabled;
                borderColorPreviewPanel.Enabled = enabled;
                clearBorderColorButton.Enabled = enabled;
            };

            borderColorPickerButton.Click += (sender, e) =>
            {
                using (ColorDialog colorDialog = new ColorDialog())
                {
                    colorDialog.Color = borderColorPreviewPanel.BackColor;
                    if (colorDialog.ShowDialog() == DialogResult.OK)
                    {
                        borderColorPreviewPanel.BackColor = colorDialog.Color;
                    }
                }
            };

            clearBorderColorButton.Click += (sender, e) =>
            {
                borderColorPreviewPanel.BackColor = Color.Transparent; // 修改为透明色而不是黑色
            };

            // 应用初始启用状态
            borderColorPickerButton.Enabled = enableBorderColorCheckBox.Checked;
            borderColorPreviewPanel.Enabled = enableBorderColorCheckBox.Checked;
            clearBorderColorButton.Enabled = enableBorderColorCheckBox.Checked;

            // 添加控件到边框颜色布局
            borderColorLayout.Controls.Add(enableBorderColorCheckBox, 0, 0);
            borderColorLayout.SetColumnSpan(enableBorderColorCheckBox, 2);

            borderColorLayout.Controls.Add(borderColorRow, 0, 1);
            borderColorLayout.SetColumnSpan(borderColorRow, 2);

            // 完成边框颜色设置组
            borderColorGroup.Controls.Add(borderColorLayout);
            borderTabLayout.Controls.Add(borderColorGroup, 0, 1);

            // 边框宽度设置组
            var borderWidthGroup = new GroupBox
            {
                Text = "边框宽度",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var borderWidthLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            borderWidthLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            borderWidthLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用边框宽度设置复选框
            enableBorderWidthCheckBox = new CheckBox
            {
                Text = "启用边框宽度设置",
                AutoSize = true,
                Checked = PageSetup.EnableBorderWidth,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 边框宽度输入行
            var borderWidthRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            borderWidthRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            borderWidthRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            borderWidthRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var borderWidthLabel = new Label {
                Text = "边框宽度:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };
            borderWidthNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                DecimalPlaces = 2,
                Increment = 0.1M,
                Minimum = 0.1M,
                Maximum = 10M,
                Value = (decimal)PageSetup.BorderWidth,
                TextAlign = HorizontalAlignment.Center
            };

            var borderWidthUnitLabel = new Label
            {
                Text = "厘米",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            borderWidthRow.Controls.Add(borderWidthLabel, 0, 0);
            borderWidthRow.Controls.Add(borderWidthNumeric, 1, 0);
            borderWidthRow.Controls.Add(borderWidthUnitLabel, 2, 0);

            // 添加事件处理
            enableBorderWidthCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBorderWidthCheckBox.Checked;
                borderWidthNumeric.Enabled = enabled;
            };

            // 应用初始启用状态
            borderWidthNumeric.Enabled = enableBorderWidthCheckBox.Checked;

            // 添加控件到边框宽度布局
            borderWidthLayout.Controls.Add(enableBorderWidthCheckBox, 0, 0);
            borderWidthLayout.SetColumnSpan(enableBorderWidthCheckBox, 2);

            borderWidthLayout.Controls.Add(borderWidthRow, 0, 1);
            borderWidthLayout.SetColumnSpan(borderWidthRow, 2);

            borderWidthGroup.Controls.Add(borderWidthLayout);
            borderTabLayout.Controls.Add(borderWidthGroup, 0, 2);

            // 边框艺术效果设置组
            var borderArtGroup = new GroupBox
            {
                Text = "边框艺术效果",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var borderArtLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            borderArtLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            borderArtLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用边框艺术效果设置复选框
            enableBorderArtCheckBox = new CheckBox
            {
                Text = "启用边框艺术效果设置",
                AutoSize = true,
                Checked = PageSetup.EnableBorderArt,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 边框艺术效果选择
            var borderArtLabel = new Label {
                Text = "边框艺术效果:",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };
            borderArtComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            borderArtComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            borderArtComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = borderArtComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加边框艺术效果选项
            borderArtComboBox.Items.Add("无");
            borderArtComboBox.Items.Add("艺术效果1");
            borderArtComboBox.Items.Add("艺术效果2");
            borderArtComboBox.Items.Add("艺术效果3");
            borderArtComboBox.Items.Add("艺术效果4");
            borderArtComboBox.Items.Add("艺术效果5");
            borderArtComboBox.Items.Add("艺术效果6");
            borderArtComboBox.Items.Add("艺术效果7");
            borderArtComboBox.Items.Add("艺术效果8");

            // 选择当前边框艺术效果
            borderArtComboBox.SelectedIndex = PageSetup.BorderArtStyle;

            // 添加事件处理
            enableBorderArtCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBorderArtCheckBox.Checked;
                borderArtComboBox.Enabled = enabled;
            };

            // 应用初始启用状态
            borderArtComboBox.Enabled = enableBorderArtCheckBox.Checked;

            // 添加控件到边框艺术效果布局
            borderArtLayout.Controls.Add(enableBorderArtCheckBox, 0, 0);
            borderArtLayout.SetColumnSpan(enableBorderArtCheckBox, 2);

            borderArtLayout.Controls.Add(borderArtLabel, 0, 1);
            borderArtLayout.Controls.Add(borderArtComboBox, 1, 1);

            borderArtGroup.Controls.Add(borderArtLayout);
            borderTabLayout.Controls.Add(borderArtGroup, 0, 3);

            // 边框高级属性设置组（新增）
            var borderAdvancedGroup = new GroupBox
            {
                Text = "边框高级属性",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var borderAdvancedLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 6,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            borderAdvancedLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            borderAdvancedLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用边框高级属性设置复选框
            enableBorderAdvancedCheckBox = new CheckBox
            {
                Text = "启用边框高级属性设置",
                AutoSize = true,
                Checked = PageSetup.EnableBorderAdvanced,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 边框总在前面复选框
            borderAlwaysInFrontCheckBox = new CheckBox
            {
                Text = "边框总在前面",
                AutoSize = true,
                Checked = PageSetup.BorderAlwaysInFront,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 边框应用范围设置
            var borderAppliesToRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            borderAppliesToRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            borderAppliesToRow.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            var borderAppliesToLabel = new Label
            {
                Text = "边框应用范围:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            borderAppliesToComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            borderAppliesToComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            borderAppliesToComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = borderAppliesToComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加边框应用范围选项
            borderAppliesToComboBox.Items.Add("所有页面");
            borderAppliesToComboBox.Items.Add("第一页");
            borderAppliesToComboBox.Items.Add("其他页面");

            // 选择当前边框应用范围
            switch (PageSetup.BorderAppliesTo)
            {
                case AW.PageBorderAppliesTo.AllPages:
                    borderAppliesToComboBox.SelectedIndex = 0;
                    break;
                case AW.PageBorderAppliesTo.FirstPage:
                    borderAppliesToComboBox.SelectedIndex = 1;
                    break;
                case AW.PageBorderAppliesTo.OtherPages:
                    borderAppliesToComboBox.SelectedIndex = 2;
                    break;
                default:
                    borderAppliesToComboBox.SelectedIndex = 0;
                    break;
            }

            borderAppliesToRow.Controls.Add(borderAppliesToLabel, 0, 0);
            borderAppliesToRow.Controls.Add(borderAppliesToComboBox, 1, 0);

            // 边框距离测量起点设置
            var borderDistanceFromRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            borderDistanceFromRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            borderDistanceFromRow.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            var borderDistanceFromLabel = new Label
            {
                Text = "边框距离测量起点:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            borderDistanceFromComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            borderDistanceFromComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            borderDistanceFromComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = borderDistanceFromComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加边框距离测量起点选项
            borderDistanceFromComboBox.Items.Add("文本");
            borderDistanceFromComboBox.Items.Add("页面边缘");

            // 选择当前边框距离测量起点
            switch (PageSetup.BorderDistanceFrom)
            {
                case AW.PageBorderDistanceFrom.Text:
                    borderDistanceFromComboBox.SelectedIndex = 0;
                    break;
                case AW.PageBorderDistanceFrom.PageEdge:
                    borderDistanceFromComboBox.SelectedIndex = 1;
                    break;
                default:
                    borderDistanceFromComboBox.SelectedIndex = 0;
                    break;
            }

            borderDistanceFromRow.Controls.Add(borderDistanceFromLabel, 0, 0);
            borderDistanceFromRow.Controls.Add(borderDistanceFromComboBox, 1, 0);

            // 边框包围页眉复选框
            borderSurroundsHeaderCheckBox = new CheckBox
            {
                Text = "边框包围页眉",
                AutoSize = true,
                Checked = PageSetup.BorderSurroundsHeader,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 边框包围页脚复选框
            borderSurroundsFooterCheckBox = new CheckBox
            {
                Text = "边框包围页脚",
                AutoSize = true,
                Checked = PageSetup.BorderSurroundsFooter,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 添加事件处理
            enableBorderAdvancedCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBorderAdvancedCheckBox.Checked;
                borderAlwaysInFrontCheckBox.Enabled = enabled;
                borderAppliesToComboBox.Enabled = enabled;
                borderDistanceFromComboBox.Enabled = enabled;
                borderSurroundsHeaderCheckBox.Enabled = enabled;
                borderSurroundsFooterCheckBox.Enabled = enabled;
            };

            // 应用初始启用状态
            borderAlwaysInFrontCheckBox.Enabled = enableBorderAdvancedCheckBox.Checked;
            borderAppliesToComboBox.Enabled = enableBorderAdvancedCheckBox.Checked;
            borderDistanceFromComboBox.Enabled = enableBorderAdvancedCheckBox.Checked;
            borderSurroundsHeaderCheckBox.Enabled = enableBorderAdvancedCheckBox.Checked;
            borderSurroundsFooterCheckBox.Enabled = enableBorderAdvancedCheckBox.Checked;

            // 添加控件到边框高级属性布局
            borderAdvancedLayout.Controls.Add(enableBorderAdvancedCheckBox, 0, 0);
            borderAdvancedLayout.SetColumnSpan(enableBorderAdvancedCheckBox, 2);

            borderAdvancedLayout.Controls.Add(borderAlwaysInFrontCheckBox, 0, 1);
            borderAdvancedLayout.SetColumnSpan(borderAlwaysInFrontCheckBox, 2);

            borderAdvancedLayout.Controls.Add(borderAppliesToRow, 0, 2);
            borderAdvancedLayout.SetColumnSpan(borderAppliesToRow, 2);

            borderAdvancedLayout.Controls.Add(borderDistanceFromRow, 0, 3);
            borderAdvancedLayout.SetColumnSpan(borderDistanceFromRow, 2);

            borderAdvancedLayout.Controls.Add(borderSurroundsHeaderCheckBox, 0, 4);
            borderAdvancedLayout.SetColumnSpan(borderSurroundsHeaderCheckBox, 2);

            borderAdvancedLayout.Controls.Add(borderSurroundsFooterCheckBox, 0, 5);
            borderAdvancedLayout.SetColumnSpan(borderSurroundsFooterCheckBox, 2);

            // 完成边框高级属性设置组
            borderAdvancedGroup.Controls.Add(borderAdvancedLayout);
            borderTabLayout.Controls.Add(borderAdvancedGroup, 0, 4);

            // 更新边框标签页布局的行数
            borderTabLayout.RowCount = 6;

            // 边框距离设置
            var distanceLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0, 5, 0, 0)
            };

            // 设置列宽
            distanceLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            distanceLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 上边距
            var distanceTopRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            distanceTopRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            distanceTopRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            distanceTopRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var distanceTopLabel = new Label
            {
                Text = "上边距:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            distanceFromTopNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 0,
                Maximum = 100,
                Value = (decimal)PageSetup.BorderDistanceFromTop,
                TextAlign = HorizontalAlignment.Center
            };

            var distanceTopUnitLabel = new Label
            {
                Text = "点",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            distanceTopRow.Controls.Add(distanceTopLabel, 0, 0);
            distanceTopRow.Controls.Add(distanceFromTopNumeric, 1, 0);
            distanceTopRow.Controls.Add(distanceTopUnitLabel, 2, 0);

            // 左边距
            var distanceLeftRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            distanceLeftRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            distanceLeftRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            distanceLeftRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var distanceLeftLabel = new Label
            {
                Text = "左边距:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            distanceFromLeftNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 0,
                Maximum = 100,
                Value = (decimal)PageSetup.BorderDistanceFromLeft,
                TextAlign = HorizontalAlignment.Center
            };

            var distanceLeftUnitLabel = new Label
            {
                Text = "点",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            distanceLeftRow.Controls.Add(distanceLeftLabel, 0, 0);
            distanceLeftRow.Controls.Add(distanceFromLeftNumeric, 1, 0);
            distanceLeftRow.Controls.Add(distanceLeftUnitLabel, 2, 0);

            // 下边距
            var distanceBottomRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            distanceBottomRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            distanceBottomRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            distanceBottomRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var distanceBottomLabel = new Label
            {
                Text = "下边距:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            distanceFromBottomNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 0,
                Maximum = 100,
                Value = (decimal)PageSetup.BorderDistanceFromBottom,
                TextAlign = HorizontalAlignment.Center
            };

            var distanceBottomUnitLabel = new Label
            {
                Text = "点",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            distanceBottomRow.Controls.Add(distanceBottomLabel, 0, 0);
            distanceBottomRow.Controls.Add(distanceFromBottomNumeric, 1, 0);
            distanceBottomRow.Controls.Add(distanceBottomUnitLabel, 2, 0);

            // 右边距
            var distanceRightRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            distanceRightRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            distanceRightRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            distanceRightRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var distanceRightLabel = new Label
            {
                Text = "右边距:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            distanceFromRightNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 0,
                Maximum = 100,
                Value = (decimal)PageSetup.BorderDistanceFromRight,
                TextAlign = HorizontalAlignment.Center
            };

            var distanceRightUnitLabel = new Label
            {
                Text = "点",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            distanceRightRow.Controls.Add(distanceRightLabel, 0, 0);
            distanceRightRow.Controls.Add(distanceFromRightNumeric, 1, 0);
            distanceRightRow.Controls.Add(distanceRightUnitLabel, 2, 0);

            // 添加到距离布局
            distanceLayout.Controls.Add(distanceTopRow, 0, 0);
            distanceLayout.SetColumnSpan(distanceTopRow, 2);

            distanceLayout.Controls.Add(distanceLeftRow, 0, 1);
            distanceLayout.SetColumnSpan(distanceLeftRow, 2);

            distanceLayout.Controls.Add(distanceBottomRow, 0, 2);
            distanceLayout.SetColumnSpan(distanceBottomRow, 2);

            distanceLayout.Controls.Add(distanceRightRow, 0, 3);
            distanceLayout.SetColumnSpan(distanceRightRow, 2);

            // 将距离设置添加到边框艺术效果布局
            borderArtLayout.Controls.Add(distanceLayout, 0, 2);
            borderArtLayout.SetColumnSpan(distanceLayout, 2);

            // 将边框布局添加到边框标签页
            borderTab.Controls.Add(borderTabLayout);
            mainTabControl.TabPages.Add(borderTab);

            // 创建"网格线"标签页
            var gridTab = new TabPage("网格线");
            gridTab.AutoScroll = true;
            gridTab.UseVisualStyleBackColor = true;

            var gridLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 5, // 五个子区域：网格线显示、网格线颜色、网格线间距、对齐网格、文档网格布局
                AutoSize = true,
                Padding = new Padding(3),
                Margin = new Padding(3)
            };

            // 设置行样式
            for (int i = 0; i < 5; i++)
            {
                gridLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 1. 网格线显示区域
            var gridLinesGroup = new GroupBox
            {
                Text = "网格线显示",
                AutoSize = true,
                Dock = DockStyle.Fill,
                MinimumSize = new Size(0, 150)
            };

            var gridLinesLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(8),
                Margin = new Padding(3)
            };

            // 设置列宽
            gridLinesLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            gridLinesLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用网格线显示复选框
            enableGridLinesCheckBox = new CheckBox
            {
                Text = "启用网格线显示",
                AutoSize = true,
                Checked = PageSetup.ShowGridLines,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 显示网格线复选框
            showGridLinesCheckBox = new CheckBox
            {
                Text = "显示网格线",
                AutoSize = true,
                Checked = PageSetup.ShowGridLines,
                Margin = new Padding(20, 0, 0, 5)
            };

            // 添加事件处理
            enableGridLinesCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableGridLinesCheckBox.Checked;
                showGridLinesCheckBox.Enabled = enabled;
            };

            // 应用初始启用状态
            showGridLinesCheckBox.Enabled = enableGridLinesCheckBox.Checked;

            // 添加控件到网格线显示布局
            gridLinesLayout.Controls.Add(enableGridLinesCheckBox, 0, 0);
            gridLinesLayout.SetColumnSpan(enableGridLinesCheckBox, 2);

            gridLinesLayout.Controls.Add(showGridLinesCheckBox, 0, 1);
            gridLinesLayout.SetColumnSpan(showGridLinesCheckBox, 2);

            gridLinesGroup.Controls.Add(gridLinesLayout);
            gridLayout.Controls.Add(gridLinesGroup, 0, 0);

            // 2. 网格线颜色区域
            var gridColorGroup = new GroupBox
            {
                Text = "网格线颜色",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var gridColorLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            gridColorLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            gridColorLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用网格线颜色设置复选框
            enableGridColorCheckBox = new CheckBox
            {
                Text = "启用网格线颜色设置",
                AutoSize = true,
                Checked = PageSetup.EnableGridColor,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 颜色选择行
            var gridColorRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 1,
                AutoSize = true
            };

            gridColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 标签
            gridColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 颜色展示框
            gridColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 选择按钮
            gridColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 清除按钮

            var gridColorLabel = new Label
            {
                Text = "网格线颜色:",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            gridColorPreviewPanel = new Panel
            {
                BorderStyle = BorderStyle.FixedSingle,
                Size = new Size(30, 20),
                BackColor = PageSetup.GridLinesColor != Color.Transparent ?
                    PageSetup.GridLinesColor : Color.LightGray,
                Margin = new Padding(0, 2, 10, 0)
            };

            gridColorPickerButton = new Button
            {
                Text = "选择颜色...",
                AutoSize = true,
                Margin = new Padding(0, 0, 10, 0)
            };

            clearGridColorButton = new Button
            {
                Text = "清除",
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 0)
            };

            gridColorRow.Controls.Add(gridColorLabel, 0, 0);
            gridColorRow.Controls.Add(gridColorPreviewPanel, 1, 0);
            gridColorRow.Controls.Add(gridColorPickerButton, 2, 0);
            gridColorRow.Controls.Add(clearGridColorButton, 3, 0);

            // 添加事件处理
            enableGridColorCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableGridColorCheckBox.Checked;
                gridColorPickerButton.Enabled = enabled;
                gridColorPreviewPanel.Enabled = enabled;
                clearGridColorButton.Enabled = enabled;
            };

            gridColorPickerButton.Click += (sender, e) =>
            {
                using (ColorDialog colorDialog = new ColorDialog())
                {
                    colorDialog.Color = gridColorPreviewPanel.BackColor;
                    if (colorDialog.ShowDialog() == DialogResult.OK)
                    {
                        gridColorPreviewPanel.BackColor = colorDialog.Color;
                    }
                }
            };

            clearGridColorButton.Click += (sender, e) =>
            {
                gridColorPreviewPanel.BackColor = Color.Transparent; // 修改为透明色而不是黑色
            };

            // 应用初始启用状态
            gridColorPickerButton.Enabled = enableGridColorCheckBox.Checked;
            gridColorPreviewPanel.Enabled = enableGridColorCheckBox.Checked;
            clearGridColorButton.Enabled = enableGridColorCheckBox.Checked;

            // 添加控件到网格线颜色布局
            gridColorLayout.Controls.Add(enableGridColorCheckBox, 0, 0);
            gridColorLayout.SetColumnSpan(enableGridColorCheckBox, 2);

            gridColorLayout.Controls.Add(gridColorRow, 0, 1);
            gridColorLayout.SetColumnSpan(gridColorRow, 2);

            // 完成网格线颜色设置组
            gridColorGroup.Controls.Add(gridColorLayout);
            gridLayout.Controls.Add(gridColorGroup, 0, 1);

            // 3. 网格线间距区域
            var gridSpacingGroup = new GroupBox
            {
                Text = "网格线间距",
                AutoSize = true,
                Dock = DockStyle.Fill,
                MinimumSize = new Size(0, 150)
            };

            var gridSpacingLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            gridSpacingLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            gridSpacingLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用网格线间距设置复选框
            enableGridSpacingCheckBox = new CheckBox
            {
                Text = "启用网格线间距设置",
                AutoSize = true,
                Checked = PageSetup.EnableGridSpacing,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 水平间距行
            var horizontalSpacingRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            horizontalSpacingRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            horizontalSpacingRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            horizontalSpacingRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var horizontalSpacingLabel = new Label
            {
                Text = "水平间距:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            horizontalSpacingNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                DecimalPlaces = 2,
                Increment = 0.1M,
                Minimum = 0.1M,
                Maximum = 10M,
                Value = (decimal)PageSetup.HorizontalGridSpacing,
                TextAlign = HorizontalAlignment.Center
            };

            var horizontalSpacingUnitLabel = new Label
            {
                Text = "厘米",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            horizontalSpacingRow.Controls.Add(horizontalSpacingLabel, 0, 0);
            horizontalSpacingRow.Controls.Add(horizontalSpacingNumeric, 1, 0);
            horizontalSpacingRow.Controls.Add(horizontalSpacingUnitLabel, 2, 0);

            // 垂直间距行
            var verticalSpacingRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            verticalSpacingRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            verticalSpacingRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            verticalSpacingRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var verticalSpacingLabel = new Label
            {
                Text = "垂直间距:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            verticalSpacingNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                DecimalPlaces = 2,
                Increment = 0.1M,
                Minimum = 0.1M,
                Maximum = 10M,
                Value = (decimal)PageSetup.VerticalGridSpacing,
                TextAlign = HorizontalAlignment.Center
            };

            var verticalSpacingUnitLabel = new Label
            {
                Text = "厘米",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            verticalSpacingRow.Controls.Add(verticalSpacingLabel, 0, 0);
            verticalSpacingRow.Controls.Add(verticalSpacingNumeric, 1, 0);
            verticalSpacingRow.Controls.Add(verticalSpacingUnitLabel, 2, 0);

            // 添加事件处理
            enableGridSpacingCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableGridSpacingCheckBox.Checked;
                horizontalSpacingNumeric.Enabled = enabled;
                verticalSpacingNumeric.Enabled = enabled;
            };

            // 应用初始启用状态
            horizontalSpacingNumeric.Enabled = enableGridSpacingCheckBox.Checked;
            verticalSpacingNumeric.Enabled = enableGridSpacingCheckBox.Checked;

            // 添加控件到网格线间距布局
            gridSpacingLayout.Controls.Add(enableGridSpacingCheckBox, 0, 0);
            gridSpacingLayout.SetColumnSpan(enableGridSpacingCheckBox, 2);

            gridSpacingLayout.Controls.Add(horizontalSpacingRow, 0, 1);
            gridSpacingLayout.SetColumnSpan(horizontalSpacingRow, 2);

            gridSpacingLayout.Controls.Add(verticalSpacingRow, 0, 2);
            gridSpacingLayout.SetColumnSpan(verticalSpacingRow, 2);

            // 完成网格线间距设置组
            gridSpacingGroup.Controls.Add(gridSpacingLayout);
            gridLayout.Controls.Add(gridSpacingGroup, 0, 2);

            // 4. 对齐网格设置组
            var snapToGridGroup = new GroupBox
            {
                Text = "对齐网格",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var snapToGridLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            snapToGridLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            snapToGridLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用对齐网格设置复选框
            enableSnapToGridCheckBox = new CheckBox
            {
                Text = "启用对齐网格设置",
                AutoSize = true,
                Checked = PageSetup.EnableSnapToGrid,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 对齐网格复选框
            snapToGridCheckBox = new CheckBox
            {
                Text = "对齐网格",
                AutoSize = true,
                Checked = PageSetup.SnapToGrid,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 分割数行
            var divisionRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            divisionRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            divisionRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            divisionRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var snapToGridDivisionLabel = new Label
            {
                Text = "分割数:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            snapToGridDivisionNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 10,
                Value = PageSetup.SnapToGridDivision,
                TextAlign = HorizontalAlignment.Center
            };

            var divisionUnitLabel = new Label
            {
                Text = "格",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            divisionRow.Controls.Add(snapToGridDivisionLabel, 0, 0);
            divisionRow.Controls.Add(snapToGridDivisionNumeric, 1, 0);
            divisionRow.Controls.Add(divisionUnitLabel, 2, 0);

            // 添加事件处理
            enableSnapToGridCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableSnapToGridCheckBox.Checked;
                snapToGridCheckBox.Enabled = enabled;
                snapToGridDivisionNumeric.Enabled = enabled;
            };

            // 应用初始启用状态
            snapToGridCheckBox.Enabled = enableSnapToGridCheckBox.Checked;
            snapToGridDivisionNumeric.Enabled = enableSnapToGridCheckBox.Checked;

            // 添加控件到对齐网格布局
            snapToGridLayout.Controls.Add(enableSnapToGridCheckBox, 0, 0);
            snapToGridLayout.SetColumnSpan(enableSnapToGridCheckBox, 2);

            snapToGridLayout.Controls.Add(snapToGridCheckBox, 0, 1);
            snapToGridLayout.SetColumnSpan(snapToGridCheckBox, 2);

            snapToGridLayout.Controls.Add(divisionRow, 0, 2);
            snapToGridLayout.SetColumnSpan(divisionRow, 2);

            // 完成对齐网格设置组
            snapToGridGroup.Controls.Add(snapToGridLayout);
            gridLayout.Controls.Add(snapToGridGroup, 0, 3);

            // 5. 文档网格布局设置组（新增）
            var documentGridGroup = new GroupBox
            {
                Text = "文档网格布局",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var documentGridLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            documentGridLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            documentGridLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 每行字符数设置
            enableCharactersPerLineCheckBox = new CheckBox
            {
                Text = "启用每行字符数设置",
                AutoSize = true,
                Checked = PageSetup.EnableCharactersPerLine,
                Margin = new Padding(0, 0, 0, 5)
            };

            var charactersPerLineRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            charactersPerLineRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            charactersPerLineRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            charactersPerLineRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var charactersPerLineLabel = new Label
            {
                Text = "每行字符数:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            charactersPerLineNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 0,
                Maximum = 200,
                Value = PageSetup.CharactersPerLine,
                TextAlign = HorizontalAlignment.Center
            };

            var charactersPerLineUnitLabel = new Label
            {
                Text = "字符 (0=不限制)",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            charactersPerLineRow.Controls.Add(charactersPerLineLabel, 0, 0);
            charactersPerLineRow.Controls.Add(charactersPerLineNumeric, 1, 0);
            charactersPerLineRow.Controls.Add(charactersPerLineUnitLabel, 2, 0);

            // 每页行数设置
            enableLinesPerPageCheckBox = new CheckBox
            {
                Text = "启用每页行数设置",
                AutoSize = true,
                Checked = PageSetup.EnableLinesPerPage,
                Margin = new Padding(0, 0, 0, 5)
            };

            var linesPerPageRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            linesPerPageRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            linesPerPageRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            linesPerPageRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var linesPerPageLabel = new Label
            {
                Text = "每页行数:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            linesPerPageNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 0,
                Maximum = 100,
                Value = PageSetup.LinesPerPage,
                TextAlign = HorizontalAlignment.Center
            };

            var linesPerPageUnitLabel = new Label
            {
                Text = "行 (0=不限制)",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            linesPerPageRow.Controls.Add(linesPerPageLabel, 0, 0);
            linesPerPageRow.Controls.Add(linesPerPageNumeric, 1, 0);
            linesPerPageRow.Controls.Add(linesPerPageUnitLabel, 2, 0);

            // 布局模式设置
            enableLayoutModeCheckBox = new CheckBox
            {
                Text = "启用布局模式设置",
                AutoSize = true,
                Checked = PageSetup.EnableLayoutMode,
                Margin = new Padding(0, 0, 0, 5)
            };

            var layoutModeRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true
            };

            layoutModeRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            layoutModeRow.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            var layoutModeLabel = new Label
            {
                Text = "布局模式:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            layoutModeComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            layoutModeComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            layoutModeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = layoutModeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加布局模式选项
            layoutModeComboBox.Items.Add("默认");
            layoutModeComboBox.Items.Add("网格");
            layoutModeComboBox.Items.Add("行网格");
            layoutModeComboBox.Items.Add("对齐字符");

            // 选择当前布局模式
            switch (PageSetup.LayoutMode)
            {
                case AW.SectionLayoutMode.Default:
                    layoutModeComboBox.SelectedIndex = 0;
                    break;
                case AW.SectionLayoutMode.Grid:
                    layoutModeComboBox.SelectedIndex = 1;
                    break;
                case AW.SectionLayoutMode.LineGrid:
                    layoutModeComboBox.SelectedIndex = 2;
                    break;
                case AW.SectionLayoutMode.SnapToChars:
                    layoutModeComboBox.SelectedIndex = 3;
                    break;
                default:
                    layoutModeComboBox.SelectedIndex = 0;
                    break;
            }

            layoutModeRow.Controls.Add(layoutModeLabel, 0, 0);
            layoutModeRow.Controls.Add(layoutModeComboBox, 1, 0);

            // 添加事件处理
            enableCharactersPerLineCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableCharactersPerLineCheckBox.Checked;
                charactersPerLineNumeric.Enabled = enabled;
            };

            enableLinesPerPageCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableLinesPerPageCheckBox.Checked;
                linesPerPageNumeric.Enabled = enabled;
            };

            enableLayoutModeCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableLayoutModeCheckBox.Checked;
                layoutModeComboBox.Enabled = enabled;
            };

            // 应用初始启用状态
            charactersPerLineNumeric.Enabled = enableCharactersPerLineCheckBox.Checked;
            linesPerPageNumeric.Enabled = enableLinesPerPageCheckBox.Checked;
            layoutModeComboBox.Enabled = enableLayoutModeCheckBox.Checked;

            // 添加控件到文档网格布局
            documentGridLayout.Controls.Add(enableCharactersPerLineCheckBox, 0, 0);
            documentGridLayout.SetColumnSpan(enableCharactersPerLineCheckBox, 2);

            documentGridLayout.Controls.Add(charactersPerLineRow, 0, 1);
            documentGridLayout.SetColumnSpan(charactersPerLineRow, 2);

            documentGridLayout.Controls.Add(enableLinesPerPageCheckBox, 0, 2);
            documentGridLayout.SetColumnSpan(enableLinesPerPageCheckBox, 2);

            documentGridLayout.Controls.Add(linesPerPageRow, 0, 3);
            documentGridLayout.SetColumnSpan(linesPerPageRow, 2);

            documentGridLayout.Controls.Add(enableLayoutModeCheckBox, 0, 4);
            documentGridLayout.SetColumnSpan(enableLayoutModeCheckBox, 2);

            documentGridLayout.Controls.Add(layoutModeRow, 0, 5);
            documentGridLayout.SetColumnSpan(layoutModeRow, 2);

            // 更新行数以容纳新控件
            documentGridLayout.RowCount = 6;

            // 完成文档网格布局设置组
            documentGridGroup.Controls.Add(documentGridLayout);
            gridLayout.Controls.Add(documentGridGroup, 0, 4);

            // 将网格布局添加到网格标签页
            gridTab.Controls.Add(gridLayout);
            mainTabControl.TabPages.Add(gridTab);

            // 创建"页面布局增强"标签页
            var enhancedLayoutTab = new TabPage("页面布局增强");
            enhancedLayoutTab.AutoScroll = true;
            enhancedLayoutTab.UseVisualStyleBackColor = true;
            var enhancedLayoutLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 4, // 四个子区域：分栏设置、页面大小缩放、页面旋转、页面镜像
                AutoSize = true,
                Padding = new Padding(3),
                Margin = new Padding(3)
            };

            // 设置行样式
            for (int i = 0; i < 4; i++)
            {
                enhancedLayoutLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 1. 分栏设置区域
            var columnsGroup = new GroupBox
            {
                Text = "分栏设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                MinimumSize = new Size(0, 150)
            };

            var columnsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(8),
                Margin = new Padding(3)
            };

            // 设置列宽
            columnsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            columnsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用分栏设置复选框
            enableColumnsCheckBox = new CheckBox
            {
                Text = "启用分栏设置",
                AutoSize = true,
                Checked = PageSetup.EnableColumns,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 栏数设置
            var columnCountLabel = new Label { Text = "栏数:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft };
            columnCountNumeric = new NumericUpDown
            {
                Dock = DockStyle.Left,
                Width = 80,
                Minimum = 1,
                Maximum = 10,
                Value = PageSetup.ColumnCount,
                TextAlign = HorizontalAlignment.Center
            };

            // 栏间距设置
            var columnSpacingLabel = new Label { Text = "栏间距(厘米):", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft };
            columnSpacingNumeric = new NumericUpDown
            {
                Dock = DockStyle.Left,
                Width = 80,
                DecimalPlaces = 2,
                Increment = 0.1M,
                Minimum = 0.1M,
                Maximum = 10M,
                Value = (decimal)PageSetup.ColumnSpacing,
                TextAlign = HorizontalAlignment.Center
            };

            // 等宽间距复选框
            evenlySpacedCheckBox = new CheckBox
            {
                Text = "等宽分栏",
                AutoSize = true,
                Checked = PageSetup.EvenlySpaced
            };

            // 添加事件处理
            enableColumnsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableColumnsCheckBox.Checked;
                columnCountNumeric.Enabled = enabled;
                columnSpacingNumeric.Enabled = enabled;
                evenlySpacedCheckBox.Enabled = enabled;
            };

            // 应用初始启用状态
            columnCountNumeric.Enabled = enableColumnsCheckBox.Checked;
            columnSpacingNumeric.Enabled = enableColumnsCheckBox.Checked;
            evenlySpacedCheckBox.Enabled = enableColumnsCheckBox.Checked;

            // 添加控件到分栏布局
            columnsLayout.Controls.Add(enableColumnsCheckBox, 0, 0);
            columnsLayout.SetColumnSpan(enableColumnsCheckBox, 2);

            columnsLayout.Controls.Add(columnCountLabel, 0, 1);
            columnsLayout.Controls.Add(columnCountNumeric, 1, 1);

            columnsLayout.Controls.Add(columnSpacingLabel, 0, 2);
            columnsLayout.Controls.Add(columnSpacingNumeric, 1, 2);

            columnsLayout.Controls.Add(evenlySpacedCheckBox, 0, 3);
            columnsLayout.SetColumnSpan(evenlySpacedCheckBox, 2);

            columnsGroup.Controls.Add(columnsLayout);
            enhancedLayoutLayout.Controls.Add(columnsGroup, 0, 0);

            // 2. 页面大小缩放区域
            var scalingGroup = new GroupBox
            {
                Text = "页面大小缩放",
                AutoSize = true,
                Dock = DockStyle.Fill,
                MinimumSize = new Size(0, 150)
            };

            var scalingLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(8),
                Margin = new Padding(3)
            };

            // 设置列宽
            scalingLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            scalingLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用页面大小缩放复选框
            enableScalingCheckBox = new CheckBox
            {
                Text = "启用页面大小缩放",
                AutoSize = true,
                Checked = PageSetup.EnableScaling,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 缩放百分比设置
            var scalePercentageLabel = new Label { Text = "缩放比例(%):", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft };
            scalePercentageNumeric = new NumericUpDown
            {
                Dock = DockStyle.Left,
                Width = 80,
                Minimum = 10,
                Maximum = 500,
                Value = PageSetup.ScalePercentage,
                TextAlign = HorizontalAlignment.Center
            };

            // 适应页数设置
            scaleToFitCheckBox = new CheckBox
            {
                Text = "缩放内容以适应页数",
                AutoSize = true,
                Checked = PageSetup.ScaleToFitPages
            };

            var scaleToPagesLabel = new Label { Text = "页数:", AutoSize = true, Anchor = AnchorStyles.Left | AnchorStyles.Right, TextAlign = ContentAlignment.MiddleLeft };
            scaleToPagesNumeric = new NumericUpDown
            {
                Dock = DockStyle.Left,
                Width = 80,
                Minimum = 1,
                Maximum = 100,
                Value = PageSetup.ScaleToPageCount,
                TextAlign = HorizontalAlignment.Center
            };

            // 添加事件处理
            enableScalingCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableScalingCheckBox.Checked;
                scalePercentageNumeric.Enabled = enabled;
                scaleToFitCheckBox.Enabled = enabled;
                scaleToPagesNumeric.Enabled = enabled && scaleToFitCheckBox.Checked;
            };

            scaleToFitCheckBox.CheckedChanged += (sender, e) =>
            {
                scaleToPagesNumeric.Enabled = scaleToFitCheckBox.Checked && enableScalingCheckBox.Checked;
                if (scaleToFitCheckBox.Checked)
                {
                    scalePercentageNumeric.Enabled = false;
                }
                else if (enableScalingCheckBox.Checked)
                {
                    scalePercentageNumeric.Enabled = true;
                }
            };

            // 应用初始启用状态
            scalePercentageNumeric.Enabled = enableScalingCheckBox.Checked && !scaleToFitCheckBox.Checked;
            scaleToFitCheckBox.Enabled = enableScalingCheckBox.Checked;
            scaleToPagesNumeric.Enabled = enableScalingCheckBox.Checked && scaleToFitCheckBox.Checked;

            // 添加控件到缩放布局
            scalingLayout.Controls.Add(enableScalingCheckBox, 0, 0);
            scalingLayout.SetColumnSpan(enableScalingCheckBox, 2);

            scalingLayout.Controls.Add(scalePercentageLabel, 0, 1);
            scalingLayout.Controls.Add(scalePercentageNumeric, 1, 1);

            scalingLayout.Controls.Add(scaleToFitCheckBox, 0, 2);
            scalingLayout.SetColumnSpan(scaleToFitCheckBox, 2);

            scalingLayout.Controls.Add(scaleToPagesLabel, 0, 3);
            scalingLayout.Controls.Add(scaleToPagesNumeric, 1, 3);

            scalingGroup.Controls.Add(scalingLayout);
            enhancedLayoutLayout.Controls.Add(scalingGroup, 0, 1);

            // 3. 页面旋转区域
            var textOrientationGroup = new GroupBox
            {
                Text = "页面旋转",
                AutoSize = true,
                Dock = DockStyle.Fill,
                MinimumSize = new Size(0, 120)
            };

            var textOrientationLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(8),
                Margin = new Padding(3)
            };

            // 设置列宽
            textOrientationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            textOrientationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用页面旋转复选框
            enableTextOrientationCheckBox = new CheckBox
            {
                Text = "启用页面旋转",
                AutoSize = true,
                Checked = PageSetup.EnableTextOrientation,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 文本方向选择
            var textOrientationLabel = new Label {
                Text = "文本方向:",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };
            textOrientationComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            textOrientationComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            textOrientationComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = textOrientationComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加文本方向选项
            textOrientationComboBox.Items.Add("水平");
            textOrientationComboBox.Items.Add("向上旋转90度");
            textOrientationComboBox.Items.Add("向下旋转90度");

            // 选择当前文本方向
            switch (PageSetup.TextOrientation)
            {
                case AW.TextOrientation.Horizontal:
                    textOrientationComboBox.SelectedIndex = 0;
                    break;
                case AW.TextOrientation.Upward:
                    textOrientationComboBox.SelectedIndex = 1;
                    break;
                case AW.TextOrientation.Downward:
                    textOrientationComboBox.SelectedIndex = 2;
                    break;
                default:
                    textOrientationComboBox.SelectedIndex = 0;
                    break;
            }

            // 添加事件处理
            enableTextOrientationCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableTextOrientationCheckBox.Checked;
                textOrientationComboBox.Enabled = enabled;
            };

            // 应用初始启用状态
            textOrientationComboBox.Enabled = enableTextOrientationCheckBox.Checked;

            // 添加控件到文本方向布局
            textOrientationLayout.Controls.Add(enableTextOrientationCheckBox, 0, 0);
            textOrientationLayout.SetColumnSpan(enableTextOrientationCheckBox, 2);

            textOrientationLayout.Controls.Add(textOrientationLabel, 0, 1);
            textOrientationLayout.Controls.Add(textOrientationComboBox, 1, 1);

            textOrientationGroup.Controls.Add(textOrientationLayout);
            enhancedLayoutLayout.Controls.Add(textOrientationGroup, 0, 2);

            // 4. 页面镜像区域
            var mirrorGroup = new GroupBox
            {
                Text = "页面镜像",
                AutoSize = true,
                Dock = DockStyle.Fill,
                MinimumSize = new Size(0, 150)
            };

            var mirrorLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true,
                Padding = new Padding(8),
                Margin = new Padding(3)
            };

            // 设置列宽和行高
            mirrorLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            mirrorLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            mirrorLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            mirrorLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            mirrorLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            // 启用页面镜像复选框
            enableMirrorMarginsCheckBox = new CheckBox
            {
                Text = "启用页面镜像设置",
                AutoSize = true,
                Checked = PageSetup.EnableMirrorMargins,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 镜像边距复选框
            mirrorMarginsCheckBox2 = new CheckBox
            {
                Text = "镜像边距",
                AutoSize = true,
                Checked = PageSetup.MirrorMargins
            };

            // 文本方向选择
            var textDirectionLabel = new Label {
                Text = "文本方向:",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            // 使用FlowLayoutPanel来水平排列单选按钮
            var textDirectionPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = false
            };

            leftToRightRadio = new RadioButton {
                Text = "从左到右",
                AutoSize = true,
                Checked = PageSetup.Bidi == 0,
                Margin = new Padding(0, 2, 10, 0) // 添加右边距以分隔两个按钮
            };

            rightToLeftRadio = new RadioButton {
                Text = "从右到左",
                AutoSize = true,
                Checked = PageSetup.Bidi != 0,
                Margin = new Padding(0, 2, 0, 0)
            };

            textDirectionPanel.Controls.Add(leftToRightRadio);
            textDirectionPanel.Controls.Add(rightToLeftRadio);

            // 添加缺失的enableBidiCheckBox控件初始化
            enableBidiCheckBox = new CheckBox
            {
                Text = "启用双向文本",
                AutoSize = true,
                Checked = PageSetup.EnableBidi,
                Margin = new Padding(0, 5, 0, 5)
            };

            // 添加事件处理
            enableMirrorMarginsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableMirrorMarginsCheckBox.Checked;
                mirrorMarginsCheckBox2.Enabled = enabled;
                leftToRightRadio.Enabled = enabled;
                rightToLeftRadio.Enabled = enabled;
                textDirectionLabel.Enabled = enabled;
                enableBidiCheckBox.Enabled = enabled; // 启用双向文本复选框
            };

            // 应用初始启用状态
            mirrorMarginsCheckBox2.Enabled = enableMirrorMarginsCheckBox.Checked;
            leftToRightRadio.Enabled = enableMirrorMarginsCheckBox.Checked;
            rightToLeftRadio.Enabled = enableMirrorMarginsCheckBox.Checked;
            textDirectionLabel.Enabled = enableMirrorMarginsCheckBox.Checked;
            enableBidiCheckBox.Enabled = enableMirrorMarginsCheckBox.Checked; // 启用双向文本复选框

            // 添加控件到镜像布局
            mirrorLayout.Controls.Add(enableMirrorMarginsCheckBox, 0, 0);
            mirrorLayout.SetColumnSpan(enableMirrorMarginsCheckBox, 2);

            mirrorLayout.Controls.Add(mirrorMarginsCheckBox2, 0, 1);
            mirrorLayout.SetColumnSpan(mirrorMarginsCheckBox2, 2);

            // 添加enableBidiCheckBox到布局
            mirrorLayout.Controls.Add(enableBidiCheckBox, 0, 2);
            mirrorLayout.SetColumnSpan(enableBidiCheckBox, 2);

            mirrorLayout.Controls.Add(textDirectionLabel, 0, 3);
            mirrorLayout.Controls.Add(textDirectionPanel, 1, 3);

            mirrorGroup.Controls.Add(mirrorLayout);
            enhancedLayoutLayout.Controls.Add(mirrorGroup, 0, 3);

            // 将布局增强布局添加到相应的标签页
            enhancedLayoutTab.Controls.Add(enhancedLayoutLayout);
            mainTabControl.TabPages.Add(enhancedLayoutTab);

            // 创建"文档视图"标签页
            var viewTab = new TabPage("文档视图");
            viewTab.AutoScroll = true;
            viewTab.UseVisualStyleBackColor = true;

            var viewTabLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 4, // 四个子区域：视图类型、缩放设置、格式标记、打印设置
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 设置行样式
            for (int i = 0; i < 4; i++)
            {
                viewTabLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 1. 视图类型设置组
            var viewTypeGroup = new GroupBox
            {
                Text = "视图类型",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var viewTypeLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            viewTypeLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            viewTypeLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用视图类型设置复选框
            enableViewTypeCheckBox = new CheckBox
            {
                Text = "启用视图类型设置",
                AutoSize = true,
                Checked = PageSetup.EnableViewType,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 视图类型选择
            var viewTypeLabel = new Label
            {
                Text = "视图类型:",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            viewTypeComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            viewTypeComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            viewTypeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = viewTypeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加视图类型选项
            viewTypeComboBox.Items.Add("默认");
            viewTypeComboBox.Items.Add("阅读模式");
            viewTypeComboBox.Items.Add("页面布局");
            viewTypeComboBox.Items.Add("大纲");
            viewTypeComboBox.Items.Add("普通");
            viewTypeComboBox.Items.Add("Web");

            // 选择当前视图类型
            int viewTypeIndex = 0;
            if (PageSetup.ViewType == AW.Settings.ViewType.Reading)
                viewTypeIndex = 1;
            else if (PageSetup.ViewType == AW.Settings.ViewType.PageLayout)
                viewTypeIndex = 2;
            else if (PageSetup.ViewType == AW.Settings.ViewType.Outline)
                viewTypeIndex = 3;
            else if (PageSetup.ViewType == AW.Settings.ViewType.Normal)
                viewTypeIndex = 4;
            else if (PageSetup.ViewType == AW.Settings.ViewType.Web)
                viewTypeIndex = 5;
            viewTypeComboBox.SelectedIndex = viewTypeIndex;

            // 添加事件处理
            enableViewTypeCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableViewTypeCheckBox.Checked;
                viewTypeComboBox.Enabled = enabled;
            };

            // 应用初始启用状态
            viewTypeComboBox.Enabled = enableViewTypeCheckBox.Checked;

            // 添加控件到视图类型布局
            viewTypeLayout.Controls.Add(enableViewTypeCheckBox, 0, 0);
            viewTypeLayout.SetColumnSpan(enableViewTypeCheckBox, 2);

            viewTypeLayout.Controls.Add(viewTypeLabel, 0, 1);
            viewTypeLayout.Controls.Add(viewTypeComboBox, 1, 1);

            // 完成视图类型设置组
            viewTypeGroup.Controls.Add(viewTypeLayout);
            viewTabLayout.Controls.Add(viewTypeGroup, 0, 0);

            // 2. 缩放设置组
            var zoomGroup = new GroupBox
            {
                Text = "缩放设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var zoomLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            zoomLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            zoomLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用缩放设置复选框
            enableZoomCheckBox = new CheckBox
            {
                Text = "启用缩放设置",
                AutoSize = true,
                Checked = PageSetup.EnableZoom,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 缩放类型选择
            var zoomTypeLabel = new Label
            {
                Text = "缩放类型:",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            zoomTypeComboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            zoomTypeComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            zoomTypeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = zoomTypeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加缩放类型选项
            zoomTypeComboBox.Items.Add("自定义");
            zoomTypeComboBox.Items.Add("适应整页");
            zoomTypeComboBox.Items.Add("适应页宽");
            zoomTypeComboBox.Items.Add("适应文本");

            // 选择当前缩放类型
            int zoomTypeIndex = 0; // 默认为自定义
            if (PageSetup.ZoomType == AW.Settings.ZoomType.FullPage)
                zoomTypeIndex = 1;
            else if (PageSetup.ZoomType == AW.Settings.ZoomType.PageWidth)
                zoomTypeIndex = 2;
            else if (PageSetup.ZoomType == AW.Settings.ZoomType.TextFit)
                zoomTypeIndex = 3;
            zoomTypeComboBox.SelectedIndex = zoomTypeIndex;

            // 缩放百分比
            var zoomPercentLabel = new Label
            {
                Text = "缩放百分比:",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            zoomPercentNumeric = new NumericUpDown
            {
                Dock = DockStyle.Left,
                Width = 80,
                Minimum = 10,
                Maximum = 500,
                Value = PageSetup.ZoomPercent,
                Increment = 10,
                TextAlign = HorizontalAlignment.Center
            };

            // 添加事件处理
            enableZoomCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableZoomCheckBox.Checked;
                zoomTypeComboBox.Enabled = enabled;
                zoomPercentNumeric.Enabled = enabled && zoomTypeComboBox.SelectedIndex == 0;
            };

            zoomTypeComboBox.SelectedIndexChanged += (sender, e) =>
            {
                zoomPercentNumeric.Enabled = enableZoomCheckBox.Checked && zoomTypeComboBox.SelectedIndex == 0;
            };

            // 应用初始启用状态
            zoomTypeComboBox.Enabled = enableZoomCheckBox.Checked;
            zoomPercentNumeric.Enabled = enableZoomCheckBox.Checked && zoomTypeComboBox.SelectedIndex == 0;

            // 添加控件到缩放布局
            zoomLayout.Controls.Add(enableZoomCheckBox, 0, 0);
            zoomLayout.SetColumnSpan(enableZoomCheckBox, 2);

            zoomLayout.Controls.Add(zoomTypeLabel, 0, 1);
            zoomLayout.Controls.Add(zoomTypeComboBox, 1, 1);

            zoomLayout.Controls.Add(zoomPercentLabel, 0, 2);
            zoomLayout.Controls.Add(zoomPercentNumeric, 1, 2);

            // 完成缩放设置组
            zoomGroup.Controls.Add(zoomLayout);
            viewTabLayout.Controls.Add(zoomGroup, 0, 1);

            // 3. 显示格式标记设置组
            var formatMarksGroup = new GroupBox
            {
                Text = "显示选项",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var formatMarksLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            formatMarksLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            formatMarksLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用格式标记设置复选框
            enableFormatMarksCheckBox = new CheckBox
            {
                Text = "启用显示选项设置",
                AutoSize = true,
                Checked = PageSetup.EnableFormatMarks,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 显示格式标记复选框
            showFormatMarksCheckBox = new CheckBox
            {
                Text = "显示格式标记（段落标记、空格等）",
                AutoSize = true,
                Checked = PageSetup.ShowFormatMarks,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 不显示页面边界复选框
            doNotDisplayPageBoundariesCheckBox = new CheckBox
            {
                Text = "不显示页面边界",
                AutoSize = true,
                Checked = PageSetup.DoNotDisplayPageBoundaries,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 显示背景形状复选框
            displayBackgroundShapeCheckBox = new CheckBox
            {
                Text = "显示背景形状",
                AutoSize = true,
                Checked = PageSetup.DisplayBackgroundShape,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 添加事件处理
            enableFormatMarksCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableFormatMarksCheckBox.Checked;
                showFormatMarksCheckBox.Enabled = enabled;
                doNotDisplayPageBoundariesCheckBox.Enabled = enabled;
                displayBackgroundShapeCheckBox.Enabled = enabled;
            };

            // 应用初始启用状态
            showFormatMarksCheckBox.Enabled = enableFormatMarksCheckBox.Checked;
            doNotDisplayPageBoundariesCheckBox.Enabled = enableFormatMarksCheckBox.Checked;
            displayBackgroundShapeCheckBox.Enabled = enableFormatMarksCheckBox.Checked;

            // 添加控件到格式标记布局
            formatMarksLayout.Controls.Add(enableFormatMarksCheckBox, 0, 0);
            formatMarksLayout.SetColumnSpan(enableFormatMarksCheckBox, 2);

            formatMarksLayout.Controls.Add(showFormatMarksCheckBox, 0, 1);
            formatMarksLayout.SetColumnSpan(showFormatMarksCheckBox, 2);

            formatMarksLayout.Controls.Add(doNotDisplayPageBoundariesCheckBox, 0, 2);
            formatMarksLayout.SetColumnSpan(doNotDisplayPageBoundariesCheckBox, 2);

            formatMarksLayout.Controls.Add(displayBackgroundShapeCheckBox, 0, 3);
            formatMarksLayout.SetColumnSpan(displayBackgroundShapeCheckBox, 2);

            // 完成格式标记设置组
            formatMarksGroup.Controls.Add(formatMarksLayout);
            viewTabLayout.Controls.Add(formatMarksGroup, 0, 2);

            // 4. 打印设置组（新增）
            var printSettingsGroup = new GroupBox
            {
                Text = "打印设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var printSettingsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            printSettingsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            printSettingsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用打印设置复选框
            enablePrintSettingsCheckBox = new CheckBox
            {
                Text = "启用打印设置",
                AutoSize = true,
                Checked = PageSetup.EnablePrintSettings,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 第一页纸盒设置
            var firstPageTrayRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            firstPageTrayRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            firstPageTrayRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            firstPageTrayRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var firstPageTrayLabel = new Label
            {
                Text = "第一页纸盒:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            firstPageTrayNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 0,
                Maximum = 10,
                Value = PageSetup.FirstPageTray,
                TextAlign = HorizontalAlignment.Center
            };

            var firstPageTrayUnitLabel = new Label
            {
                Text = "纸盒编号",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            firstPageTrayRow.Controls.Add(firstPageTrayLabel, 0, 0);
            firstPageTrayRow.Controls.Add(firstPageTrayNumeric, 1, 0);
            firstPageTrayRow.Controls.Add(firstPageTrayUnitLabel, 2, 0);

            // 其他页纸盒设置
            var otherPagesTrayRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            otherPagesTrayRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            otherPagesTrayRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            otherPagesTrayRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var otherPagesTrayLabel = new Label
            {
                Text = "其他页纸盒:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            otherPagesTrayNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 0,
                Maximum = 10,
                Value = PageSetup.OtherPagesTray,
                TextAlign = HorizontalAlignment.Center
            };

            var otherPagesTrayUnitLabel = new Label
            {
                Text = "纸盒编号",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            otherPagesTrayRow.Controls.Add(otherPagesTrayLabel, 0, 0);
            otherPagesTrayRow.Controls.Add(otherPagesTrayNumeric, 1, 0);
            otherPagesTrayRow.Controls.Add(otherPagesTrayUnitLabel, 2, 0);

            // 添加事件处理
            enablePrintSettingsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enablePrintSettingsCheckBox.Checked;
                firstPageTrayNumeric.Enabled = enabled;
                otherPagesTrayNumeric.Enabled = enabled;
            };

            // 应用初始启用状态
            firstPageTrayNumeric.Enabled = enablePrintSettingsCheckBox.Checked;
            otherPagesTrayNumeric.Enabled = enablePrintSettingsCheckBox.Checked;

            // 添加控件到打印设置布局
            printSettingsLayout.Controls.Add(enablePrintSettingsCheckBox, 0, 0);
            printSettingsLayout.SetColumnSpan(enablePrintSettingsCheckBox, 2);

            printSettingsLayout.Controls.Add(firstPageTrayRow, 0, 1);
            printSettingsLayout.SetColumnSpan(firstPageTrayRow, 2);

            printSettingsLayout.Controls.Add(otherPagesTrayRow, 0, 2);
            printSettingsLayout.SetColumnSpan(otherPagesTrayRow, 2);

            // 完成打印设置组
            printSettingsGroup.Controls.Add(printSettingsLayout);
            viewTabLayout.Controls.Add(printSettingsGroup, 0, 3);

            // 将视图标签页布局添加到视图标签页
            viewTab.Controls.Add(viewTabLayout);
            mainTabControl.TabPages.Add(viewTab);

            // 创建"脚注尾注选项"标签页（新增）
            var footnoteEndnoteTab = new TabPage("脚注尾注选项");
            footnoteEndnoteTab.AutoScroll = true;
            footnoteEndnoteTab.UseVisualStyleBackColor = true;
            CreateFootnoteEndnoteTab(mainTabControl, footnoteEndnoteTab);

            // 创建"高级设置"标签页（新增）
            var advancedTab = new TabPage("高级设置");
            advancedTab.AutoScroll = true;
            advancedTab.UseVisualStyleBackColor = true;
            CreateAdvancedTab(mainTabControl, advancedTab);

            // 按钮面板
            var buttonPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.RightToLeft,
                AutoSize = true,
                Dock = DockStyle.Bottom,
                Margin = new Padding(3)
            };

            var cancelButton = new Button
            {
                Text = "取消",
                AutoSize = true,
                Padding = new Padding(8, 3, 8, 3)
            };
            cancelButton.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            var okButton = new Button
            {
                Text = "确定",
                AutoSize = true,
                Padding = new Padding(8, 3, 8, 3),
                Margin = new Padding(8, 0, 0, 0)
            };
            okButton.Click += (s, e) =>
            {
                SaveSettings();
                this.DialogResult = DialogResult.OK;
            };

            buttonPanel.Controls.Add(cancelButton);
            buttonPanel.Controls.Add(okButton);

            // 创建主布局面板来容纳TabControl和按钮面板
            var rootLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(3),
                AutoSize = false,  // 不自动调整大小
                Size = new Size(400, 550)  // 设置合适的大小
            };

            // 设置行样式
            rootLayout.RowStyles.Clear();  // 清除之前的样式
            rootLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 95));  // TabControl占95%
            rootLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 5));  // 按钮面板占5%

            // 添加控件到根布局
            rootLayout.Controls.Add(mainTabControl, 0, 0);
            rootLayout.Controls.Add(buttonPanel, 0, 1);

            this.Controls.Add(rootLayout);

            // 确保自适应大小生效
            this.PerformLayout();

            // 增加表单整体高度
            this.MinimumSize = new System.Drawing.Size(450, 700);  // 增加最小高度
            this.Size = new System.Drawing.Size(500, 750);  // 增加初始高度

            // 确保标签页可以滚动
            basicSettingsTab.AutoScroll = true;
            marginsTab.AutoScroll = true;
            backgroundTab.AutoScroll = true;
            borderTab.AutoScroll = true;
            gridTab.AutoScroll = true;
            enhancedLayoutTab.AutoScroll = true;

            // 修改TabControl以支持内容滚动
            mainTabControl.Dock = DockStyle.Fill;
            mainTabControl.Padding = new Point(3, 3);
            mainTabControl.Multiline = false;
            mainTabControl.SizeMode = TabSizeMode.Normal;

            // 确保主TabControl占据足够的空间
            rootLayout.RowStyles.Clear();
            rootLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 90));  // TabControl占90%
            rootLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));  // 按钮面板自动调整大小
        }

        private void SaveSettings()
        {
            // 边距设置 - 无论是否启用，都保存实际设置的值
            if (topMarginNumeric != null) PageSetup.TopMargin = (double)topMarginNumeric.Value;
            if (bottomMarginNumeric != null) PageSetup.BottomMargin = (double)bottomMarginNumeric.Value;
            if (leftMarginNumeric != null) PageSetup.LeftMargin = (double)leftMarginNumeric.Value;
            if (rightMarginNumeric != null) PageSetup.RightMargin = (double)rightMarginNumeric.Value;

            // 纸张方向设置 - 无论是否启用，都保存实际选择的值
            if (portraitRadio != null) PageSetup.Orientation = portraitRadio.Checked ? AW.Orientation.Portrait : AW.Orientation.Landscape;

            // 纸张大小设置 - 无论是否启用，都保存实际选择的值
            if (paperSizeComboBox != null)
            {
                switch (paperSizeComboBox.SelectedIndex)
                {
                    case 0: // A3
                        PageSetup.PaperSize = AW.PaperSize.A3;
                        break;
                    case 1: // A4
                        PageSetup.PaperSize = AW.PaperSize.A4;
                        break;
                    case 2: // A5
                        PageSetup.PaperSize = AW.PaperSize.A5;
                        break;
                    case 3: // B4
                        PageSetup.PaperSize = AW.PaperSize.B4;
                        break;
                    case 4: // B5
                        PageSetup.PaperSize = AW.PaperSize.B5;
                        break;
                    case 5: // JIS B4
                        PageSetup.PaperSize = AW.PaperSize.JisB4;
                        break;
                    case 6: // JIS B5
                        PageSetup.PaperSize = AW.PaperSize.JisB5;
                        break;
                    case 7: // Letter
                        PageSetup.PaperSize = AW.PaperSize.Letter;
                        break;
                    case 8: // Legal
                        PageSetup.PaperSize = AW.PaperSize.Legal;
                        break;
                    case 9: // Executive
                        PageSetup.PaperSize = AW.PaperSize.Executive;
                        break;
                    case 10: // Tabloid
                        PageSetup.PaperSize = AW.PaperSize.Tabloid;
                        break;
                    case 11: // Ledger
                        PageSetup.PaperSize = AW.PaperSize.Ledger;
                        break;
                    case 12: // Folio
                        PageSetup.PaperSize = AW.PaperSize.Folio;
                        break;
                    case 13: // Quarto
                        PageSetup.PaperSize = AW.PaperSize.Quarto;
                        break;
                    case 14: // Statement
                        PageSetup.PaperSize = AW.PaperSize.Statement;
                        break;
                    case 15: // 10×14英寸
                        PageSetup.PaperSize = AW.PaperSize.Paper10x14;
                        break;
                    case 16: // 11×17英寸
                        PageSetup.PaperSize = AW.PaperSize.Paper11x17;
                        break;
                    case 17: // 信封DL
                        PageSetup.PaperSize = AW.PaperSize.EnvelopeDL;
                        break;
                    case 18: // 10号信封
                        PageSetup.PaperSize = AW.PaperSize.Number10Envelope;
                        break;
                    case 19: // 自定义
                        PageSetup.PaperSize = AW.PaperSize.Custom;
                        if (customWidthNumeric != null) PageSetup.PaperWidth = (double)customWidthNumeric.Value;
                        if (customHeightNumeric != null) PageSetup.PaperHeight = (double)customHeightNumeric.Value;
                        break;
                }
            }

            // 装订线设置 - 无论是否启用，都保存实际设置的值
            if (gutterNumeric != null) PageSetup.Gutter = (double)gutterNumeric.Value;
            if (mirrorMarginsCheckBox != null) PageSetup.MirrorMargins = mirrorMarginsCheckBox.Checked;
            if (rtlGutterCheckBox != null) PageSetup.RtlGutter = rtlGutterCheckBox.Checked;

            // 背景颜色设置 - 无论是否启用，都保存实际设置的值
            if (colorPreviewPanel != null) PageSetup.BackgroundColor = colorPreviewPanel.BackColor;

            // 背景图片设置 - 无论是否启用，都保存实际设置的值
            if (imagePathTextBox != null) PageSetup.BackgroundImage = imagePathTextBox.Text.Trim();
            if (imageWrapTypeComboBox != null) PageSetup.BackgroundImageDisplayMode = (PageSetupFixed.DisplayMode)imageWrapTypeComboBox.SelectedIndex;

            // 边框样式设置 - 无论是否启用，都保存实际设置的值
            if (borderStyleComboBox != null)
                PageSetup.BorderStyle = borderStyleComboBox.SelectedIndex >= 0 ?
                    (PageSetupFixed.BorderLineStyle)borderStyleComboBox.SelectedIndex : PageSetupFixed.BorderLineStyle.None;
            if (applyBorderTopCheckBox != null) PageSetup.ApplyBorderTop = applyBorderTopCheckBox.Checked;
            if (applyBorderLeftCheckBox != null) PageSetup.ApplyBorderLeft = applyBorderLeftCheckBox.Checked;
            if (applyBorderBottomCheckBox != null) PageSetup.ApplyBorderBottom = applyBorderBottomCheckBox.Checked;
            if (applyBorderRightCheckBox != null) PageSetup.ApplyBorderRight = applyBorderRightCheckBox.Checked;

            // 边框颜色设置 - 无论是否启用，都保存实际设置的值
            if (borderColorPreviewPanel != null) PageSetup.BorderColor = borderColorPreviewPanel.BackColor;

            // 边框宽度设置 - 无论是否启用，都保存实际设置的值
            if (borderWidthNumeric != null) PageSetup.BorderWidth = (double)borderWidthNumeric.Value;

            // 艺术边框设置 - 无论是否启用，都保存实际设置的值
            if (borderArtComboBox != null) PageSetup.BorderArtStyle = borderArtComboBox.SelectedIndex;
            if (distanceFromTopNumeric != null) PageSetup.BorderDistanceFromTop = (double)distanceFromTopNumeric.Value;
            if (distanceFromLeftNumeric != null) PageSetup.BorderDistanceFromLeft = (double)distanceFromLeftNumeric.Value;
            if (distanceFromBottomNumeric != null) PageSetup.BorderDistanceFromBottom = (double)distanceFromBottomNumeric.Value;
            if (distanceFromRightNumeric != null) PageSetup.BorderDistanceFromRight = (double)distanceFromRightNumeric.Value;

            // 网格线设置 - 无论是否启用，都保存实际设置的值
            if (enableGridLinesCheckBox != null) PageSetup.ShowGridLines = enableGridLinesCheckBox.Checked;

            // 网格颜色设置 - 无论是否启用，都保存实际设置的值
            if (gridColorPreviewPanel != null) PageSetup.GridLinesColor = gridColorPreviewPanel.BackColor;

            // 网格间距设置 - 无论是否启用，都保存实际设置的值
            if (horizontalSpacingNumeric != null) PageSetup.HorizontalGridSpacing = (double)horizontalSpacingNumeric.Value;
            if (verticalSpacingNumeric != null) PageSetup.VerticalGridSpacing = (double)verticalSpacingNumeric.Value;

            // 对齐到网格设置 - 无论是否启用，都保存实际设置的值
            if (snapToGridCheckBox != null) PageSetup.SnapToGrid = snapToGridCheckBox.Checked;
            if (snapToGridDivisionNumeric != null) PageSetup.SnapToGridDivision = (int)snapToGridDivisionNumeric.Value;

            // 分栏设置 - 无论是否启用，都保存实际设置的值
            if (columnCountNumeric != null) PageSetup.ColumnCount = (int)columnCountNumeric.Value;
            if (columnSpacingNumeric != null) PageSetup.ColumnSpacing = (double)columnSpacingNumeric.Value;
            if (evenlySpacedCheckBox != null) PageSetup.EvenlySpaced = evenlySpacedCheckBox.Checked;

            // 缩放设置 - 无论是否启用，都保存实际设置的值
            if (scalePercentageNumeric != null) PageSetup.ScalePercentage = (int)scalePercentageNumeric.Value;
            if (scaleToFitCheckBox != null) PageSetup.ScaleToFitPages = scaleToFitCheckBox.Checked;
            if (scaleToPagesNumeric != null) PageSetup.ScaleToPageCount = (int)scaleToPagesNumeric.Value;

            // 文本方向设置 - 无论是否启用，都保存实际设置的值
            if (textOrientationComboBox != null)
                PageSetup.TextOrientation = textOrientationComboBox.SelectedIndex >= 0 ?
                    (AW.TextOrientation)textOrientationComboBox.SelectedIndex : AW.TextOrientation.Horizontal;

            // 双向文本设置 - 无论是否启用，都保存实际设置的值
            if (rightToLeftRadio != null) PageSetup.Bidi = rightToLeftRadio.Checked ? 1 : 0;

            // 行号设置 - 无论是否启用，都保存实际设置的值
            if (enableLineNumberingCheckBox != null) PageSetup.LineNumberingIsActive = enableLineNumberingCheckBox.Checked;
            if (lineNumberStartNumeric != null) PageSetup.LineNumberingStartValue = (int)lineNumberStartNumeric.Value;
            if (lineNumberCountByNumeric != null) PageSetup.LineNumberingCountBy = (int)lineNumberCountByNumeric.Value;
            if (lineNumberDistanceNumeric != null) PageSetup.LineNumberingDistance = (double)lineNumberDistanceNumeric.Value;
            if (lineNumberRestartModeComboBox != null && lineNumberRestartModeComboBox.SelectedIndex >= 0)
            {
                PageSetup.LineNumberingRestartMode = (AW.LineNumberRestartMode)lineNumberRestartModeComboBox.SelectedIndex;
            }

            // 页眉页脚距离设置 - 无论是否启用，都保存实际设置的值
            if (headerDistanceNumeric != null) PageSetup.HeaderDistance = (double)headerDistanceNumeric.Value;
            if (footerDistanceNumeric != null) PageSetup.FooterDistance = (double)footerDistanceNumeric.Value;

            // 保存启用状态信息
            if (enableMarginsCheckBox != null) PageSetup.EnableMargins = enableMarginsCheckBox.Checked;
            if (enableOrientationCheckBox != null) PageSetup.EnableOrientation = enableOrientationCheckBox.Checked;
            if (enablePaperSizeCheckBox != null) PageSetup.EnablePaperSize = enablePaperSizeCheckBox.Checked;
            if (enableGutterCheckBox != null) PageSetup.EnableGutter = enableGutterCheckBox.Checked;
            if (enableBackgroundColorCheckBox != null) PageSetup.EnableBackgroundColor = enableBackgroundColorCheckBox.Checked;
            if (enableBackgroundImageCheckBox != null) PageSetup.EnableBackgroundImage = enableBackgroundImageCheckBox.Checked;
            if (enableBorderStyleCheckBox != null) PageSetup.EnableBorderStyle = enableBorderStyleCheckBox.Checked;
            if (enableBorderColorCheckBox != null) PageSetup.EnableBorderColor = enableBorderColorCheckBox.Checked;
            if (enableBorderWidthCheckBox != null) PageSetup.EnableBorderWidth = enableBorderWidthCheckBox.Checked;
            if (enableBorderArtCheckBox != null) PageSetup.EnableBorderArt = enableBorderArtCheckBox.Checked;
            if (enableGridColorCheckBox != null) PageSetup.EnableGridColor = enableGridColorCheckBox.Checked;
            if (enableGridSpacingCheckBox != null) PageSetup.EnableGridSpacing = enableGridSpacingCheckBox.Checked;
            if (enableSnapToGridCheckBox != null) PageSetup.EnableSnapToGrid = enableSnapToGridCheckBox.Checked;
            if (enableColumnsCheckBox != null) PageSetup.EnableColumns = enableColumnsCheckBox.Checked;
            if (enableScalingCheckBox != null) PageSetup.EnableScaling = enableScalingCheckBox.Checked;
            if (enableTextOrientationCheckBox != null) PageSetup.EnableTextOrientation = enableTextOrientationCheckBox.Checked;
            if (enableMirrorMarginsCheckBox != null) PageSetup.EnableMirrorMargins = enableMirrorMarginsCheckBox.Checked;
            if (enableBidiCheckBox != null) PageSetup.EnableBidi = enableBidiCheckBox.Checked;

            // 保存页面边距标签页的新增启用状态
            if (enableHeaderFooterDistanceCheckBox != null) PageSetup.EnableHeaderFooterDistance = enableHeaderFooterDistanceCheckBox.Checked;
            if (enableMarginPresetsCheckBox != null) PageSetup.EnableMarginPresets = enableMarginPresetsCheckBox.Checked;
            if (enableMarginValidationCheckBox != null) PageSetup.EnableMarginValidation = enableMarginValidationCheckBox.Checked;

            // 保存新增功能的启用状态（新增）
            if (enableCharactersPerLineCheckBox != null) PageSetup.EnableCharactersPerLine = enableCharactersPerLineCheckBox.Checked;
            if (enableLinesPerPageCheckBox != null) PageSetup.EnableLinesPerPage = enableLinesPerPageCheckBox.Checked;
            if (enableLayoutModeCheckBox != null) PageSetup.EnableLayoutMode = enableLayoutModeCheckBox.Checked;
            if (enableBorderAdvancedCheckBox != null) PageSetup.EnableBorderAdvanced = enableBorderAdvancedCheckBox.Checked;

            // 保存新增功能的实际值（新增）
            if (charactersPerLineNumeric != null) PageSetup.CharactersPerLine = (int)charactersPerLineNumeric.Value;
            if (linesPerPageNumeric != null) PageSetup.LinesPerPage = (int)linesPerPageNumeric.Value;
            if (layoutModeComboBox != null && layoutModeComboBox.SelectedIndex >= 0)
            {
                switch (layoutModeComboBox.SelectedIndex)
                {
                    case 0:
                        PageSetup.LayoutMode = AW.SectionLayoutMode.Default;
                        break;
                    case 1:
                        PageSetup.LayoutMode = AW.SectionLayoutMode.Grid;
                        break;
                    case 2:
                        PageSetup.LayoutMode = AW.SectionLayoutMode.LineGrid;
                        break;
                    case 3:
                        PageSetup.LayoutMode = AW.SectionLayoutMode.SnapToChars;
                        break;
                    default:
                        PageSetup.LayoutMode = AW.SectionLayoutMode.Default;
                        break;
                }
            }

            // 保存边框高级属性（新增）
            if (borderAlwaysInFrontCheckBox != null) PageSetup.BorderAlwaysInFront = borderAlwaysInFrontCheckBox.Checked;
            if (borderAppliesToComboBox != null && borderAppliesToComboBox.SelectedIndex >= 0)
            {
                switch (borderAppliesToComboBox.SelectedIndex)
                {
                    case 0:
                        PageSetup.BorderAppliesTo = AW.PageBorderAppliesTo.AllPages;
                        break;
                    case 1:
                        PageSetup.BorderAppliesTo = AW.PageBorderAppliesTo.FirstPage;
                        break;
                    case 2:
                        PageSetup.BorderAppliesTo = AW.PageBorderAppliesTo.OtherPages;
                        break;
                    default:
                        PageSetup.BorderAppliesTo = AW.PageBorderAppliesTo.AllPages;
                        break;
                }
            }
            if (borderDistanceFromComboBox != null && borderDistanceFromComboBox.SelectedIndex >= 0)
            {
                switch (borderDistanceFromComboBox.SelectedIndex)
                {
                    case 0:
                        PageSetup.BorderDistanceFrom = AW.PageBorderDistanceFrom.Text;
                        break;
                    case 1:
                        PageSetup.BorderDistanceFrom = AW.PageBorderDistanceFrom.PageEdge;
                        break;
                    default:
                        PageSetup.BorderDistanceFrom = AW.PageBorderDistanceFrom.Text;
                        break;
                }
            }
            if (borderSurroundsHeaderCheckBox != null) PageSetup.BorderSurroundsHeader = borderSurroundsHeaderCheckBox.Checked;
            if (borderSurroundsFooterCheckBox != null) PageSetup.BorderSurroundsFooter = borderSurroundsFooterCheckBox.Checked;

            // 保存打印设置的启用状态（新增）
            if (enablePrintSettingsCheckBox != null) PageSetup.EnablePrintSettings = enablePrintSettingsCheckBox.Checked;

            // 保存打印设置的实际值（新增）
            if (firstPageTrayNumeric != null) PageSetup.FirstPageTray = (int)firstPageTrayNumeric.Value;
            if (otherPagesTrayNumeric != null) PageSetup.OtherPagesTray = (int)otherPagesTrayNumeric.Value;

            // 保存脚注选项设置（新增）
            if (enableFootnoteOptionsCheckBox != null) PageSetup.EnableFootnoteOptions = enableFootnoteOptionsCheckBox.Checked;
            if (footnotePositionComboBox != null && footnotePositionComboBox.SelectedIndex >= 0)
            {
                PageSetup.FootnotePosition = footnotePositionComboBox.SelectedIndex == 0 ?
                    AW.Notes.FootnotePosition.BottomOfPage : AW.Notes.FootnotePosition.BeneathText;
            }
            if (footnoteNumberStyleComboBox != null && footnoteNumberStyleComboBox.SelectedIndex >= 0)
            {
                switch (footnoteNumberStyleComboBox.SelectedIndex)
                {
                    case 0:
                        PageSetup.FootnoteNumberStyle = AW.NumberStyle.Arabic;
                        break;
                    case 1:
                        PageSetup.FootnoteNumberStyle = AW.NumberStyle.LowercaseRoman;
                        break;
                    case 2:
                        PageSetup.FootnoteNumberStyle = AW.NumberStyle.UppercaseRoman;
                        break;
                    case 3:
                        PageSetup.FootnoteNumberStyle = AW.NumberStyle.LowercaseLetter;
                        break;
                    case 4:
                        PageSetup.FootnoteNumberStyle = AW.NumberStyle.UppercaseLetter;
                        break;
                    default:
                        PageSetup.FootnoteNumberStyle = AW.NumberStyle.Arabic;
                        break;
                }
            }
            if (footnoteStartNumberNumeric != null) PageSetup.FootnoteStartNumber = (int)footnoteStartNumberNumeric.Value;
            if (footnoteRestartRuleComboBox != null && footnoteRestartRuleComboBox.SelectedIndex >= 0)
            {
                switch (footnoteRestartRuleComboBox.SelectedIndex)
                {
                    case 0:
                        PageSetup.FootnoteRestartRule = AW.Notes.FootnoteNumberingRule.Continuous;
                        break;
                    case 1:
                        PageSetup.FootnoteRestartRule = AW.Notes.FootnoteNumberingRule.RestartSection;
                        break;
                    case 2:
                        PageSetup.FootnoteRestartRule = AW.Notes.FootnoteNumberingRule.RestartPage;
                        break;
                    default:
                        PageSetup.FootnoteRestartRule = AW.Notes.FootnoteNumberingRule.Continuous;
                        break;
                }
            }
            if (footnoteColumnsNumeric != null) PageSetup.FootnoteColumns = (int)footnoteColumnsNumeric.Value;

            // 保存尾注选项设置（新增）
            if (enableEndnoteOptionsCheckBox != null) PageSetup.EnableEndnoteOptions = enableEndnoteOptionsCheckBox.Checked;
            if (endnotePositionComboBox != null && endnotePositionComboBox.SelectedIndex >= 0)
            {
                PageSetup.EndnotePosition = endnotePositionComboBox.SelectedIndex == 0 ?
                    AW.Notes.EndnotePosition.EndOfDocument : AW.Notes.EndnotePosition.EndOfSection;
            }
            if (endnoteNumberStyleComboBox != null && endnoteNumberStyleComboBox.SelectedIndex >= 0)
            {
                switch (endnoteNumberStyleComboBox.SelectedIndex)
                {
                    case 0:
                        PageSetup.EndnoteNumberStyle = AW.NumberStyle.Arabic;
                        break;
                    case 1:
                        PageSetup.EndnoteNumberStyle = AW.NumberStyle.LowercaseRoman;
                        break;
                    case 2:
                        PageSetup.EndnoteNumberStyle = AW.NumberStyle.UppercaseRoman;
                        break;
                    case 3:
                        PageSetup.EndnoteNumberStyle = AW.NumberStyle.LowercaseLetter;
                        break;
                    case 4:
                        PageSetup.EndnoteNumberStyle = AW.NumberStyle.UppercaseLetter;
                        break;
                    default:
                        PageSetup.EndnoteNumberStyle = AW.NumberStyle.LowercaseRoman;
                        break;
                }
            }
            if (endnoteStartNumberNumeric != null) PageSetup.EndnoteStartNumber = (int)endnoteStartNumberNumeric.Value;
            if (endnoteRestartRuleComboBox != null && endnoteRestartRuleComboBox.SelectedIndex >= 0)
            {
                switch (endnoteRestartRuleComboBox.SelectedIndex)
                {
                    case 0:
                        PageSetup.EndnoteRestartRule = AW.Notes.FootnoteNumberingRule.Continuous;
                        break;
                    case 1:
                        PageSetup.EndnoteRestartRule = AW.Notes.FootnoteNumberingRule.RestartSection;
                        break;
                    case 2:
                        PageSetup.EndnoteRestartRule = AW.Notes.FootnoteNumberingRule.RestartPage;
                        break;
                    default:
                        PageSetup.EndnoteRestartRule = AW.Notes.FootnoteNumberingRule.Continuous;
                        break;
                }
            }

            // 保存分栏高级设置（新增）
            if (enableColumnAdvancedCheckBox != null) PageSetup.EnableColumnAdvanced = enableColumnAdvancedCheckBox.Checked;
            if (lineBetweenCheckBox != null) PageSetup.LineBetween = lineBetweenCheckBox.Checked;
            if (separatorLineWidthNumeric != null) PageSetup.SeparatorLineWidth = (double)separatorLineWidthNumeric.Value;
            if (separatorLineColorPreview != null) PageSetup.SeparatorLineColor = separatorLineColorPreview.BackColor;

            // 保存节设置（新增）
            if (enableSectionSettingsCheckBox != null) PageSetup.EnableSectionSettings = enableSectionSettingsCheckBox.Checked;
            if (sectionStartTypeComboBox != null && sectionStartTypeComboBox.SelectedIndex >= 0)
            {
                switch (sectionStartTypeComboBox.SelectedIndex)
                {
                    case 0:
                        PageSetup.SectionStartType = AW.SectionStart.NewPage;
                        break;
                    case 1:
                        PageSetup.SectionStartType = AW.SectionStart.Continuous;
                        break;
                    case 2:
                        PageSetup.SectionStartType = AW.SectionStart.EvenPage;
                        break;
                    case 3:
                        PageSetup.SectionStartType = AW.SectionStart.OddPage;
                        break;
                    case 4:
                        PageSetup.SectionStartType = AW.SectionStart.NewColumn;
                        break;
                    default:
                        PageSetup.SectionStartType = AW.SectionStart.NewPage;
                        break;
                }
            }
            if (differentFirstPageHeaderFooterCheckBox != null) PageSetup.DifferentFirstPageHeaderFooter = differentFirstPageHeaderFooterCheckBox.Checked;
            if (oddAndEvenPagesHeaderFooterCheckBox != null) PageSetup.OddAndEvenPagesHeaderFooter = oddAndEvenPagesHeaderFooterCheckBox.Checked;

            // 保存垂直对齐设置（新增）
            if (enableVerticalAlignmentCheckBox != null) PageSetup.EnableVerticalAlignment = enableVerticalAlignmentCheckBox.Checked;
            if (verticalAlignmentComboBox != null && verticalAlignmentComboBox.SelectedIndex >= 0)
            {
                switch (verticalAlignmentComboBox.SelectedIndex)
                {
                    case 0:
                        PageSetup.VerticalAlignment = AW.PageVerticalAlignment.Top;
                        break;
                    case 1:
                        PageSetup.VerticalAlignment = AW.PageVerticalAlignment.Center;
                        break;
                    case 2:
                        PageSetup.VerticalAlignment = AW.PageVerticalAlignment.Bottom;
                        break;
                    case 3:
                        PageSetup.VerticalAlignment = AW.PageVerticalAlignment.Justify;
                        break;
                    default:
                        PageSetup.VerticalAlignment = AW.PageVerticalAlignment.Top;
                        break;
                }
            }

            // 保存多页设置（新增）
            if (enableMultiplePagesCheckBox != null) PageSetup.EnableMultiplePages = enableMultiplePagesCheckBox.Checked;
            if (multiplePagesComboBox != null && multiplePagesComboBox.SelectedIndex >= 0)
            {
                switch (multiplePagesComboBox.SelectedIndex)
                {
                    case 0:
                        PageSetup.MultiplePages = AW.Settings.MultiplePagesType.Normal;
                        break;
                    case 1:
                        PageSetup.MultiplePages = AW.Settings.MultiplePagesType.MirrorMargins;
                        break;
                    case 2:
                        PageSetup.MultiplePages = AW.Settings.MultiplePagesType.TwoPagesPerSheet;
                        break;
                    case 3:
                        PageSetup.MultiplePages = AW.Settings.MultiplePagesType.BookFoldPrinting;
                        break;
                    default:
                        PageSetup.MultiplePages = AW.Settings.MultiplePagesType.Normal;
                        break;
                }
            }

            // 保存书籍折页每册页数设置（新增）
            if (sheetsPerBookletNumeric != null) PageSetup.SheetsPerBookletCustom = (int)sheetsPerBookletNumeric.Value;

            // 保存章节设置（新增）
            if (enableChapterSettingsCheckBox != null) PageSetup.EnableChapterSettings = enableChapterSettingsCheckBox.Checked;
            if (headingLevelNumeric != null) PageSetup.HeadingLevelForChapter = (int)headingLevelNumeric.Value;
            if (chapterSeparatorComboBox != null && chapterSeparatorComboBox.SelectedIndex >= 0)
            {
                switch (chapterSeparatorComboBox.SelectedIndex)
                {
                    case 0:
                        PageSetup.ChapterPageSeparator = AW.ChapterPageSeparator.Hyphen;
                        break;
                    case 1:
                        PageSetup.ChapterPageSeparator = AW.ChapterPageSeparator.Period;
                        break;
                    case 2:
                        PageSetup.ChapterPageSeparator = AW.ChapterPageSeparator.Colon;
                        break;
                    case 3:
                        PageSetup.ChapterPageSeparator = AW.ChapterPageSeparator.EmDash;
                        break;
                    case 4:
                        PageSetup.ChapterPageSeparator = AW.ChapterPageSeparator.EnDash;
                        break;
                    default:
                        PageSetup.ChapterPageSeparator = AW.ChapterPageSeparator.Hyphen;
                        break;
                }
            }

            // 保存双向文本设置（新增）
            if (bidiCheckBox != null) PageSetup.Bidi = bidiCheckBox.Checked ? 1 : 0;

            // 调用视图选项保存
            SaveViewOptions();
        }

        /// <summary>
        /// 保存文档视图设置
        /// </summary>
        private void SaveViewOptions()
        {
            // 视图类型设置 - 无论是否启用，都保存实际选择的值
            if (viewTypeComboBox != null && viewTypeComboBox.SelectedIndex >= 0)
            {
                switch (viewTypeComboBox.SelectedIndex)
                {
                    case 0: // 默认
                        PageSetup.ViewType = AW.Settings.ViewType.None;
                        break;
                    case 1: // 阅读模式
                        PageSetup.ViewType = AW.Settings.ViewType.Reading;
                        break;
                    case 2: // 页面布局
                        PageSetup.ViewType = AW.Settings.ViewType.PageLayout;
                        break;
                    case 3: // 大纲
                        PageSetup.ViewType = AW.Settings.ViewType.Outline;
                        break;
                    case 4: // 普通
                        PageSetup.ViewType = AW.Settings.ViewType.Normal;
                        break;
                    case 5: // Web
                        PageSetup.ViewType = AW.Settings.ViewType.Web;
                        break;
                }
            }

            // 缩放设置 - 无论是否启用，都保存实际选择的值
            if (zoomTypeComboBox != null && zoomTypeComboBox.SelectedIndex >= 0)
            {
                switch (zoomTypeComboBox.SelectedIndex)
                {
                    case 0: // 自定义
                        PageSetup.ZoomType = AW.Settings.ZoomType.Custom;
                        if (zoomPercentNumeric != null) PageSetup.ZoomPercent = (int)zoomPercentNumeric.Value;
                        break;
                    case 1: // 适应整页
                        PageSetup.ZoomType = AW.Settings.ZoomType.FullPage;
                        if (zoomPercentNumeric != null) PageSetup.ZoomPercent = (int)zoomPercentNumeric.Value;
                        break;
                    case 2: // 适应页宽
                        PageSetup.ZoomType = AW.Settings.ZoomType.PageWidth;
                        if (zoomPercentNumeric != null) PageSetup.ZoomPercent = (int)zoomPercentNumeric.Value;
                        break;
                    case 3: // 适应文本
                        PageSetup.ZoomType = AW.Settings.ZoomType.TextFit;
                        if (zoomPercentNumeric != null) PageSetup.ZoomPercent = (int)zoomPercentNumeric.Value;
                        break;
                }
            }

            // 格式标记设置 - 无论是否启用，都保存实际设置的值
            if (showFormatMarksCheckBox != null) PageSetup.ShowFormatMarks = showFormatMarksCheckBox.Checked;
            if (doNotDisplayPageBoundariesCheckBox != null) PageSetup.DoNotDisplayPageBoundaries = doNotDisplayPageBoundariesCheckBox.Checked;
            if (displayBackgroundShapeCheckBox != null) PageSetup.DisplayBackgroundShape = displayBackgroundShapeCheckBox.Checked;

            // 保存启用状态信息
            if (enableViewTypeCheckBox != null) PageSetup.EnableViewType = enableViewTypeCheckBox.Checked;
            if (enableZoomCheckBox != null) PageSetup.EnableZoom = enableZoomCheckBox.Checked;
            if (enableFormatMarksCheckBox != null) PageSetup.EnableFormatMarks = enableFormatMarksCheckBox.Checked;
        }

        /// <summary>
        /// 创建页面边距标签页
        /// </summary>
        private void CreateMarginsTab(TabControl mainTabControl, TabPage marginsTab)
        {
            var marginsTabLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 6, // 六个区域：基本边距、页眉页脚距离、装订线设置、镜像边距、边距预设、边距验证
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 设置行样式
            for (int i = 0; i < 6; i++)
            {
                marginsTabLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 1. 基本边距设置区域
            CreateBasicMarginsGroup(marginsTabLayout);

            // 2. 页眉页脚距离设置区域
            CreateHeaderFooterDistanceGroup(marginsTabLayout);

            // 3. 装订线设置区域
            CreateGutterGroup(marginsTabLayout);

            // 4. 镜像边距设置区域
            CreateMirrorMarginsGroup(marginsTabLayout);

            // 5. 边距预设区域
            CreateMarginPresetsGroup(marginsTabLayout);

            // 6. 边距验证区域
            CreateMarginValidationGroup(marginsTabLayout);

            // 将布局添加到标签页
            marginsTab.Controls.Add(marginsTabLayout);
            mainTabControl.TabPages.Add(marginsTab);
        }

        /// <summary>
        /// 创建基本边距设置区域
        /// </summary>
        private void CreateBasicMarginsGroup(TableLayoutPanel parentLayout)
        {
            var basicMarginsGroup = new GroupBox
            {
                Text = "基本边距设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var basicMarginsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 5,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            basicMarginsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            basicMarginsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用基本边距设置复选框
            enableMarginsCheckBox = new CheckBox
            {
                Text = "启用基本边距设置",
                AutoSize = true,
                Checked = PageSetup.EnableMargins,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 上边距
            var topMarginRow = CreateMarginRow("上边距:", PageSetup.TopMargin, out topMarginNumeric);
            // 下边距
            var bottomMarginRow = CreateMarginRow("下边距:", PageSetup.BottomMargin, out bottomMarginNumeric);
            // 左边距
            var leftMarginRow = CreateMarginRow("左边距:", PageSetup.LeftMargin, out leftMarginNumeric);
            // 右边距
            var rightMarginRow = CreateMarginRow("右边距:", PageSetup.RightMargin, out rightMarginNumeric);

            // 添加事件处理
            enableMarginsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableMarginsCheckBox.Checked;
                topMarginNumeric.Enabled = enabled;
                bottomMarginNumeric.Enabled = enabled;
                leftMarginNumeric.Enabled = enabled;
                rightMarginNumeric.Enabled = enabled;
            };

            // 应用初始启用状态
            topMarginNumeric.Enabled = enableMarginsCheckBox.Checked;
            bottomMarginNumeric.Enabled = enableMarginsCheckBox.Checked;
            leftMarginNumeric.Enabled = enableMarginsCheckBox.Checked;
            rightMarginNumeric.Enabled = enableMarginsCheckBox.Checked;

            // 添加控件到布局
            basicMarginsLayout.Controls.Add(enableMarginsCheckBox, 0, 0);
            basicMarginsLayout.SetColumnSpan(enableMarginsCheckBox, 2);

            basicMarginsLayout.Controls.Add(topMarginRow, 0, 1);
            basicMarginsLayout.SetColumnSpan(topMarginRow, 2);

            basicMarginsLayout.Controls.Add(bottomMarginRow, 0, 2);
            basicMarginsLayout.SetColumnSpan(bottomMarginRow, 2);

            basicMarginsLayout.Controls.Add(leftMarginRow, 0, 3);
            basicMarginsLayout.SetColumnSpan(leftMarginRow, 2);

            basicMarginsLayout.Controls.Add(rightMarginRow, 0, 4);
            basicMarginsLayout.SetColumnSpan(rightMarginRow, 2);

            basicMarginsGroup.Controls.Add(basicMarginsLayout);
            parentLayout.Controls.Add(basicMarginsGroup, 0, 0);
        }

        /// <summary>
        /// 创建边距输入行的辅助方法
        /// </summary>
        private TableLayoutPanel CreateMarginRow(string labelText, double value, out NumericUpDown numericUpDown)
        {
            var marginRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            marginRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            marginRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            marginRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var label = new Label
            {
                Text = labelText,
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            numericUpDown = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                DecimalPlaces = 2,
                Increment = 0.1M,
                Minimum = 0,
                Maximum = 100,
                Value = (decimal)value,
                TextAlign = HorizontalAlignment.Center
            };

            var unitLabel = new Label
            {
                Text = "厘米",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            marginRow.Controls.Add(label, 0, 0);
            marginRow.Controls.Add(numericUpDown, 1, 0);
            marginRow.Controls.Add(unitLabel, 2, 0);

            return marginRow;
        }

        /// <summary>
        /// 创建页眉页脚距离设置区域
        /// </summary>
        private void CreateHeaderFooterDistanceGroup(TableLayoutPanel parentLayout)
        {
            var headerFooterGroup = new GroupBox
            {
                Text = "页眉页脚距离设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var headerFooterLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            headerFooterLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            headerFooterLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用页眉页脚距离设置复选框
            enableHeaderFooterDistanceCheckBox = new CheckBox
            {
                Text = "启用页眉页脚距离设置",
                AutoSize = true,
                Checked = PageSetup.EnableHeaderFooterDistance,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 页眉距离
            var headerDistanceRow = CreateMarginRow("页眉距离:", PageSetup.HeaderDistance, out headerDistanceNumeric);

            // 页脚距离
            var footerDistanceRow = CreateMarginRow("页脚距离:", PageSetup.FooterDistance, out footerDistanceNumeric);

            // 添加事件处理
            enableHeaderFooterDistanceCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableHeaderFooterDistanceCheckBox.Checked;
                headerDistanceNumeric.Enabled = enabled;
                footerDistanceNumeric.Enabled = enabled;
            };

            // 应用初始启用状态
            headerDistanceNumeric.Enabled = enableHeaderFooterDistanceCheckBox.Checked;
            footerDistanceNumeric.Enabled = enableHeaderFooterDistanceCheckBox.Checked;

            // 添加控件到布局
            headerFooterLayout.Controls.Add(enableHeaderFooterDistanceCheckBox, 0, 0);
            headerFooterLayout.SetColumnSpan(enableHeaderFooterDistanceCheckBox, 2);

            headerFooterLayout.Controls.Add(headerDistanceRow, 0, 1);
            headerFooterLayout.SetColumnSpan(headerDistanceRow, 2);

            headerFooterLayout.Controls.Add(footerDistanceRow, 0, 2);
            headerFooterLayout.SetColumnSpan(footerDistanceRow, 2);

            headerFooterGroup.Controls.Add(headerFooterLayout);
            parentLayout.Controls.Add(headerFooterGroup, 0, 1);
        }

        /// <summary>
        /// 创建装订线设置区域
        /// </summary>
        private void CreateGutterGroup(TableLayoutPanel parentLayout)
        {
            var gutterGroup = new GroupBox
            {
                Text = "装订线设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var gutterLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            gutterLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            gutterLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用装订线设置复选框
            var enableGutterCheckBox = new CheckBox
            {
                Text = "启用装订线设置",
                AutoSize = true,
                Checked = PageSetup.EnableGutter,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 装订线宽度
            var gutterWidthRow = CreateMarginRow("装订线宽度:", PageSetup.Gutter, out var gutterNumeric);

            // 镜像边距复选框
            var mirrorMarginsCheckBox = new CheckBox
            {
                Text = "镜像边距",
                AutoSize = true,
                Checked = PageSetup.MirrorMargins,
                Margin = new Padding(0, 5, 0, 5)
            };

            // 装订线位置复选框
            var rtlGutterCheckBox = new CheckBox
            {
                Text = "装订线位于顶部",
                AutoSize = true,
                Checked = PageSetup.RtlGutter,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 添加事件处理
            enableGutterCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableGutterCheckBox.Checked;
                gutterNumeric.Enabled = enabled;
                mirrorMarginsCheckBox.Enabled = enabled;
                rtlGutterCheckBox.Enabled = enabled;
            };

            // 应用初始启用状态
            gutterNumeric.Enabled = enableGutterCheckBox.Checked;
            mirrorMarginsCheckBox.Enabled = enableGutterCheckBox.Checked;
            rtlGutterCheckBox.Enabled = enableGutterCheckBox.Checked;

            // 添加控件到布局
            gutterLayout.Controls.Add(enableGutterCheckBox, 0, 0);
            gutterLayout.SetColumnSpan(enableGutterCheckBox, 2);

            gutterLayout.Controls.Add(gutterWidthRow, 0, 1);
            gutterLayout.SetColumnSpan(gutterWidthRow, 2);

            gutterLayout.Controls.Add(mirrorMarginsCheckBox, 0, 2);
            gutterLayout.SetColumnSpan(mirrorMarginsCheckBox, 2);

            gutterLayout.Controls.Add(rtlGutterCheckBox, 0, 3);
            gutterLayout.SetColumnSpan(rtlGutterCheckBox, 2);

            gutterGroup.Controls.Add(gutterLayout);
            parentLayout.Controls.Add(gutterGroup, 0, 2);
        }

        /// <summary>
        /// 创建镜像边距设置区域
        /// </summary>
        private void CreateMirrorMarginsGroup(TableLayoutPanel parentLayout)
        {
            var mirrorGroup = new GroupBox
            {
                Text = "镜像边距高级设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var mirrorLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            mirrorLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            mirrorLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用镜像边距高级设置复选框
            var enableMirrorMarginsCheckBox = new CheckBox
            {
                Text = "启用镜像边距高级设置",
                AutoSize = true,
                Checked = PageSetup.EnableMirrorMargins,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 首页不同页眉页脚
            var differentFirstPageCheckBox = new CheckBox
            {
                Text = "首页使用不同页眉页脚",
                AutoSize = true,
                Checked = PageSetup.DifferentFirstPageHeaderFooter,
                Margin = new Padding(0, 5, 0, 5)
            };

            // 奇偶页不同页眉页脚
            var oddEvenPagesCheckBox = new CheckBox
            {
                Text = "奇偶页使用不同页眉页脚",
                AutoSize = true,
                Checked = PageSetup.OddAndEvenPagesHeaderFooter,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 说明标签
            var infoLabel = new Label
            {
                Text = "注意：基本镜像边距功能在装订线设置中启用",
                AutoSize = true,
                ForeColor = Color.Gray,
                Font = new Font(Font.FontFamily, Font.Size - 1),
                Margin = new Padding(0, 5, 0, 0)
            };

            // 添加事件处理
            enableMirrorMarginsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableMirrorMarginsCheckBox.Checked;
                differentFirstPageCheckBox.Enabled = enabled;
                oddEvenPagesCheckBox.Enabled = enabled;
            };

            // 应用初始启用状态
            differentFirstPageCheckBox.Enabled = enableMirrorMarginsCheckBox.Checked;
            oddEvenPagesCheckBox.Enabled = enableMirrorMarginsCheckBox.Checked;

            // 添加控件到布局
            mirrorLayout.Controls.Add(enableMirrorMarginsCheckBox, 0, 0);
            mirrorLayout.SetColumnSpan(enableMirrorMarginsCheckBox, 2);

            mirrorLayout.Controls.Add(differentFirstPageCheckBox, 0, 1);
            mirrorLayout.SetColumnSpan(differentFirstPageCheckBox, 2);

            mirrorLayout.Controls.Add(oddEvenPagesCheckBox, 0, 2);
            mirrorLayout.SetColumnSpan(oddEvenPagesCheckBox, 2);

            mirrorLayout.Controls.Add(infoLabel, 0, 3);
            mirrorLayout.SetColumnSpan(infoLabel, 2);

            mirrorGroup.Controls.Add(mirrorLayout);
            parentLayout.Controls.Add(mirrorGroup, 0, 3);
        }

        /// <summary>
        /// 创建边距预设区域
        /// </summary>
        private void CreateMarginPresetsGroup(TableLayoutPanel parentLayout)
        {
            var presetsGroup = new GroupBox
            {
                Text = "边距预设",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var presetsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 3,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            for (int i = 0; i < 4; i++)
            {
                presetsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));
            }

            // 启用边距预设复选框
            enableMarginPresetsCheckBox = new CheckBox
            {
                Text = "启用边距预设",
                AutoSize = true,
                Checked = PageSetup.EnableMarginPresets,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 预设按钮样式
            Action<Button> stylePresetButton = (btn) => {
                btn.Dock = DockStyle.Fill;
                btn.Margin = new Padding(2);
                btn.FlatStyle = FlatStyle.System;
                btn.Font = new Font(Font.FontFamily, 8F);
                btn.Height = 40;
            };

            // 常用边距预设按钮
            var normalButton = new Button { Text = "标准\n(2.54cm)" };
            stylePresetButton(normalButton);
            normalButton.Click += (s, e) => ApplyMarginPreset(2.54, 2.54, 2.54, 2.54);

            var narrowButton = new Button { Text = "窄边距\n(1.27cm)" };
            stylePresetButton(narrowButton);
            narrowButton.Click += (s, e) => ApplyMarginPreset(1.27, 1.27, 1.27, 1.27);

            var moderateButton = new Button { Text = "适中\n(1.91cm)" };
            stylePresetButton(moderateButton);
            moderateButton.Click += (s, e) => ApplyMarginPreset(1.91, 1.91, 2.54, 2.54);

            var wideButton = new Button { Text = "宽边距\n(2.54cm)" };
            stylePresetButton(wideButton);
            wideButton.Click += (s, e) => ApplyMarginPreset(2.54, 2.54, 3.18, 3.18);

            // 专业预设按钮
            var officeButton = new Button { Text = "办公\n(2.0cm)" };
            stylePresetButton(officeButton);
            officeButton.Click += (s, e) => ApplyMarginPreset(2.0, 2.0, 2.0, 2.0);

            var bookButton = new Button { Text = "书籍\n(内外不同)" };
            stylePresetButton(bookButton);
            bookButton.Click += (s, e) => ApplyMarginPreset(2.5, 2.0, 3.0, 2.0);

            var reportButton = new Button { Text = "报告\n(3.0cm)" };
            stylePresetButton(reportButton);
            reportButton.Click += (s, e) => ApplyMarginPreset(3.0, 2.5, 2.5, 2.5);

            var customButton = new Button { Text = "自定义\n(当前值)" };
            stylePresetButton(customButton);
            customButton.Click += (s, e) => { /* 显示当前值，不做更改 */ };

            // 添加事件处理
            enableMarginPresetsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableMarginPresetsCheckBox.Checked;
                normalButton.Enabled = enabled;
                narrowButton.Enabled = enabled;
                moderateButton.Enabled = enabled;
                wideButton.Enabled = enabled;
                officeButton.Enabled = enabled;
                bookButton.Enabled = enabled;
                reportButton.Enabled = enabled;
                customButton.Enabled = enabled;
            };

            // 应用初始启用状态
            bool presetsEnabled = enableMarginPresetsCheckBox.Checked;
            normalButton.Enabled = presetsEnabled;
            narrowButton.Enabled = presetsEnabled;
            moderateButton.Enabled = presetsEnabled;
            wideButton.Enabled = presetsEnabled;
            officeButton.Enabled = presetsEnabled;
            bookButton.Enabled = presetsEnabled;
            reportButton.Enabled = presetsEnabled;
            customButton.Enabled = presetsEnabled;

            // 添加控件到布局
            presetsLayout.Controls.Add(enableMarginPresetsCheckBox, 0, 0);
            presetsLayout.SetColumnSpan(enableMarginPresetsCheckBox, 4);

            // 第一行预设按钮
            presetsLayout.Controls.Add(normalButton, 0, 1);
            presetsLayout.Controls.Add(narrowButton, 1, 1);
            presetsLayout.Controls.Add(moderateButton, 2, 1);
            presetsLayout.Controls.Add(wideButton, 3, 1);

            // 第二行预设按钮
            presetsLayout.Controls.Add(officeButton, 0, 2);
            presetsLayout.Controls.Add(bookButton, 1, 2);
            presetsLayout.Controls.Add(reportButton, 2, 2);
            presetsLayout.Controls.Add(customButton, 3, 2);

            presetsGroup.Controls.Add(presetsLayout);
            parentLayout.Controls.Add(presetsGroup, 0, 4);
        }

        /// <summary>
        /// 创建边距验证区域
        /// </summary>
        private void CreateMarginValidationGroup(TableLayoutPanel parentLayout)
        {
            var validationGroup = new GroupBox
            {
                Text = "边距验证",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var validationLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 5,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            validationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            validationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用边距验证复选框
            enableMarginValidationCheckBox = new CheckBox
            {
                Text = "启用边距验证",
                AutoSize = true,
                Checked = PageSetup.EnableMarginValidation,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 验证选项
            var validateMinimumCheckBox = new CheckBox
            {
                Text = "检查最小边距 (0.5cm)",
                AutoSize = true,
                Checked = true,
                Margin = new Padding(0, 2, 0, 2)
            };

            var validateMaximumCheckBox = new CheckBox
            {
                Text = "检查最大边距 (10cm)",
                AutoSize = true,
                Checked = true,
                Margin = new Padding(0, 2, 0, 2)
            };

            var validatePrintableAreaCheckBox = new CheckBox
            {
                Text = "检查可打印区域",
                AutoSize = true,
                Checked = true,
                Margin = new Padding(0, 2, 0, 2)
            };

            // 验证结果标签
            var validationResultLabel = new Label
            {
                Text = "边距设置正常",
                AutoSize = true,
                ForeColor = Color.Green,
                Font = new Font(Font.FontFamily, Font.Size - 1),
                Margin = new Padding(0, 5, 0, 0)
            };

            // 添加事件处理
            enableMarginValidationCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableMarginValidationCheckBox.Checked;
                validateMinimumCheckBox.Enabled = enabled;
                validateMaximumCheckBox.Enabled = enabled;
                validatePrintableAreaCheckBox.Enabled = enabled;
                if (enabled)
                {
                    ValidateMargins(validationResultLabel);
                }
                else
                {
                    validationResultLabel.Text = "边距验证已禁用";
                    validationResultLabel.ForeColor = Color.Gray;
                }
            };

            // 边距值变化时重新验证
            if (topMarginNumeric != null)
            {
                topMarginNumeric.ValueChanged += (s, e) => {
                    if (enableMarginValidationCheckBox.Checked)
                        ValidateMargins(validationResultLabel);
                };
            }

            // 应用初始启用状态
            bool validationEnabled = enableMarginValidationCheckBox.Checked;
            validateMinimumCheckBox.Enabled = validationEnabled;
            validateMaximumCheckBox.Enabled = validationEnabled;
            validatePrintableAreaCheckBox.Enabled = validationEnabled;

            if (validationEnabled)
            {
                ValidateMargins(validationResultLabel);
            }

            // 添加控件到布局
            validationLayout.Controls.Add(enableMarginValidationCheckBox, 0, 0);
            validationLayout.SetColumnSpan(enableMarginValidationCheckBox, 2);

            validationLayout.Controls.Add(validateMinimumCheckBox, 0, 1);
            validationLayout.SetColumnSpan(validateMinimumCheckBox, 2);

            validationLayout.Controls.Add(validateMaximumCheckBox, 0, 2);
            validationLayout.SetColumnSpan(validateMaximumCheckBox, 2);

            validationLayout.Controls.Add(validatePrintableAreaCheckBox, 0, 3);
            validationLayout.SetColumnSpan(validatePrintableAreaCheckBox, 2);

            validationLayout.Controls.Add(validationResultLabel, 0, 4);
            validationLayout.SetColumnSpan(validationResultLabel, 2);

            validationGroup.Controls.Add(validationLayout);
            parentLayout.Controls.Add(validationGroup, 0, 5);
        }

        /// <summary>
        /// 应用边距预设
        /// </summary>
        private void ApplyMarginPreset(double top, double bottom, double left, double right)
        {
            if (topMarginNumeric != null) topMarginNumeric.Value = (decimal)top;
            if (bottomMarginNumeric != null) bottomMarginNumeric.Value = (decimal)bottom;
            if (leftMarginNumeric != null) leftMarginNumeric.Value = (decimal)left;
            if (rightMarginNumeric != null) rightMarginNumeric.Value = (decimal)right;
        }

        /// <summary>
        /// 验证边距设置
        /// </summary>
        private void ValidateMargins(Label resultLabel)
        {
            if (topMarginNumeric == null || bottomMarginNumeric == null ||
                leftMarginNumeric == null || rightMarginNumeric == null)
            {
                resultLabel.Text = "边距控件未初始化";
                resultLabel.ForeColor = Color.Red;
                return;
            }

            var top = (double)topMarginNumeric.Value;
            var bottom = (double)bottomMarginNumeric.Value;
            var left = (double)leftMarginNumeric.Value;
            var right = (double)rightMarginNumeric.Value;

            var issues = new List<string>();

            // 检查最小边距
            const double minMargin = 0.5;
            if (top < minMargin) issues.Add($"上边距过小 ({top:F2}cm < {minMargin}cm)");
            if (bottom < minMargin) issues.Add($"下边距过小 ({bottom:F2}cm < {minMargin}cm)");
            if (left < minMargin) issues.Add($"左边距过小 ({left:F2}cm < {minMargin}cm)");
            if (right < minMargin) issues.Add($"右边距过小 ({right:F2}cm < {minMargin}cm)");

            // 检查最大边距
            const double maxMargin = 10.0;
            if (top > maxMargin) issues.Add($"上边距过大 ({top:F2}cm > {maxMargin}cm)");
            if (bottom > maxMargin) issues.Add($"下边距过大 ({bottom:F2}cm > {maxMargin}cm)");
            if (left > maxMargin) issues.Add($"左边距过大 ({left:F2}cm > {maxMargin}cm)");
            if (right > maxMargin) issues.Add($"右边距过大 ({right:F2}cm > {maxMargin}cm)");

            // 检查可打印区域
            var paperWidth = PageSetup.PaperWidth;
            var paperHeight = PageSetup.PaperHeight;
            var printableWidth = paperWidth - left - right;
            var printableHeight = paperHeight - top - bottom;

            if (printableWidth < 2.0) issues.Add($"可打印宽度过小 ({printableWidth:F2}cm)");
            if (printableHeight < 2.0) issues.Add($"可打印高度过小 ({printableHeight:F2}cm)");

            // 显示验证结果
            if (issues.Count == 0)
            {
                resultLabel.Text = "边距设置正常";
                resultLabel.ForeColor = Color.Green;
            }
            else
            {
                resultLabel.Text = $"发现 {issues.Count} 个问题：{string.Join("; ", issues.Take(2))}";
                if (issues.Count > 2) resultLabel.Text += "...";
                resultLabel.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// 创建脚注尾注选项标签页
        /// </summary>
        private void CreateFootnoteEndnoteTab(TabControl mainTabControl, TabPage footnoteEndnoteTab)
        {
            var footnoteEndnoteLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 2, // 两个子区域：脚注选项、尾注选项
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 设置行样式
            for (int i = 0; i < 2; i++)
            {
                footnoteEndnoteLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 1. 脚注选项组
            var footnoteOptionsGroup = new GroupBox
            {
                Text = "脚注选项",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var footnoteOptionsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 6,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            footnoteOptionsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            footnoteOptionsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用脚注选项复选框
            enableFootnoteOptionsCheckBox = new CheckBox
            {
                Text = "启用脚注选项设置",
                AutoSize = true,
                Checked = PageSetup.EnableFootnoteOptions,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 脚注位置设置
            var footnotePositionRow = CreateComboBoxRow("脚注位置:", out footnotePositionComboBox);
            footnotePositionComboBox.Items.Add("页面底部");
            footnotePositionComboBox.Items.Add("文本下方");
            footnotePositionComboBox.SelectedIndex = PageSetup.FootnotePosition == AW.Notes.FootnotePosition.BottomOfPage ? 0 : 1;

            // 脚注编号样式设置
            var footnoteNumberStyleRow = CreateComboBoxRow("编号样式:", out footnoteNumberStyleComboBox);
            footnoteNumberStyleComboBox.Items.Add("阿拉伯数字");
            footnoteNumberStyleComboBox.Items.Add("小写罗马数字");
            footnoteNumberStyleComboBox.Items.Add("大写罗马数字");
            footnoteNumberStyleComboBox.Items.Add("小写字母");
            footnoteNumberStyleComboBox.Items.Add("大写字母");
            footnoteNumberStyleComboBox.SelectedIndex = 0; // 默认阿拉伯数字

            // 脚注起始编号设置
            var footnoteStartNumberRow = CreateNumericRow("起始编号:", PageSetup.FootnoteStartNumber, out footnoteStartNumberNumeric);
            footnoteStartNumberNumeric.Minimum = 1;
            footnoteStartNumberNumeric.Maximum = 999;

            // 脚注重启规则设置
            var footnoteRestartRuleRow = CreateComboBoxRow("重启规则:", out footnoteRestartRuleComboBox);
            footnoteRestartRuleComboBox.Items.Add("连续编号");
            footnoteRestartRuleComboBox.Items.Add("每节重启");
            footnoteRestartRuleComboBox.Items.Add("每页重启");
            footnoteRestartRuleComboBox.SelectedIndex = 0; // 默认连续编号

            // 脚注分栏数设置
            var footnoteColumnsRow = CreateNumericRow("分栏数:", PageSetup.FootnoteColumns, out footnoteColumnsNumeric);
            footnoteColumnsNumeric.Minimum = 0;
            footnoteColumnsNumeric.Maximum = 10;

            // 添加事件处理
            enableFootnoteOptionsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableFootnoteOptionsCheckBox.Checked;
                footnotePositionComboBox.Enabled = enabled;
                footnoteNumberStyleComboBox.Enabled = enabled;
                footnoteStartNumberNumeric.Enabled = enabled;
                footnoteRestartRuleComboBox.Enabled = enabled;
                footnoteColumnsNumeric.Enabled = enabled;
            };

            // 应用初始启用状态
            footnotePositionComboBox.Enabled = enableFootnoteOptionsCheckBox.Checked;
            footnoteNumberStyleComboBox.Enabled = enableFootnoteOptionsCheckBox.Checked;
            footnoteStartNumberNumeric.Enabled = enableFootnoteOptionsCheckBox.Checked;
            footnoteRestartRuleComboBox.Enabled = enableFootnoteOptionsCheckBox.Checked;
            footnoteColumnsNumeric.Enabled = enableFootnoteOptionsCheckBox.Checked;

            // 添加控件到脚注选项布局
            footnoteOptionsLayout.Controls.Add(enableFootnoteOptionsCheckBox, 0, 0);
            footnoteOptionsLayout.SetColumnSpan(enableFootnoteOptionsCheckBox, 2);

            footnoteOptionsLayout.Controls.Add(footnotePositionRow, 0, 1);
            footnoteOptionsLayout.SetColumnSpan(footnotePositionRow, 2);

            footnoteOptionsLayout.Controls.Add(footnoteNumberStyleRow, 0, 2);
            footnoteOptionsLayout.SetColumnSpan(footnoteNumberStyleRow, 2);

            footnoteOptionsLayout.Controls.Add(footnoteStartNumberRow, 0, 3);
            footnoteOptionsLayout.SetColumnSpan(footnoteStartNumberRow, 2);

            footnoteOptionsLayout.Controls.Add(footnoteRestartRuleRow, 0, 4);
            footnoteOptionsLayout.SetColumnSpan(footnoteRestartRuleRow, 2);

            footnoteOptionsLayout.Controls.Add(footnoteColumnsRow, 0, 5);
            footnoteOptionsLayout.SetColumnSpan(footnoteColumnsRow, 2);

            // 完成脚注选项组
            footnoteOptionsGroup.Controls.Add(footnoteOptionsLayout);
            footnoteEndnoteLayout.Controls.Add(footnoteOptionsGroup, 0, 0);

            // 2. 尾注选项组
            var endnoteOptionsGroup = new GroupBox
            {
                Text = "尾注选项",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var endnoteOptionsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 5,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            endnoteOptionsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            endnoteOptionsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用尾注选项复选框
            enableEndnoteOptionsCheckBox = new CheckBox
            {
                Text = "启用尾注选项设置",
                AutoSize = true,
                Checked = PageSetup.EnableEndnoteOptions,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 尾注位置设置
            var endnotePositionRow = CreateComboBoxRow("尾注位置:", out endnotePositionComboBox);
            endnotePositionComboBox.Items.Add("文档末尾");
            endnotePositionComboBox.Items.Add("节末尾");
            endnotePositionComboBox.SelectedIndex = PageSetup.EndnotePosition == AW.Notes.EndnotePosition.EndOfDocument ? 0 : 1;

            // 尾注编号样式设置
            var endnoteNumberStyleRow = CreateComboBoxRow("编号样式:", out endnoteNumberStyleComboBox);
            endnoteNumberStyleComboBox.Items.Add("阿拉伯数字");
            endnoteNumberStyleComboBox.Items.Add("小写罗马数字");
            endnoteNumberStyleComboBox.Items.Add("大写罗马数字");
            endnoteNumberStyleComboBox.Items.Add("小写字母");
            endnoteNumberStyleComboBox.Items.Add("大写字母");
            endnoteNumberStyleComboBox.SelectedIndex = 1; // 默认小写罗马数字

            // 尾注起始编号设置
            var endnoteStartNumberRow = CreateNumericRow("起始编号:", PageSetup.EndnoteStartNumber, out endnoteStartNumberNumeric);
            endnoteStartNumberNumeric.Minimum = 1;
            endnoteStartNumberNumeric.Maximum = 999;

            // 尾注重启规则设置
            var endnoteRestartRuleRow = CreateComboBoxRow("重启规则:", out endnoteRestartRuleComboBox);
            endnoteRestartRuleComboBox.Items.Add("连续编号");
            endnoteRestartRuleComboBox.Items.Add("每节重启");
            endnoteRestartRuleComboBox.Items.Add("每页重启");
            endnoteRestartRuleComboBox.SelectedIndex = 0; // 默认连续编号

            // 添加事件处理
            enableEndnoteOptionsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableEndnoteOptionsCheckBox.Checked;
                endnotePositionComboBox.Enabled = enabled;
                endnoteNumberStyleComboBox.Enabled = enabled;
                endnoteStartNumberNumeric.Enabled = enabled;
                endnoteRestartRuleComboBox.Enabled = enabled;
            };

            // 应用初始启用状态
            endnotePositionComboBox.Enabled = enableEndnoteOptionsCheckBox.Checked;
            endnoteNumberStyleComboBox.Enabled = enableEndnoteOptionsCheckBox.Checked;
            endnoteStartNumberNumeric.Enabled = enableEndnoteOptionsCheckBox.Checked;
            endnoteRestartRuleComboBox.Enabled = enableEndnoteOptionsCheckBox.Checked;

            // 添加控件到尾注选项布局
            endnoteOptionsLayout.Controls.Add(enableEndnoteOptionsCheckBox, 0, 0);
            endnoteOptionsLayout.SetColumnSpan(enableEndnoteOptionsCheckBox, 2);

            endnoteOptionsLayout.Controls.Add(endnotePositionRow, 0, 1);
            endnoteOptionsLayout.SetColumnSpan(endnotePositionRow, 2);

            endnoteOptionsLayout.Controls.Add(endnoteNumberStyleRow, 0, 2);
            endnoteOptionsLayout.SetColumnSpan(endnoteNumberStyleRow, 2);

            endnoteOptionsLayout.Controls.Add(endnoteStartNumberRow, 0, 3);
            endnoteOptionsLayout.SetColumnSpan(endnoteStartNumberRow, 2);

            endnoteOptionsLayout.Controls.Add(endnoteRestartRuleRow, 0, 4);
            endnoteOptionsLayout.SetColumnSpan(endnoteRestartRuleRow, 2);

            // 完成尾注选项组
            endnoteOptionsGroup.Controls.Add(endnoteOptionsLayout);
            footnoteEndnoteLayout.Controls.Add(endnoteOptionsGroup, 0, 1);

            // 将脚注尾注标签页布局添加到标签页
            footnoteEndnoteTab.Controls.Add(footnoteEndnoteLayout);
            mainTabControl.TabPages.Add(footnoteEndnoteTab);
        }

        /// <summary>
        /// 创建下拉框行的辅助方法
        /// </summary>
        private TableLayoutPanel CreateComboBoxRow(string labelText, out ComboBox comboBox)
        {
            var row = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            row.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            row.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            row.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var label = new Label
            {
                Text = labelText,
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            comboBox = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // 设置ComboBox文本居中显示
            comboBox.DrawMode = DrawMode.OwnerDrawFixed;
            var comboBoxRef = comboBox; // 创建局部变量以避免在lambda中使用out参数
            comboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = comboBoxRef.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            row.Controls.Add(label, 0, 0);
            row.Controls.Add(comboBox, 1, 0);

            return row;
        }

        /// <summary>
        /// 创建数值输入行的辅助方法
        /// </summary>
        private TableLayoutPanel CreateNumericRow(string labelText, double value, out NumericUpDown numericUpDown)
        {
            var row = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            row.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            row.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            row.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var label = new Label
            {
                Text = labelText,
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            numericUpDown = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Value = (decimal)value,
                DecimalPlaces = 0,
                TextAlign = HorizontalAlignment.Center
            };

            row.Controls.Add(label, 0, 0);
            row.Controls.Add(numericUpDown, 1, 0);

            return row;
        }

        /// <summary>
        /// 创建高级设置标签页
        /// </summary>
        private void CreateAdvancedTab(TabControl mainTabControl, TabPage advancedTab)
        {
            var advancedLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 6, // 六个子区域：分栏高级设置、节设置、垂直对齐、多页设置、双向文本设置、章节设置
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 设置行样式
            for (int i = 0; i < 6; i++)
            {
                advancedLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 1. 分栏高级设置组
            var columnAdvancedGroup = new GroupBox
            {
                Text = "分栏高级设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var columnAdvancedLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            columnAdvancedLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            columnAdvancedLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用分栏高级设置复选框
            enableColumnAdvancedCheckBox = new CheckBox
            {
                Text = "启用分栏高级设置",
                AutoSize = true,
                Checked = PageSetup.EnableColumnAdvanced,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 分栏间分隔线复选框
            lineBetweenCheckBox = new CheckBox
            {
                Text = "分栏间显示分隔线",
                AutoSize = true,
                Checked = PageSetup.LineBetween,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 分隔线宽度设置
            var separatorWidthRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            separatorWidthRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            separatorWidthRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            separatorWidthRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var separatorWidthLabel = new Label
            {
                Text = "分隔线宽度:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            separatorLineWidthNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Value = (decimal)PageSetup.SeparatorLineWidth,
                Minimum = 0.25M,
                Maximum = 10,
                DecimalPlaces = 2,
                Increment = 0.25M,
                TextAlign = HorizontalAlignment.Center
            };

            var separatorWidthUnitLabel = new Label
            {
                Text = "磅",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            separatorWidthRow.Controls.Add(separatorWidthLabel, 0, 0);
            separatorWidthRow.Controls.Add(separatorLineWidthNumeric, 1, 0);
            separatorWidthRow.Controls.Add(separatorWidthUnitLabel, 2, 0);

            // 分隔线颜色设置
            var separatorColorRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 1,
                AutoSize = true
            };

            separatorColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 标签
            separatorColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 颜色展示框
            separatorColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 选择按钮
            separatorColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // 清除按钮

            var separatorColorLabel = new Label
            {
                Text = "分隔线颜色:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            separatorLineColorPreview = new Panel
            {
                BorderStyle = BorderStyle.FixedSingle,
                Size = new Size(30, 20),
                BackColor = PageSetup.SeparatorLineColor,
                Margin = new Padding(0, 2, 10, 0)
            };

            separatorLineColorButton = new Button
            {
                Text = "选择颜色...",
                AutoSize = true,
                Margin = new Padding(0, 0, 10, 0)
            };

            var clearSeparatorColorButton = new Button
            {
                Text = "重置",
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 0)
            };

            separatorColorRow.Controls.Add(separatorColorLabel, 0, 0);
            separatorColorRow.Controls.Add(separatorLineColorPreview, 1, 0);
            separatorColorRow.Controls.Add(separatorLineColorButton, 2, 0);
            separatorColorRow.Controls.Add(clearSeparatorColorButton, 3, 0);

            // 添加事件处理
            enableColumnAdvancedCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableColumnAdvancedCheckBox.Checked;
                lineBetweenCheckBox.Enabled = enabled;
                separatorLineWidthNumeric.Enabled = enabled;
                separatorLineColorButton.Enabled = enabled;
                separatorLineColorPreview.Enabled = enabled;
                clearSeparatorColorButton.Enabled = enabled;
            };

            separatorLineColorButton.Click += (sender, e) =>
            {
                using (var colorDialog = new ColorDialog())
                {
                    colorDialog.Color = separatorLineColorPreview.BackColor;
                    if (colorDialog.ShowDialog() == DialogResult.OK)
                    {
                        separatorLineColorPreview.BackColor = colorDialog.Color;
                    }
                }
            };

            clearSeparatorColorButton.Click += (sender, e) =>
            {
                separatorLineColorPreview.BackColor = Color.Black;
            };

            // 应用初始启用状态
            lineBetweenCheckBox.Enabled = enableColumnAdvancedCheckBox.Checked;
            separatorLineWidthNumeric.Enabled = enableColumnAdvancedCheckBox.Checked;
            separatorLineColorButton.Enabled = enableColumnAdvancedCheckBox.Checked;
            separatorLineColorPreview.Enabled = enableColumnAdvancedCheckBox.Checked;
            clearSeparatorColorButton.Enabled = enableColumnAdvancedCheckBox.Checked;

            // 添加控件到分栏高级设置布局
            columnAdvancedLayout.Controls.Add(enableColumnAdvancedCheckBox, 0, 0);
            columnAdvancedLayout.SetColumnSpan(enableColumnAdvancedCheckBox, 2);

            columnAdvancedLayout.Controls.Add(lineBetweenCheckBox, 0, 1);
            columnAdvancedLayout.SetColumnSpan(lineBetweenCheckBox, 2);

            columnAdvancedLayout.Controls.Add(separatorWidthRow, 0, 2);
            columnAdvancedLayout.SetColumnSpan(separatorWidthRow, 2);

            columnAdvancedLayout.Controls.Add(separatorColorRow, 0, 3);
            columnAdvancedLayout.SetColumnSpan(separatorColorRow, 2);

            // 完成分栏高级设置组
            columnAdvancedGroup.Controls.Add(columnAdvancedLayout);
            advancedLayout.Controls.Add(columnAdvancedGroup, 0, 0);

            // 2. 节设置组
            var sectionGroup = new GroupBox
            {
                Text = "节设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var sectionLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            sectionLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            sectionLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用节设置复选框
            enableSectionSettingsCheckBox = new CheckBox
            {
                Text = "启用节设置",
                AutoSize = true,
                Checked = PageSetup.EnableSectionSettings,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 节起始类型设置
            var sectionStartRow = CreateComboBoxRow("节起始类型:", out sectionStartTypeComboBox);
            sectionStartTypeComboBox.Items.Add("新页");
            sectionStartTypeComboBox.Items.Add("连续");
            sectionStartTypeComboBox.Items.Add("偶数页");
            sectionStartTypeComboBox.Items.Add("奇数页");
            sectionStartTypeComboBox.Items.Add("新栏");

            // 添加工具提示
            var sectionStartToolTip = new ToolTip();
            sectionStartToolTip.SetToolTip(sectionStartTypeComboBox,
                "新页：在新页开始节\n" +
                "连续：在同一页继续节\n" +
                "偶数页：在下一个偶数页开始节\n" +
                "奇数页：在下一个奇数页开始节\n" +
                "新栏：在新栏开始节");

            // 选择当前节起始类型
            switch (PageSetup.SectionStartType)
            {
                case AW.SectionStart.NewPage:
                    sectionStartTypeComboBox.SelectedIndex = 0;
                    break;
                case AW.SectionStart.Continuous:
                    sectionStartTypeComboBox.SelectedIndex = 1;
                    break;
                case AW.SectionStart.EvenPage:
                    sectionStartTypeComboBox.SelectedIndex = 2;
                    break;
                case AW.SectionStart.OddPage:
                    sectionStartTypeComboBox.SelectedIndex = 3;
                    break;
                case AW.SectionStart.NewColumn:
                    sectionStartTypeComboBox.SelectedIndex = 4;
                    break;
                default:
                    sectionStartTypeComboBox.SelectedIndex = 0;
                    break;
            }

            // 首页不同页眉页脚复选框
            differentFirstPageHeaderFooterCheckBox = new CheckBox
            {
                Text = "首页不同页眉页脚",
                AutoSize = true,
                Checked = PageSetup.DifferentFirstPageHeaderFooter,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 奇偶页不同页眉页脚复选框
            oddAndEvenPagesHeaderFooterCheckBox = new CheckBox
            {
                Text = "奇偶页不同页眉页脚",
                AutoSize = true,
                Checked = PageSetup.OddAndEvenPagesHeaderFooter,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 添加事件处理
            enableSectionSettingsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableSectionSettingsCheckBox.Checked;
                sectionStartTypeComboBox.Enabled = enabled;
                differentFirstPageHeaderFooterCheckBox.Enabled = enabled;
                oddAndEvenPagesHeaderFooterCheckBox.Enabled = enabled;
            };

            // 应用初始启用状态
            sectionStartTypeComboBox.Enabled = enableSectionSettingsCheckBox.Checked;
            differentFirstPageHeaderFooterCheckBox.Enabled = enableSectionSettingsCheckBox.Checked;
            oddAndEvenPagesHeaderFooterCheckBox.Enabled = enableSectionSettingsCheckBox.Checked;

            // 添加控件到节设置布局
            sectionLayout.Controls.Add(enableSectionSettingsCheckBox, 0, 0);
            sectionLayout.SetColumnSpan(enableSectionSettingsCheckBox, 2);

            sectionLayout.Controls.Add(sectionStartRow, 0, 1);
            sectionLayout.SetColumnSpan(sectionStartRow, 2);

            sectionLayout.Controls.Add(differentFirstPageHeaderFooterCheckBox, 0, 2);
            sectionLayout.SetColumnSpan(differentFirstPageHeaderFooterCheckBox, 2);

            sectionLayout.Controls.Add(oddAndEvenPagesHeaderFooterCheckBox, 0, 3);
            sectionLayout.SetColumnSpan(oddAndEvenPagesHeaderFooterCheckBox, 2);

            // 完成节设置组
            sectionGroup.Controls.Add(sectionLayout);
            advancedLayout.Controls.Add(sectionGroup, 0, 1);

            // 3. 垂直对齐设置组
            var verticalAlignmentGroup = new GroupBox
            {
                Text = "垂直对齐设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var verticalAlignmentLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            verticalAlignmentLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            verticalAlignmentLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用垂直对齐设置复选框
            enableVerticalAlignmentCheckBox = new CheckBox
            {
                Text = "启用垂直对齐设置",
                AutoSize = true,
                Checked = PageSetup.EnableVerticalAlignment,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 垂直对齐方式设置
            var verticalAlignmentRow = CreateComboBoxRow("垂直对齐:", out verticalAlignmentComboBox);
            verticalAlignmentComboBox.Items.Add("顶部对齐");
            verticalAlignmentComboBox.Items.Add("居中对齐");
            verticalAlignmentComboBox.Items.Add("底部对齐");
            verticalAlignmentComboBox.Items.Add("两端对齐");

            // 添加工具提示
            var verticalAlignmentToolTip = new ToolTip();
            verticalAlignmentToolTip.SetToolTip(verticalAlignmentComboBox,
                "顶部对齐：文本从页面顶部开始\n" +
                "居中对齐：文本在页面中央垂直居中\n" +
                "底部对齐：文本从页面底部开始\n" +
                "两端对齐：文本在页面上下边距间均匀分布");

            // 选择当前垂直对齐方式
            switch (PageSetup.VerticalAlignment)
            {
                case AW.PageVerticalAlignment.Top:
                    verticalAlignmentComboBox.SelectedIndex = 0;
                    break;
                case AW.PageVerticalAlignment.Center:
                    verticalAlignmentComboBox.SelectedIndex = 1;
                    break;
                case AW.PageVerticalAlignment.Bottom:
                    verticalAlignmentComboBox.SelectedIndex = 2;
                    break;
                case AW.PageVerticalAlignment.Justify:
                    verticalAlignmentComboBox.SelectedIndex = 3;
                    break;
                default:
                    verticalAlignmentComboBox.SelectedIndex = 0;
                    break;
            }

            // 添加事件处理
            enableVerticalAlignmentCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableVerticalAlignmentCheckBox.Checked;
                verticalAlignmentComboBox.Enabled = enabled;
            };

            // 应用初始启用状态
            verticalAlignmentComboBox.Enabled = enableVerticalAlignmentCheckBox.Checked;

            // 添加控件到垂直对齐布局
            verticalAlignmentLayout.Controls.Add(enableVerticalAlignmentCheckBox, 0, 0);
            verticalAlignmentLayout.SetColumnSpan(enableVerticalAlignmentCheckBox, 2);

            verticalAlignmentLayout.Controls.Add(verticalAlignmentRow, 0, 1);
            verticalAlignmentLayout.SetColumnSpan(verticalAlignmentRow, 2);

            // 完成垂直对齐设置组
            verticalAlignmentGroup.Controls.Add(verticalAlignmentLayout);
            advancedLayout.Controls.Add(verticalAlignmentGroup, 0, 2);

            // 4. 多页设置组
            var multiplePagesGroup = new GroupBox
            {
                Text = "多页设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var multiplePagesLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3, // 增加一行用于书籍折页设置
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            multiplePagesLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            multiplePagesLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用多页设置复选框
            enableMultiplePagesCheckBox = new CheckBox
            {
                Text = "启用多页设置",
                AutoSize = true,
                Checked = PageSetup.EnableMultiplePages,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 多页类型设置
            var multiplePagesRow = CreateComboBoxRow("多页类型:", out multiplePagesComboBox);
            multiplePagesComboBox.Items.Add("普通");
            multiplePagesComboBox.Items.Add("镜像边距");
            multiplePagesComboBox.Items.Add("对折");
            multiplePagesComboBox.Items.Add("书籍折页");

            // 添加工具提示
            var multiplePagesToolTip = new ToolTip();
            multiplePagesToolTip.SetToolTip(multiplePagesComboBox,
                "普通：标准单页布局\n" +
                "镜像边距：奇偶页使用不同的边距设置\n" +
                "对折：两页并排显示在一张纸上\n" +
                "书籍折页：用于制作小册子的折页布局");

            // 选择当前多页类型
            switch (PageSetup.MultiplePages)
            {
                case AW.Settings.MultiplePagesType.Normal:
                    multiplePagesComboBox.SelectedIndex = 0;
                    break;
                case AW.Settings.MultiplePagesType.MirrorMargins:
                    multiplePagesComboBox.SelectedIndex = 1;
                    break;
                case AW.Settings.MultiplePagesType.TwoPagesPerSheet:
                    multiplePagesComboBox.SelectedIndex = 2;
                    break;
                case AW.Settings.MultiplePagesType.BookFoldPrinting:
                    multiplePagesComboBox.SelectedIndex = 3;
                    break;
                default:
                    multiplePagesComboBox.SelectedIndex = 0;
                    break;
            }

            // 书籍折页每册页数设置
            var sheetsPerBookletRow = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 5, 0, 0)
            };

            sheetsPerBookletRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            sheetsPerBookletRow.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            sheetsPerBookletRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            var sheetsPerBookletLabel = new Label
            {
                Text = "每册页数:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 4, 10, 0)
            };

            sheetsPerBookletNumeric = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Value = PageSetup.SheetsPerBookletCustom,
                Minimum = 4,
                Maximum = 40,
                Increment = 4, // 通常是4的倍数
                TextAlign = HorizontalAlignment.Center
            };

            var sheetsPerBookletUnitLabel = new Label
            {
                Text = "页",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(3, 4, 0, 0)
            };

            sheetsPerBookletRow.Controls.Add(sheetsPerBookletLabel, 0, 0);
            sheetsPerBookletRow.Controls.Add(sheetsPerBookletNumeric, 1, 0);
            sheetsPerBookletRow.Controls.Add(sheetsPerBookletUnitLabel, 2, 0);

            // 根据多页类型显示/隐藏书籍折页设置
            sheetsPerBookletRow.Visible = (multiplePagesComboBox.SelectedIndex == 3);

            // 添加多页类型选择改变事件
            multiplePagesComboBox.SelectedIndexChanged += (sender, e) =>
            {
                bool isBookFold = multiplePagesComboBox.SelectedIndex == 3;
                sheetsPerBookletRow.Visible = isBookFold;
                sheetsPerBookletNumeric.Enabled = isBookFold && enableMultiplePagesCheckBox.Checked;
            };

            // 添加事件处理
            enableMultiplePagesCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableMultiplePagesCheckBox.Checked;
                multiplePagesComboBox.Enabled = enabled;
                sheetsPerBookletNumeric.Enabled = enabled && sheetsPerBookletRow.Visible;
            };

            // 应用初始启用状态
            multiplePagesComboBox.Enabled = enableMultiplePagesCheckBox.Checked;
            sheetsPerBookletNumeric.Enabled = enableMultiplePagesCheckBox.Checked && sheetsPerBookletRow.Visible;

            // 添加控件到多页设置布局
            multiplePagesLayout.Controls.Add(enableMultiplePagesCheckBox, 0, 0);
            multiplePagesLayout.SetColumnSpan(enableMultiplePagesCheckBox, 2);

            multiplePagesLayout.Controls.Add(multiplePagesRow, 0, 1);
            multiplePagesLayout.SetColumnSpan(multiplePagesRow, 2);

            multiplePagesLayout.Controls.Add(sheetsPerBookletRow, 0, 2);
            multiplePagesLayout.SetColumnSpan(sheetsPerBookletRow, 2);

            // 完成多页设置组
            multiplePagesGroup.Controls.Add(multiplePagesLayout);
            advancedLayout.Controls.Add(multiplePagesGroup, 0, 3);

            // 5. 双向文本设置组（新增）
            var bidiGroup = new GroupBox
            {
                Text = "双向文本设置",
                AutoSize = true,
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 5)
            };

            var bidiLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(0),
                Margin = new Padding(0)
            };

            // 设置列宽
            bidiLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            bidiLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 启用双向文本设置复选框
            var enableBidiCheckBox = new CheckBox
            {
                Text = "启用双向文本设置",
                AutoSize = true,
                Checked = PageSetup.EnableBidi,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 双向文本复选框
            bidiCheckBox = new CheckBox
            {
                Text = "启用双向文本（从右到左）",
                AutoSize = true,
                Checked = PageSetup.Bidi != 0,
                Margin = new Padding(0, 0, 0, 5)
            };

            // 添加工具提示
            var bidiToolTip = new ToolTip();
            bidiToolTip.SetToolTip(bidiCheckBox, "启用双向文本支持，适用于阿拉伯语、希伯来语等从右到左的语言");

            // 添加事件处理
            enableBidiCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBidiCheckBox.Checked;
                bidiCheckBox.Enabled = enabled;
            };

            // 应用初始启用状态
            bidiCheckBox.Enabled = enableBidiCheckBox.Checked;

            // 添加控件到双向文本布局
            bidiLayout.Controls.Add(enableBidiCheckBox, 0, 0);
            bidiLayout.SetColumnSpan(enableBidiCheckBox, 2);

            bidiLayout.Controls.Add(bidiCheckBox, 0, 1);
            bidiLayout.SetColumnSpan(bidiCheckBox, 2);

            // 完成双向文本设置组
            bidiGroup.Controls.Add(bidiLayout);
            advancedLayout.Controls.Add(bidiGroup, 0, 4);

            // 将高级设置标签页布局添加到标签页
            advancedTab.Controls.Add(advancedLayout);
            mainTabControl.TabPages.Add(advancedTab);
        }
    }
}