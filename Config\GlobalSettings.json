// 全局应用设置
// 此文件包含应用程序的基本配置选项
{
  // 路径设置
  "SourceDirectory": "D:\\Documents\\InputFiles", // 源文件目录，存放需要处理的Word文档
  "OutputDirectory": "D:\\Documents\\OutputFiles", // 输出目录，处理后的文件将保存在这里
  "IncludeSubdirectories": true, // 是否包含子目录中的文件
  "KeepDirectoryStructure": true, // 是否保持目录结构
  "ConflictHandling": "覆盖", // 文件冲突处理方式：覆盖、跳过、重命名
  "ProcessOriginalFiles": false, // 是否直接处理原始文件
  "MoveFiles": false, // 是否移动文件（false为复制）

  // 性能设置
  "MaxThreads": 1, // 最大线程数，默认为1线程
  "MaxRetryCount": 3, // 处理失败后的最大重试次数
  "BatchSize": 50, // 每批处理文件数，用于优化内存使用

  // 功能开关
  "EnablePageSetup": true, // 是否启用页面设置
  "EnableDeleteContent": true, // 是否启用内容删除
  "EnableContentReplace": false, // 是否启用内容替换
  "EnableGlobalParagraphFormat": true, // 是否启用全局段落格式
  "EnableParagraphMatch": false, // 是否启用段落匹配规则
  "EnableHeaderFooter": true, // 是否启用页眉页脚设置
  "EnableDocumentProperties": true, // 是否启用文档属性设置
  "EnableFileNameReplace": false, // 是否启用文件名替换
  "EnableWordToPdf": false, // 是否启用Word转PDF功能

  // 文件处理相关设置
  "BackupOriginalFiles": true, // 是否备份原始文件
  "BackupDirectory": "backup", // 备份目录名称

  // 文档处理选项
  "RemoveEmptyParagraphs": true, // 是否删除空段落
  "NormalizeWhitespace": true, // 是否规范化空白字符
  "RemoveMultipleSpaces": true, // 是否删除多余空格
  "RemoveHiddenContent": false, // 是否删除隐藏内容
  "RemoveComments": false, // 是否删除注释
  "RemoveHeadersFooters": false, // 是否删除页眉页脚

  // 段落格式默认值
  "ApplyDefaultParagraphFormat": true, // 是否应用默认段落格式
  "DefaultLineSpacing": 1.5, // 默认行距
  "DefaultFirstLineIndent": 14.4, // 默认首行缩进（磅，约2字符）
  "DefaultLeftIndent": 0.0, // 默认左缩进（磅）
  "DefaultRightIndent": 0.0, // 默认右缩进（磅）
  "DefaultSpaceBefore": 0.0, // 默认段前间距（磅）
  "DefaultSpaceAfter": 0.0, // 默认段后间距（磅）

  // 字体格式默认值
  "ApplyDefaultFontFormat": true, // 是否应用默认字体格式
  "DefaultFontName": "宋体", // 默认字体名称
  "DefaultFontSize": 12.0, // 默认字体大小（磅）
  "PreserveFontStyles": true, // 是否保持原字体样式

  // 导出选项
  "OptimizeFileSize": true, // 是否优化文件大小
  "UpdateFields": true, // 是否更新域
  "UpdateTOC": true, // 是否更新目录
  "SaveFormat": 20, // 保存格式：16=Doc, 20=Docx

  // 内容替换选项
  "ReplaceManualLineBreaks": false // 是否替换手动换行符
}