/*
 * ========================================
 * 文件名: GlobalParagraphFormatForm.cs
 * 功能描述: 全局段落格式配置窗体
 * ========================================
 *
 * 主要功能:
 * 1. 全局段落格式的详细配置
 * 2. 字体格式的全面设置
 * 3. 图片格式和效果配置
 * 4. 水印设置和管理
 * 5. 表格格式和样式配置
 * 6. 分标签页的组织化界面
 *
 * 界面结构:
 * - 段落格式标签页：基本段落设置、边框底纹、分页控制、制表位
 * - 字体格式标签页：中文、西文、复杂脚本字体设置
 * - 图片格式标签页：图片尺寸、位置、效果、边框、裁剪等
 * - 水印格式标签页：文本水印、图片水印、位置和格式设置
 * - 表格格式标签页：表格布局、样式、单元格格式等
 *
 * 段落格式功能:
 * - 对齐方式、大纲级别、文本方向
 * - 段前段后间距、缩进设置
 * - 行距类型和数值
 * - 段落边框和底纹
 * - 分页和连接控制
 * - 制表位管理
 *
 * 字体格式功能:
 * - 中文字体（字体、样式、大小）
 * - 西文字体（字体、样式、大小）
 * - 复杂脚本字体（字体、样式、大小）
 * - 字体颜色和突出显示
 * - 字体效果和格式
 *
 * 图片格式功能:
 * - 图片尺寸（精确尺寸/缩放比例）
 * - 图片位置和对齐
 * - 文本环绕方式
 * - 图片效果（亮度、对比度、透明度）
 * - 图片裁剪和边框
 * - 图片旋转和翻转
 * - 图片超链接设置
 * - 图片锁定选项
 *
 * 水印功能:
 * - 文本水印（内容、字体、颜色、透明度）
 * - 图片水印（路径、布局方式）
 * - 水印位置（居中/自定义位置）
 * - 水印页面范围（全部/指定页面）
 * - 水印格式（边框、环绕、锁定）
 *
 * 表格格式功能:
 * - 表格宽度自适应（内容/窗口/固定宽度）
 * - 表格布局（对齐、缩进、环绕）
 * - 表格样式（边框、网格线、底纹）
 * - 单元格格式（对齐、边距、边框、底纹）
 *
 * 数据管理:
 * - 与ParagraphMatchRule模型深度集成
 * - 支持所有格式选项的启用/禁用控制
 * - 实时数据绑定和验证
 * - 颜色选择和管理
 *
 * 注意事项:
 * - 界面复杂，包含大量控件和设置选项
 * - 支持分标签页的组织化管理
 * - 实现了完整的数据绑定机制
 * - 包含丰富的格式化选项和效果设置
 */

using System;
using System.Windows.Forms;
using System.Drawing;
using System.Linq;
using System.Collections.Generic;
using AsposeWordFormatter.Models;
using AW = Aspose.Words;

namespace AsposeWordFormatter
{
    public partial class GlobalParagraphFormatForm : Form
    {
        private readonly ParagraphMatchRule formatRule;

        // 常规段落格式控件
        private ComboBox alignmentComboBox = null!;
        private ComboBox outlineLevelComboBox = null!;
        private ComboBox textDirectionComboBox = null!;
        private NumericUpDown beforeSpacingNumeric = null!;
        private NumericUpDown afterSpacingNumeric = null!;
        private ComboBox indentTypeComboBox = null!;
        private NumericUpDown indentValueNumeric = null!;
        private ComboBox indentUnitComboBox = null!; // 特殊缩进单位选择
        private NumericUpDown leftIndentNumeric = null!;
        private NumericUpDown rightIndentNumeric = null!;
        private ComboBox lineSpacingTypeComboBox = null!;
        private NumericUpDown lineSpacingValueNumeric = null!;
        private ComboBox lineSpacingUnitComboBox = null!; // 行距单位选择
        private Label lineSpacingUnitLabel = null!; // 行距单位显示标签
        private CheckBox adjustImageLineSpacingCheckBox = null!;

        // 基本段落格式各组件启用控件
        private CheckBox enableAlignmentCheckBox = null!;
        private CheckBox enableOutlineLevelCheckBox = null!;
        private CheckBox enableTextDirectionCheckBox = null!;
        private CheckBox enableIndentCheckBox = null!;
        private CheckBox enableSpacingCheckBox = null!;

        // 段落边框相关控件
        private CheckBox hasBordersCheckBox = null!;
        private ComboBox borderTypeComboBox = null!;
        private NumericUpDown borderWidthNumeric = null!;
        private Button borderColorButton = null!;
        private Panel borderColorPanel = null!;
        private Button clearBorderColorButton = null!;
        private CheckBox hasTopBorderCheckBox = null!;
        private CheckBox hasBottomBorderCheckBox = null!;
        private CheckBox hasLeftBorderCheckBox = null!;
        private CheckBox hasRightBorderCheckBox = null!;

        // 底纹相关控件
        private CheckBox hasShadingCheckBox = null!;
        private ComboBox shadingPatternComboBox = null!;
        private Button shadingColorButton = null!;
        private Panel shadingColorPanel = null!;
        private Button clearShadingColorButton = null!;

        // 分页和连接控制相关控件
        private CheckBox keepWithNextCheckBox = null!;
        private CheckBox keepLinesTogetherCheckBox = null!;
        private CheckBox pageBreakBeforeCheckBox = null!;
        private CheckBox widowOrphanControlCheckBox = null!;
        private CheckBox noSpaceBetweenParagraphsCheckBox = null!;

        // 制表位相关控件
        private ListView tabStopsListView = null!;
        private Button addTabStopButton = null!;
        private Button removeTabStopButton = null!;

        // 字体相关控件
        private ComboBox chineseFontComboBox = null!;
        private ComboBox chineseFontStyleComboBox = null!;
        private ComboBox chineseFontSizeComboBox = null!; // 中文字号下拉框
        private NumericUpDown chineseFontSizeNumeric = null!; // 自定义字号输入
        private ComboBox westernFontComboBox = null!;
        private ComboBox westernFontStyleComboBox = null!;
        private ComboBox westernFontSizeComboBox = null!; // 西文字号下拉框
        private NumericUpDown westernFontSizeNumeric = null!; // 自定义字号输入
        private ComboBox complexScriptFontComboBox = null!;
        private ComboBox complexScriptFontStyleComboBox = null!;
        private ComboBox complexScriptFontSizeComboBox = null!; // 复杂字体字号下拉框
        private NumericUpDown complexScriptFontSizeNumeric = null!; // 自定义字号输入
        private Button fontColorButton = null!;
        private Panel fontColorPanel = null!;
        private Button clearFontColorButton = null!;

        // 突出显示相关控件
        private Button highlightColorButton = null!;
        private Panel highlightColorPanel = null!;
        private Button clearHighlightColorButton = null!;

        // 字体格式启用控件
        private CheckBox enableChineseFontCheckBox = null!;
        private CheckBox enableWesternFontCheckBox = null!;
        private CheckBox enableComplexScriptFontCheckBox = null!;
        private CheckBox enableFontColorCheckBox = null!;
        private CheckBox enableHighlightColorCheckBox = null!;

        // 图片格式启用控件
        private CheckBox enableImageSizeCheckBox = null!;
        private CheckBox enableImageWrapCheckBox = null!;
        private CheckBox enableImageEffectCheckBox = null!;
        private CheckBox enableBrightnessCheckBox = null!;
        private CheckBox enableContrastCheckBox = null!;
        private CheckBox enableTransparencyCheckBox = null!;
        private CheckBox enableColorModeCheckBox = null!;

        // 图片格式相关控件
        private CheckBox enableImageFormatCheckBox = null!; // 图片格式主开关
        private CheckBox preserveAspectRatioCheckBox = null!;
        private RadioButton useExactSizeRadio = null!;
        private RadioButton useScaleRadio = null!;
        private NumericUpDown imageWidthNumeric = null!;
        private NumericUpDown imageHeightNumeric = null!;
        private NumericUpDown imageScaleXNumeric = null!;
        private NumericUpDown imageScaleYNumeric = null!;

        // 图片文本环绕控件
        private ComboBox imageWrapTypeComboBox = null!;
        private RadioButton wrapTextNormalRadio = null!;
        private RadioButton wrapTextBehindRadio = null!;
        private RadioButton wrapTextFrontRadio = null!;

        // 图片效果相关控件
        private TrackBar brightnessTrackBar = null!;
        private TrackBar contrastTrackBar = null!;
        private TrackBar transparencyTrackBar = null!;
        private Label brightnessValueLabel = null!;
        private Label contrastValueLabel = null!;
        private Label transparencyValueLabel = null!;
        private ComboBox colorModeComboBox = null!;

        // 图片裁剪相关控件
        private CheckBox enableCropCheckBox = null!;
        private NumericUpDown cropTopNumeric = null!;
        private NumericUpDown cropBottomNumeric = null!;
        private NumericUpDown cropLeftNumeric = null!;
        private NumericUpDown cropRightNumeric = null!;

        // 图片边框相关控件
        private CheckBox enableImageBorderCheckBox = null!;
        private ComboBox imageBorderStyleComboBox = null!;
        private NumericUpDown imageBorderWidthNumeric = null!;
        private Button imageBorderColorButton = null!;
        private Panel imageBorderColorPanel = null!;
        private Button clearImageBorderColorButton = null!;

        // 图片位置相关控件
        private CheckBox enableImagePositionCheckBox = null!;
        private ComboBox horizontalRelativeComboBox = null!;
        private ComboBox verticalRelativeComboBox = null!;
        private ComboBox horizontalAlignmentComboBox = null!;
        private ComboBox verticalAlignmentComboBox = null!;
        private NumericUpDown horizontalPositionNumeric = null!;
        private NumericUpDown verticalPositionNumeric = null!;
        private RadioButton useAlignmentRadio = null!;
        private RadioButton useExactPositionRadio = null!;

        // 图片旋转和翻转相关控件
        private CheckBox enableImageRotateFlipCheckBox = null!;
        private NumericUpDown rotationAngleNumeric = null!;
        private CheckBox flipHorizontalCheckBox = null!;
        private CheckBox flipVerticalCheckBox = null!;

        // 图片超链接相关控件
        private CheckBox enableImageHyperlinkCheckBox = null!;
        private ComboBox hyperlinkTypeComboBox = null!;
        private TextBox hyperlinkUrlTextBox = null!;
        private TextBox bookmarkNameTextBox = null!;
        private TextBox hyperlinkToolTipTextBox = null!;
        private CheckBox openInNewWindowCheckBox = null!;

        // 图片锁定相关控件
        private CheckBox enableImageLockingCheckBox = null!;
        private CheckBox lockPositionCheckBox = null!;
        private CheckBox lockAspectRatioCheckBox = null!;
        private CheckBox lockFormattingCheckBox = null!;

        // 水印相关控件
        private CheckBox enableWatermarkCheckBox = null!;
        private TextBox watermarkTextBox = null!;
        private ComboBox watermarkFontFamilyComboBox = null!;
        private NumericUpDown watermarkFontSizeNumeric = null!;
        private Button watermarkColorButton = null!;
        private Panel watermarkColorPanel = null!;
        private Button clearWatermarkColorButton = null!;
        private TrackBar watermarkOpacityTrackBar = null!;
        private Label watermarkOpacityValueLabel = null!;
        private NumericUpDown watermarkRotationAngleNumeric = null!;

        // 图片水印相关控件
        private CheckBox enableImageWatermarkCheckBox = null!;
        private TextBox watermarkImagePathTextBox = null!;
        private Button browseImageButton = null!;
        private ComboBox imageWatermarkLayoutComboBox = null!;

        // 水印位置相关控件
        private CheckBox enableWatermarkPositionCheckBox = null!;
        private RadioButton watermarkPositionCenterRadio = null!;
        private RadioButton watermarkPositionCustomRadio = null!;
        private NumericUpDown watermarkHorizontalPositionNumeric = null!;
        private NumericUpDown watermarkVerticalPositionNumeric = null!;
        private RadioButton watermarkAllPagesRadio = null!;
        private RadioButton watermarkSpecificPagesRadio = null!;
        private TextBox watermarkPageRangeTextBox = null!;
        private CheckBox watermarkBehindContentCheckBox = null!;

        // 水印格式相关控件
        private CheckBox enableWatermarkFormatCheckBox = null!;
        private CheckBox watermarkHasBorderCheckBox = null!;
        private ComboBox watermarkBorderStyleComboBox = null!;
        private NumericUpDown watermarkBorderWidthNumeric = null!;
        private Button watermarkBorderColorButton = null!;
        private Panel watermarkBorderColorPanel = null!;
        private Button clearWatermarkBorderColorButton = null!;
        private ComboBox watermarkWrapTypeComboBox = null!;
        private CheckBox lockWatermarkCheckBox = null!;
        private CheckBox showWatermarkInPrintCheckBox = null!;

        // 段落格式区域控制开关
        private CheckBox enableBasicFormatCheckBox = null!;
        private CheckBox enableBorderShadingCheckBox = null!;
        private CheckBox enablePaginationCheckBox = null!;
        private CheckBox enableTabStopsCheckBox = null!;
        private CheckBox enableFontFormatCheckBox = null!; // 新增：字体格式启用开关

        // 应用范围控制相关控件
        private CheckBox applyToMainDocumentCheckBox = null!;
        private CheckBox applyToHeaderCheckBox = null!;
        private CheckBox applyToFooterCheckBox = null!;
        private CheckBox applyToTextBoxCheckBox = null!;
        private CheckBox applyToFootnoteCheckBox = null!;
        private CheckBox applyToEndnoteCheckBox = null!;
        private CheckBox applyToCommentCheckBox = null!;

        // 表格格式标签页相关控件
        // private TabPage tableTabPage = null!;  // 已注释掉，在下方TabControl部分有定义
        // private Panel tableScrollPanel = null!;  // 已注释掉，在下方ScrollPanel部分有定义

        // 表格宽度自适应相关控件
        private CheckBox enableTableAutoFitCheckBox = null!;
        private RadioButton autoFitToContentsRadio = null!;
        private RadioButton autoFitToWindowRadio = null!;
        private RadioButton useFixedColumnWidthRadio = null!;
        private NumericUpDown preferredTableWidthNumeric = null!;

        // 表格布局相关控件
        private CheckBox enableTableLayoutCheckBox = null!;
        private ComboBox tableAlignmentComboBox = null!;
        private NumericUpDown tableLeftIndentNumeric = null!;
        private NumericUpDown tableRightIndentNumeric = null!;
        private ComboBox tableTextWrappingComboBox = null!;
        private CheckBox tableAllowAutoFitCheckBox = null!;
        private NumericUpDown tableDefaultCellSpacingNumeric = null!;

        // 表格样式相关控件
        private CheckBox enableTableStyleCheckBox = null!;
        private CheckBox tableHasBordersCheckBox = null!;
        private ComboBox tableBorderStyleComboBox = null!;
        private NumericUpDown tableBorderWidthNumeric = null!;
        private Button tableBorderColorButton = null!;
        private Panel tableBorderColorPanel = null!;
        private Button clearTableBorderColorButton = null!;
        private CheckBox tableHasGridlinesCheckBox = null!;
        private ComboBox tableGridlineStyleComboBox = null!;
        private NumericUpDown tableGridlineWidthNumeric = null!;
        private Button tableGridlineColorButton = null!;
        private Panel tableGridlineColorPanel = null!;
        private Button clearTableGridlineColorButton = null!;
        private CheckBox tableHasShadingCheckBox = null!;
        private Button tableShadingColorButton = null!;
        private Panel tableShadingColorPanel = null!;
        private Button clearTableShadingColorButton = null!;

        // 单元格操作相关控件
        private CheckBox enableCellFormatCheckBox = null!;
        private ComboBox cellverticalAlignmentComboBox = null!;
        private NumericUpDown cellMarginTopNumeric = null!;
        private NumericUpDown cellMarginBottomNumeric = null!;
        private NumericUpDown cellMarginLeftNumeric = null!;
        private NumericUpDown cellMarginRightNumeric = null!;
        private NumericUpDown cellPaddingTopNumeric = null!;
        private NumericUpDown cellPaddingBottomNumeric = null!;
        private NumericUpDown cellPaddingLeftNumeric = null!;
        private NumericUpDown cellPaddingRightNumeric = null!;
        private CheckBox cellHasShadingCheckBox = null!;
        private Button cellShadingColorButton = null!;
        private Panel cellShadingColorPanel = null!;
        private Button clearCellShadingColorButton = null!;
        private CheckBox cellHasBordersCheckBox = null!;
        private ComboBox cellBorderStyleComboBox = null!;
        private NumericUpDown cellBorderWidthNumeric = null!;
        private Button cellBorderColorButton = null!;
        private Panel cellBorderColorPanel = null!;
        private Button clearCellBorderColorButton = null!;

        // 主布局面板
        private TableLayoutPanel mainLayout = null!;
        private GroupBox basicPanel = null!;

        // 添加TabControl
        private TabControl tabControl = null!;
        private TabPage paragraphTabPage = null!;
        private TabPage fontTabPage = null!;
        private TabPage imageTabPage = null!;
        private TabPage watermarkTabPage = null!;
        private TabPage tableTabPage = null!;

        // 添加ScrollPanel成员变量
        private Panel paragraphScrollPanel = null!;
        private Panel fontScrollPanel = null!;
        private Panel imageScrollPanel = null!;
        private Panel watermarkScrollPanel = null!;
        private Panel tableScrollPanel = null!;

        public ParagraphMatchRule FormatRule => formatRule;

        public GlobalParagraphFormatForm(ParagraphMatchRule formatRule)
        {
            this.formatRule = formatRule ?? new ParagraphMatchRule
            {
                // 初始化required属性
                HyperlinkUrl = string.Empty,
                HyperlinkToolTip = string.Empty,
                BookmarkName = string.Empty,
                // 初始化默认颜色
                FontColor = Color.Black,
                BorderColor = Color.Black,
                ShadingColor = Color.LightGray,
                WatermarkColor = Color.FromArgb(128, Color.Gray),
                WatermarkBorderColor = Color.Black,
                TableBorderColor = Color.Black,
                TableGridlineColor = Color.Black,
                TableShadingColor = Color.LightGray,
                CellBorderColor = Color.Black,
                CellShadingColor = Color.LightGray,
                // 设置默认复选框状态
                EnableBorderShading = false,
                EnablePagination = false,
                EnableTabStops = false,
                // 设置图片相关复选框默认状态
                EnableImageBorder = false,
                EnableImagePosition = false,
                EnableImageRotateFlip = false,
                EnableImageHyperlink = false,
                EnableImageLocking = false,
                EnableImageCrop = false,
                // 设置水印相关复选框默认状态
                EnableWatermark = false,
                EnableImageWatermark = false,
                EnableWatermarkPosition = false,
                EnableWatermarkFormat = false,
                // 设置应用范围默认状态
                ApplyToMainDocument = true,
                ApplyToHeader = false,
                ApplyToFooter = false,
                ApplyToTextBox = true,
                ApplyToFootnote = false,
                ApplyToEndnote = false,
                ApplyToComment = false
            };

            // 设置默认值
            this.formatRule.SpecialIndent = AsposeWordFormatter.Models.SpecialIndent.FirstLine; // 首行缩进
            this.formatRule.SpecialIndentValue = UnitConverter.ConvertToPoints(2.0, IndentUnit.Characters); // 2字符
            this.formatRule.LineSpacingRule = AW.LineSpacingRule.Exactly; // 固定值
            this.formatRule.LineSpacing = 28; // 行距28磅

            InitializeComponent();
            LoadSettings();

            // 添加Load事件处理
            this.Load += GlobalParagraphFormatForm_Load;
        }

        // 添加Load事件处理方法
        private void GlobalParagraphFormatForm_Load(object? sender, EventArgs e)
        {
            // 等待全部控件初始化完成后，统一调整所有区域的宽度
            AdjustPanelWidths();
        }

        // 添加统一调整宽度的方法
        private void AdjustPanelWidths()
        {
            // 获取所有TabPage中ScrollPanel的宽度（减去边距）
            int width = paragraphScrollPanel.ClientSize.Width - 20;

            // 遍历段落格式标签页中的所有GroupBox，统一其宽度
            foreach (Control control in paragraphScrollPanel.Controls)
            {
                if (control is GroupBox)
                {
                    control.Width = width;
                }
            }

            // 字体格式标签页
            if (fontScrollPanel != null)
            {
                foreach (Control control in fontScrollPanel.Controls)
                {
                    if (control is GroupBox)
                    {
                        control.Width = width;
                    }
                }
            }

            // 图片格式标签页
            if (imageScrollPanel != null)
            {
                foreach (Control control in imageScrollPanel.Controls)
                {
                    if (control is GroupBox)
                    {
                        control.Width = width;
                    }
                }
            }

            // 水印格式标签页
            if (watermarkScrollPanel != null)
            {
                foreach (Control control in watermarkScrollPanel.Controls)
                {
                    if (control is GroupBox)
                    {
                        control.Width = width;
                    }
                }
            }

            // 表格格式标签页
            if (tableScrollPanel != null)
            {
                foreach (Control control in tableScrollPanel.Controls)
                {
                    if (control is GroupBox)
                    {
                        control.Width = width;
                    }
                }
            }
        }

        private void InitializeComponent()
        {
            this.Text = "全局段落格式";
            this.Size = new Size(800, 700);  // Taller to accommodate content
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(5)
            };

            mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3, // 应用范围 + TabControl + 按钮
                Padding = new Padding(10),
                AutoSize = true
            };

            // 设置行高比例
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 应用范围区域
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F)); // TabControl区域
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 按钮区域

            // === 全局应用范围控制区域 ===
            var globalApplyScopePanel = new GroupBox
            {
                Text = "全局应用范围",
                Dock = DockStyle.Fill,
                Height = 60,
                Padding = new Padding(3),
                Margin = new Padding(0, 0, 0, 10)
            };

            var globalApplyScopeLayout = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = true,
                Padding = new Padding(3)
            };

            // 创建全局应用范围复选框
            applyToMainDocumentCheckBox = new CheckBox
            {
                Text = "正文",
                AutoSize = true,
                Checked = formatRule.ApplyToMainDocument,
                Margin = new Padding(10, 3, 15, 3)
            };
            applyToMainDocumentCheckBox.CheckedChanged += (sender, e) =>
            {
                formatRule.ApplyToMainDocument = applyToMainDocumentCheckBox.Checked;
            };

            applyToHeaderCheckBox = new CheckBox
            {
                Text = "页眉",
                AutoSize = true,
                Checked = formatRule.ApplyToHeader,
                Margin = new Padding(5, 3, 15, 3)
            };
            applyToHeaderCheckBox.CheckedChanged += (sender, e) =>
            {
                formatRule.ApplyToHeader = applyToHeaderCheckBox.Checked;
            };

            applyToFooterCheckBox = new CheckBox
            {
                Text = "页脚",
                AutoSize = true,
                Checked = formatRule.ApplyToFooter,
                Margin = new Padding(5, 3, 15, 3)
            };
            applyToFooterCheckBox.CheckedChanged += (sender, e) =>
            {
                formatRule.ApplyToFooter = applyToFooterCheckBox.Checked;
            };

            applyToTextBoxCheckBox = new CheckBox
            {
                Text = "文本框",
                AutoSize = true,
                Checked = formatRule.ApplyToTextBox,
                Margin = new Padding(5, 3, 15, 3)
            };
            applyToTextBoxCheckBox.CheckedChanged += (sender, e) =>
            {
                formatRule.ApplyToTextBox = applyToTextBoxCheckBox.Checked;
            };

            applyToFootnoteCheckBox = new CheckBox
            {
                Text = "脚注",
                AutoSize = true,
                Checked = formatRule.ApplyToFootnote,
                Margin = new Padding(5, 3, 15, 3)
            };
            applyToFootnoteCheckBox.CheckedChanged += (sender, e) =>
            {
                formatRule.ApplyToFootnote = applyToFootnoteCheckBox.Checked;
            };

            applyToEndnoteCheckBox = new CheckBox
            {
                Text = "尾注",
                AutoSize = true,
                Checked = formatRule.ApplyToEndnote,
                Margin = new Padding(5, 3, 15, 3)
            };
            applyToEndnoteCheckBox.CheckedChanged += (sender, e) =>
            {
                formatRule.ApplyToEndnote = applyToEndnoteCheckBox.Checked;
            };

            applyToCommentCheckBox = new CheckBox
            {
                Text = "批注",
                AutoSize = true,
                Checked = formatRule.ApplyToComment,
                Margin = new Padding(5, 3, 15, 3)
            };
            applyToCommentCheckBox.CheckedChanged += (sender, e) =>
            {
                formatRule.ApplyToComment = applyToCommentCheckBox.Checked;
            };



            // 将复选框添加到布局面板
            globalApplyScopeLayout.Controls.Add(applyToMainDocumentCheckBox);
            globalApplyScopeLayout.Controls.Add(applyToHeaderCheckBox);
            globalApplyScopeLayout.Controls.Add(applyToFooterCheckBox);
            globalApplyScopeLayout.Controls.Add(applyToTextBoxCheckBox);
            globalApplyScopeLayout.Controls.Add(applyToFootnoteCheckBox);
            globalApplyScopeLayout.Controls.Add(applyToEndnoteCheckBox);
            globalApplyScopeLayout.Controls.Add(applyToCommentCheckBox);


            globalApplyScopePanel.Controls.Add(globalApplyScopeLayout);

            // 将全局应用范围面板添加到主布局
            mainLayout.Controls.Add(globalApplyScopePanel, 0, 0);

            // 初始化TabControl
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                TabIndex = 0
            };

            // 创建标签页
            paragraphTabPage = new TabPage("段落格式");
            fontTabPage = new TabPage("字体格式");
            imageTabPage = new TabPage("图片格式");
            watermarkTabPage = new TabPage("水印格式");
            tableTabPage = new TabPage("表格格式");

            // 为每个标签页添加滚动面板，并统一设置padding
            paragraphScrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(10)
            };
            paragraphTabPage.Controls.Add(paragraphScrollPanel);

            fontScrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(10)
            };
            fontTabPage.Controls.Add(fontScrollPanel);

            imageScrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(10)
            };
            imageTabPage.Controls.Add(imageScrollPanel);

            watermarkScrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(10)
            };
            watermarkTabPage.Controls.Add(watermarkScrollPanel);

            tableScrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(10)
            };
            tableTabPage.Controls.Add(tableScrollPanel);



            // === 基本段落格式 ===
            basicPanel = new GroupBox
            {
                Text = "基本段落格式",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            // 创建主布局面板
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 添加启用基本段落格式的复选框
            enableBasicFormatCheckBox = new CheckBox
            {
                Text = "启用基本段落格式",
                AutoSize = true,
                Checked = formatRule.EnableBasicFormat
            };

            enableBasicFormatCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBasicFormatCheckBox.Checked;

                // 控制所有子功能启用复选框的启用状态
                enableAlignmentCheckBox.Enabled = enabled;
                enableOutlineLevelCheckBox.Enabled = enabled;
                enableTextDirectionCheckBox.Enabled = enabled;
                enableIndentCheckBox.Enabled = enabled;
                enableSpacingCheckBox.Enabled = enabled;

                // 更新所有基本段落格式控件的启用状态
                UpdateBasicFormatControlsEnabled();
            };

            mainPanel.Controls.Add(enableBasicFormatCheckBox, 0, 0);

            // 创建内容布局面板
            var formatPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.TopDown,
                WrapContents = false,
                Margin = new Padding(10)
            };

            // 1. 对齐方式
            var alignmentPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight,
                Margin = new Padding(0, 3, 0, 3)
            };

            // 添加启用对齐方式的复选框
            enableAlignmentCheckBox = new CheckBox
            {
                Text = "",
                AutoSize = true,
                Checked = formatRule.EnableAlignment,
                Width = 20,
                Enabled = enableBasicFormatCheckBox.Checked
            };
            alignmentPanel.Controls.Add(enableAlignmentCheckBox);

            alignmentPanel.Controls.Add(new Label
            {
                Text = "对齐方式：",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Width = 80  // 调整标签宽度
            });

            alignmentComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = formatRule.EnableAlignment && formatRule.EnableBasicFormat
            };

            // 设置下拉框文字居中显示
            alignmentComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = alignmentComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            alignmentComboBox.Items.Add("左对齐");
            alignmentComboBox.Items.Add("居中对齐");
            alignmentComboBox.Items.Add("右对齐");
            alignmentComboBox.Items.Add("两端对齐");
            alignmentComboBox.Items.Add("分散对齐");
            if (alignmentComboBox != null)
                alignmentComboBox.SelectedIndex = 0; // 默认左对齐

            alignmentPanel.Controls.Add(alignmentComboBox);
            formatPanel.Controls.Add(alignmentPanel);

            // 添加对齐方式启用复选框的事件处理
            enableAlignmentCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableAlignmentCheckBox.Checked && enableBasicFormatCheckBox.Checked;
                alignmentComboBox.Enabled = enabled;
                formatRule.EnableAlignment = enableAlignmentCheckBox.Checked;
            };

            // 2. 大纲级别
            var outlineLevelPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight,
                Margin = new Padding(0, 3, 0, 3)
            };

            // 添加启用大纲级别的复选框
            enableOutlineLevelCheckBox = new CheckBox
            {
                Text = "",
                AutoSize = true,
                Checked = formatRule.EnableOutlineLevel,
                Width = 20,
                Enabled = enableBasicFormatCheckBox.Checked
            };
            outlineLevelPanel.Controls.Add(enableOutlineLevelCheckBox);

            outlineLevelPanel.Controls.Add(new Label
            {
                Text = "大纲级别：",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Width = 80  // 调整标签宽度
            });

            outlineLevelComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = formatRule.EnableOutlineLevel && formatRule.EnableBasicFormat
            };

            // 设置下拉框文字居中显示
            outlineLevelComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = outlineLevelComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            outlineLevelComboBox.Items.Add("正文文本");
            outlineLevelComboBox.Items.Add("1级");
            outlineLevelComboBox.Items.Add("2级");
            outlineLevelComboBox.Items.Add("3级");
            outlineLevelComboBox.Items.Add("4级");
            outlineLevelComboBox.Items.Add("5级");
            outlineLevelComboBox.Items.Add("6级");
            outlineLevelComboBox.Items.Add("7级");
            outlineLevelComboBox.Items.Add("8级");
            outlineLevelComboBox.SelectedIndex = 0; // 默认正文文本

            outlineLevelPanel.Controls.Add(outlineLevelComboBox);
            formatPanel.Controls.Add(outlineLevelPanel);

            // 添加大纲级别启用复选框的事件处理
            enableOutlineLevelCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableOutlineLevelCheckBox.Checked && enableBasicFormatCheckBox.Checked;
                outlineLevelComboBox.Enabled = enabled;
                formatRule.EnableOutlineLevel = enableOutlineLevelCheckBox.Checked;
            };

            // 3. 对齐方向
            var textDirectionPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight,
                Margin = new Padding(0, 3, 0, 3)
            };

            // 添加启用对齐方向的复选框
            enableTextDirectionCheckBox = new CheckBox
            {
                Text = "",
                AutoSize = true,
                Checked = formatRule.EnableTextDirection,
                Width = 20,
                Enabled = enableBasicFormatCheckBox.Checked
            };
            textDirectionPanel.Controls.Add(enableTextDirectionCheckBox);

            textDirectionPanel.Controls.Add(new Label
            {
                Text = "对齐方向：",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Width = 80  // 调整标签宽度
            });

            textDirectionComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = formatRule.EnableTextDirection && formatRule.EnableBasicFormat
            };

            // 设置下拉框文字居中显示
            textDirectionComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = textDirectionComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            textDirectionComboBox.Items.Add("从左向右");
            textDirectionComboBox.Items.Add("从右向左");
            if (textDirectionComboBox != null)
                textDirectionComboBox.SelectedIndex = 0; // 默认从左向右

            textDirectionPanel.Controls.Add(textDirectionComboBox);
            formatPanel.Controls.Add(textDirectionPanel);

            // 添加对齐方向启用复选框的事件处理
            enableTextDirectionCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableTextDirectionCheckBox.Checked && enableBasicFormatCheckBox.Checked;
                textDirectionComboBox.Enabled = enabled;
                formatRule.EnableTextDirection = enableTextDirectionCheckBox.Checked;
            };

            // 4. 缩进设置
            var indentPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight,
                Margin = new Padding(0, 3, 0, 3)
            };

            // 添加启用缩进的复选框
            enableIndentCheckBox = new CheckBox
            {
                Text = "",
                AutoSize = true,
                Checked = formatRule.EnableIndent,
                Width = 20,
                Enabled = enableBasicFormatCheckBox.Checked
            };
            indentPanel.Controls.Add(enableIndentCheckBox);

            indentPanel.Controls.Add(new Label
            {
                Text = "缩进：",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Width = 80  // 调整标签宽度
            });

            var indentContentPanel = new TableLayoutPanel
            {
                AutoSize = true,
                ColumnCount = 2,
                RowCount = 3,
                Margin = new Padding(0)
            };

            // 左右缩进
            indentContentPanel.Controls.Add(new Label { Text = "文本之前：", AutoSize = true }, 0, 0);
            leftIndentNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 2,
                Increment = 0.1m,
                Width = 70,
                TextAlign = HorizontalAlignment.Center,
                Enabled = formatRule.EnableIndent && formatRule.EnableBasicFormat
            };
            var leftUnitLabel = new Label { Text = "磅", AutoSize = true };
            var leftFlow = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight, Margin = new Padding(0) };
            leftFlow.Controls.Add(leftIndentNumeric);
            leftFlow.Controls.Add(leftUnitLabel);
            indentContentPanel.Controls.Add(leftFlow, 1, 0);

            indentContentPanel.Controls.Add(new Label { Text = "文本之后：", AutoSize = true }, 0, 1);
            rightIndentNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 2,
                Increment = 0.1m,
                Width = 70,
                TextAlign = HorizontalAlignment.Center,
                Enabled = formatRule.EnableIndent && formatRule.EnableBasicFormat
            };
            var rightUnitLabel = new Label { Text = "磅", AutoSize = true };
            var rightFlow = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight, Margin = new Padding(0) };
            rightFlow.Controls.Add(rightIndentNumeric);
            rightFlow.Controls.Add(rightUnitLabel);
            indentContentPanel.Controls.Add(rightFlow, 1, 1);

            // 特殊缩进
            indentContentPanel.Controls.Add(new Label { Text = "特殊缩进：", AutoSize = true }, 0, 2);

            var specialFlow = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true };

            indentTypeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 100,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = formatRule.EnableIndent && formatRule.EnableBasicFormat
            };

            // 设置下拉框文字居中显示
            indentTypeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = indentTypeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            indentTypeComboBox.Items.Add("无");
            indentTypeComboBox.Items.Add("首行缩进");
            indentTypeComboBox.Items.Add("悬挂缩进");
            indentTypeComboBox.SelectedIndex = 1; // 默认首行缩进

            indentValueNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 2,
                Increment = 0.1m,
                Width = 70,
                Value = 2.0m, // 默认2字符
                Enabled = formatRule.EnableIndent && formatRule.EnableBasicFormat && indentTypeComboBox.SelectedIndex > 0,
                TextAlign = HorizontalAlignment.Center
            };

            // 特殊缩进单位选择下拉框
            indentUnitComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 60,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = formatRule.EnableIndent && formatRule.EnableBasicFormat && indentTypeComboBox.SelectedIndex > 0
            };

            // 设置单位下拉框文字居中显示
            indentUnitComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = indentUnitComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加单位选项
            indentUnitComboBox.Items.Add("字符");
            indentUnitComboBox.Items.Add("厘米");
            indentUnitComboBox.Items.Add("毫米");
            indentUnitComboBox.Items.Add("磅");
            indentUnitComboBox.Items.Add("英寸");
            indentUnitComboBox.SelectedIndex = 0; // 默认字符单位

            // 当选择缩进类型时，启用或禁用数值选择器和单位选择
            indentTypeComboBox.SelectedIndexChanged += (sender, e) =>
            {
                bool hasSpecialIndent = indentTypeComboBox.SelectedIndex > 0;
                bool enabled = hasSpecialIndent && enableIndentCheckBox.Checked && enableBasicFormatCheckBox.Checked;
                indentValueNumeric.Enabled = enabled;
                indentUnitComboBox.Enabled = enabled;

                // 根据缩进类型设置默认值
                if (hasSpecialIndent)
                {
                    indentValueNumeric.Value = 2.0m; // 默认2字符
                    indentUnitComboBox.SelectedIndex = 0; // 默认字符单位
                    indentUnitComboBox.Tag = IndentUnit.Characters; // 保存当前单位
                }
            };

            // 当单位改变时，转换数值
            indentUnitComboBox.SelectedIndexChanged += (sender, e) =>
            {
                if (indentUnitComboBox.SelectedIndex >= 0)
                {
                    // 获取当前磅值
                    var currentUnit = (IndentUnit)(indentUnitComboBox.Tag ?? IndentUnit.Characters);
                    var newUnit = (IndentUnit)indentUnitComboBox.SelectedIndex;

                    if (currentUnit != newUnit)
                    {
                        // 先转换为磅，再转换为新单位
                        double pointsValue = UnitConverter.ConvertToPoints((double)indentValueNumeric.Value, currentUnit);
                        double newValue = UnitConverter.ConvertFromPoints(pointsValue, newUnit);

                        indentValueNumeric.Value = (decimal)Math.Round(newValue, 2);
                        indentUnitComboBox.Tag = newUnit; // 保存当前单位
                    }
                }
            };

            // 初始化单位标记
            indentUnitComboBox.Tag = IndentUnit.Characters;

            specialFlow.Controls.Add(indentTypeComboBox);
            specialFlow.Controls.Add(indentValueNumeric);
            specialFlow.Controls.Add(indentUnitComboBox);

            indentContentPanel.Controls.Add(specialFlow, 1, 2);

            indentPanel.Controls.Add(indentContentPanel);
            formatPanel.Controls.Add(indentPanel);

            // 添加缩进启用复选框的事件处理
            enableIndentCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableIndentCheckBox.Checked && enableBasicFormatCheckBox.Checked;
                leftIndentNumeric.Enabled = enabled;
                rightIndentNumeric.Enabled = enabled;
                indentTypeComboBox.Enabled = enabled;
                indentValueNumeric.Enabled = enabled && indentTypeComboBox.SelectedIndex > 0;
                formatRule.EnableIndent = enableIndentCheckBox.Checked;
            };

            // 5. 间距设置
            var spacingPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight,
                Margin = new Padding(0, 3, 0, 3)
            };

            // 添加启用间距的复选框
            enableSpacingCheckBox = new CheckBox
            {
                Text = "",
                AutoSize = true,
                Checked = formatRule.EnableSpacing,
                Width = 20,
                Enabled = enableBasicFormatCheckBox.Checked
            };
            spacingPanel.Controls.Add(enableSpacingCheckBox);

            spacingPanel.Controls.Add(new Label
            {
                Text = "间距：",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Width = 80  // 调整标签宽度
            });

            var spacingContentPanel = new TableLayoutPanel
            {
                AutoSize = true,
                ColumnCount = 2,
                RowCount = 3,
                Margin = new Padding(0)
            };

            spacingContentPanel.Controls.Add(new Label { Text = "段前：", AutoSize = true }, 0, 0);
            beforeSpacingNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Increment = 0.5m,
                Width = 70,
                TextAlign = HorizontalAlignment.Center,
                Enabled = formatRule.EnableSpacing && formatRule.EnableBasicFormat
            };
            var beforeUnitLabel = new Label { Text = "磅", AutoSize = true };
            var beforeFlow = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight, Margin = new Padding(0) };
            beforeFlow.Controls.Add(beforeSpacingNumeric);
            beforeFlow.Controls.Add(beforeUnitLabel);
            spacingContentPanel.Controls.Add(beforeFlow, 1, 0);

            spacingContentPanel.Controls.Add(new Label { Text = "段后：", AutoSize = true }, 0, 1);
            afterSpacingNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Increment = 0.5m,
                Width = 70,
                TextAlign = HorizontalAlignment.Center,
                Enabled = formatRule.EnableSpacing && formatRule.EnableBasicFormat
            };
            var afterUnitLabel = new Label { Text = "磅", AutoSize = true };
            var afterFlow = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight, Margin = new Padding(0) };
            afterFlow.Controls.Add(afterSpacingNumeric);
            afterFlow.Controls.Add(afterUnitLabel);
            spacingContentPanel.Controls.Add(afterFlow, 1, 1);

            // 行距设置
            spacingContentPanel.Controls.Add(new Label { Text = "行距：", AutoSize = true }, 0, 2);

            var lineSpaceFlow = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight, Margin = new Padding(0) };

            lineSpacingTypeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 100,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = formatRule.EnableSpacing && formatRule.EnableBasicFormat
            };

            // 设置下拉框文字居中显示
            lineSpacingTypeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = lineSpacingTypeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            lineSpacingTypeComboBox.Items.Add("单倍行距");
            lineSpacingTypeComboBox.Items.Add("1.5倍行距");
            lineSpacingTypeComboBox.Items.Add("2倍行距");
            lineSpacingTypeComboBox.Items.Add("最小值");
            lineSpacingTypeComboBox.Items.Add("固定值");
            lineSpacingTypeComboBox.Items.Add("多倍行距");
            lineSpacingTypeComboBox.SelectedIndex = 4; // 默认单倍行距

            lineSpacingValueNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 2,
                Increment = 0.1m,
                Value = 28.0m, // 默认固定值28磅
                Width = 70,
                Enabled = formatRule.EnableSpacing && formatRule.EnableBasicFormat,
                TextAlign = HorizontalAlignment.Center
            };

            // 行距单位显示标签（用于显示"倍"或其他单位）
            lineSpacingUnitLabel = new Label
            {
                Text = "磅",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Anchor = AnchorStyles.Left
            };

            // 行距单位选择下拉框（仅在固定值和最小值时显示）
            lineSpacingUnitComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 60,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = formatRule.EnableSpacing && formatRule.EnableBasicFormat,
                Visible = false // 初始隐藏，只在固定值和最小值时显示
            };

            // 设置单位下拉框文字居中显示
            lineSpacingUnitComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = lineSpacingUnitComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加行距单位选项
            lineSpacingUnitComboBox.Items.Add("磅");
            lineSpacingUnitComboBox.Items.Add("厘米");
            lineSpacingUnitComboBox.Items.Add("毫米");
            lineSpacingUnitComboBox.Items.Add("英寸");
            lineSpacingUnitComboBox.SelectedIndex = 0; // 默认磅单位

            // 当选择行距类型时，根据类型更新数值和单位显示
            lineSpacingTypeComboBox.SelectedIndexChanged += (sender, e) =>
            {
                switch (lineSpacingTypeComboBox.SelectedIndex)
                {
                    case 0: // 单倍行距
                        lineSpacingValueNumeric.Value = 1.0m;
                        lineSpacingUnitLabel.Text = "倍";
                        lineSpacingUnitLabel.Visible = true;
                        lineSpacingUnitComboBox.Visible = false;
                        break;
                    case 1: // 1.5倍行距
                        lineSpacingValueNumeric.Value = 1.5m;
                        lineSpacingUnitLabel.Text = "倍";
                        lineSpacingUnitLabel.Visible = true;
                        lineSpacingUnitComboBox.Visible = false;
                        break;
                    case 2: // 2倍行距
                        lineSpacingValueNumeric.Value = 2.0m;
                        lineSpacingUnitLabel.Text = "倍";
                        lineSpacingUnitLabel.Visible = true;
                        lineSpacingUnitComboBox.Visible = false;
                        break;
                    case 3: // 最小值
                    case 4: // 固定值
                        lineSpacingValueNumeric.Value = 28.0m; // 默认28磅
                        lineSpacingUnitComboBox.SelectedIndex = 0; // 磅单位
                        lineSpacingUnitComboBox.Tag = LineSpacingUnit.Points; // 同步设置Tag
                        lineSpacingUnitLabel.Visible = false;
                        lineSpacingUnitComboBox.Visible = true;
                        lineSpacingUnitComboBox.Enabled = enableBasicFormatCheckBox.Checked && enableSpacingCheckBox.Checked;
                        break;
                    case 5: // 多倍行距
                        lineSpacingValueNumeric.Value = 3.0m; // 默认3倍
                        lineSpacingUnitLabel.Text = "倍";
                        lineSpacingUnitLabel.Visible = true;
                        lineSpacingUnitComboBox.Visible = false;
                        break;
                }

                // 更新行距值的启用状态 - 所有类型都可以修改数值
                lineSpacingValueNumeric.Enabled = enableBasicFormatCheckBox.Checked && enableSpacingCheckBox.Checked;
            };

            // 当单位改变时，转换数值（仅对固定值和最小值有效）
            lineSpacingUnitComboBox.SelectedIndexChanged += (sender, e) =>
            {
                if (lineSpacingUnitComboBox.SelectedIndex >= 0 && lineSpacingUnitComboBox.Enabled)
                {
                    // 只有固定值和最小值才允许单位转换
                    int typeIndex = lineSpacingTypeComboBox.SelectedIndex;
                    if (typeIndex == 3 || typeIndex == 4) // 最小值或固定值
                    {
                        var currentUnit = (LineSpacingUnit)(lineSpacingUnitComboBox.Tag ?? LineSpacingUnit.Points);
                        var newUnit = (LineSpacingUnit)lineSpacingUnitComboBox.SelectedIndex;

                        if (currentUnit != newUnit)
                        {
                            // 先转换为磅，再转换为新单位
                            double pointsValue = UnitConverter.ConvertToPoints((double)lineSpacingValueNumeric.Value, currentUnit);
                            double newValue = UnitConverter.ConvertFromPoints(pointsValue, newUnit);

                            lineSpacingValueNumeric.Value = (decimal)Math.Round(newValue, 2);
                            lineSpacingUnitComboBox.Tag = newUnit; // 保存当前单位
                        }
                    }
                }
            };

            // 初始化单位标记
            lineSpacingUnitComboBox.Tag = LineSpacingUnit.Points;

            lineSpaceFlow.Controls.Add(lineSpacingTypeComboBox);
            lineSpaceFlow.Controls.Add(lineSpacingValueNumeric);
            lineSpaceFlow.Controls.Add(lineSpacingUnitLabel); // 添加单位标签
            lineSpaceFlow.Controls.Add(lineSpacingUnitComboBox); // 添加单位下拉框

            spacingContentPanel.Controls.Add(lineSpaceFlow, 1, 2);

            // 添加图片行距控制选项
            adjustImageLineSpacingCheckBox = new CheckBox
            {
                Text = "是否调整图片所在行的行距",
                AutoSize = true,
                Checked = formatRule.AdjustImageLineSpacing,
                Enabled = formatRule.EnableSpacing && formatRule.EnableBasicFormat
            };
            spacingContentPanel.Controls.Add(adjustImageLineSpacingCheckBox, 0, 3);
            spacingContentPanel.SetColumnSpan(adjustImageLineSpacingCheckBox, 2);

            spacingPanel.Controls.Add(spacingContentPanel);
            formatPanel.Controls.Add(spacingPanel);

            // 添加间距启用复选框的事件处理
            enableSpacingCheckBox.CheckedChanged += (sender, e) =>
            {
                UpdateBasicFormatControlsEnabled();
                formatRule.EnableSpacing = enableSpacingCheckBox.Checked;
            };

            // 将formatPanel添加到mainPanel
            mainPanel.Controls.Add(formatPanel, 0, 1);

            // 将mainPanel添加到basicPanel
            basicPanel.Controls.Add(mainPanel);

            // 将basicPanel添加到paragraphScrollPanel
            paragraphScrollPanel.Controls.Add(basicPanel);

            // 设置基本段落格式面板的边距
            basicPanel.Margin = new Padding(0, 0, 0, 5);  // 只保留底部边距

            // === 边框和底纹 ===
            var borderShadingPanel = new GroupBox
            {
                Text = "边框和底纹",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var borderShadingMainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 添加启用边框和底纹的复选框
            enableBorderShadingCheckBox = new CheckBox
            {
                Text = "启用边框和底纹",
                AutoSize = true,
                Checked = formatRule.EnableBorderShading
            };

            enableBorderShadingCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBorderShadingCheckBox.Checked;

                // 启用或禁用所有边框和底纹控件
                hasBordersCheckBox.Enabled = enabled;
                borderTypeComboBox.Enabled = enabled && hasBordersCheckBox.Checked;
                borderWidthNumeric.Enabled = enabled && hasBordersCheckBox.Checked;
                borderColorButton.Enabled = enabled && hasBordersCheckBox.Checked;
                clearBorderColorButton.Enabled = enabled && hasBordersCheckBox.Checked;
                hasTopBorderCheckBox.Enabled = enabled && hasBordersCheckBox.Checked;
                hasBottomBorderCheckBox.Enabled = enabled && hasBordersCheckBox.Checked;
                hasLeftBorderCheckBox.Enabled = enabled && hasBordersCheckBox.Checked;
                hasRightBorderCheckBox.Enabled = enabled && hasBordersCheckBox.Checked;

                hasShadingCheckBox.Enabled = enabled;
                shadingPatternComboBox.Enabled = enabled && hasShadingCheckBox.Checked;
                shadingColorButton.Enabled = enabled && hasShadingCheckBox.Checked;
                clearShadingColorButton.Enabled = enabled && hasShadingCheckBox.Checked;
            };

            borderShadingMainLayout.Controls.Add(enableBorderShadingCheckBox, 0, 0);

            var borderShadingLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                Padding = new Padding(10),
                AutoSize = true
            };

            // 设置列宽
            borderShadingLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            borderShadingLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));

            // 边框设置
            var borderGroupBox = new GroupBox
            {
                Text = "边框设置",
                Dock = DockStyle.Fill,
                AutoSize = true
            };

            var borderLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                Padding = new Padding(5),
                AutoSize = true
            };

            hasBordersCheckBox = new CheckBox
            {
                Text = "段落边框",
                AutoSize = true
            };

            hasBordersCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = hasBordersCheckBox.Checked && enableBorderShadingCheckBox.Checked;
                borderTypeComboBox.Enabled = enabled;
                borderWidthNumeric.Enabled = enabled;
                borderColorButton.Enabled = enabled;
                clearBorderColorButton.Enabled = enabled;
                hasTopBorderCheckBox.Enabled = enabled;
                hasBottomBorderCheckBox.Enabled = enabled;
                hasLeftBorderCheckBox.Enabled = enabled;
                hasRightBorderCheckBox.Enabled = enabled;
            };

            borderLayout.Controls.Add(hasBordersCheckBox, 0, 0);
            borderLayout.SetColumnSpan(hasBordersCheckBox, 2);

            // 边框类型
            borderLayout.Controls.Add(new Label { Text = "样式：", AutoSize = true }, 0, 1);

            borderTypeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            borderTypeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = borderTypeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            borderTypeComboBox.Items.Add("单线");
            borderTypeComboBox.Items.Add("双线");
            borderTypeComboBox.Items.Add("虚线");
            borderTypeComboBox.Items.Add("点线");
            borderTypeComboBox.SelectedIndex = 0;

            borderLayout.Controls.Add(borderTypeComboBox, 1, 1);

            // 边框宽度
            borderLayout.Controls.Add(new Label { Text = "宽度：", AutoSize = true }, 0, 2);

            var borderWidthFlow = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            borderWidthNumeric = new NumericUpDown
            {
                Minimum = 0.25m,
                Maximum = 10m,
                DecimalPlaces = 2,
                Increment = 0.25m,
                Value = 0.5m,
                Width = 70,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };

            var borderWidthUnitLabel = new Label { Text = "磅", AutoSize = true };

            borderWidthFlow.Controls.Add(borderWidthNumeric);
            borderWidthFlow.Controls.Add(borderWidthUnitLabel);

            borderLayout.Controls.Add(borderWidthFlow, 1, 2);

            // 边框颜色
            borderLayout.Controls.Add(new Label { Text = "颜色：", AutoSize = true }, 0, 3);

            var borderColorFlow = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            borderColorButton = new Button
            {
                Text = "选择...",
                Width = 70,
                Enabled = false
            };

            borderColorPanel = new Panel
            {
                BackColor = Color.Black,
                Size = new Size(24, 24),
                BorderStyle = BorderStyle.FixedSingle
            };

            borderColorButton.Click += (sender, e) =>
            {
                var colorDialog = new ColorDialog();
                colorDialog.Color = borderColorPanel.BackColor;

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    borderColorPanel.BackColor = colorDialog.Color;
                    formatRule.BorderColor = colorDialog.Color; // 立即更新格式规则对象
                }
            };

            clearBorderColorButton = new Button
            {
                Text = "清除",
                Width = 60,
                Enabled = false
            };

            clearBorderColorButton.Click += (sender, e) =>
            {
                // 重置为黑色
                borderColorPanel.BackColor = Color.Black;
                formatRule.BorderColor = Color.Black; // 立即更新格式规则对象
            };

            borderColorFlow.Controls.Add(borderColorButton);
            borderColorFlow.Controls.Add(borderColorPanel);
            borderColorFlow.Controls.Add(clearBorderColorButton);

            borderLayout.Controls.Add(borderColorFlow, 1, 3);

            borderGroupBox.Controls.Add(borderLayout);

            // 边框位置
            var borderPositionPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Bottom,
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            hasTopBorderCheckBox = new CheckBox { Text = "上", AutoSize = true, Checked = true, Enabled = false };
            hasBottomBorderCheckBox = new CheckBox { Text = "下", AutoSize = true, Checked = true, Enabled = false };
            hasLeftBorderCheckBox = new CheckBox { Text = "左", AutoSize = true, Checked = true, Enabled = false };
            hasRightBorderCheckBox = new CheckBox { Text = "右", AutoSize = true, Checked = true, Enabled = false };

            borderPositionPanel.Controls.Add(hasTopBorderCheckBox);
            borderPositionPanel.Controls.Add(hasBottomBorderCheckBox);
            borderPositionPanel.Controls.Add(hasLeftBorderCheckBox);
            borderPositionPanel.Controls.Add(hasRightBorderCheckBox);

            borderGroupBox.Controls.Add(borderPositionPanel);
            borderShadingLayout.Controls.Add(borderGroupBox, 0, 0);

            // 底纹设置
            var shadingGroupBox = new GroupBox
            {
                Text = "底纹设置",
                Dock = DockStyle.Fill,
                AutoSize = true
            };

            var shadingLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                Padding = new Padding(5),
                AutoSize = true
            };

            hasShadingCheckBox = new CheckBox
            {
                Text = "段落底纹",
                AutoSize = true
            };

            hasShadingCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = hasShadingCheckBox.Checked && enableBorderShadingCheckBox.Checked;
                shadingPatternComboBox.Enabled = enabled;
                shadingColorButton.Enabled = enabled;
                clearShadingColorButton.Enabled = enabled;

                // 立即更新格式规则对象中的HasShading属性
                formatRule.HasShading = hasShadingCheckBox.Checked;
            };

            shadingLayout.Controls.Add(hasShadingCheckBox, 0, 0);
            shadingLayout.SetColumnSpan(hasShadingCheckBox, 2);

            // 底纹样式
            shadingLayout.Controls.Add(new Label { Text = "样式：", AutoSize = true }, 0, 1);

            shadingPatternComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            shadingPatternComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = shadingPatternComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            shadingPatternComboBox.Items.Add("实心");
            shadingPatternComboBox.Items.Add("5%");
            shadingPatternComboBox.Items.Add("10%");
            shadingPatternComboBox.Items.Add("20%");
            shadingPatternComboBox.Items.Add("25%");
            shadingPatternComboBox.Items.Add("30%");
            shadingPatternComboBox.Items.Add("40%");
            shadingPatternComboBox.Items.Add("50%");
            shadingPatternComboBox.Items.Add("60%");
            shadingPatternComboBox.Items.Add("70%");
            shadingPatternComboBox.Items.Add("75%");
            shadingPatternComboBox.Items.Add("80%");
            shadingPatternComboBox.Items.Add("90%");
            shadingPatternComboBox.SelectedIndex = 0;

            shadingLayout.Controls.Add(shadingPatternComboBox, 1, 1);

            // 底纹颜色
            shadingLayout.Controls.Add(new Label { Text = "颜色：", AutoSize = true }, 0, 2);

            var shadingColorFlow = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            shadingColorButton = new Button
            {
                Text = "选择...",
                Width = 70,
                Enabled = false
            };

            shadingColorPanel = new Panel
            {
                BackColor = Color.LightGray,
                Size = new Size(24, 24),
                BorderStyle = BorderStyle.FixedSingle
            };

            shadingColorButton.Click += (sender, e) =>
            {
                var colorDialog = new ColorDialog();
                colorDialog.Color = shadingColorPanel.BackColor;

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    // 确保不选择黑色作为底纹颜色，避免文档背景变黑
                    if (colorDialog.Color.ToArgb() == Color.Black.ToArgb())
                    {
                        MessageBox.Show("不建议使用黑色作为底纹颜色，这可能导致文档背景变黑。\n已自动调整为浅灰色。",
                            "底纹颜色警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        shadingColorPanel.BackColor = Color.LightGray;
                        formatRule.ShadingColor = Color.LightGray;
                    }
                    else
                    {
                        shadingColorPanel.BackColor = colorDialog.Color;
                        formatRule.ShadingColor = colorDialog.Color; // 立即更新格式规则对象
                    }
                }
            };

            clearShadingColorButton = new Button
            {
                Text = "清除",
                Width = 60,
                Enabled = false
            };

            clearShadingColorButton.Click += (sender, e) =>
            {
                // 重置为浅灰色
                shadingColorPanel.BackColor = Color.LightGray;
                formatRule.ShadingColor = Color.LightGray; // 立即更新格式规则对象

                // 如果底纹已启用，提示用户可能需要禁用底纹
                if (hasShadingCheckBox.Checked)
                {
                    if (MessageBox.Show("您是否想要同时禁用段落底纹功能？\n点击\"是\"将禁用底纹功能，点击\"否\"将保持底纹功能启用但使用浅灰色。",
                        "底纹设置", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        hasShadingCheckBox.Checked = false;
                        formatRule.HasShading = false;
                    }
                }
            };

            shadingColorFlow.Controls.Add(shadingColorButton);
            shadingColorFlow.Controls.Add(shadingColorPanel);
            shadingColorFlow.Controls.Add(clearShadingColorButton);

            shadingLayout.Controls.Add(shadingColorFlow, 1, 2);

            shadingGroupBox.Controls.Add(shadingLayout);
            borderShadingLayout.Controls.Add(shadingGroupBox, 1, 0);

            borderShadingMainLayout.Controls.Add(borderShadingLayout, 0, 1);
            borderShadingPanel.Controls.Add(borderShadingMainLayout);
            paragraphScrollPanel.Controls.Add(borderShadingPanel);

            // 分页和连接控制
            var paginationGroupBox = new GroupBox
            {
                Text = "分页和连接控制",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var paginationMainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 添加启用分页和连接控制的复选框
            enablePaginationCheckBox = new CheckBox
            {
                Text = "启用分页和连接控制",
                AutoSize = true,
                Checked = formatRule.EnablePagination
            };

            enablePaginationCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enablePaginationCheckBox.Checked;

                // 启用或禁用所有分页和连接控制控件
                keepWithNextCheckBox.Enabled = enabled;
                keepLinesTogetherCheckBox.Enabled = enabled;
                pageBreakBeforeCheckBox.Enabled = enabled;
                widowOrphanControlCheckBox.Enabled = enabled;
                noSpaceBetweenParagraphsCheckBox.Enabled = enabled;
            };

            paginationMainLayout.Controls.Add(enablePaginationCheckBox, 0, 0);

            var paginationLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                Padding = new Padding(5),
                AutoSize = true
            };

            keepWithNextCheckBox = new CheckBox { Text = "与下段同页", AutoSize = true };
            keepLinesTogetherCheckBox = new CheckBox { Text = "段中不分页", AutoSize = true };
            pageBreakBeforeCheckBox = new CheckBox { Text = "段前分页", AutoSize = true };
            widowOrphanControlCheckBox = new CheckBox { Text = "孤行控制", AutoSize = true, Checked = true };
            noSpaceBetweenParagraphsCheckBox = new CheckBox { Text = "相同样式段落间无间距", AutoSize = true };

            paginationLayout.Controls.Add(keepWithNextCheckBox, 0, 0);
            paginationLayout.Controls.Add(keepLinesTogetherCheckBox, 1, 0);
            paginationLayout.Controls.Add(pageBreakBeforeCheckBox, 0, 1);
            paginationLayout.Controls.Add(widowOrphanControlCheckBox, 1, 1);
            paginationLayout.Controls.Add(noSpaceBetweenParagraphsCheckBox, 0, 2);
            paginationLayout.SetColumnSpan(noSpaceBetweenParagraphsCheckBox, 2);

            paginationMainLayout.Controls.Add(paginationLayout, 0, 1);
            paginationGroupBox.Controls.Add(paginationMainLayout);
            paragraphScrollPanel.Controls.Add(paginationGroupBox);

            // 添加制表位设置
            var tabStopsGroupBox = new GroupBox
            {
                Text = "制表位设置",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var tabStopsMainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 添加启用制表位设置的复选框
            enableTabStopsCheckBox = new CheckBox
            {
                Text = "启用制表位设置",
                AutoSize = true,
                Checked = formatRule.EnableTabStops
            };

            enableTabStopsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableTabStopsCheckBox.Checked;

                // 启用或禁用所有制表位设置控件
                tabStopsListView.Enabled = enabled;
                addTabStopButton.Enabled = enabled;
                removeTabStopButton.Enabled = enabled && tabStopsListView.SelectedItems.Count > 0;
            };

            tabStopsMainLayout.Controls.Add(enableTabStopsCheckBox, 0, 0);

            var tabStopsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(5),
                AutoSize = true
            };

            // 制表位列表
            tabStopsListView = new ListView
            {
                View = View.Details,
                FullRowSelect = true,
                Height = 100,
                Dock = DockStyle.Fill
            };

            tabStopsListView.Columns.Add("位置 (厘米)", 100);
            tabStopsListView.Columns.Add("对齐方式", 80);
            tabStopsListView.Columns.Add("前导符", 80);

            tabStopsLayout.Controls.Add(tabStopsListView, 0, 0);

            // 制表位按钮
            var tabStopsButtonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Bottom,
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            addTabStopButton = new Button
            {
                Text = "添加...",
                Width = 80
            };

            removeTabStopButton = new Button
            {
                Text = "删除",
                Width = 80,
                Enabled = false
            };

            tabStopsButtonPanel.Controls.Add(addTabStopButton);
            tabStopsButtonPanel.Controls.Add(removeTabStopButton);

            tabStopsLayout.Controls.Add(tabStopsButtonPanel, 0, 1);

            // 添加制表位按钮事件
            addTabStopButton.Click += (sender, e) =>
            {
                // 创建并显示制表位设置对话框
                using (var form = new TabStopDialog())
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // 获取用户在对话框中设置的值
                        var position = form.TabStopPosition;
                        var alignment = form.TabAlignment;
                        var leader = form.TabLeader;

                        // 创建ListView的列表项
                        var item = new ListViewItem(new string[]
                        {
                            position.ToString("F2"),
                            GetTabAlignmentText(alignment),
                            GetTabLeaderText(leader)
                        });

                        item.Tag = new TabStop(position, alignment, leader); // 将TabStop对象附加到列表项
                        tabStopsListView.Items.Add(item);
                    }
                }
            };

            // 删除制表位按钮事件
            removeTabStopButton.Click += (sender, e) =>
            {
                if (tabStopsListView.SelectedItems.Count > 0)
                {
                    foreach (ListViewItem item in tabStopsListView.SelectedItems)
                    {
                        tabStopsListView.Items.Remove(item);
                    }
                }
            };

            // 选择制表位事件
            tabStopsListView.SelectedIndexChanged += (sender, e) =>
            {
                removeTabStopButton.Enabled = tabStopsListView.SelectedItems.Count > 0 && enableTabStopsCheckBox.Checked;
            };

            // 添加双击编辑制表位功能
            tabStopsListView.DoubleClick += (sender, e) =>
            {
                if (tabStopsListView.SelectedItems.Count > 0)
                {
                    ListViewItem selectedItem = tabStopsListView.SelectedItems[0];

                    using (var form = new TabStopDialog())
                    {
                        // 如果有保存的TabStop对象，则加载它的值
                        if (selectedItem.Tag is TabStop tabStop)
                        {
                            form.SetValues(tabStop.Position, tabStop.Alignment, tabStop.Leader);
                        }
                        else
                        {
                            // 否则从列表项文本解析值
                            double position = double.Parse(selectedItem.SubItems[0].Text);
                            AW.TabAlignment alignment = GetTabAlignmentFromText(selectedItem.SubItems[1].Text);
                            AW.TabLeader leader = AW.TabLeader.None;

                            // 解析前导符
                            string leaderText = selectedItem.SubItems[2].Text;
                            switch (leaderText)
                            {
                                case "点号":
                                    leader = AW.TabLeader.Dots;
                                    break;
                                case "短划线":
                                    leader = AW.TabLeader.Dashes;
                                    break;
                                case "下划线":
                                    leader = AW.TabLeader.Line;
                                    break;
                            }

                            form.SetValues(position, alignment, leader);
                        }

                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            // 更新列表项
                            selectedItem.SubItems[0].Text = form.TabStopPosition.ToString("F2");
                            selectedItem.SubItems[1].Text = GetTabAlignmentText(form.TabAlignment);
                            selectedItem.SubItems[2].Text = GetTabLeaderText(form.TabLeader);

                            // 更新Tag
                            selectedItem.Tag = new TabStop(form.TabStopPosition, form.TabAlignment, form.TabLeader);
                        }
                    }
                }
            };

            tabStopsMainLayout.Controls.Add(tabStopsLayout, 0, 1);
            tabStopsGroupBox.Controls.Add(tabStopsMainLayout);

            // 完全清空面板
            paragraphScrollPanel.Controls.Clear();

            // 移除全部区域的Dock属性，改为绝对位置布局
            int yPos = 10;
            int panelWidth = paragraphScrollPanel.ClientSize.Width - 20; // 统一面板宽度



            // 1. 添加基本段落格式区域
            basicPanel.Dock = DockStyle.None;
            basicPanel.Width = panelWidth;
            basicPanel.Location = new Point(10, yPos);
            paragraphScrollPanel.Controls.Add(basicPanel);
            yPos += basicPanel.Height + 10;

            // 2. 添加边框和底纹区域
            borderShadingPanel.Dock = DockStyle.None;
            borderShadingPanel.Width = panelWidth;
            borderShadingPanel.Location = new Point(10, yPos);
            paragraphScrollPanel.Controls.Add(borderShadingPanel);
            yPos += borderShadingPanel.Height + 10;

            // 3. 添加分页和连接控制区域
            paginationGroupBox.Dock = DockStyle.None;
            paginationGroupBox.Width = panelWidth;
            paginationGroupBox.Location = new Point(10, yPos);
            paragraphScrollPanel.Controls.Add(paginationGroupBox);
            yPos += paginationGroupBox.Height + 10;

            // 4. 添加制表位设置区域
            tabStopsGroupBox.Dock = DockStyle.None;
            tabStopsGroupBox.Width = panelWidth;
            tabStopsGroupBox.Location = new Point(10, yPos);
            paragraphScrollPanel.Controls.Add(tabStopsGroupBox);
            yPos += tabStopsGroupBox.Height + 10;

            // 5. 添加高级段落格式区域
            var advancedFormatGroupBox = CreateAdvancedParagraphFormatPanel();
            advancedFormatGroupBox.Dock = DockStyle.None;
            advancedFormatGroupBox.Width = panelWidth;
            advancedFormatGroupBox.Location = new Point(10, yPos);
            paragraphScrollPanel.Controls.Add(advancedFormatGroupBox);
            yPos += advancedFormatGroupBox.Height + 10;

            // 6. 添加中文排版增强区域
            var chineseTypographyGroupBox = CreateChineseTypographyPanel();
            chineseTypographyGroupBox.Dock = DockStyle.None;
            chineseTypographyGroupBox.Width = panelWidth;
            chineseTypographyGroupBox.Location = new Point(10, yPos);
            paragraphScrollPanel.Controls.Add(chineseTypographyGroupBox);
            yPos += chineseTypographyGroupBox.Height + 10;

            // 7. 添加字符单位设置区域
            var characterUnitGroupBox = CreateCharacterUnitPanel();
            characterUnitGroupBox.Dock = DockStyle.None;
            characterUnitGroupBox.Width = panelWidth;
            characterUnitGroupBox.Location = new Point(10, yPos);
            paragraphScrollPanel.Controls.Add(characterUnitGroupBox);

            // 添加其他标签页的内容
            fontScrollPanel.Controls.Add(CreateFontFormatPanel());
            imageScrollPanel.Controls.Add(CreateImageFormatPanel());
            watermarkScrollPanel.Controls.Add(CreateWatermarkFormatPanel());
            tableScrollPanel.Controls.Add(CreateTableFormatPanel());

            // 添加标签页到TabControl
            tabControl.Controls.Add(paragraphTabPage);
            tabControl.Controls.Add(fontTabPage);
            tabControl.Controls.Add(imageTabPage);
            tabControl.Controls.Add(watermarkTabPage);
            tabControl.Controls.Add(tableTabPage);

            // 将TabControl添加到mainLayout
            mainLayout.Controls.Add(tabControl, 0, 1);

            // 添加按钮面板
            mainLayout.Controls.Add(CreateButtonPanel(), 0, 2);

            // 将面板添加到表单
            contentPanel.Controls.Add(mainLayout);
            this.Controls.Add(contentPanel);
        }



        // 创建高级段落格式面板
        private GroupBox CreateAdvancedParagraphFormatPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "高级段落格式",
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 启用高级段落格式的复选框
            var enableAdvancedFormatCheckBox = new CheckBox
            {
                Text = "启用高级段落格式",
                AutoSize = true,
                Checked = false // 默认不启用
            };

            mainLayout.Controls.Add(enableAdvancedFormatCheckBox, 0, 0);

            var contentLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 7,
                Padding = new Padding(5),
                AutoSize = true
            };

            // 中英文间距自动调整
            var farEastAlphaCheckBox = new CheckBox
            {
                Text = "中文与英文间距自动调整",
                AutoSize = true,
                Checked = formatRule.AddSpaceBetweenFarEastAndAlpha
            };
            contentLayout.Controls.Add(farEastAlphaCheckBox, 0, 0);

            var farEastDigitCheckBox = new CheckBox
            {
                Text = "中文与数字间距自动调整",
                AutoSize = true,
                Checked = formatRule.AddSpaceBetweenFarEastAndDigit
            };
            contentLayout.Controls.Add(farEastDigitCheckBox, 1, 0);

            // 基线对齐设置
            contentLayout.Controls.Add(new Label { Text = "基线对齐：", AutoSize = true }, 0, 1);
            var baselineAlignmentComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120
            };
            baselineAlignmentComboBox.Items.Add("自动");
            baselineAlignmentComboBox.Items.Add("顶端");
            baselineAlignmentComboBox.Items.Add("居中");
            baselineAlignmentComboBox.Items.Add("基线");
            baselineAlignmentComboBox.Items.Add("底端");
            baselineAlignmentComboBox.SelectedIndex = (int)formatRule.BaselineAlignment;
            contentLayout.Controls.Add(baselineAlignmentComboBox, 1, 1);

            // 首字下沉功能
            var enableDropCapCheckBox = new CheckBox
            {
                Text = "启用首字下沉",
                AutoSize = true,
                Checked = formatRule.EnableDropCap
            };
            contentLayout.Controls.Add(enableDropCapCheckBox, 0, 2);

            var dropCapPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            var dropCapPositionComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 80
            };
            dropCapPositionComboBox.Items.Add("无");
            dropCapPositionComboBox.Items.Add("下沉");
            dropCapPositionComboBox.Items.Add("边距");
            dropCapPositionComboBox.SelectedIndex = (int)formatRule.DropCapPosition;

            var linesToDropNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 10,
                Value = formatRule.LinesToDrop,
                Width = 60,
                TextAlign = HorizontalAlignment.Center
            };

            dropCapPanel.Controls.Add(new Label { Text = "位置：", AutoSize = true });
            dropCapPanel.Controls.Add(dropCapPositionComboBox);
            dropCapPanel.Controls.Add(new Label { Text = "行数：", AutoSize = true });
            dropCapPanel.Controls.Add(linesToDropNumeric);

            contentLayout.Controls.Add(dropCapPanel, 1, 2);

            // 自动间距控制
            var spaceAfterAutoCheckBox = new CheckBox
            {
                Text = "段后间距自动",
                AutoSize = true,
                Checked = formatRule.SpaceAfterAuto
            };
            contentLayout.Controls.Add(spaceAfterAutoCheckBox, 0, 3);

            var spaceBeforeAutoCheckBox = new CheckBox
            {
                Text = "段前间距自动",
                AutoSize = true,
                Checked = formatRule.SpaceBeforeAuto
            };
            contentLayout.Controls.Add(spaceBeforeAutoCheckBox, 1, 3);

            // 其他高级控制选项
            var suppressAutoHyphensCheckBox = new CheckBox
            {
                Text = "禁止自动连字符",
                AutoSize = true,
                Checked = formatRule.SuppressAutoHyphens
            };
            contentLayout.Controls.Add(suppressAutoHyphensCheckBox, 0, 4);

            var snapToGridCheckBox = new CheckBox
            {
                Text = "对齐到网格",
                AutoSize = true,
                Checked = formatRule.SnapToGrid
            };
            contentLayout.Controls.Add(snapToGridCheckBox, 1, 4);

            // 单词换行控制
            var wordWrapCheckBox = new CheckBox
            {
                Text = "单词换行",
                AutoSize = true,
                Checked = formatRule.WordWrap
            };
            contentLayout.Controls.Add(wordWrapCheckBox, 0, 5);

            var suppressLineNumbersCheckBox = new CheckBox
            {
                Text = "禁止行号显示",
                AutoSize = true,
                Checked = formatRule.SuppressLineNumbers
            };
            contentLayout.Controls.Add(suppressLineNumbersCheckBox, 1, 5);

            // 镜像缩进（用于双面打印）
            var mirrorIndentsCheckBox = new CheckBox
            {
                Text = "镜像缩进（双面打印）",
                AutoSize = true,
                Checked = formatRule.MirrorIndents
            };
            contentLayout.Controls.Add(mirrorIndentsCheckBox, 0, 6);

            // 启用/禁用控件的事件处理
            enableAdvancedFormatCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableAdvancedFormatCheckBox.Checked;
                foreach (Control control in contentLayout.Controls)
                {
                    control.Enabled = enabled;
                }
            };

            // 首字下沉启用/禁用事件
            enableDropCapCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableDropCapCheckBox.Checked && enableAdvancedFormatCheckBox.Checked;
                dropCapPositionComboBox.Enabled = enabled;
                linesToDropNumeric.Enabled = enabled;
            };

            // 保存数据的事件处理
            farEastAlphaCheckBox.CheckedChanged += (sender, e) => formatRule.AddSpaceBetweenFarEastAndAlpha = farEastAlphaCheckBox.Checked;
            farEastDigitCheckBox.CheckedChanged += (sender, e) => formatRule.AddSpaceBetweenFarEastAndDigit = farEastDigitCheckBox.Checked;
            baselineAlignmentComboBox.SelectedIndexChanged += (sender, e) => formatRule.BaselineAlignment = (AW.BaselineAlignment)baselineAlignmentComboBox.SelectedIndex;
            enableDropCapCheckBox.CheckedChanged += (sender, e) => formatRule.EnableDropCap = enableDropCapCheckBox.Checked;
            dropCapPositionComboBox.SelectedIndexChanged += (sender, e) => formatRule.DropCapPosition = (AW.DropCapPosition)dropCapPositionComboBox.SelectedIndex;
            linesToDropNumeric.ValueChanged += (sender, e) => formatRule.LinesToDrop = (int)linesToDropNumeric.Value;
            spaceAfterAutoCheckBox.CheckedChanged += (sender, e) => formatRule.SpaceAfterAuto = spaceAfterAutoCheckBox.Checked;
            spaceBeforeAutoCheckBox.CheckedChanged += (sender, e) => formatRule.SpaceBeforeAuto = spaceBeforeAutoCheckBox.Checked;
            suppressAutoHyphensCheckBox.CheckedChanged += (sender, e) => formatRule.SuppressAutoHyphens = suppressAutoHyphensCheckBox.Checked;
            snapToGridCheckBox.CheckedChanged += (sender, e) => formatRule.SnapToGrid = snapToGridCheckBox.Checked;
            wordWrapCheckBox.CheckedChanged += (sender, e) => formatRule.WordWrap = wordWrapCheckBox.Checked;
            suppressLineNumbersCheckBox.CheckedChanged += (sender, e) => formatRule.SuppressLineNumbers = suppressLineNumbersCheckBox.Checked;
            mirrorIndentsCheckBox.CheckedChanged += (sender, e) => formatRule.MirrorIndents = mirrorIndentsCheckBox.Checked;

            // 初始化控件状态
            bool initialEnabled = enableAdvancedFormatCheckBox.Checked;
            foreach (Control control in contentLayout.Controls)
            {
                control.Enabled = initialEnabled;
            }

            mainLayout.Controls.Add(contentLayout, 0, 1);
            groupBox.Controls.Add(mainLayout);

            return groupBox;
        }

        // 创建中文排版增强面板
        private GroupBox CreateChineseTypographyPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "中文排版增强",
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 启用中文排版增强的复选框
            var enableChineseTypographyCheckBox = new CheckBox
            {
                Text = "启用中文排版增强",
                AutoSize = true,
                Checked = formatRule.EnableChineseTypography
            };

            mainLayout.Controls.Add(enableChineseTypographyCheckBox, 0, 0);

            var contentLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                Padding = new Padding(5),
                AutoSize = true
            };

            // 远东换行规则
            var farEastLineBreakCheckBox = new CheckBox
            {
                Text = "远东换行规则控制",
                AutoSize = true,
                Checked = formatRule.FarEastLineBreakControl
            };
            contentLayout.Controls.Add(farEastLineBreakCheckBox, 0, 0);

            // 悬挂标点符号
            var hangingPunctuationCheckBox = new CheckBox
            {
                Text = "悬挂标点符号",
                AutoSize = true,
                Checked = formatRule.HangingPunctuation
            };
            contentLayout.Controls.Add(hangingPunctuationCheckBox, 1, 0);

            // 网格线单位间距
            var enableLineUnitSpacingCheckBox = new CheckBox
            {
                Text = "启用网格线单位间距",
                AutoSize = true,
                Checked = formatRule.EnableLineUnitSpacing
            };
            contentLayout.Controls.Add(enableLineUnitSpacingCheckBox, 0, 1);

            var lineUnitPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            var lineUnitBeforeNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.LineUnitBefore,
                Width = 60,
                TextAlign = HorizontalAlignment.Center
            };

            var lineUnitAfterNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.LineUnitAfter,
                Width = 60,
                TextAlign = HorizontalAlignment.Center
            };

            lineUnitPanel.Controls.Add(new Label { Text = "段前：", AutoSize = true });
            lineUnitPanel.Controls.Add(lineUnitBeforeNumeric);
            lineUnitPanel.Controls.Add(new Label { Text = "段后：", AutoSize = true });
            lineUnitPanel.Controls.Add(lineUnitAfterNumeric);

            contentLayout.Controls.Add(lineUnitPanel, 1, 1);

            // 竖排文字
            var verticalTextCheckBox = new CheckBox
            {
                Text = "竖排文字",
                AutoSize = true,
                Checked = formatRule.VerticalText
            };
            contentLayout.Controls.Add(verticalTextCheckBox, 0, 2);

            // 标点压缩
            var punctuationCompressionCheckBox = new CheckBox
            {
                Text = "启用标点压缩",
                AutoSize = true,
                Checked = formatRule.EnablePunctuationCompression
            };
            contentLayout.Controls.Add(punctuationCompressionCheckBox, 1, 2);

            // 网格对齐
            var chineseGridAlignmentCheckBox = new CheckBox
            {
                Text = "启用中文网格对齐",
                AutoSize = true,
                Checked = formatRule.EnableChineseGridAlignment
            };
            contentLayout.Controls.Add(chineseGridAlignmentCheckBox, 0, 3);

            var gridAlignmentPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            var gridAlignmentLevelComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 80
            };
            gridAlignmentLevelComboBox.Items.Add("默认");
            gridAlignmentLevelComboBox.Items.Add("低");
            gridAlignmentLevelComboBox.Items.Add("中");
            gridAlignmentLevelComboBox.Items.Add("高");
            gridAlignmentLevelComboBox.SelectedIndex = formatRule.GridAlignmentLevel;

            gridAlignmentPanel.Controls.Add(new Label { Text = "级别：", AutoSize = true });
            gridAlignmentPanel.Controls.Add(gridAlignmentLevelComboBox);

            contentLayout.Controls.Add(gridAlignmentPanel, 1, 3);

            // 启用/禁用控件的事件处理
            enableChineseTypographyCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableChineseTypographyCheckBox.Checked;
                foreach (Control control in contentLayout.Controls)
                {
                    control.Enabled = enabled;
                }
            };

            // 网格线单位间距启用/禁用事件
            enableLineUnitSpacingCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableLineUnitSpacingCheckBox.Checked && enableChineseTypographyCheckBox.Checked;
                lineUnitBeforeNumeric.Enabled = enabled;
                lineUnitAfterNumeric.Enabled = enabled;
            };

            // 网格对齐启用/禁用事件
            chineseGridAlignmentCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = chineseGridAlignmentCheckBox.Checked && enableChineseTypographyCheckBox.Checked;
                gridAlignmentLevelComboBox.Enabled = enabled;
            };

            // 保存数据的事件处理
            enableChineseTypographyCheckBox.CheckedChanged += (sender, e) => formatRule.EnableChineseTypography = enableChineseTypographyCheckBox.Checked;
            farEastLineBreakCheckBox.CheckedChanged += (sender, e) => formatRule.FarEastLineBreakControl = farEastLineBreakCheckBox.Checked;
            hangingPunctuationCheckBox.CheckedChanged += (sender, e) => formatRule.HangingPunctuation = hangingPunctuationCheckBox.Checked;
            enableLineUnitSpacingCheckBox.CheckedChanged += (sender, e) => formatRule.EnableLineUnitSpacing = enableLineUnitSpacingCheckBox.Checked;
            lineUnitBeforeNumeric.ValueChanged += (sender, e) => formatRule.LineUnitBefore = (double)lineUnitBeforeNumeric.Value;
            lineUnitAfterNumeric.ValueChanged += (sender, e) => formatRule.LineUnitAfter = (double)lineUnitAfterNumeric.Value;
            verticalTextCheckBox.CheckedChanged += (sender, e) => formatRule.VerticalText = verticalTextCheckBox.Checked;
            punctuationCompressionCheckBox.CheckedChanged += (sender, e) => formatRule.EnablePunctuationCompression = punctuationCompressionCheckBox.Checked;
            chineseGridAlignmentCheckBox.CheckedChanged += (sender, e) => formatRule.EnableChineseGridAlignment = chineseGridAlignmentCheckBox.Checked;
            gridAlignmentLevelComboBox.SelectedIndexChanged += (sender, e) => formatRule.GridAlignmentLevel = gridAlignmentLevelComboBox.SelectedIndex;

            // 初始化控件状态
            bool initialEnabled = enableChineseTypographyCheckBox.Checked;
            foreach (Control control in contentLayout.Controls)
            {
                control.Enabled = initialEnabled;
            }

            // 初始化子控件状态
            bool lineUnitEnabled = enableLineUnitSpacingCheckBox.Checked && initialEnabled;
            lineUnitBeforeNumeric.Enabled = lineUnitEnabled;
            lineUnitAfterNumeric.Enabled = lineUnitEnabled;

            bool gridEnabled = chineseGridAlignmentCheckBox.Checked && initialEnabled;
            gridAlignmentLevelComboBox.Enabled = gridEnabled;

            mainLayout.Controls.Add(contentLayout, 0, 1);
            groupBox.Controls.Add(mainLayout);

            return groupBox;
        }

        // 创建字符单位设置面板
        private GroupBox CreateCharacterUnitPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "字符单位设置",
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 启用字符单位设置的复选框
            var enableCharacterUnitCheckBox = new CheckBox
            {
                Text = "启用字符单位缩进",
                AutoSize = true,
                Checked = formatRule.EnableCharacterUnitIndent
            };

            mainLayout.Controls.Add(enableCharacterUnitCheckBox, 0, 0);

            var contentLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                Padding = new Padding(5),
                AutoSize = true
            };

            // 字符单位首行缩进
            contentLayout.Controls.Add(new Label { Text = "首行缩进：", AutoSize = true }, 0, 0);
            var charUnitFirstLinePanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            var charUnitFirstLineNumeric = new NumericUpDown
            {
                Minimum = -100,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.CharacterUnitFirstLineIndent,
                Width = 70,
                TextAlign = HorizontalAlignment.Center
            };

            charUnitFirstLinePanel.Controls.Add(charUnitFirstLineNumeric);
            charUnitFirstLinePanel.Controls.Add(new Label { Text = "字符", AutoSize = true });

            contentLayout.Controls.Add(charUnitFirstLinePanel, 1, 0);

            // 字符单位左缩进
            contentLayout.Controls.Add(new Label { Text = "左缩进：", AutoSize = true }, 0, 1);
            var charUnitLeftPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            var charUnitLeftNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.CharacterUnitLeftIndent,
                Width = 70,
                TextAlign = HorizontalAlignment.Center
            };

            charUnitLeftPanel.Controls.Add(charUnitLeftNumeric);
            charUnitLeftPanel.Controls.Add(new Label { Text = "字符", AutoSize = true });

            contentLayout.Controls.Add(charUnitLeftPanel, 1, 1);

            // 字符单位右缩进
            contentLayout.Controls.Add(new Label { Text = "右缩进：", AutoSize = true }, 0, 2);
            var charUnitRightPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            var charUnitRightNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.CharacterUnitRightIndent,
                Width = 70,
                TextAlign = HorizontalAlignment.Center
            };

            charUnitRightPanel.Controls.Add(charUnitRightNumeric);
            charUnitRightPanel.Controls.Add(new Label { Text = "字符", AutoSize = true });

            contentLayout.Controls.Add(charUnitRightPanel, 1, 2);

            // 启用/禁用控件的事件处理
            enableCharacterUnitCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableCharacterUnitCheckBox.Checked;
                foreach (Control control in contentLayout.Controls)
                {
                    control.Enabled = enabled;
                }
            };

            // 保存数据的事件处理
            enableCharacterUnitCheckBox.CheckedChanged += (sender, e) => formatRule.EnableCharacterUnitIndent = enableCharacterUnitCheckBox.Checked;
            charUnitFirstLineNumeric.ValueChanged += (sender, e) => formatRule.CharacterUnitFirstLineIndent = (double)charUnitFirstLineNumeric.Value;
            charUnitLeftNumeric.ValueChanged += (sender, e) => formatRule.CharacterUnitLeftIndent = (double)charUnitLeftNumeric.Value;
            charUnitRightNumeric.ValueChanged += (sender, e) => formatRule.CharacterUnitRightIndent = (double)charUnitRightNumeric.Value;

            // 初始化控件状态
            bool initialEnabled = enableCharacterUnitCheckBox.Checked;
            foreach (Control control in contentLayout.Controls)
            {
                control.Enabled = initialEnabled;
            }

            mainLayout.Controls.Add(contentLayout, 0, 1);
            groupBox.Controls.Add(mainLayout);

            return groupBox;
        }

        private void OkButton_Click(object? sender, EventArgs e)
        {
            SaveSettings();
        }

        private void LoadSettings()
        {
            // 从formatRule加载设置

            // 加载应用范围设置
            if (applyToMainDocumentCheckBox != null)
                applyToMainDocumentCheckBox.Checked = formatRule.ApplyToMainDocument;
            if (applyToHeaderCheckBox != null)
                applyToHeaderCheckBox.Checked = formatRule.ApplyToHeader;
            if (applyToFooterCheckBox != null)
                applyToFooterCheckBox.Checked = formatRule.ApplyToFooter;
            if (applyToTextBoxCheckBox != null)
                applyToTextBoxCheckBox.Checked = formatRule.ApplyToTextBox;
            if (applyToFootnoteCheckBox != null)
                applyToFootnoteCheckBox.Checked = formatRule.ApplyToFootnote;
            if (applyToEndnoteCheckBox != null)
                applyToEndnoteCheckBox.Checked = formatRule.ApplyToEndnote;
            if (applyToCommentCheckBox != null)
                applyToCommentCheckBox.Checked = formatRule.ApplyToComment;

            // 设置区域启用状态
            if (enableBasicFormatCheckBox != null)
                enableBasicFormatCheckBox.Checked = formatRule.EnableBasicFormat;

            // 加载基本段落格式子功能启用状态
            if (enableAlignmentCheckBox != null)
                enableAlignmentCheckBox.Checked = formatRule.EnableAlignment;
            if (enableOutlineLevelCheckBox != null)
                enableOutlineLevelCheckBox.Checked = formatRule.EnableOutlineLevel;
            if (enableTextDirectionCheckBox != null)
                enableTextDirectionCheckBox.Checked = formatRule.EnableTextDirection;
            if (enableIndentCheckBox != null)
                enableIndentCheckBox.Checked = formatRule.EnableIndent;
            if (enableSpacingCheckBox != null)
                enableSpacingCheckBox.Checked = formatRule.EnableSpacing;

            // 更新基本段落格式控件的启用状态
            bool enabled = enableBasicFormatCheckBox != null && enableBasicFormatCheckBox.Checked;

            if (alignmentComboBox != null) alignmentComboBox.Enabled = enabled;
            if (outlineLevelComboBox != null) outlineLevelComboBox.Enabled = enabled;
            if (textDirectionComboBox != null) textDirectionComboBox.Enabled = enabled;
            if (beforeSpacingNumeric != null) beforeSpacingNumeric.Enabled = enabled;
            if (afterSpacingNumeric != null) afterSpacingNumeric.Enabled = enabled;
            if (indentTypeComboBox != null) indentTypeComboBox.Enabled = enabled;
            if (indentValueNumeric != null && indentTypeComboBox != null)
                indentValueNumeric.Enabled = enabled && indentTypeComboBox.SelectedIndex > 0;
            if (leftIndentNumeric != null) leftIndentNumeric.Enabled = enabled;
            if (rightIndentNumeric != null) rightIndentNumeric.Enabled = enabled;
            if (lineSpacingTypeComboBox != null) lineSpacingTypeComboBox.Enabled = enabled;
            if (lineSpacingValueNumeric != null) lineSpacingValueNumeric.Enabled = enabled;

            if (enableBorderShadingCheckBox != null)
                enableBorderShadingCheckBox.Checked = formatRule.EnableBorderShading;

            // 更新边框和底纹控件的启用状态
            enabled = enableBorderShadingCheckBox != null && enableBorderShadingCheckBox.Checked;

            if (hasBordersCheckBox != null) hasBordersCheckBox.Enabled = enabled;
            if (borderTypeComboBox != null && hasBordersCheckBox != null)
                borderTypeComboBox.Enabled = enabled && hasBordersCheckBox.Checked;
            if (borderWidthNumeric != null && hasBordersCheckBox != null)
                borderWidthNumeric.Enabled = enabled && hasBordersCheckBox.Checked;
            if (borderColorButton != null && hasBordersCheckBox != null)
                borderColorButton.Enabled = enabled && hasBordersCheckBox.Checked;
            if (clearBorderColorButton != null && hasBordersCheckBox != null)
                clearBorderColorButton.Enabled = enabled && hasBordersCheckBox.Checked;
            if (hasTopBorderCheckBox != null && hasBordersCheckBox != null)
                hasTopBorderCheckBox.Enabled = enabled && hasBordersCheckBox.Checked;
            if (hasBottomBorderCheckBox != null && hasBordersCheckBox != null)
                hasBottomBorderCheckBox.Enabled = enabled && hasBordersCheckBox.Checked;
            if (hasLeftBorderCheckBox != null && hasBordersCheckBox != null)
                hasLeftBorderCheckBox.Enabled = enabled && hasBordersCheckBox.Checked;
            if (hasRightBorderCheckBox != null && hasBordersCheckBox != null)
                hasRightBorderCheckBox.Enabled = enabled && hasBordersCheckBox.Checked;

            if (hasShadingCheckBox != null) hasShadingCheckBox.Enabled = enabled;
            if (shadingPatternComboBox != null && hasShadingCheckBox != null)
                shadingPatternComboBox.Enabled = enabled && hasShadingCheckBox.Checked;
            if (shadingColorButton != null && hasShadingCheckBox != null)
                shadingColorButton.Enabled = enabled && hasShadingCheckBox.Checked;
            if (clearShadingColorButton != null && hasShadingCheckBox != null)
                clearShadingColorButton.Enabled = enabled && hasShadingCheckBox.Checked;

            if (enablePaginationCheckBox != null)
                enablePaginationCheckBox.Checked = formatRule.EnablePagination;

            // 更新分页和连接控制控件的启用状态
            enabled = enablePaginationCheckBox != null && enablePaginationCheckBox.Checked;

            if (keepWithNextCheckBox != null) keepWithNextCheckBox.Enabled = enabled;
            if (keepLinesTogetherCheckBox != null) keepLinesTogetherCheckBox.Enabled = enabled;
            if (pageBreakBeforeCheckBox != null) pageBreakBeforeCheckBox.Enabled = enabled;
            if (widowOrphanControlCheckBox != null) widowOrphanControlCheckBox.Enabled = enabled;
            if (noSpaceBetweenParagraphsCheckBox != null) noSpaceBetweenParagraphsCheckBox.Enabled = enabled;

            if (enableTabStopsCheckBox != null)
                enableTabStopsCheckBox.Checked = formatRule.EnableTabStops;

            // 更新制表位设置控件的启用状态
            enabled = enableTabStopsCheckBox != null && enableTabStopsCheckBox.Checked;

            if (tabStopsListView != null) tabStopsListView.Enabled = enabled;
            if (addTabStopButton != null) addTabStopButton.Enabled = enabled;
            if (removeTabStopButton != null && tabStopsListView != null)
                removeTabStopButton.Enabled = enabled && tabStopsListView.SelectedItems.Count > 0;

            if (enableFontFormatCheckBox != null)
            {
                enableFontFormatCheckBox.Checked = formatRule.EnableFontFormat; // 使用formatRule中的值

                // 加载字体格式子功能启用状态
                if (enableChineseFontCheckBox != null)
                    enableChineseFontCheckBox.Checked = formatRule.EnableChineseFont;
                if (enableWesternFontCheckBox != null)
                    enableWesternFontCheckBox.Checked = formatRule.EnableWesternFont;
                if (enableComplexScriptFontCheckBox != null)
                    enableComplexScriptFontCheckBox.Checked = formatRule.EnableComplexScriptFont;
                if (enableFontColorCheckBox != null)
                    enableFontColorCheckBox.Checked = formatRule.EnableFontColor;
                if (enableHighlightColorCheckBox != null)
                    enableHighlightColorCheckBox.Checked = formatRule.EnableHighlightColor;

                // 更新字体格式控件的启用状态
                enabled = enableFontFormatCheckBox.Checked;

                if (chineseFontComboBox != null) chineseFontComboBox.Enabled = enabled;
                if (chineseFontStyleComboBox != null) chineseFontStyleComboBox.Enabled = enabled;
                if (chineseFontSizeNumeric != null) chineseFontSizeNumeric.Enabled = enabled;
                if (westernFontComboBox != null) westernFontComboBox.Enabled = enabled;
                if (westernFontStyleComboBox != null) westernFontStyleComboBox.Enabled = enabled;
                if (westernFontSizeNumeric != null) westernFontSizeNumeric.Enabled = enabled;
                if (complexScriptFontComboBox != null) complexScriptFontComboBox.Enabled = enabled;
                if (complexScriptFontStyleComboBox != null) complexScriptFontStyleComboBox.Enabled = enabled;
                if (complexScriptFontSizeNumeric != null) complexScriptFontSizeNumeric.Enabled = enabled;
                if (fontColorButton != null) fontColorButton.Enabled = enabled;
                if (clearFontColorButton != null) clearFontColorButton.Enabled = enabled;
                if (highlightColorButton != null) highlightColorButton.Enabled = enabled;
                if (clearHighlightColorButton != null) clearHighlightColorButton.Enabled = enabled;
            }

            // 加载段落格式设置
            if (alignmentComboBox != null)
            {
                switch (formatRule.Alignment)
                {
                    case AW.ParagraphAlignment.Left:
                        alignmentComboBox.SelectedIndex = 0;
                        break;
                    case AW.ParagraphAlignment.Center:
                        alignmentComboBox.SelectedIndex = 1;
                        break;
                    case AW.ParagraphAlignment.Right:
                        alignmentComboBox.SelectedIndex = 2;
                        break;
                    case AW.ParagraphAlignment.Justify:
                        alignmentComboBox.SelectedIndex = 3;
                        break;
                    default:
                        alignmentComboBox.SelectedIndex = 0;
                        break;
                }
            }

            // 限制大纲级别索引在有效范围内
            if (outlineLevelComboBox != null)
            {
                int outlineLevelIndex = (int)formatRule.OutlineLevel;
                if (outlineLevelIndex >= 0 && outlineLevelIndex < outlineLevelComboBox.Items.Count)
                    outlineLevelComboBox.SelectedIndex = outlineLevelIndex;
                else
                    outlineLevelComboBox.SelectedIndex = 0; // 默认为正文文本
            }

            if (textDirectionComboBox != null)
            {
                switch (formatRule.TextDirection)
                {
                    case TextDirection.LeftToRight:
                        textDirectionComboBox.SelectedIndex = 0;
                        break;
                    case TextDirection.RightToLeft:
                        textDirectionComboBox.SelectedIndex = 1;
                        break;
                    default:
                        textDirectionComboBox.SelectedIndex = 0;
                        break;
                }
            }

            if (beforeSpacingNumeric != null)
                beforeSpacingNumeric.Value = (decimal)formatRule.SpaceBefore; // 从formatRule加载到UI
            if (afterSpacingNumeric != null)
                afterSpacingNumeric.Value = (decimal)formatRule.SpaceAfter; // 从formatRule加载到UI
            if (leftIndentNumeric != null)
                leftIndentNumeric.Value = (decimal)(formatRule.LeftIndent); // 直接使用磅值
            if (rightIndentNumeric != null)
                rightIndentNumeric.Value = (decimal)(formatRule.RightIndent); // 直接使用磅值

            // 加载特殊缩进设置
            if (indentTypeComboBox != null && indentValueNumeric != null && indentUnitComboBox != null)
            {
                indentTypeComboBox.SelectedIndex = (int)formatRule.SpecialIndent;

                // 默认使用字符单位显示
                var unit = IndentUnit.Characters;
                double displayValue = UnitConverter.ConvertFromPoints(formatRule.SpecialIndentValue, unit);
                indentValueNumeric.Value = (decimal)Math.Round(displayValue, 2);
                indentUnitComboBox.SelectedIndex = (int)unit;
                indentUnitComboBox.Tag = unit;
            }

            // 加载行距设置
            if (lineSpacingTypeComboBox != null && lineSpacingValueNumeric != null && lineSpacingUnitLabel != null && lineSpacingUnitComboBox != null)
            {
                // 根据formatRule的LineSpacingRule和LineSpacing来设置UI控件
                switch (formatRule.LineSpacingRule)
                {
                    case AW.LineSpacingRule.Multiple:
                        if (Math.Abs(formatRule.LineSpacing - 1.0) < 0.01)
                        {
                            lineSpacingTypeComboBox.SelectedIndex = 0; // 单倍行距
                            lineSpacingValueNumeric.Value = 1.0m;
                            lineSpacingUnitLabel.Text = "倍";
                            lineSpacingUnitLabel.Visible = true;
                            lineSpacingUnitComboBox.Visible = false;
                        }
                        else if (Math.Abs(formatRule.LineSpacing - 1.5) < 0.01)
                        {
                            lineSpacingTypeComboBox.SelectedIndex = 1; // 1.5倍行距
                            lineSpacingValueNumeric.Value = 1.5m;
                            lineSpacingUnitLabel.Text = "倍";
                            lineSpacingUnitLabel.Visible = true;
                            lineSpacingUnitComboBox.Visible = false;
                        }
                        else if (Math.Abs(formatRule.LineSpacing - 2.0) < 0.01)
                        {
                            lineSpacingTypeComboBox.SelectedIndex = 2; // 2倍行距
                            lineSpacingValueNumeric.Value = 2.0m;
                            lineSpacingUnitLabel.Text = "倍";
                            lineSpacingUnitLabel.Visible = true;
                            lineSpacingUnitComboBox.Visible = false;
                        }
                        else
                        {
                            lineSpacingTypeComboBox.SelectedIndex = 5; // 多倍行距
                            lineSpacingValueNumeric.Value = (decimal)formatRule.LineSpacing;
                            lineSpacingUnitLabel.Text = "倍";
                            lineSpacingUnitLabel.Visible = true;
                            lineSpacingUnitComboBox.Visible = false;
                        }
                        break;
                    case AW.LineSpacingRule.AtLeast:
                        lineSpacingTypeComboBox.SelectedIndex = 3; // 最小值
                        lineSpacingValueNumeric.Value = (decimal)formatRule.LineSpacing;
                        lineSpacingUnitLabel.Visible = false;
                        lineSpacingUnitComboBox.Visible = true;
                        lineSpacingUnitComboBox.SelectedIndex = 0; // 磅单位
                        lineSpacingUnitComboBox.Tag = LineSpacingUnit.Points;
                        break;
                    case AW.LineSpacingRule.Exactly:
                        lineSpacingTypeComboBox.SelectedIndex = 4; // 固定值
                        lineSpacingValueNumeric.Value = (decimal)formatRule.LineSpacing;
                        lineSpacingUnitLabel.Visible = false;
                        lineSpacingUnitComboBox.Visible = true;
                        lineSpacingUnitComboBox.SelectedIndex = 0; // 磅单位
                        lineSpacingUnitComboBox.Tag = LineSpacingUnit.Points;
                        break;
                    default:
                        lineSpacingTypeComboBox.SelectedIndex = 0; // 默认单倍行距
                        lineSpacingValueNumeric.Value = 1.0m;
                        lineSpacingUnitLabel.Text = "倍";
                        lineSpacingUnitLabel.Visible = true;
                        lineSpacingUnitComboBox.Visible = false;
                        break;
                }
            }

            // 加载边框设置
            if (hasBordersCheckBox != null && borderTypeComboBox != null && borderWidthNumeric != null &&
                borderColorPanel != null && hasTopBorderCheckBox != null && hasBottomBorderCheckBox != null &&
                hasLeftBorderCheckBox != null && hasRightBorderCheckBox != null)
            {
                hasBordersCheckBox.Checked = formatRule.HasBorders;

                // 显式映射边框类型，确保与保存逻辑一致
                borderTypeComboBox.SelectedIndex = formatRule.BorderType switch
                {
                    AW.LineStyle.Single => 0,
                    AW.LineStyle.Double => 1,
                    AW.LineStyle.DashSmallGap => 2,
                    AW.LineStyle.Dot => 3,
                    AW.LineStyle.DotDash => 4,
                    AW.LineStyle.DotDotDash => 5,
                    _ => 0
                };

                borderWidthNumeric.Value = (decimal)formatRule.BorderWidth;
                borderColorPanel.BackColor = formatRule.BorderColor;
                hasTopBorderCheckBox.Checked = formatRule.HasTopBorder;
                hasBottomBorderCheckBox.Checked = formatRule.HasBottomBorder;
                hasLeftBorderCheckBox.Checked = formatRule.HasLeftBorder;
                hasRightBorderCheckBox.Checked = formatRule.HasRightBorder;
            }

            // 加载底纹设置
            if (hasShadingCheckBox != null && shadingPatternComboBox != null && shadingColorPanel != null)
            {
                hasShadingCheckBox.Checked = formatRule.HasShading;

                // 显式映射底纹图案，确保与保存逻辑一致
                shadingPatternComboBox.SelectedIndex = formatRule.ShadingPattern switch
                {
                    AW.TextureIndex.TextureNone => 0,
                    AW.TextureIndex.TextureSolid => 1,
                    AW.TextureIndex.Texture5Percent => 2,
                    AW.TextureIndex.Texture10Percent => 3,
                    AW.TextureIndex.Texture20Percent => 4,
                    AW.TextureIndex.Texture25Percent => 5,
                    AW.TextureIndex.Texture30Percent => 6,
                    AW.TextureIndex.Texture40Percent => 7,
                    AW.TextureIndex.Texture50Percent => 8,
                    AW.TextureIndex.Texture60Percent => 9,
                    AW.TextureIndex.Texture70Percent => 10,
                    AW.TextureIndex.Texture75Percent => 11,
                    AW.TextureIndex.Texture80Percent => 12,
                    AW.TextureIndex.Texture90Percent => 13,
                    _ => 0
                };

                shadingColorPanel.BackColor = formatRule.ShadingColor; // 直接使用用户设置的颜色

                // 更新控件状态
                bool shadingEnabled = hasShadingCheckBox.Checked && enableBorderShadingCheckBox.Checked;
                shadingPatternComboBox.Enabled = shadingEnabled;
                shadingColorButton.Enabled = shadingEnabled;
                clearShadingColorButton.Enabled = shadingEnabled;
            }

            // 加载字体设置
            if (chineseFontComboBox != null && !string.IsNullOrEmpty(formatRule.ChineseFontName))
                chineseFontComboBox.Text = formatRule.ChineseFontName;
            if (chineseFontStyleComboBox != null)
                chineseFontStyleComboBox.SelectedIndex = GetFontStyleIndex(formatRule.ChineseFontStyle);
            if (chineseFontSizeNumeric != null)
                chineseFontSizeNumeric.Value = (decimal)formatRule.ChineseFontSize;
            if (westernFontComboBox != null && !string.IsNullOrEmpty(formatRule.WesternFontName))
                westernFontComboBox.Text = formatRule.WesternFontName;
            if (westernFontStyleComboBox != null)
                westernFontStyleComboBox.SelectedIndex = GetFontStyleIndex(formatRule.WesternFontStyle);
            if (westernFontSizeNumeric != null)
                westernFontSizeNumeric.Value = (decimal)formatRule.WesternFontSize;
            if (complexScriptFontComboBox != null && !string.IsNullOrEmpty(formatRule.ComplexScriptFontName))
                complexScriptFontComboBox.Text = formatRule.ComplexScriptFontName;
            if (complexScriptFontStyleComboBox != null)
                complexScriptFontStyleComboBox.SelectedIndex = GetFontStyleIndex(formatRule.ComplexScriptFontStyle);
            if (complexScriptFontSizeNumeric != null)
                complexScriptFontSizeNumeric.Value = (decimal)formatRule.ComplexScriptFontSize;

            // 正确加载字体颜色和高亮颜色
            if (fontColorPanel != null)
            {
                // 如果没有设置颜色值或者颜色值为空,使用默认的黑色
                if (!formatRule.FontColor.HasValue || formatRule.FontColor.Value == Color.Empty)
                {
                    fontColorPanel.BackColor = Color.Black;
                    // 不要在LoadFromRule中修改formatRule对象，只设置UI
                }
                else
                {
                    fontColorPanel.BackColor = formatRule.FontColor.Value;
                }
            }

            if (highlightColorPanel != null)
            {
                // 如果没有设置高亮颜色或者颜色值为空,使用默认的透明色
                if (!formatRule.HighlightColor.HasValue || formatRule.HighlightColor.Value == Color.Empty)
                {
                    highlightColorPanel.BackColor = Color.Transparent;
                    // 不要在LoadFromRule中修改formatRule对象，只设置UI
                }
                else
                {
                    highlightColorPanel.BackColor = formatRule.HighlightColor.Value;
                }
            }

            // 加载分页和连接控制
            if (keepWithNextCheckBox != null)
                keepWithNextCheckBox.Checked = formatRule.KeepWithNext;
            if (keepLinesTogetherCheckBox != null)
                keepLinesTogetherCheckBox.Checked = formatRule.KeepLinesTogether;
            if (pageBreakBeforeCheckBox != null)
                pageBreakBeforeCheckBox.Checked = formatRule.PageBreakBefore;
            if (widowOrphanControlCheckBox != null)
                widowOrphanControlCheckBox.Checked = formatRule.WidowOrphanControl;
            if (noSpaceBetweenParagraphsCheckBox != null)
                noSpaceBetweenParagraphsCheckBox.Checked = formatRule.NoSpaceBetweenParagraphs;

            // 加载制表位设置
            if (tabStopsListView != null)
            {
                tabStopsListView.Items.Clear();
                if (formatRule.TabStops != null)
                {
                    foreach (var tabStop in formatRule.TabStops)
                    {
                        if (tabStop != null)
                        {
                            var item = new ListViewItem(new string[]
                            {
                                tabStop.Position.ToString("F2"),
                                GetTabAlignmentText(tabStop.Alignment),
                                GetTabLeaderText(tabStop.Leader)
                            });

                            item.Tag = tabStop; // 将TabStop对象附加到列表项
                            tabStopsListView.Items.Add(item);
                        }
                    }
                }
            }

            // 加载图片行距控制设置
            if (adjustImageLineSpacingCheckBox != null)
                adjustImageLineSpacingCheckBox.Checked = formatRule.AdjustImageLineSpacing;

            // 加载图片格式主开关设置
            if (enableImageFormatCheckBox != null)
                enableImageFormatCheckBox.Checked = formatRule.EnableImageFormat;

            // 加载图片格式启用控制设置
            if (enableImageSizeCheckBox != null)
                enableImageSizeCheckBox.Checked = formatRule.EnableImageSize;
            if (enableImageWrapCheckBox != null)
                enableImageWrapCheckBox.Checked = formatRule.EnableImageWrap;
            if (enableImageEffectCheckBox != null)
                enableImageEffectCheckBox.Checked = formatRule.EnableImageEffect;
            if (enableBrightnessCheckBox != null)
                enableBrightnessCheckBox.Checked = formatRule.EnableBrightness;
            if (enableContrastCheckBox != null)
                enableContrastCheckBox.Checked = formatRule.EnableContrast;
            if (enableTransparencyCheckBox != null)
                enableTransparencyCheckBox.Checked = formatRule.EnableTransparency;
            if (enableColorModeCheckBox != null)
                enableColorModeCheckBox.Checked = formatRule.EnableColorMode;

            // 加载图片格式设置
            if (preserveAspectRatioCheckBox != null)
                preserveAspectRatioCheckBox.Checked = formatRule.PreserveAspectRatio;

            if (useExactSizeRadio != null && imageWidthNumeric != null && imageHeightNumeric != null &&
                useScaleRadio != null && imageScaleXNumeric != null && imageScaleYNumeric != null)
            {
                if (formatRule.ImageWidth > 0 || formatRule.ImageHeight > 0)
                {
                    useExactSizeRadio.Checked = true;
                    useScaleRadio.Checked = false;
                    imageWidthNumeric.Value = (decimal)Math.Max(0, Math.Min(5000, formatRule.ImageWidth));
                    imageHeightNumeric.Value = (decimal)Math.Max(0, Math.Min(5000, formatRule.ImageHeight));
                }
                else
                {
                    useExactSizeRadio.Checked = false;
                    useScaleRadio.Checked = true;
                    imageScaleXNumeric.Value = (decimal)Math.Max(1, Math.Min(500, formatRule.ImageScaleX));
                    imageScaleYNumeric.Value = (decimal)Math.Max(1, Math.Min(500, formatRule.ImageScaleY));
                }
            }

            // 环绕设置
            if (imageWrapTypeComboBox != null)
            {
                switch (formatRule.ImageWrapType)
                {
                    case AW.Drawing.WrapType.Inline:
                        imageWrapTypeComboBox.SelectedIndex = 0; // 嵌入式
                        break;
                    case AW.Drawing.WrapType.Square:
                        imageWrapTypeComboBox.SelectedIndex = 1; // 四周型
                        break;
                    case AW.Drawing.WrapType.Tight:
                        imageWrapTypeComboBox.SelectedIndex = 2; // 紧密型
                        break;
                    case AW.Drawing.WrapType.Through:
                        imageWrapTypeComboBox.SelectedIndex = 3; // 通过型
                        break;
                    case AW.Drawing.WrapType.TopBottom:
                        imageWrapTypeComboBox.SelectedIndex = 4; // 上下型
                        break;
                    default:
                        imageWrapTypeComboBox.SelectedIndex = 0; // 默认嵌入式
                        break;
                }
            }

            if (wrapTextNormalRadio != null && wrapTextBehindRadio != null && wrapTextFrontRadio != null)
            {
                wrapTextNormalRadio.Checked = !formatRule.WrapTextBehind && !formatRule.WrapTextFront;
                wrapTextBehindRadio.Checked = formatRule.WrapTextBehind;
                wrapTextFrontRadio.Checked = formatRule.WrapTextFront;
            }

            // 图片效果设置
            if (brightnessTrackBar != null && brightnessValueLabel != null)
            {
                brightnessTrackBar.Value = (int)Math.Max(brightnessTrackBar.Minimum, Math.Min(brightnessTrackBar.Maximum, formatRule.ImageBrightness));
                brightnessValueLabel.Text = $"{brightnessTrackBar.Value}%";
            }

            if (contrastTrackBar != null && contrastValueLabel != null)
            {
                contrastTrackBar.Value = (int)Math.Max(contrastTrackBar.Minimum, Math.Min(contrastTrackBar.Maximum, formatRule.ImageContrast));
                contrastValueLabel.Text = $"{contrastTrackBar.Value}%";
            }

            if (transparencyTrackBar != null && transparencyValueLabel != null)
            {
                // ImageTransparency本身已经是0-100范围，无需乘以100
                int transparency = (int)Math.Max(transparencyTrackBar.Minimum, Math.Min(transparencyTrackBar.Maximum, formatRule.ImageTransparency));
                transparencyTrackBar.Value = transparency;
                transparencyValueLabel.Text = $"{transparency}%";
            }

            if (colorModeComboBox != null)
            {
                switch (formatRule.ImageColorMode)
                {
                    case ImageColorMode.Color:
                        colorModeComboBox.SelectedIndex = 0; // 自动
                        break;
                    case ImageColorMode.Grayscale:
                        colorModeComboBox.SelectedIndex = 1; // 灰度
                        break;
                    case ImageColorMode.BlackWhite:
                        colorModeComboBox.SelectedIndex = 2; // 黑白
                        break;
                    default:
                        colorModeComboBox.SelectedIndex = 0; // 默认自动
                        break;
                }
            }

            // 设置裁剪
            if (enableCropCheckBox != null)
            {
                enableCropCheckBox.Checked = formatRule.EnableImageCrop;

                bool cropEnabled = enableCropCheckBox.Checked;
                if (cropTopNumeric != null) cropTopNumeric.Enabled = cropEnabled;
                if (cropBottomNumeric != null) cropBottomNumeric.Enabled = cropEnabled;
                if (cropLeftNumeric != null) cropLeftNumeric.Enabled = cropEnabled;
                if (cropRightNumeric != null) cropRightNumeric.Enabled = cropEnabled;

                if (cropTopNumeric != null) cropTopNumeric.Value = (decimal)formatRule.CropTop;
                if (cropBottomNumeric != null) cropBottomNumeric.Value = (decimal)formatRule.CropBottom;
                if (cropLeftNumeric != null) cropLeftNumeric.Value = (decimal)formatRule.CropLeft;
                if (cropRightNumeric != null) cropRightNumeric.Value = (decimal)formatRule.CropRight;
            }

            // 加载图片边框设置
            if (enableImageBorderCheckBox != null)
            {
                enableImageBorderCheckBox.Checked = formatRule.EnableImageBorder;

                bool borderEnabled = enableImageBorderCheckBox.Checked;
                if (imageBorderStyleComboBox != null) imageBorderStyleComboBox.Enabled = borderEnabled;
                if (imageBorderWidthNumeric != null) imageBorderWidthNumeric.Enabled = borderEnabled;
                if (imageBorderColorButton != null) imageBorderColorButton.Enabled = borderEnabled;
                if (clearImageBorderColorButton != null) clearImageBorderColorButton.Enabled = borderEnabled;

                if (imageBorderStyleComboBox != null)
                {
                    switch (formatRule.ImageBorderStyle)
                    {
                        case AW.LineStyle.Single:
                            imageBorderStyleComboBox.SelectedIndex = 0; // 实线
                            break;
                        case AW.LineStyle.Double:
                            imageBorderStyleComboBox.SelectedIndex = 1; // 双线
                            break;
                        case AW.LineStyle.DashSmallGap:
                            imageBorderStyleComboBox.SelectedIndex = 2; // 虚线
                            break;
                        case AW.LineStyle.Dot:
                            imageBorderStyleComboBox.SelectedIndex = 3; // 点线
                            break;
                        case AW.LineStyle.DotDash:
                            imageBorderStyleComboBox.SelectedIndex = 4; // 点划线
                            break;
                        case AW.LineStyle.DotDotDash:
                            imageBorderStyleComboBox.SelectedIndex = 5; // 双点划线
                            break;
                        default:
                            imageBorderStyleComboBox.SelectedIndex = 0; // 默认实线
                            break;
                    }
                }

                // 设置边框宽度
                if (imageBorderWidthNumeric != null)
                    imageBorderWidthNumeric.Value = (decimal)formatRule.ImageBorderWidth;

                // 设置边框颜色
                if (imageBorderColorPanel != null)
                    imageBorderColorPanel.BackColor = formatRule.ImageBorderColor;
            }

            // 设置位置
            if (enableImagePositionCheckBox != null)
                enableImagePositionCheckBox.Checked = formatRule.EnableImagePosition;

            if (horizontalRelativeComboBox != null)
            {
                switch (horizontalRelativeComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.HorizontalRelativePosition = AW.Drawing.RelativeHorizontalPosition.Margin;
                        break;
                    case 1:
                        formatRule.HorizontalRelativePosition = AW.Drawing.RelativeHorizontalPosition.Page;
                        break;
                    case 2:
                        formatRule.HorizontalRelativePosition = AW.Drawing.RelativeHorizontalPosition.Column;
                        break;
                    case 3:
                        formatRule.HorizontalRelativePosition = AW.Drawing.RelativeHorizontalPosition.Character;
                        break;
                    default:
                        formatRule.HorizontalRelativePosition = AW.Drawing.RelativeHorizontalPosition.Column;
                        break;
                }
            }

            if (verticalRelativeComboBox != null)
            {
                switch (verticalRelativeComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.VerticalRelativePosition = AW.Drawing.RelativeVerticalPosition.Margin;
                        break;
                    case 1:
                        formatRule.VerticalRelativePosition = AW.Drawing.RelativeVerticalPosition.Page;
                        break;
                    case 2:
                        formatRule.VerticalRelativePosition = AW.Drawing.RelativeVerticalPosition.Paragraph;
                        break;
                    case 3:
                        formatRule.VerticalRelativePosition = AW.Drawing.RelativeVerticalPosition.Line;
                        break;
                    default:
                        formatRule.VerticalRelativePosition = AW.Drawing.RelativeVerticalPosition.Paragraph;
                        break;
                }
            }

            if (horizontalAlignmentComboBox != null)
            {
                switch (formatRule.HorizontalAlignment)
                {
                    case AW.Drawing.HorizontalAlignment.Left:
                        horizontalAlignmentComboBox.SelectedIndex = 0;
                        break;
                    case AW.Drawing.HorizontalAlignment.Center:
                        horizontalAlignmentComboBox.SelectedIndex = 1;
                        break;
                    case AW.Drawing.HorizontalAlignment.Right:
                        horizontalAlignmentComboBox.SelectedIndex = 2;
                        break;
                    default:
                        horizontalAlignmentComboBox.SelectedIndex = 0;
                        break;
                }
            }

            if (verticalAlignmentComboBox != null)
            {
                switch (formatRule.VerticalAlignment)
                {
                    case AW.Drawing.VerticalAlignment.Top:
                        verticalAlignmentComboBox.SelectedIndex = 0;
                        break;
                    case AW.Drawing.VerticalAlignment.Center:
                        verticalAlignmentComboBox.SelectedIndex = 1;
                        break;
                    case AW.Drawing.VerticalAlignment.Bottom:
                        verticalAlignmentComboBox.SelectedIndex = 2;
                        break;
                    default:
                        verticalAlignmentComboBox.SelectedIndex = 0;
                        break;
                }
            }

            if (horizontalPositionNumeric != null)
                horizontalPositionNumeric.Value = (decimal)formatRule.HorizontalPosition;

            if (verticalPositionNumeric != null)
                verticalPositionNumeric.Value = (decimal)formatRule.VerticalPosition;

            // 更新位置控件状态
            UpdatePositionControlsState();

            // 图片旋转和翻转设置
            if (enableImageRotateFlipCheckBox != null)
                enableImageRotateFlipCheckBox.Checked = formatRule.EnableImageRotateFlip;

            if (rotationAngleNumeric != null)
                rotationAngleNumeric.Value = (decimal)formatRule.RotationAngle;

            if (flipHorizontalCheckBox != null)
                flipHorizontalCheckBox.Checked = formatRule.FlipHorizontal;

            if (flipVerticalCheckBox != null)
                flipVerticalCheckBox.Checked = formatRule.FlipVertical;

            // 加载锁定设置
            if (enableImageLockingCheckBox != null)
            {
                enableImageLockingCheckBox.Checked = formatRule.EnableImageLocking;

                if (lockPositionCheckBox != null)
                    lockPositionCheckBox.Checked = formatRule.LockPosition;

                if (lockAspectRatioCheckBox != null)
                    lockAspectRatioCheckBox.Checked = formatRule.LockAspectRatio;

                if (lockFormattingCheckBox != null)
                    lockFormattingCheckBox.Checked = formatRule.LockFormatting;
            }

            // 加载超链接设置
            if (enableImageHyperlinkCheckBox != null)
            {
                enableImageHyperlinkCheckBox.Checked = formatRule.EnableImageHyperlink;

                if (hyperlinkTypeComboBox != null)
                {
                    switch (formatRule.HyperlinkType)
                    {
                        case HyperlinkType.Url:
                            hyperlinkTypeComboBox.SelectedIndex = 0; // URL
                            break;
                        case HyperlinkType.Bookmark:
                            hyperlinkTypeComboBox.SelectedIndex = 1; // 书签
                            break;
                        case HyperlinkType.File:
                            hyperlinkTypeComboBox.SelectedIndex = 2; // 文件
                            break;
                        default:
                            hyperlinkTypeComboBox.SelectedIndex = 0; // 默认URL
                            break;
                    }
                }

                if (hyperlinkUrlTextBox != null)
                    hyperlinkUrlTextBox.Text = formatRule.HyperlinkUrl;

                if (bookmarkNameTextBox != null)
                    bookmarkNameTextBox.Text = formatRule.BookmarkName;

                if (hyperlinkToolTipTextBox != null)
                    hyperlinkToolTipTextBox.Text = formatRule.HyperlinkToolTip;

                if (openInNewWindowCheckBox != null)
                    openInNewWindowCheckBox.Checked = formatRule.OpenHyperlinkInNewWindow;
            }

            // 水印设置
            if (enableWatermarkCheckBox != null)
            {
                enableWatermarkCheckBox.Checked = formatRule.EnableWatermark;

                bool watermarkEnabled = enableWatermarkCheckBox.Checked;
                if (watermarkTextBox != null) watermarkTextBox.Enabled = watermarkEnabled;
                if (watermarkFontFamilyComboBox != null) watermarkFontFamilyComboBox.Enabled = watermarkEnabled;
                if (watermarkFontSizeNumeric != null) watermarkFontSizeNumeric.Enabled = watermarkEnabled;
                if (watermarkColorButton != null) watermarkColorButton.Enabled = watermarkEnabled;
                if (clearWatermarkColorButton != null) clearWatermarkColorButton.Enabled = watermarkEnabled;
                if (watermarkOpacityTrackBar != null) watermarkOpacityTrackBar.Enabled = watermarkEnabled;
                if (watermarkRotationAngleNumeric != null) watermarkRotationAngleNumeric.Enabled = watermarkEnabled;

                if (watermarkTextBox != null) watermarkTextBox.Text = formatRule.WatermarkText;
                if (watermarkFontFamilyComboBox != null) watermarkFontFamilyComboBox.Text = formatRule.WatermarkFontFamily;
                if (watermarkFontSizeNumeric != null) watermarkFontSizeNumeric.Value = (decimal)formatRule.WatermarkFontSize;
                if (watermarkColorPanel != null) watermarkColorPanel.BackColor = formatRule.WatermarkColor;

                // 限制水印不透明度的值在有效范围内
                double opacity = Math.Max(0, Math.Min(1, formatRule.WatermarkOpacity));
                if (watermarkOpacityTrackBar != null) watermarkOpacityTrackBar.Value = (int)(opacity * 100);
                if (watermarkOpacityValueLabel != null) watermarkOpacityValueLabel.Text = $"{opacity * 100}%";

                if (watermarkRotationAngleNumeric != null) watermarkRotationAngleNumeric.Value = (decimal)formatRule.WatermarkRotationAngle;
            }

            // 图片水印设置
            if (enableImageWatermarkCheckBox != null)
            {
                enableImageWatermarkCheckBox.Checked = formatRule.EnableImageWatermark;

                bool imageWatermarkEnabled = enableImageWatermarkCheckBox.Checked;
                if (watermarkImagePathTextBox != null) watermarkImagePathTextBox.Enabled = imageWatermarkEnabled;
                if (browseImageButton != null) browseImageButton.Enabled = imageWatermarkEnabled;
                if (imageWatermarkLayoutComboBox != null) imageWatermarkLayoutComboBox.Enabled = imageWatermarkEnabled;

                if (watermarkImagePathTextBox != null) watermarkImagePathTextBox.Text = formatRule.WatermarkImagePath;

                // 设置默认布局
                if (imageWatermarkLayoutComboBox != null)
                {
                    switch (formatRule.ImageWatermarkLayout)
                    {
                        case AW.WatermarkLayout.Diagonal:
                            imageWatermarkLayoutComboBox.SelectedIndex = 0;
                            break;
                        case AW.WatermarkLayout.Horizontal:
                            imageWatermarkLayoutComboBox.SelectedIndex = 1;
                            break;
                        default:
                            imageWatermarkLayoutComboBox.SelectedIndex = 0;
                            break;
                    }
                }
            }

            // 水印位置设置
            if (enableWatermarkPositionCheckBox != null)
            {
                enableWatermarkPositionCheckBox.Checked = formatRule.EnableWatermarkPosition;

                bool watermarkPositionEnabled = enableWatermarkPositionCheckBox.Checked;
                if (watermarkPositionCenterRadio != null) watermarkPositionCenterRadio.Enabled = watermarkPositionEnabled;
                if (watermarkPositionCustomRadio != null) watermarkPositionCustomRadio.Enabled = watermarkPositionEnabled;

                if (watermarkPositionCenterRadio != null) watermarkPositionCenterRadio.Checked = formatRule.WatermarkAtPageCenter;
                if (watermarkPositionCustomRadio != null) watermarkPositionCustomRadio.Checked = !formatRule.WatermarkAtPageCenter;

                UpdateWatermarkPositionControlsState();

                if (watermarkHorizontalPositionNumeric != null) watermarkHorizontalPositionNumeric.Value = (decimal)formatRule.WatermarkHorizontalPosition;
                if (watermarkVerticalPositionNumeric != null) watermarkVerticalPositionNumeric.Value = (decimal)formatRule.WatermarkVerticalPosition;

                if (watermarkAllPagesRadio != null) watermarkAllPagesRadio.Checked = formatRule.WatermarkOnAllPages;
                if (watermarkSpecificPagesRadio != null) watermarkSpecificPagesRadio.Checked = !formatRule.WatermarkOnAllPages;
                if (watermarkPageRangeTextBox != null) watermarkPageRangeTextBox.Text = formatRule.WatermarkPageRange;
                if (watermarkBehindContentCheckBox != null) watermarkBehindContentCheckBox.Checked = formatRule.WatermarkBehindContent;
            }

            // 水印格式设置
            if (enableWatermarkFormatCheckBox != null)
            {
                enableWatermarkFormatCheckBox.Checked = formatRule.EnableWatermarkFormat;

                bool formatEnabled = enableWatermarkFormatCheckBox.Checked;
                if (watermarkHasBorderCheckBox != null) watermarkHasBorderCheckBox.Enabled = formatEnabled;
                if (watermarkBorderStyleComboBox != null && watermarkHasBorderCheckBox != null)
                    watermarkBorderStyleComboBox.Enabled = formatEnabled && watermarkHasBorderCheckBox.Checked;
                if (watermarkBorderWidthNumeric != null && watermarkHasBorderCheckBox != null)
                    watermarkBorderWidthNumeric.Enabled = formatEnabled && watermarkHasBorderCheckBox.Checked;
                if (watermarkBorderColorButton != null && watermarkHasBorderCheckBox != null)
                    watermarkBorderColorButton.Enabled = formatEnabled && watermarkHasBorderCheckBox.Checked;
                if (clearWatermarkBorderColorButton != null && watermarkHasBorderCheckBox != null)
                    clearWatermarkBorderColorButton.Enabled = formatEnabled && watermarkHasBorderCheckBox.Checked;
                if (watermarkWrapTypeComboBox != null)
                    watermarkWrapTypeComboBox.Enabled = formatEnabled;
                if (lockWatermarkCheckBox != null)
                    lockWatermarkCheckBox.Enabled = formatEnabled;
                if (showWatermarkInPrintCheckBox != null)
                    showWatermarkInPrintCheckBox.Enabled = formatEnabled;

                if (watermarkHasBorderCheckBox != null) watermarkHasBorderCheckBox.Checked = formatRule.WatermarkHasBorder;
                if (watermarkBorderStyleComboBox != null)
                    watermarkBorderStyleComboBox.SelectedIndex = (int)formatRule.WatermarkBorderStyle;
                if (watermarkBorderWidthNumeric != null)
                    watermarkBorderWidthNumeric.Value = (decimal)formatRule.WatermarkBorderWidth;
                if (watermarkBorderColorPanel != null)
                    watermarkBorderColorPanel.BackColor = formatRule.WatermarkBorderColor;

                if (watermarkWrapTypeComboBox != null)
                {
                    switch (formatRule.WatermarkWrapType)
                    {
                        case AW.Drawing.WrapType.Inline:
                            watermarkWrapTypeComboBox.SelectedIndex = 0;
                            break;
                        case AW.Drawing.WrapType.Square:
                            watermarkWrapTypeComboBox.SelectedIndex = 1;
                            break;
                        case AW.Drawing.WrapType.Tight:
                            watermarkWrapTypeComboBox.SelectedIndex = 2;
                            break;
                        case AW.Drawing.WrapType.Through:
                            watermarkWrapTypeComboBox.SelectedIndex = 3;
                            break;
                        case AW.Drawing.WrapType.TopBottom:
                            watermarkWrapTypeComboBox.SelectedIndex = 4;
                            break;
                        default:
                            watermarkWrapTypeComboBox.SelectedIndex = 0;
                            break;
                    }
                }

                if (lockWatermarkCheckBox != null) lockWatermarkCheckBox.Checked = formatRule.LockWatermark;
                if (showWatermarkInPrintCheckBox != null) showWatermarkInPrintCheckBox.Checked = formatRule.ShowWatermarkInPrint;
            }

            // 在LoadSettings()方法中添加表格格式的加载代码
            // 在这个方法大约1400行左右，需要添加以下代码，可以放在加载水印格式相关设置之后

            // 加载表格宽度自适应设置
            if (enableTableAutoFitCheckBox != null)
                enableTableAutoFitCheckBox.Checked = formatRule.EnableTableAutoFit;

            if (autoFitToContentsRadio != null)
                autoFitToContentsRadio.Checked = formatRule.AutoFitToContents;
            if (autoFitToWindowRadio != null)
                autoFitToWindowRadio.Checked = formatRule.AutoFitToWindow;
            if (useFixedColumnWidthRadio != null)
                useFixedColumnWidthRadio.Checked = formatRule.UseFixedColumnWidth;
            if (preferredTableWidthNumeric != null)
                preferredTableWidthNumeric.Value = (decimal)formatRule.PreferredTableWidth;

            // 加载表格布局设置
            if (enableTableLayoutCheckBox != null)
                enableTableLayoutCheckBox.Checked = formatRule.EnableTableLayout;

            if (tableAlignmentComboBox != null)
            {
                switch (formatRule.TableAlignment)
                {
                    case AW.Tables.TableAlignment.Left:
                        tableAlignmentComboBox.SelectedIndex = 0;
                        break;
                    case AW.Tables.TableAlignment.Center:
                        tableAlignmentComboBox.SelectedIndex = 1;
                        break;
                    case AW.Tables.TableAlignment.Right:
                        tableAlignmentComboBox.SelectedIndex = 2;
                        break;
                    default:
                        tableAlignmentComboBox.SelectedIndex = 0;
                        break;
                }
            }

            if (tableLeftIndentNumeric != null)
                tableLeftIndentNumeric.Value = (decimal)formatRule.TableLeftIndent;
            if (tableRightIndentNumeric != null)
                tableRightIndentNumeric.Value = (decimal)formatRule.TableRightIndent;

            if (tableTextWrappingComboBox != null)
            {
                switch (formatRule.TableTextWrapping)
                {
                    case AW.Tables.TextWrapping.Around:
                        tableTextWrappingComboBox.SelectedIndex = 0;
                        break;
                    case AW.Tables.TextWrapping.None:
                        tableTextWrappingComboBox.SelectedIndex = 1;
                        break;
                    default:
                        tableTextWrappingComboBox.SelectedIndex = 0;
                        break;
                }
            }

            if (tableAllowAutoFitCheckBox != null)
                tableAllowAutoFitCheckBox.Checked = formatRule.TableAllowAutoFit;
            if (tableDefaultCellSpacingNumeric != null)
                tableDefaultCellSpacingNumeric.Value = (decimal)formatRule.TableDefaultCellSpacing;

            // 加载表格样式设置
            if (enableTableStyleCheckBox != null)
                enableTableStyleCheckBox.Checked = formatRule.EnableTableStyle;

            if (tableHasBordersCheckBox != null)
                tableHasBordersCheckBox.Checked = formatRule.TableHasBorders;
            if (tableBorderStyleComboBox != null)
            {
                switch (formatRule.TableBorderStyle)
                {
                    case AW.LineStyle.Single:
                        tableBorderStyleComboBox.SelectedIndex = 0;
                        break;
                    case AW.LineStyle.Double:
                        tableBorderStyleComboBox.SelectedIndex = 1;
                        break;
                    case AW.LineStyle.DashSmallGap:
                        tableBorderStyleComboBox.SelectedIndex = 2;
                        break;
                    case AW.LineStyle.Dot:
                        tableBorderStyleComboBox.SelectedIndex = 3;
                        break;
                    default:
                        tableBorderStyleComboBox.SelectedIndex = 0;
                        break;
                }
            }
            if (tableBorderWidthNumeric != null)
                tableBorderWidthNumeric.Value = (decimal)formatRule.TableBorderWidth;
            if (tableBorderColorPanel != null)
                tableBorderColorPanel.BackColor = formatRule.TableBorderColor;

            if (tableHasGridlinesCheckBox != null)
                tableHasGridlinesCheckBox.Checked = formatRule.TableHasGridlines;
            if (tableGridlineStyleComboBox != null)
            {
                switch (formatRule.TableGridlineStyle)
                {
                    case AW.LineStyle.Single:
                        tableGridlineStyleComboBox.SelectedIndex = 0;
                        break;
                    case AW.LineStyle.Double:
                        tableGridlineStyleComboBox.SelectedIndex = 1;
                        break;
                    case AW.LineStyle.DashSmallGap:
                        tableGridlineStyleComboBox.SelectedIndex = 2;
                        break;
                    case AW.LineStyle.Dot:
                        tableGridlineStyleComboBox.SelectedIndex = 3;
                        break;
                    default:
                        tableGridlineStyleComboBox.SelectedIndex = 0;
                        break;
                }
            }
            if (tableGridlineWidthNumeric != null)
                tableGridlineWidthNumeric.Value = (decimal)formatRule.TableGridlineWidth;
            if (tableGridlineColorPanel != null)
                tableGridlineColorPanel.BackColor = formatRule.TableGridlineColor;

            if (tableHasShadingCheckBox != null)
                tableHasShadingCheckBox.Checked = formatRule.TableHasShading;
            if (tableShadingColorPanel != null)
                tableShadingColorPanel.BackColor = formatRule.TableShadingColor;

            // 加载单元格格式设置
            if (enableCellFormatCheckBox != null)
                enableCellFormatCheckBox.Checked = formatRule.EnableCellFormat;

            if (cellverticalAlignmentComboBox != null)
            {
                switch (formatRule.CellVerticalAlignment)
                {
                    case AW.Tables.CellVerticalAlignment.Top:
                        cellverticalAlignmentComboBox.SelectedIndex = 0;
                        break;
                    case AW.Tables.CellVerticalAlignment.Center:
                        cellverticalAlignmentComboBox.SelectedIndex = 1;
                        break;
                    case AW.Tables.CellVerticalAlignment.Bottom:
                        cellverticalAlignmentComboBox.SelectedIndex = 2;
                        break;
                    default:
                        cellverticalAlignmentComboBox.SelectedIndex = 0;
                        break;
                }
            }

            if (cellMarginTopNumeric != null)
                cellMarginTopNumeric.Value = (decimal)formatRule.CellMarginTop;
            if (cellMarginBottomNumeric != null)
                cellMarginBottomNumeric.Value = (decimal)formatRule.CellMarginBottom;
            if (cellMarginLeftNumeric != null)
                cellMarginLeftNumeric.Value = (decimal)formatRule.CellMarginLeft;
            if (cellMarginRightNumeric != null)
                cellMarginRightNumeric.Value = (decimal)formatRule.CellMarginRight;

            if (cellHasShadingCheckBox != null)
                cellHasShadingCheckBox.Checked = formatRule.CellHasShading;
            if (cellShadingColorPanel != null)
                cellShadingColorPanel.BackColor = formatRule.CellShadingColor;

            if (cellHasBordersCheckBox != null)
                cellHasBordersCheckBox.Checked = formatRule.CellHasBorders;
            if (cellBorderStyleComboBox != null)
            {
                switch (formatRule.CellBorderStyle)
                {
                    case AW.LineStyle.Single:
                        cellBorderStyleComboBox.SelectedIndex = 0;
                        break;
                    case AW.LineStyle.Double:
                        cellBorderStyleComboBox.SelectedIndex = 1;
                        break;
                    case AW.LineStyle.DashSmallGap:
                        cellBorderStyleComboBox.SelectedIndex = 2;
                        break;
                    case AW.LineStyle.Dot:
                        cellBorderStyleComboBox.SelectedIndex = 3;
                        break;
                    default:
                        cellBorderStyleComboBox.SelectedIndex = 0;
                        break;
                }
            }
            if (cellBorderWidthNumeric != null)
                cellBorderWidthNumeric.Value = (decimal)formatRule.CellBorderWidth;
            if (cellBorderColorPanel != null)
                cellBorderColorPanel.BackColor = formatRule.CellBorderColor;

            // 更新表格相关控件状态
            if (tableTabPage != null && tableTabPage.Controls.Count > 0)
            {
                UpdateTableControlsState();
            }

            // 在LoadSettings()方法的最后，调用更新方法确保所有控件状态正确
            UpdateBasicFormatControlsEnabled();
            UpdateFontFormatControlsEnabled();
            UpdateImageSizeControlsEnabled();
            UpdateImageWrapControlsEnabled();
            UpdateImageEffectControlsEnabled();
        }

        // 根据文本获取制表位对齐方式
        private AW.TabAlignment GetTabAlignmentFromText(string text)
        {
            switch (text)
            {
                case "左对齐":
                    return AW.TabAlignment.Left;
                case "居中对齐":
                    return AW.TabAlignment.Center;
                case "右对齐":
                    return AW.TabAlignment.Right;
                case "小数点对齐":
                    return AW.TabAlignment.Decimal;
                case "竖线对齐":
                    return AW.TabAlignment.Bar;
                default:
                    return AW.TabAlignment.Left;
            }
        }

        // 获取制表位前导符的文本描述
        private string GetTabLeaderText(AW.TabLeader leader)
        {
            switch (leader)
            {
                case AW.TabLeader.None:
                    return "无";
                case AW.TabLeader.Dots:
                    return "点号";
                case AW.TabLeader.Dashes:
                    return "短划线";
                case AW.TabLeader.Line:
                    return "下划线";
                default:
                    return "无";
            }
        }

        // 保存设置到格式规则对象
        private void SaveSettings()
        {
            // 保存区域启用状态
            if (enableBasicFormatCheckBox != null)
                formatRule.EnableBasicFormat = enableBasicFormatCheckBox.Checked;
            if (enableBorderShadingCheckBox != null)
                formatRule.EnableBorderShading = enableBorderShadingCheckBox.Checked;
            if (enablePaginationCheckBox != null)
                formatRule.EnablePagination = enablePaginationCheckBox.Checked;
            if (enableTabStopsCheckBox != null)
                formatRule.EnableTabStops = enableTabStopsCheckBox.Checked;
            if (enableFontFormatCheckBox != null)
                formatRule.EnableFontFormat = enableFontFormatCheckBox.Checked;

            // 保存基本段落格式子功能启用状态
            if (enableAlignmentCheckBox != null)
                formatRule.EnableAlignment = enableAlignmentCheckBox.Checked;
            if (enableOutlineLevelCheckBox != null)
                formatRule.EnableOutlineLevel = enableOutlineLevelCheckBox.Checked;
            if (enableTextDirectionCheckBox != null)
                formatRule.EnableTextDirection = enableTextDirectionCheckBox.Checked;
            if (enableIndentCheckBox != null)
                formatRule.EnableIndent = enableIndentCheckBox.Checked;
            if (enableSpacingCheckBox != null)
                formatRule.EnableSpacing = enableSpacingCheckBox.Checked;

            // 保存字体格式子功能启用状态
            if (enableChineseFontCheckBox != null)
                formatRule.EnableChineseFont = enableChineseFontCheckBox.Checked;
            if (enableWesternFontCheckBox != null)
                formatRule.EnableWesternFont = enableWesternFontCheckBox.Checked;
            if (enableComplexScriptFontCheckBox != null)
                formatRule.EnableComplexScriptFont = enableComplexScriptFontCheckBox.Checked;
            if (enableFontColorCheckBox != null)
                formatRule.EnableFontColor = enableFontColorCheckBox.Checked;
            if (enableHighlightColorCheckBox != null)
                formatRule.EnableHighlightColor = enableHighlightColorCheckBox.Checked;

            // 保存段落设置
            if (alignmentComboBox != null)
                formatRule.Alignment = (AW.ParagraphAlignment)alignmentComboBox.SelectedIndex;
            if (outlineLevelComboBox != null)
                formatRule.OutlineLevel = (AW.OutlineLevel)outlineLevelComboBox.SelectedIndex;
            if (textDirectionComboBox != null)
            {
                // 将TextDirection转换为Bidi属性
                formatRule.TextDirection = (TextDirection)textDirectionComboBox.SelectedIndex;
                formatRule.Bidi = textDirectionComboBox.SelectedIndex == 1; // 1表示从右到左
            }
            if (beforeSpacingNumeric != null)
                formatRule.SpaceBefore = (double)beforeSpacingNumeric.Value; // 直接使用磅值
            if (afterSpacingNumeric != null)
                formatRule.SpaceAfter = (double)afterSpacingNumeric.Value; // 直接使用磅值

            // 保存换行与分页设置
            if (keepWithNextCheckBox != null)
                formatRule.KeepWithNext = keepWithNextCheckBox.Checked;
            if (keepLinesTogetherCheckBox != null)
                formatRule.KeepLinesTogether = keepLinesTogetherCheckBox.Checked;
            if (pageBreakBeforeCheckBox != null)
                formatRule.PageBreakBefore = pageBreakBeforeCheckBox.Checked;
            if (widowOrphanControlCheckBox != null)
                formatRule.WidowOrphanControl = widowOrphanControlCheckBox.Checked;
            if (noSpaceBetweenParagraphsCheckBox != null)
                formatRule.NoSpaceBetweenParagraphs = noSpaceBetweenParagraphsCheckBox.Checked;

            // 保存缩进设置
            if (leftIndentNumeric != null)
                formatRule.LeftIndent = (double)leftIndentNumeric.Value; // 直接使用磅值
            if (rightIndentNumeric != null)
                formatRule.RightIndent = (double)rightIndentNumeric.Value; // 直接使用磅值

            // 保存特殊缩进设置
            if (indentTypeComboBox != null)
                formatRule.SpecialIndent = (AsposeWordFormatter.Models.SpecialIndent)indentTypeComboBox.SelectedIndex;
            if (indentValueNumeric != null && indentUnitComboBox != null)
            {
                // 根据选择的单位转换为磅值
                var unit = (IndentUnit)indentUnitComboBox.SelectedIndex;
                double pointsValue = UnitConverter.ConvertToPoints((double)indentValueNumeric.Value, unit);
                formatRule.SpecialIndentValue = pointsValue;
            }

            // 保存行距设置
            if (lineSpacingTypeComboBox != null)
            {
                switch (lineSpacingTypeComboBox.SelectedIndex)
                {
                    case 0: // 单倍行距
                        formatRule.LineSpacingRule = AW.LineSpacingRule.Multiple;
                        formatRule.LineSpacing = 1.0; // 单倍行距使用倍数值1.0
                        break;
                    case 1: // 1.5倍行距
                        formatRule.LineSpacingRule = AW.LineSpacingRule.Multiple;
                        formatRule.LineSpacing = 1.5; // 1.5倍行距使用倍数值1.5
                        break;
                    case 2: // 2倍行距
                        formatRule.LineSpacingRule = AW.LineSpacingRule.Multiple;
                        formatRule.LineSpacing = 2.0; // 2倍行距使用倍数值2.0
                        break;
                    case 3: // 最小值
                        formatRule.LineSpacingRule = AW.LineSpacingRule.AtLeast;
                        if (lineSpacingValueNumeric != null)
                            formatRule.LineSpacing = Math.Max(0.1, (double)lineSpacingValueNumeric.Value);
                        break;
                    case 4: // 固定值
                        formatRule.LineSpacingRule = AW.LineSpacingRule.Exactly;
                        if (lineSpacingValueNumeric != null)
                            formatRule.LineSpacing = Math.Max(0.1, (double)lineSpacingValueNumeric.Value);
                        break;
                    case 5: // 多倍行距
                        formatRule.LineSpacingRule = AW.LineSpacingRule.Multiple;
                        if (lineSpacingValueNumeric != null)
                        {
                            // 确保倍数值有效（大于0）
                            double multiplier = (double)lineSpacingValueNumeric.Value;
                            formatRule.LineSpacing = multiplier > 0 ? multiplier : 1.0;
                        }
                        break;
                    default:
                        formatRule.LineSpacingRule = AW.LineSpacingRule.Multiple;
                        formatRule.LineSpacing = 1.0; // 默认单倍行距
                        break;
                }
            }

            // 保存图片行距控制设置
            if (adjustImageLineSpacingCheckBox != null)
                formatRule.AdjustImageLineSpacing = adjustImageLineSpacingCheckBox.Checked;

            // 保存边框设置
            if (hasBordersCheckBox != null)
                formatRule.HasBorders = hasBordersCheckBox.Checked;
            if (borderTypeComboBox != null)
            {
                // 显式映射边框类型，确保与Aspose.Words枚举值对应
                formatRule.BorderType = borderTypeComboBox.SelectedIndex switch
                {
                    0 => AW.LineStyle.Single,
                    1 => AW.LineStyle.Double,
                    2 => AW.LineStyle.DashSmallGap,
                    3 => AW.LineStyle.Dot,
                    4 => AW.LineStyle.DotDash,
                    5 => AW.LineStyle.DotDotDash,
                    _ => AW.LineStyle.Single
                };
            }
            if (borderWidthNumeric != null)
                formatRule.BorderWidth = (double)borderWidthNumeric.Value;
            if (borderColorPanel != null)
                formatRule.BorderColor = borderColorPanel.BackColor;
            if (hasTopBorderCheckBox != null)
                formatRule.HasTopBorder = hasTopBorderCheckBox.Checked;
            if (hasBottomBorderCheckBox != null)
                formatRule.HasBottomBorder = hasBottomBorderCheckBox.Checked;
            if (hasLeftBorderCheckBox != null)
                formatRule.HasLeftBorder = hasLeftBorderCheckBox.Checked;
            if (hasRightBorderCheckBox != null)
                formatRule.HasRightBorder = hasRightBorderCheckBox.Checked;

            // 保存底纹设置
            if (hasShadingCheckBox != null)
                formatRule.HasShading = hasShadingCheckBox.Checked;
            if (shadingPatternComboBox != null)
            {
                // 显式映射底纹图案，确保与Aspose.Words枚举值对应
                formatRule.ShadingPattern = shadingPatternComboBox.SelectedIndex switch
                {
                    0 => AW.TextureIndex.TextureNone,
                    1 => AW.TextureIndex.TextureSolid,
                    2 => AW.TextureIndex.Texture5Percent,
                    3 => AW.TextureIndex.Texture10Percent,
                    4 => AW.TextureIndex.Texture20Percent,
                    5 => AW.TextureIndex.Texture25Percent,
                    6 => AW.TextureIndex.Texture30Percent,
                    7 => AW.TextureIndex.Texture40Percent,
                    8 => AW.TextureIndex.Texture50Percent,
                    9 => AW.TextureIndex.Texture60Percent,
                    10 => AW.TextureIndex.Texture70Percent,
                    11 => AW.TextureIndex.Texture75Percent,
                    12 => AW.TextureIndex.Texture80Percent,
                    13 => AW.TextureIndex.Texture90Percent,
                    _ => AW.TextureIndex.TextureNone
                };
            }
            if (shadingColorPanel != null)
                formatRule.ShadingColor = shadingColorPanel.BackColor;

            // 保存字体设置
            if (chineseFontComboBox != null)
                formatRule.ChineseFontName = chineseFontComboBox.Text;
            if (chineseFontStyleComboBox != null)
                formatRule.ChineseFontStyle = GetFontStyleFromIndex(chineseFontStyleComboBox.SelectedIndex);
            if (chineseFontSizeNumeric != null)
                formatRule.ChineseFontSize = (double)chineseFontSizeNumeric.Value;
            if (westernFontComboBox != null)
                formatRule.WesternFontName = westernFontComboBox.Text;
            if (westernFontStyleComboBox != null)
                formatRule.WesternFontStyle = GetFontStyleFromIndex(westernFontStyleComboBox.SelectedIndex);
            if (westernFontSizeNumeric != null)
                formatRule.WesternFontSize = (double)westernFontSizeNumeric.Value;
            if (complexScriptFontComboBox != null)
                formatRule.ComplexScriptFontName = complexScriptFontComboBox.Text;
            if (complexScriptFontStyleComboBox != null)
                formatRule.ComplexScriptFontStyle = GetFontStyleFromIndex(complexScriptFontStyleComboBox.SelectedIndex);
            if (complexScriptFontSizeNumeric != null)
                formatRule.ComplexScriptFontSize = (double)complexScriptFontSizeNumeric.Value;

            // 正确保存字体颜色和高亮颜色
            if (fontColorPanel != null)
            {
                // 从UI面板保存颜色到规则对象
                formatRule.FontColor = fontColorPanel.BackColor;
            }

            if (highlightColorPanel != null)
            {
                // 从UI面板保存高亮颜色到规则对象
                if (highlightColorPanel.BackColor != Color.Transparent)
                {
                    formatRule.HighlightColor = highlightColorPanel.BackColor;
                }
                else
                {
                    formatRule.HighlightColor = null;
                }
            }

            // 保存图片格式主开关设置
            if (enableImageFormatCheckBox != null)
                formatRule.EnableImageFormat = enableImageFormatCheckBox.Checked;

            // 保存图片格式启用控制设置
            if (enableImageSizeCheckBox != null)
                formatRule.EnableImageSize = enableImageSizeCheckBox.Checked;
            if (enableImageWrapCheckBox != null)
                formatRule.EnableImageWrap = enableImageWrapCheckBox.Checked;
            if (enableImageEffectCheckBox != null)
                formatRule.EnableImageEffect = enableImageEffectCheckBox.Checked;
            if (enableBrightnessCheckBox != null)
                formatRule.EnableBrightness = enableBrightnessCheckBox.Checked;
            if (enableContrastCheckBox != null)
                formatRule.EnableContrast = enableContrastCheckBox.Checked;
            if (enableTransparencyCheckBox != null)
                formatRule.EnableTransparency = enableTransparencyCheckBox.Checked;
            if (enableColorModeCheckBox != null)
                formatRule.EnableColorMode = enableColorModeCheckBox.Checked;

            // 保存图片格式设置
            if (preserveAspectRatioCheckBox != null)
                formatRule.PreserveAspectRatio = preserveAspectRatioCheckBox.Checked;

            if (useExactSizeRadio != null && useScaleRadio != null)
            {
                if (useExactSizeRadio.Checked)
                {
                    // 使用精确尺寸
                    if (imageWidthNumeric != null)
                        formatRule.ImageWidth = (double)imageWidthNumeric.Value;
                    if (imageHeightNumeric != null)
                        formatRule.ImageHeight = (double)imageHeightNumeric.Value;

                    // 重置缩放值为默认
                    formatRule.ImageScaleX = 100;
                    formatRule.ImageScaleY = 100;
                }
                else
                {
                    // 使用缩放
                    if (imageScaleXNumeric != null)
                        formatRule.ImageScaleX = (double)imageScaleXNumeric.Value;
                    if (imageScaleYNumeric != null)
                        formatRule.ImageScaleY = (double)imageScaleYNumeric.Value;

                    // 重置尺寸值为0（表示不使用）
                    formatRule.ImageWidth = 0;
                    formatRule.ImageHeight = 0;
                }
            }

            // 保存环绕方式
            if (imageWrapTypeComboBox != null)
            {
                switch (imageWrapTypeComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.ImageWrapType = AW.Drawing.WrapType.Inline;
                        break;
                    case 1:
                        formatRule.ImageWrapType = AW.Drawing.WrapType.Square;
                        break;
                    case 2:
                        formatRule.ImageWrapType = AW.Drawing.WrapType.Tight;
                        break;
                    case 3:
                        formatRule.ImageWrapType = AW.Drawing.WrapType.Through;
                        break;
                    case 4:
                        formatRule.ImageWrapType = AW.Drawing.WrapType.TopBottom;
                        break;
                    default:
                        formatRule.ImageWrapType = AW.Drawing.WrapType.Inline;
                        break;
                }
            }

            // 设置文本环绕位置
            if (wrapTextBehindRadio != null && wrapTextFrontRadio != null)
            {
                formatRule.WrapTextBehind = wrapTextBehindRadio.Checked;
                formatRule.WrapTextFront = wrapTextFrontRadio.Checked;
            }

            // 保存图片效果设置
            if (brightnessTrackBar != null)
                formatRule.ImageBrightness = brightnessTrackBar.Value;
            if (contrastTrackBar != null)
                formatRule.ImageContrast = contrastTrackBar.Value;
            if (transparencyTrackBar != null)
                formatRule.ImageTransparency = transparencyTrackBar.Value;

            if (colorModeComboBox != null)
            {
                switch (colorModeComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.ImageColorMode = ImageColorMode.Color;
                        break;
                    case 1:
                        formatRule.ImageColorMode = ImageColorMode.Grayscale;
                        break;
                    case 2:
                        formatRule.ImageColorMode = ImageColorMode.BlackWhite;
                        break;
                    default:
                        formatRule.ImageColorMode = ImageColorMode.Color;
                        break;
                }
            }

            // 保存图片裁剪设置
            if (enableCropCheckBox != null)
                formatRule.EnableImageCrop = enableCropCheckBox.Checked;
            if (cropTopNumeric != null)
                formatRule.CropTop = (double)cropTopNumeric.Value;
            if (cropBottomNumeric != null)
                formatRule.CropBottom = (double)cropBottomNumeric.Value;
            if (cropLeftNumeric != null)
                formatRule.CropLeft = (double)cropLeftNumeric.Value;
            if (cropRightNumeric != null)
                formatRule.CropRight = (double)cropRightNumeric.Value;

            // 保存图片边框设置
            if (enableImageBorderCheckBox != null)
                formatRule.EnableImageBorder = enableImageBorderCheckBox.Checked;

            if (imageBorderStyleComboBox != null)
            {
                switch (imageBorderStyleComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.ImageBorderStyle = AW.LineStyle.Single;
                        break;
                    case 1:
                        formatRule.ImageBorderStyle = AW.LineStyle.Double;
                        break;
                    case 2:
                        formatRule.ImageBorderStyle = AW.LineStyle.DashSmallGap;
                        break;
                    case 3:
                        formatRule.ImageBorderStyle = AW.LineStyle.Dot;
                        break;
                    case 4:
                        formatRule.ImageBorderStyle = AW.LineStyle.DotDash;
                        break;
                    case 5:
                        formatRule.ImageBorderStyle = AW.LineStyle.DotDotDash;
                        break;
                    default:
                        formatRule.ImageBorderStyle = AW.LineStyle.Single;
                        break;
                }
            }
            if (imageBorderWidthNumeric != null)
                formatRule.ImageBorderWidth = (double)imageBorderWidthNumeric.Value;
            if (imageBorderColorPanel != null)
                formatRule.ImageBorderColor = imageBorderColorPanel.BackColor;

            // 保存图片位置设置
            if (enableImagePositionCheckBox != null)
                formatRule.EnableImagePosition = enableImagePositionCheckBox.Checked;

            // 保存位置相关设置
            if (horizontalRelativeComboBox != null)
            {
                switch (horizontalRelativeComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.HorizontalRelativePosition = AW.Drawing.RelativeHorizontalPosition.Margin;
                        break;
                    case 1:
                        formatRule.HorizontalRelativePosition = AW.Drawing.RelativeHorizontalPosition.Page;
                        break;
                    case 2:
                        formatRule.HorizontalRelativePosition = AW.Drawing.RelativeHorizontalPosition.Column;
                        break;
                    case 3:
                        formatRule.HorizontalRelativePosition = AW.Drawing.RelativeHorizontalPosition.Character;
                        break;
                    default:
                        formatRule.HorizontalRelativePosition = AW.Drawing.RelativeHorizontalPosition.Column;
                        break;
                }
            }

            if (verticalRelativeComboBox != null)
            {
                switch (verticalRelativeComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.VerticalRelativePosition = AW.Drawing.RelativeVerticalPosition.Margin;
                        break;
                    case 1:
                        formatRule.VerticalRelativePosition = AW.Drawing.RelativeVerticalPosition.Page;
                        break;
                    case 2:
                        formatRule.VerticalRelativePosition = AW.Drawing.RelativeVerticalPosition.Paragraph;
                        break;
                    case 3:
                        formatRule.VerticalRelativePosition = AW.Drawing.RelativeVerticalPosition.Line;
                        break;
                    default:
                        formatRule.VerticalRelativePosition = AW.Drawing.RelativeVerticalPosition.Paragraph;
                        break;
                }
            }

            if (useAlignmentRadio != null && useAlignmentRadio.Checked)
            {
                formatRule.UseImageAlignment = true;

                if (horizontalAlignmentComboBox != null)
                {
                    switch (horizontalAlignmentComboBox.SelectedIndex)
                    {
                        case 0:
                            formatRule.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Left;
                            break;
                        case 1:
                            formatRule.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Center;
                            break;
                        case 2:
                            formatRule.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Right;
                            break;
                        default:
                            formatRule.HorizontalAlignment = AW.Drawing.HorizontalAlignment.Center;
                            break;
                    }
                }

                if (verticalAlignmentComboBox != null)
                {
                    switch (verticalAlignmentComboBox.SelectedIndex)
                    {
                        case 0:
                            formatRule.VerticalAlignment = AW.Drawing.VerticalAlignment.Top;
                            break;
                        case 1:
                            formatRule.VerticalAlignment = AW.Drawing.VerticalAlignment.Center;
                            break;
                        case 2:
                            formatRule.VerticalAlignment = AW.Drawing.VerticalAlignment.Bottom;
                            break;
                        default:
                            formatRule.VerticalAlignment = AW.Drawing.VerticalAlignment.Center;
                            break;
                    }
                }
            }
            else if (useExactPositionRadio != null && useExactPositionRadio.Checked)
            {
                formatRule.UseImageAlignment = false;

                if (horizontalPositionNumeric != null)
                    formatRule.HorizontalPosition = (double)horizontalPositionNumeric.Value;
                if (verticalPositionNumeric != null)
                    formatRule.VerticalPosition = (double)verticalPositionNumeric.Value;
            }

            // 保存图片旋转和翻转设置
            if (enableImageRotateFlipCheckBox != null)
                formatRule.EnableImageRotateFlip = enableImageRotateFlipCheckBox.Checked;

            if (rotationAngleNumeric != null)
                formatRule.RotationAngle = (double)rotationAngleNumeric.Value;
            if (flipHorizontalCheckBox != null)
                formatRule.FlipHorizontal = flipHorizontalCheckBox.Checked;
            if (flipVerticalCheckBox != null)
                formatRule.FlipVertical = flipVerticalCheckBox.Checked;

            // 保存图片超链接设置
            if (enableImageHyperlinkCheckBox != null)
                formatRule.EnableImageHyperlink = enableImageHyperlinkCheckBox.Checked;

            if (hyperlinkTypeComboBox != null)
            {
                switch (hyperlinkTypeComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.HyperlinkType = HyperlinkType.Url;
                        break;
                    case 1:
                        formatRule.HyperlinkType = HyperlinkType.Email;
                        break;
                    case 2:
                        formatRule.HyperlinkType = HyperlinkType.Bookmark;
                        break;
                    default:
                        formatRule.HyperlinkType = HyperlinkType.Url;
                        break;
                }
            }

            if (hyperlinkUrlTextBox != null)
                formatRule.HyperlinkUrl = hyperlinkUrlTextBox.Text;
            if (bookmarkNameTextBox != null)
                formatRule.BookmarkName = bookmarkNameTextBox.Text;
            if (hyperlinkToolTipTextBox != null)
                formatRule.HyperlinkToolTip = hyperlinkToolTipTextBox.Text;
            if (openInNewWindowCheckBox != null)
                formatRule.OpenHyperlinkInNewWindow = openInNewWindowCheckBox.Checked;

            // 保存图片锁定设置
            if (enableImageLockingCheckBox != null)
                formatRule.EnableImageLocking = enableImageLockingCheckBox.Checked;
            if (lockPositionCheckBox != null)
                formatRule.LockPosition = lockPositionCheckBox.Checked;
            if (lockAspectRatioCheckBox != null)
                formatRule.LockAspectRatio = lockAspectRatioCheckBox.Checked;
            if (lockFormattingCheckBox != null)
                formatRule.LockFormatting = lockFormattingCheckBox.Checked;

            // 保存水印设置
            if (enableWatermarkCheckBox != null && watermarkTextBox != null && watermarkFontFamilyComboBox != null && watermarkFontSizeNumeric != null)
            {
                formatRule.EnableWatermark = enableWatermarkCheckBox.Checked;
                formatRule.WatermarkText = watermarkTextBox.Text;
                formatRule.WatermarkFontFamily = watermarkFontFamilyComboBox.SelectedItem?.ToString() ?? "Arial";
                formatRule.WatermarkFontSize = (float)(double)watermarkFontSizeNumeric.Value;
            }

            // 保存水印颜色和透明度
            if (watermarkFontFamilyComboBox != null)
                formatRule.WatermarkFontFamily = watermarkFontFamilyComboBox.Text;
            if (watermarkFontSizeNumeric != null)
                formatRule.WatermarkFontSize = (float)(double)watermarkFontSizeNumeric.Value;
            if (watermarkColorPanel != null)
                formatRule.WatermarkColor = watermarkColorPanel.BackColor;
            if (watermarkOpacityTrackBar != null)
                formatRule.WatermarkOpacity = watermarkOpacityTrackBar.Value / 100.0;
            if (watermarkRotationAngleNumeric != null)
                formatRule.WatermarkRotationAngle = (double)watermarkRotationAngleNumeric.Value;

            // 保存图片水印设置
            if (enableImageWatermarkCheckBox != null)
                formatRule.EnableImageWatermark = enableImageWatermarkCheckBox.Checked;
            if (watermarkImagePathTextBox != null)
                formatRule.WatermarkImagePath = watermarkImagePathTextBox.Text;

            if (imageWatermarkLayoutComboBox != null)
            {
                switch (imageWatermarkLayoutComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.ImageWatermarkLayout = AW.WatermarkLayout.Diagonal;
                        break;
                    case 1:
                        formatRule.ImageWatermarkLayout = AW.WatermarkLayout.Horizontal;
                        break;
                    default:
                        formatRule.ImageWatermarkLayout = AW.WatermarkLayout.Diagonal;
                        break;
                }
            }

            // 保存水印位置设置
            if (enableWatermarkPositionCheckBox != null)
                formatRule.EnableWatermarkPosition = enableWatermarkPositionCheckBox.Checked;
            if (watermarkPositionCenterRadio != null)
                formatRule.WatermarkAtPageCenter = watermarkPositionCenterRadio.Checked;
            if (watermarkHorizontalPositionNumeric != null)
                formatRule.WatermarkHorizontalPosition = (double)watermarkHorizontalPositionNumeric.Value;
            if (watermarkVerticalPositionNumeric != null)
                formatRule.WatermarkVerticalPosition = (double)watermarkVerticalPositionNumeric.Value;
            if (watermarkAllPagesRadio != null)
                formatRule.WatermarkOnAllPages = watermarkAllPagesRadio.Checked;
            if (watermarkPageRangeTextBox != null)
                formatRule.WatermarkPageRange = watermarkPageRangeTextBox.Text;
            if (watermarkBehindContentCheckBox != null)
                formatRule.WatermarkBehindContent = watermarkBehindContentCheckBox.Checked;

            // 保存水印格式设置
            if (enableWatermarkFormatCheckBox != null)
                formatRule.EnableWatermarkFormat = enableWatermarkFormatCheckBox.Checked;
            if (watermarkHasBorderCheckBox != null)
                formatRule.WatermarkHasBorder = watermarkHasBorderCheckBox.Checked;
            if (watermarkBorderStyleComboBox != null)
                formatRule.WatermarkBorderStyle = (AW.LineStyle)watermarkBorderStyleComboBox.SelectedIndex;
            if (watermarkBorderWidthNumeric != null)
                formatRule.WatermarkBorderWidth = (double)watermarkBorderWidthNumeric.Value;
            if (watermarkBorderColorPanel != null)
                formatRule.WatermarkBorderColor = watermarkBorderColorPanel.BackColor;
            if (watermarkWrapTypeComboBox != null)
            {
                switch (watermarkWrapTypeComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.WatermarkWrapType = AW.Drawing.WrapType.Inline;
                        break;
                    case 1:
                        formatRule.WatermarkWrapType = AW.Drawing.WrapType.Square;
                        break;
                    case 2:
                        formatRule.WatermarkWrapType = AW.Drawing.WrapType.Tight;
                        break;
                    case 3:
                        formatRule.WatermarkWrapType = AW.Drawing.WrapType.Through;
                        break;
                    case 4:
                        formatRule.WatermarkWrapType = AW.Drawing.WrapType.TopBottom;
                        break;
                    default:
                        formatRule.WatermarkWrapType = AW.Drawing.WrapType.Inline;
                        break;
                }
            }
            if (lockWatermarkCheckBox != null)
                formatRule.LockWatermark = lockWatermarkCheckBox.Checked;
            if (showWatermarkInPrintCheckBox != null)
                formatRule.ShowWatermarkInPrint = showWatermarkInPrintCheckBox.Checked;

            // 保存表格宽度自适应设置
            if (enableTableAutoFitCheckBox != null)
                formatRule.EnableTableAutoFit = enableTableAutoFitCheckBox.Checked;
            if (autoFitToContentsRadio != null)
                formatRule.AutoFitToContents = autoFitToContentsRadio.Checked;
            if (autoFitToWindowRadio != null)
                formatRule.AutoFitToWindow = autoFitToWindowRadio.Checked;
            if (useFixedColumnWidthRadio != null)
                formatRule.UseFixedColumnWidth = useFixedColumnWidthRadio.Checked;
            if (preferredTableWidthNumeric != null)
                formatRule.PreferredTableWidth = (double)preferredTableWidthNumeric.Value;

            // 保存表格布局设置
            if (enableTableLayoutCheckBox != null)
                formatRule.EnableTableLayout = enableTableLayoutCheckBox.Checked;

            if (tableAlignmentComboBox != null)
            {
                switch (tableAlignmentComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.TableAlignment = AW.Tables.TableAlignment.Left;
                        break;
                    case 1:
                        formatRule.TableAlignment = AW.Tables.TableAlignment.Center;
                        break;
                    case 2:
                        formatRule.TableAlignment = AW.Tables.TableAlignment.Right;
                        break;
                    default:
                        formatRule.TableAlignment = AW.Tables.TableAlignment.Left;
                        break;
                }
            }

            if (tableLeftIndentNumeric != null)
                formatRule.TableLeftIndent = (double)tableLeftIndentNumeric.Value;
            if (tableRightIndentNumeric != null)
                formatRule.TableRightIndent = (double)tableRightIndentNumeric.Value;

            if (tableTextWrappingComboBox != null)
            {
                switch (tableTextWrappingComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.TableTextWrapping = AW.Tables.TextWrapping.Around;
                        break;
                    case 1:
                        formatRule.TableTextWrapping = AW.Tables.TextWrapping.None;
                        break;
                    default:
                        formatRule.TableTextWrapping = AW.Tables.TextWrapping.Around;
                        break;
                }
            }

            if (tableAllowAutoFitCheckBox != null)
                formatRule.TableAllowAutoFit = tableAllowAutoFitCheckBox.Checked;
            if (tableDefaultCellSpacingNumeric != null)
                formatRule.TableDefaultCellSpacing = (double)tableDefaultCellSpacingNumeric.Value;

            // 保存表格样式设置
            if (enableTableStyleCheckBox != null)
                formatRule.EnableTableStyle = enableTableStyleCheckBox.Checked;

            if (tableHasBordersCheckBox != null)
                formatRule.TableHasBorders = tableHasBordersCheckBox.Checked;
            if (tableBorderStyleComboBox != null)
            {
                switch (tableBorderStyleComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.TableBorderStyle = AW.LineStyle.Single;
                        break;
                    case 1:
                        formatRule.TableBorderStyle = AW.LineStyle.Double;
                        break;
                    case 2:
                        formatRule.TableBorderStyle = AW.LineStyle.DashSmallGap;
                        break;
                    case 3:
                        formatRule.TableBorderStyle = AW.LineStyle.Dot;
                        break;
                    default:
                        formatRule.TableBorderStyle = AW.LineStyle.Single;
                        break;
                }
            }
            if (tableBorderWidthNumeric != null)
                formatRule.TableBorderWidth = (double)tableBorderWidthNumeric.Value;
            if (tableBorderColorPanel != null)
                formatRule.TableBorderColor = tableBorderColorPanel.BackColor;

            // 保存表格网格线颜色
            if (tableGridlineColorPanel != null)
                formatRule.TableGridlineColor = tableGridlineColorPanel.BackColor;

            // 保存表格底纹颜色
            if (tableShadingColorPanel != null)
                formatRule.TableShadingColor = tableShadingColorPanel.BackColor;

            // 保存单元格边框颜色
            if (cellBorderColorPanel != null)
                formatRule.CellBorderColor = cellBorderColorPanel.BackColor;

            // 保存单元格底纹颜色
            if (cellShadingColorPanel != null)
                formatRule.CellShadingColor = cellShadingColorPanel.BackColor;

            // 保存水印边框颜色
            if (watermarkBorderColorPanel != null)
                formatRule.WatermarkBorderColor = watermarkBorderColorPanel.BackColor;

            if (tableHasGridlinesCheckBox != null)
                formatRule.TableHasGridlines = tableHasGridlinesCheckBox.Checked;
            if (tableGridlineStyleComboBox != null)
            {
                switch (tableGridlineStyleComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.TableGridlineStyle = AW.LineStyle.Single;
                        break;
                    case 1:
                        formatRule.TableGridlineStyle = AW.LineStyle.Double;
                        break;
                    case 2:
                        formatRule.TableGridlineStyle = AW.LineStyle.DashSmallGap;
                        break;
                    case 3:
                        formatRule.TableGridlineStyle = AW.LineStyle.Dot;
                        break;
                    default:
                        formatRule.TableGridlineStyle = AW.LineStyle.Single;
                        break;
                }
            }
            if (tableGridlineWidthNumeric != null)
                formatRule.TableGridlineWidth = (double)tableGridlineWidthNumeric.Value;
            if (tableGridlineColorPanel != null)
                formatRule.TableGridlineColor = tableGridlineColorPanel.BackColor;

            if (tableHasShadingCheckBox != null)
                formatRule.TableHasShading = tableHasShadingCheckBox.Checked;
            if (tableShadingColorPanel != null)
                formatRule.TableShadingColor = tableShadingColorPanel.BackColor;

            // 保存单元格格式设置
            if (enableCellFormatCheckBox != null)
                formatRule.EnableCellFormat = enableCellFormatCheckBox.Checked;

            if (cellverticalAlignmentComboBox != null)
            {
                switch (cellverticalAlignmentComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.CellVerticalAlignment = AW.Tables.CellVerticalAlignment.Top;
                        break;
                    case 1:
                        formatRule.CellVerticalAlignment = AW.Tables.CellVerticalAlignment.Center;
                        break;
                    case 2:
                        formatRule.CellVerticalAlignment = AW.Tables.CellVerticalAlignment.Bottom;
                        break;
                    default:
                        formatRule.CellVerticalAlignment = AW.Tables.CellVerticalAlignment.Top;
                        break;
                }
            }

            if (cellMarginTopNumeric != null)
                formatRule.CellMarginTop = (double)cellMarginTopNumeric.Value;
            if (cellMarginBottomNumeric != null)
                formatRule.CellMarginBottom = (double)cellMarginBottomNumeric.Value;
            if (cellMarginLeftNumeric != null)
                formatRule.CellMarginLeft = (double)cellMarginLeftNumeric.Value;
            if (cellMarginRightNumeric != null)
                formatRule.CellMarginRight = (double)cellMarginRightNumeric.Value;

            if (cellHasShadingCheckBox != null)
                formatRule.CellHasShading = cellHasShadingCheckBox.Checked;
            if (cellShadingColorPanel != null)
                formatRule.CellShadingColor = cellShadingColorPanel.BackColor;

            if (cellHasBordersCheckBox != null)
                formatRule.CellHasBorders = cellHasBordersCheckBox.Checked;
            if (cellBorderStyleComboBox != null)
            {
                switch (cellBorderStyleComboBox.SelectedIndex)
                {
                    case 0:
                        formatRule.CellBorderStyle = AW.LineStyle.Single;
                        break;
                    case 1:
                        formatRule.CellBorderStyle = AW.LineStyle.Double;
                        break;
                    case 2:
                        formatRule.CellBorderStyle = AW.LineStyle.DashSmallGap;
                        break;
                    case 3:
                        formatRule.CellBorderStyle = AW.LineStyle.Dot;
                        break;
                    default:
                        formatRule.CellBorderStyle = AW.LineStyle.Single;
                        break;
                }
            }
            if (cellBorderWidthNumeric != null)
                formatRule.CellBorderWidth = (double)cellBorderWidthNumeric.Value;
            if (cellBorderColorPanel != null)
                formatRule.CellBorderColor = cellBorderColorPanel.BackColor;

            // 更新表格相关控件状态
            if (tableTabPage != null && tableTabPage.Controls.Count > 0)
            {
                UpdateTableControlsState();
            }
        }

        // 更新水印格式控件状态
        private void UpdateWatermarkFormatControlsState()
        {
            bool enabled = enableWatermarkFormatCheckBox != null && enableWatermarkFormatCheckBox.Checked;

            if (watermarkHasBorderCheckBox != null)
                watermarkHasBorderCheckBox.Enabled = enabled;

            if (watermarkBorderStyleComboBox != null && watermarkHasBorderCheckBox != null)
                watermarkBorderStyleComboBox.Enabled = enabled && watermarkHasBorderCheckBox.Checked;

            if (watermarkBorderWidthNumeric != null)
                watermarkBorderWidthNumeric.Enabled = enabled && watermarkHasBorderCheckBox != null && watermarkHasBorderCheckBox.Checked;

            if (watermarkBorderColorButton != null)
                watermarkBorderColorButton.Enabled = enabled && watermarkHasBorderCheckBox != null && watermarkHasBorderCheckBox.Checked;

            if (clearWatermarkBorderColorButton != null)
                clearWatermarkBorderColorButton.Enabled = enabled && watermarkHasBorderCheckBox != null && watermarkHasBorderCheckBox.Checked;

            if (watermarkWrapTypeComboBox != null)
                watermarkWrapTypeComboBox.Enabled = enabled;

            if (lockWatermarkCheckBox != null)
                lockWatermarkCheckBox.Enabled = enabled;

            if (showWatermarkInPrintCheckBox != null)
                showWatermarkInPrintCheckBox.Enabled = enabled;

            if (watermarkColorButton != null)
                watermarkColorButton.Enabled = enabled;

            if (clearWatermarkColorButton != null)
                clearWatermarkColorButton.Enabled = enabled;
        }

        // 添加缺失的GetTabAlignmentText方法
        private static string GetTabAlignmentText(AW.TabAlignment alignment)
        {
            return alignment switch
            {
                AW.TabAlignment.Left => "左对齐",
                AW.TabAlignment.Center => "居中对齐",
                AW.TabAlignment.Right => "右对齐",
                AW.TabAlignment.Decimal => "小数点对齐",
                AW.TabAlignment.Bar => "竖线对齐",
                _ => "左对齐"
            };
        }

        private GroupBox CreateFontFormatPanel()
        {
            var groupBox = new GroupBox {
                Text = "字体格式",
                Dock = DockStyle.Top,  // 改为顶部停靠，与其他面板保持一致
                AutoSize = true,
                Padding = new Padding(10)
                // 删除Width属性设置，避免使用未初始化的fontScrollPanel
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
                // 删除Width属性设置，避免使用未初始化的fontScrollPanel
            };

            // 添加启用字体格式的复选框
            enableFontFormatCheckBox = new CheckBox
            {
                Text = "启用字体格式",
                AutoSize = true,
                Checked = true // 默认启用，不使用formatRule.EnableFontFormat
            };

            enableFontFormatCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableFontFormatCheckBox.Checked;

                // 控制所有子功能启用复选框的启用状态
                enableChineseFontCheckBox.Enabled = enabled;
                enableWesternFontCheckBox.Enabled = enabled;
                enableComplexScriptFontCheckBox.Enabled = enabled;
                enableFontColorCheckBox.Enabled = enabled;
                enableHighlightColorCheckBox.Enabled = enabled;

                // 更新所有字体格式控件的启用状态
                UpdateFontFormatControlsEnabled();
            };

            mainLayout.Controls.Add(enableFontFormatCheckBox, 0, 0);

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 6, // 增加一行用于显示背景色
                Padding = new Padding(10),
                AutoSize = true
            };

            // 设置列宽
            layout.ColumnStyles.Clear();
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120F)); // 第一列固定宽度
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40F));   // 第二列占40%
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));   // 第三列占30%
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));   // 第四列占30%

            // 标题行
            layout.Controls.Add(new Label { Text = "字体类型", AutoSize = true, TextAlign = ContentAlignment.MiddleCenter }, 1, 0);
            layout.Controls.Add(new Label { Text = "字形", AutoSize = true, TextAlign = ContentAlignment.MiddleCenter }, 2, 0);
            layout.Controls.Add(new Label { Text = "字号", AutoSize = true, TextAlign = ContentAlignment.MiddleCenter }, 3, 0);

            // 1. 中文字体设置
            var chineseFontLabelPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            enableChineseFontCheckBox = new CheckBox
            {
                Text = "",
                AutoSize = true,
                Checked = true, // 默认启用
                Width = 20,
                Enabled = enableFontFormatCheckBox.Checked
            };
            chineseFontLabelPanel.Controls.Add(enableChineseFontCheckBox);
            chineseFontLabelPanel.Controls.Add(new Label { Text = "中文字体：", AutoSize = true });
            layout.Controls.Add(chineseFontLabelPanel, 0, 1);

            // 中文字体类型
            chineseFontComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 180,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = enableChineseFontCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            // 设置下拉框文字居中显示
            chineseFontComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = chineseFontComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 加载系统字体
            foreach (var fontFamily in System.Drawing.FontFamily.Families)
            {
                chineseFontComboBox.Items.Add(fontFamily.Name);
            }

            // 默认选择宋体，如果存在的话
            int songtiIndex = chineseFontComboBox.Items.IndexOf("宋体");
            if (songtiIndex >= 0)
            {
                chineseFontComboBox.SelectedIndex = songtiIndex;
            }
            else if (chineseFontComboBox.Items.Count > 0)
            {
                chineseFontComboBox.SelectedIndex = 0;
            }

            layout.Controls.Add(chineseFontComboBox, 1, 1);

            // 中文字体字形
            chineseFontStyleComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = enableChineseFontCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            // 设置下拉框文字居中显示
            chineseFontStyleComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = chineseFontStyleComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            chineseFontStyleComboBox.Items.Add("常规");
            chineseFontStyleComboBox.Items.Add("倾斜");
            chineseFontStyleComboBox.Items.Add("加粗");
            chineseFontStyleComboBox.Items.Add("加粗倾斜");
            chineseFontStyleComboBox.SelectedIndex = 0; // 默认常规

            layout.Controls.Add(chineseFontStyleComboBox, 2, 1);

            // 中文字体字号
            var chineseFontSizePanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = false
            };

            // 中文字号下拉框
            chineseFontSizeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 80,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = enableChineseFontCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            // 设置下拉框文字居中显示
            chineseFontSizeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = chineseFontSizeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加中文字号选项
            var fontSizeNames = ChineseFontSizeConverter.GetAllFontSizeNames();
            foreach (var name in fontSizeNames)
            {
                chineseFontSizeComboBox.Items.Add(name);
            }
            chineseFontSizeComboBox.SelectedIndex = 8; // 默认四号 (14磅)

            // 自定义字号输入框
            chineseFontSizeNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 100,
                Value = 14, // 默认14磅
                Width = 50,
                TextAlign = HorizontalAlignment.Center,
                Enabled = enableChineseFontCheckBox.Checked && enableFontFormatCheckBox.Checked,
                Visible = false // 初始隐藏
            };

            // 单位标签
            var chineseFontSizeUnitLabel = new Label
            {
                Text = "磅",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Anchor = AnchorStyles.Left,
                Visible = false // 初始隐藏，与输入框同步显示
            };

            // 保存事件处理器引用以便临时移除
            EventHandler chineseFontSizeComboBoxHandler = null!;
            chineseFontSizeComboBoxHandler = (sender, e) =>
            {
                if (chineseFontSizeComboBox.SelectedIndex >= 0)
                {
                    string selectedText = chineseFontSizeComboBox.Items[chineseFontSizeComboBox.SelectedIndex].ToString() ?? "";
                    if (selectedText == "自定义")
                    {
                        // 显示自定义输入框和单位标签，下拉框保持显示
                        chineseFontSizeNumeric.Visible = true;
                        chineseFontSizeUnitLabel.Visible = true;
                        chineseFontSizeNumeric.Focus(); // 自动聚焦到输入框
                    }
                    else
                    {
                        // 使用预设字号，隐藏自定义输入框和单位标签
                        chineseFontSizeNumeric.Visible = false;
                        chineseFontSizeUnitLabel.Visible = false;
                        var fontSize = (ChineseFontSize)(chineseFontSizeComboBox.SelectedIndex);
                        double pointSize = ChineseFontSizeConverter.GetPointSize(fontSize);
                        chineseFontSizeNumeric.Value = (decimal)pointSize;
                    }
                }
            };

            // 添加事件处理器
            chineseFontSizeComboBox.SelectedIndexChanged += chineseFontSizeComboBoxHandler;

            // 自定义字号输入框值改变时的处理
            chineseFontSizeNumeric.ValueChanged += (sender, e) =>
            {
                // 检查是否匹配预设字号
                double currentSize = (double)chineseFontSizeNumeric.Value;
                var matchedFontSize = ChineseFontSizeConverter.GetChineseFontSize(currentSize);

                if (matchedFontSize != ChineseFontSize.Custom)
                {
                    // 匹配到预设字号，自动选择对应的预设项
                    chineseFontSizeComboBox.SelectedIndexChanged -= chineseFontSizeComboBoxHandler;
                    chineseFontSizeComboBox.SelectedIndex = (int)matchedFontSize;
                    chineseFontSizeComboBox.SelectedIndexChanged += chineseFontSizeComboBoxHandler;
                    chineseFontSizeNumeric.Visible = false;
                    chineseFontSizeUnitLabel.Visible = false;
                }
                else
                {
                    // 不匹配预设字号，确保选择"自定义"
                    if (chineseFontSizeComboBox.SelectedIndex != chineseFontSizeComboBox.Items.Count - 1)
                    {
                        chineseFontSizeComboBox.SelectedIndexChanged -= chineseFontSizeComboBoxHandler;
                        chineseFontSizeComboBox.SelectedIndex = chineseFontSizeComboBox.Items.Count - 1; // 选择"自定义"
                        chineseFontSizeComboBox.SelectedIndexChanged += chineseFontSizeComboBoxHandler;
                    }
                }
            };

            chineseFontSizePanel.Controls.Add(chineseFontSizeComboBox);
            chineseFontSizePanel.Controls.Add(chineseFontSizeNumeric);
            chineseFontSizePanel.Controls.Add(chineseFontSizeUnitLabel);

            layout.Controls.Add(chineseFontSizePanel, 3, 1);

            // 2. 西文字体设置
            var westernFontLabelPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            enableWesternFontCheckBox = new CheckBox
            {
                Text = "",
                AutoSize = true,
                Checked = true, // 默认启用
                Width = 20,
                Enabled = enableFontFormatCheckBox.Checked
            };
            westernFontLabelPanel.Controls.Add(enableWesternFontCheckBox);
            westernFontLabelPanel.Controls.Add(new Label { Text = "西文字体：", AutoSize = true });
            layout.Controls.Add(westernFontLabelPanel, 0, 2);

            // 西文字体类型
            westernFontComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 180,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = enableWesternFontCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            // 设置下拉框文字居中显示
            westernFontComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = westernFontComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 加载系统字体
            foreach (var fontFamily in System.Drawing.FontFamily.Families)
            {
                westernFontComboBox.Items.Add(fontFamily.Name);
            }

            // 默认选择Times New Roman，如果存在的话
            int timesNewRomanIndex = westernFontComboBox.Items.IndexOf("Times New Roman");
            if (timesNewRomanIndex >= 0)
            {
                westernFontComboBox.SelectedIndex = timesNewRomanIndex;
            }
            else if (westernFontComboBox.Items.Count > 0)
            {
                westernFontComboBox.SelectedIndex = 0;
            }

            layout.Controls.Add(westernFontComboBox, 1, 2);

            // 西文字体字形
            westernFontStyleComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = enableWesternFontCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            // 设置下拉框文字居中显示
            westernFontStyleComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = westernFontStyleComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            westernFontStyleComboBox.Items.Add("常规");
            westernFontStyleComboBox.Items.Add("倾斜");
            westernFontStyleComboBox.Items.Add("加粗");
            westernFontStyleComboBox.Items.Add("加粗倾斜");
            westernFontStyleComboBox.SelectedIndex = 0; // 默认常规

            layout.Controls.Add(westernFontStyleComboBox, 2, 2);

            // 西文字体字号
            var westernFontSizePanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = false
            };

            // 西文字号下拉框
            westernFontSizeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 80,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = enableWesternFontCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            // 设置下拉框文字居中显示
            westernFontSizeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = westernFontSizeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加西文字号选项
            var westernFontSizeNames = ChineseFontSizeConverter.GetAllFontSizeNames();
            foreach (var name in westernFontSizeNames)
            {
                westernFontSizeComboBox.Items.Add(name);
            }
            westernFontSizeComboBox.SelectedIndex = 8; // 默认四号 (14磅)

            // 自定义字号输入框
            westernFontSizeNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 100,
                Value = 14, // 默认14磅
                Width = 50,
                TextAlign = HorizontalAlignment.Center,
                Enabled = enableWesternFontCheckBox.Checked && enableFontFormatCheckBox.Checked,
                Visible = false // 初始隐藏
            };

            // 单位标签
            var westernFontSizeUnitLabel = new Label
            {
                Text = "磅",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Anchor = AnchorStyles.Left,
                Visible = false // 初始隐藏，与输入框同步显示
            };

            // 保存事件处理器引用以便临时移除
            EventHandler westernFontSizeComboBoxHandler = null!;
            westernFontSizeComboBoxHandler = (sender, e) =>
            {
                if (westernFontSizeComboBox.SelectedIndex >= 0)
                {
                    string selectedText = westernFontSizeComboBox.Items[westernFontSizeComboBox.SelectedIndex].ToString() ?? "";
                    if (selectedText == "自定义")
                    {
                        // 显示自定义输入框和单位标签，下拉框保持显示
                        westernFontSizeNumeric.Visible = true;
                        westernFontSizeUnitLabel.Visible = true;
                        westernFontSizeNumeric.Focus(); // 自动聚焦到输入框
                    }
                    else
                    {
                        // 使用预设字号，隐藏自定义输入框和单位标签
                        westernFontSizeNumeric.Visible = false;
                        westernFontSizeUnitLabel.Visible = false;
                        var fontSize = (ChineseFontSize)(westernFontSizeComboBox.SelectedIndex);
                        double pointSize = ChineseFontSizeConverter.GetPointSize(fontSize);
                        westernFontSizeNumeric.Value = (decimal)pointSize;
                    }
                }
            };

            // 添加事件处理器
            westernFontSizeComboBox.SelectedIndexChanged += westernFontSizeComboBoxHandler;

            // 自定义字号输入框值改变时的处理
            westernFontSizeNumeric.ValueChanged += (sender, e) =>
            {
                // 检查是否匹配预设字号
                double currentSize = (double)westernFontSizeNumeric.Value;
                var matchedFontSize = ChineseFontSizeConverter.GetChineseFontSize(currentSize);

                if (matchedFontSize != ChineseFontSize.Custom)
                {
                    // 匹配到预设字号，自动选择对应的预设项
                    westernFontSizeComboBox.SelectedIndexChanged -= westernFontSizeComboBoxHandler;
                    westernFontSizeComboBox.SelectedIndex = (int)matchedFontSize;
                    westernFontSizeComboBox.SelectedIndexChanged += westernFontSizeComboBoxHandler;
                    westernFontSizeNumeric.Visible = false;
                    westernFontSizeUnitLabel.Visible = false;
                }
                else
                {
                    // 不匹配预设字号，确保选择"自定义"
                    if (westernFontSizeComboBox.SelectedIndex != westernFontSizeComboBox.Items.Count - 1)
                    {
                        westernFontSizeComboBox.SelectedIndexChanged -= westernFontSizeComboBoxHandler;
                        westernFontSizeComboBox.SelectedIndex = westernFontSizeComboBox.Items.Count - 1; // 选择"自定义"
                        westernFontSizeComboBox.SelectedIndexChanged += westernFontSizeComboBoxHandler;
                    }
                }
            };

            westernFontSizePanel.Controls.Add(westernFontSizeComboBox);
            westernFontSizePanel.Controls.Add(westernFontSizeNumeric);
            westernFontSizePanel.Controls.Add(westernFontSizeUnitLabel);

            layout.Controls.Add(westernFontSizePanel, 3, 2);

            // 3. 复杂文种字体设置
            var complexScriptFontLabelPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            enableComplexScriptFontCheckBox = new CheckBox
            {
                Text = "",
                AutoSize = true,
                Checked = true, // 默认启用
                Width = 20,
                Enabled = enableFontFormatCheckBox.Checked
            };
            complexScriptFontLabelPanel.Controls.Add(enableComplexScriptFontCheckBox);
            complexScriptFontLabelPanel.Controls.Add(new Label { Text = "复杂字体：", AutoSize = true });
            layout.Controls.Add(complexScriptFontLabelPanel, 0, 3);

            // 复杂文种字体类型
            complexScriptFontComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 180,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = enableComplexScriptFontCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            // 设置下拉框文字居中显示
            complexScriptFontComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = complexScriptFontComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 加载系统字体
            foreach (var fontFamily in System.Drawing.FontFamily.Families)
            {
                complexScriptFontComboBox.Items.Add(fontFamily.Name);
            }

            // 默认选择Arial，如果存在的话
            int arialIndex = complexScriptFontComboBox.Items.IndexOf("Arial");
            if (arialIndex >= 0)
            {
                complexScriptFontComboBox.SelectedIndex = arialIndex;
            }
            else if (complexScriptFontComboBox.Items.Count > 0)
            {
                complexScriptFontComboBox.SelectedIndex = 0;
            }

            layout.Controls.Add(complexScriptFontComboBox, 1, 3);

            // 复杂文种字体字形
            complexScriptFontStyleComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = enableComplexScriptFontCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            // 设置下拉框文字居中显示
            complexScriptFontStyleComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = complexScriptFontStyleComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            complexScriptFontStyleComboBox.Items.Add("常规");
            complexScriptFontStyleComboBox.Items.Add("倾斜");
            complexScriptFontStyleComboBox.Items.Add("加粗");
            complexScriptFontStyleComboBox.Items.Add("加粗倾斜");
            complexScriptFontStyleComboBox.SelectedIndex = 0; // 默认常规

            layout.Controls.Add(complexScriptFontStyleComboBox, 2, 3);

            // 复杂文种字体字号
            var complexScriptFontSizePanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = false
            };

            // 复杂文种字号下拉框
            complexScriptFontSizeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 80,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = enableComplexScriptFontCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            // 设置下拉框文字居中显示
            complexScriptFontSizeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = complexScriptFontSizeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加复杂文种字号选项
            var complexScriptFontSizeNames = ChineseFontSizeConverter.GetAllFontSizeNames();
            foreach (var name in complexScriptFontSizeNames)
            {
                complexScriptFontSizeComboBox.Items.Add(name);
            }
            complexScriptFontSizeComboBox.SelectedIndex = 8; // 默认四号 (14磅)

            // 自定义字号输入框
            complexScriptFontSizeNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 100,
                Value = 14, // 默认14磅
                Width = 50,
                TextAlign = HorizontalAlignment.Center,
                Enabled = enableComplexScriptFontCheckBox.Checked && enableFontFormatCheckBox.Checked,
                Visible = false // 初始隐藏
            };

            // 单位标签
            var complexScriptFontSizeUnitLabel = new Label
            {
                Text = "磅",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Anchor = AnchorStyles.Left,
                Visible = false // 初始隐藏，与输入框同步显示
            };

            // 保存事件处理器引用以便临时移除
            EventHandler complexScriptFontSizeComboBoxHandler = null!;
            complexScriptFontSizeComboBoxHandler = (sender, e) =>
            {
                if (complexScriptFontSizeComboBox.SelectedIndex >= 0)
                {
                    string selectedText = complexScriptFontSizeComboBox.Items[complexScriptFontSizeComboBox.SelectedIndex].ToString() ?? "";
                    if (selectedText == "自定义")
                    {
                        // 显示自定义输入框和单位标签，下拉框保持显示
                        complexScriptFontSizeNumeric.Visible = true;
                        complexScriptFontSizeUnitLabel.Visible = true;
                        complexScriptFontSizeNumeric.Focus(); // 自动聚焦到输入框
                    }
                    else
                    {
                        // 使用预设字号，隐藏自定义输入框和单位标签
                        complexScriptFontSizeNumeric.Visible = false;
                        complexScriptFontSizeUnitLabel.Visible = false;
                        var fontSize = (ChineseFontSize)(complexScriptFontSizeComboBox.SelectedIndex);
                        double pointSize = ChineseFontSizeConverter.GetPointSize(fontSize);
                        complexScriptFontSizeNumeric.Value = (decimal)pointSize;
                    }
                }
            };

            // 添加事件处理器
            complexScriptFontSizeComboBox.SelectedIndexChanged += complexScriptFontSizeComboBoxHandler;

            // 自定义字号输入框值改变时的处理
            complexScriptFontSizeNumeric.ValueChanged += (sender, e) =>
            {
                // 检查是否匹配预设字号
                double currentSize = (double)complexScriptFontSizeNumeric.Value;
                var matchedFontSize = ChineseFontSizeConverter.GetChineseFontSize(currentSize);

                if (matchedFontSize != ChineseFontSize.Custom)
                {
                    // 匹配到预设字号，自动选择对应的预设项
                    complexScriptFontSizeComboBox.SelectedIndexChanged -= complexScriptFontSizeComboBoxHandler;
                    complexScriptFontSizeComboBox.SelectedIndex = (int)matchedFontSize;
                    complexScriptFontSizeComboBox.SelectedIndexChanged += complexScriptFontSizeComboBoxHandler;
                    complexScriptFontSizeNumeric.Visible = false;
                    complexScriptFontSizeUnitLabel.Visible = false;
                }
                else
                {
                    // 不匹配预设字号，确保选择"自定义"
                    if (complexScriptFontSizeComboBox.SelectedIndex != complexScriptFontSizeComboBox.Items.Count - 1)
                    {
                        complexScriptFontSizeComboBox.SelectedIndexChanged -= complexScriptFontSizeComboBoxHandler;
                        complexScriptFontSizeComboBox.SelectedIndex = complexScriptFontSizeComboBox.Items.Count - 1; // 选择"自定义"
                        complexScriptFontSizeComboBox.SelectedIndexChanged += complexScriptFontSizeComboBoxHandler;
                    }
                }
            };

            complexScriptFontSizePanel.Controls.Add(complexScriptFontSizeComboBox);
            complexScriptFontSizePanel.Controls.Add(complexScriptFontSizeNumeric);
            complexScriptFontSizePanel.Controls.Add(complexScriptFontSizeUnitLabel);

            layout.Controls.Add(complexScriptFontSizePanel, 3, 3);

            // 字体颜色选择
            var colorPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Margin = new Padding(0, 10, 0, 0)
            };

            enableFontColorCheckBox = new CheckBox
            {
                Text = "",
                AutoSize = true,
                Checked = true, // 默认启用
                Width = 20,
                Enabled = enableFontFormatCheckBox.Checked
            };
            colorPanel.Controls.Add(enableFontColorCheckBox);
            colorPanel.Controls.Add(new Label { Text = "文字字体颜色：", AutoSize = true });

            fontColorPanel = new Panel
            {
                Size = new Size(30, 20),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.Black, // 默认黑色
                Margin = new Padding(5, 0, 5, 0)
            };

            colorPanel.Controls.Add(fontColorPanel);

            fontColorButton = new Button
            {
                Text = "选择颜色",
                AutoSize = true,
                Enabled = enableFontColorCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            fontColorButton.Click += FontColorButton_Click;
            colorPanel.Controls.Add(fontColorButton);

            // 添加清除按钮
            clearFontColorButton = new Button
            {
                Text = "清除",
                AutoSize = true,
                Margin = new Padding(5, 0, 0, 0),
                Enabled = enableFontColorCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            clearFontColorButton.Click += (s, e) =>
            {
                fontColorPanel.BackColor = Color.Black;
                formatRule.FontColor = Color.Black; // 立即更新格式规则对象
            };

            colorPanel.Controls.Add(clearFontColorButton);

            // 添加到表格
            layout.Controls.Add(colorPanel, 0, 4);
            layout.SetColumnSpan(colorPanel, 4);

            // 添加文字突出显示颜色（背景色）选择
            var highlightColorPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Margin = new Padding(0, 10, 0, 0)
            };

            enableHighlightColorCheckBox = new CheckBox
            {
                Text = "",
                AutoSize = true,
                Checked = true, // 默认启用
                Width = 20,
                Enabled = enableFontFormatCheckBox.Checked
            };
            highlightColorPanel.Controls.Add(enableHighlightColorCheckBox);
            highlightColorPanel.Controls.Add(new Label { Text = "文字突出显示颜色：", AutoSize = true });

            this.highlightColorPanel = new Panel
            {
                Size = new Size(30, 20),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.Transparent, // 默认无背景色
                Margin = new Padding(5, 0, 5, 0)
            };

            highlightColorPanel.Controls.Add(this.highlightColorPanel);

            highlightColorButton = new Button
            {
                Text = "选择颜色",
                AutoSize = true,
                Enabled = enableHighlightColorCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            highlightColorButton.Click += HighlightColorButton_Click;
            highlightColorPanel.Controls.Add(highlightColorButton);

            // 添加清除按钮
            clearHighlightColorButton = new Button
            {
                Text = "清除",
                AutoSize = true,
                Margin = new Padding(5, 0, 0, 0),
                Enabled = enableHighlightColorCheckBox.Checked && enableFontFormatCheckBox.Checked
            };

            clearHighlightColorButton.Click += (s, e) =>
            {
                this.highlightColorPanel.BackColor = Color.Transparent;
                formatRule.HighlightColor = null; // 立即更新格式规则对象
            };

            highlightColorPanel.Controls.Add(clearHighlightColorButton);

            // 添加到表格
            layout.Controls.Add(highlightColorPanel, 0, 5);
            layout.SetColumnSpan(highlightColorPanel, 4);

            mainLayout.Controls.Add(layout, 0, 1);
            groupBox.Controls.Add(mainLayout);

            // 添加各字体组件启用复选框的事件处理
            enableChineseFontCheckBox.CheckedChanged += (sender, e) => UpdateFontFormatControlsEnabled();
            enableWesternFontCheckBox.CheckedChanged += (sender, e) => UpdateFontFormatControlsEnabled();
            enableComplexScriptFontCheckBox.CheckedChanged += (sender, e) => UpdateFontFormatControlsEnabled();
            enableFontColorCheckBox.CheckedChanged += (sender, e) => UpdateFontFormatControlsEnabled();
            enableHighlightColorCheckBox.CheckedChanged += (sender, e) => UpdateFontFormatControlsEnabled();

            return groupBox;
        }

        private FlowLayoutPanel CreateButtonPanel()
        {
            var panel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.RightToLeft,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Margin = new Padding(0, 10, 0, 0)
            };

            var cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                AutoSize = true,
                Margin = new Padding(20, 0, 35, 0)
            };

            var okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK,
                AutoSize = true
            };

            okButton.Click += OkButton_Click;

            panel.Controls.Add(cancelButton);
            panel.Controls.Add(okButton);

            this.AcceptButton = okButton;
            this.CancelButton = cancelButton;

            return panel;
        }

        private void FontColorButton_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog
            {
                Color = fontColorPanel.BackColor,
                FullOpen = true
            };

            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                fontColorPanel.BackColor = colorDialog.Color;
                formatRule.FontColor = colorDialog.Color; // 立即更新格式规则对象
            }
        }

        private void HighlightColorButton_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog
            {
                Color = highlightColorPanel.BackColor != Color.Transparent ?
                    highlightColorPanel.BackColor : Color.Yellow,
                FullOpen = true
            };

            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                highlightColorPanel.BackColor = colorDialog.Color;
                formatRule.HighlightColor = colorDialog.Color; // 立即更新格式规则对象
            }
        }

        // 创建图片格式面板
        private GroupBox CreateImageFormatPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "图片格式",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 9, // 9个主要区域: 主开关、尺寸、环绕、效果、边框、位置、旋转与翻转、超链接、锁定
                AutoSize = true
            };

            // 添加图片格式主开关
            var mainEnablePanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 15),
                BackColor = Color.LightBlue,
                Padding = new Padding(10, 5, 10, 5)
            };

            enableImageFormatCheckBox = new CheckBox
            {
                Text = "启用图片格式处理",
                AutoSize = true,
                Checked = false, // 默认关闭
                Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold)
            };
            mainEnablePanel.Controls.Add(enableImageFormatCheckBox);
            mainLayout.Controls.Add(mainEnablePanel, 0, 0);

            // 添加主开关事件处理
            enableImageFormatCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableImageFormatCheckBox.Checked;

                // 控制所有子功能启用复选框的启用状态
                if (enableImageSizeCheckBox != null) enableImageSizeCheckBox.Enabled = enabled;
                if (enableImageWrapCheckBox != null) enableImageWrapCheckBox.Enabled = enabled;
                if (enableImageEffectCheckBox != null) enableImageEffectCheckBox.Enabled = enabled;
                if (enableImageBorderCheckBox != null) enableImageBorderCheckBox.Enabled = enabled;
                if (enableImagePositionCheckBox != null) enableImagePositionCheckBox.Enabled = enabled;
                if (enableImageRotateFlipCheckBox != null) enableImageRotateFlipCheckBox.Enabled = enabled;
                if (enableImageHyperlinkCheckBox != null) enableImageHyperlinkCheckBox.Enabled = enabled;
                if (enableImageLockingCheckBox != null) enableImageLockingCheckBox.Enabled = enabled;

                // 更新所有图片格式控件的启用状态
                UpdateAllImageFormatControlsEnabled();
            };

            // 图片尺寸设置
            var sizePanel = new GroupBox
            {
                Text = "图片尺寸",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var sizeMainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 添加图片尺寸总启用复选框
            var sizeEnablePanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 10)
            };

            enableImageSizeCheckBox = new CheckBox
            {
                Text = "启用图片尺寸设置",
                AutoSize = true,
                Checked = true // 默认启用
            };
            sizeEnablePanel.Controls.Add(enableImageSizeCheckBox);
            sizeMainLayout.Controls.Add(sizeEnablePanel, 0, 0);

            var sizeLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true
            };

            preserveAspectRatioCheckBox = new CheckBox { Text = "保持长宽比", AutoSize = true, Checked = true, Enabled = enableImageSizeCheckBox.Checked };
            sizeLayout.Controls.Add(preserveAspectRatioCheckBox, 0, 0);
            sizeLayout.SetColumnSpan(preserveAspectRatioCheckBox, 2);

            // 使用精确尺寸或比例选项
            useExactSizeRadio = new RadioButton { Text = "使用精确尺寸", AutoSize = true, Checked = true, Enabled = enableImageSizeCheckBox.Checked };
            sizeLayout.Controls.Add(useExactSizeRadio, 0, 1);

            useScaleRadio = new RadioButton { Text = "使用缩放比例", AutoSize = true, Enabled = enableImageSizeCheckBox.Checked };
            sizeLayout.Controls.Add(useScaleRadio, 1, 1);

            // 精确尺寸控件
            var widthPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            widthPanel.Controls.Add(new Label { Text = "宽度:", Width = 60, TextAlign = ContentAlignment.MiddleLeft });
            imageWidthNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 2000,
                DecimalPlaces = 2,
                Value = 100,
                Width = 80,
                TextAlign = HorizontalAlignment.Center,
                Enabled = enableImageSizeCheckBox.Checked
            };
            widthPanel.Controls.Add(imageWidthNumeric);
            widthPanel.Controls.Add(new Label { Text = "像素", AutoSize = true });
            sizeLayout.Controls.Add(widthPanel, 0, 2);

            var heightPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            heightPanel.Controls.Add(new Label { Text = "高度:", Width = 60, TextAlign = ContentAlignment.MiddleLeft });
            imageHeightNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 2000,
                DecimalPlaces = 2,
                Value = 100,
                Width = 80,
                TextAlign = HorizontalAlignment.Center,
                Enabled = enableImageSizeCheckBox.Checked
            };
            heightPanel.Controls.Add(imageHeightNumeric);
            heightPanel.Controls.Add(new Label { Text = "像素", AutoSize = true });
            sizeLayout.Controls.Add(heightPanel, 1, 2);

            // 缩放比例控件
            var scaleXPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            scaleXPanel.Controls.Add(new Label { Text = "横向比例:", Width = 60, TextAlign = ContentAlignment.MiddleLeft });
            imageScaleXNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 500,
                DecimalPlaces = 2,
                Value = 100,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };
            scaleXPanel.Controls.Add(imageScaleXNumeric);
            scaleXPanel.Controls.Add(new Label { Text = "%", AutoSize = true });
            sizeLayout.Controls.Add(scaleXPanel, 0, 3);

            var scaleYPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            scaleYPanel.Controls.Add(new Label { Text = "纵向比例:", Width = 60, TextAlign = ContentAlignment.MiddleLeft });
            imageScaleYNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 500,
                DecimalPlaces = 2,
                Value = 100,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };
            scaleYPanel.Controls.Add(imageScaleYNumeric);
            scaleYPanel.Controls.Add(new Label { Text = "%", AutoSize = true });
            sizeLayout.Controls.Add(scaleYPanel, 1, 3);

            // 添加事件处理
            enableImageSizeCheckBox.CheckedChanged += (sender, e) => UpdateImageSizeControlsEnabled();

            useExactSizeRadio.CheckedChanged += (sender, e) => UpdateImageSizeControlsEnabled();
            useScaleRadio.CheckedChanged += (sender, e) => UpdateImageSizeControlsEnabled();
            preserveAspectRatioCheckBox.CheckedChanged += (sender, e) => UpdateImageSizeControlsEnabled();

            sizeMainLayout.Controls.Add(sizeLayout, 0, 1);
            sizePanel.Controls.Add(sizeMainLayout);
            mainLayout.Controls.Add(sizePanel, 0, 1);

            // 图片文本环绕设置
            var wrapPanel = new GroupBox
            {
                Text = "文本环绕",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var wrapMainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 添加文本环绕总启用复选框
            var wrapEnablePanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 10)
            };

            enableImageWrapCheckBox = new CheckBox
            {
                Text = "启用文本环绕设置",
                AutoSize = true,
                Checked = true // 默认启用
            };
            wrapEnablePanel.Controls.Add(enableImageWrapCheckBox);
            wrapMainLayout.Controls.Add(wrapEnablePanel, 0, 0);

            var wrapLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true
            };

            wrapLayout.Controls.Add(new Label { Text = "环绕方式:", AutoSize = true }, 0, 0);

            imageWrapTypeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = enableImageWrapCheckBox.Checked
            };

            // 设置下拉框文字居中显示
            imageWrapTypeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = imageWrapTypeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            imageWrapTypeComboBox.Items.Add("嵌入式");
            imageWrapTypeComboBox.Items.Add("四周型");
            imageWrapTypeComboBox.Items.Add("紧密型");
            imageWrapTypeComboBox.Items.Add("穿越型");
            imageWrapTypeComboBox.Items.Add("上下型");
            imageWrapTypeComboBox.SelectedIndex = 0;

            wrapLayout.Controls.Add(imageWrapTypeComboBox, 1, 0);

            var wrapTextPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            wrapTextNormalRadio = new RadioButton { Text = "文字在前景", AutoSize = true, Checked = true, Enabled = enableImageWrapCheckBox.Checked };
            wrapTextBehindRadio = new RadioButton { Text = "文字在背景", AutoSize = true, Enabled = enableImageWrapCheckBox.Checked };
            wrapTextFrontRadio = new RadioButton { Text = "文字在前景", AutoSize = true, Enabled = enableImageWrapCheckBox.Checked };

            wrapTextPanel.Controls.Add(wrapTextNormalRadio);
            wrapTextPanel.Controls.Add(wrapTextBehindRadio);
            wrapTextPanel.Controls.Add(wrapTextFrontRadio);

            wrapLayout.Controls.Add(new Label { Text = "文本层次:", AutoSize = true }, 0, 1);
            wrapLayout.Controls.Add(wrapTextPanel, 1, 1);

            // 添加文本环绕事件处理
            enableImageWrapCheckBox.CheckedChanged += (sender, e) => UpdateImageWrapControlsEnabled();

            wrapMainLayout.Controls.Add(wrapLayout, 0, 1);
            wrapPanel.Controls.Add(wrapMainLayout);
            mainLayout.Controls.Add(wrapPanel, 0, 2);

            // 图片效果设置
            var effectPanel = new GroupBox
            {
                Text = "图片效果",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var effectMainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 添加图片效果总启用复选框
            var effectEnablePanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 10)
            };

            enableImageEffectCheckBox = new CheckBox
            {
                Text = "启用图片效果设置",
                AutoSize = true,
                Checked = true // 默认启用
            };
            effectEnablePanel.Controls.Add(enableImageEffectCheckBox);
            effectMainLayout.Controls.Add(effectEnablePanel, 0, 0);

            var effectLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 4,
                AutoSize = true
            };

            // 亮度控制
            enableBrightnessCheckBox = new CheckBox { Text = "亮度", AutoSize = true, Checked = true, Enabled = enableImageEffectCheckBox.Checked };
            effectLayout.Controls.Add(enableBrightnessCheckBox, 0, 0);

            var brightnessPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            brightnessTrackBar = new TrackBar
            {
                Minimum = -100,
                Maximum = 100,
                TickFrequency = 10,
                Value = 0,
                Width = 150,
                Enabled = enableImageEffectCheckBox.Checked && enableBrightnessCheckBox.Checked
            };

            brightnessValueLabel = new Label { Text = "0%", AutoSize = true, Width = 40 };

            brightnessTrackBar.ValueChanged += (sender, e) =>
            {
                brightnessValueLabel.Text = $"{brightnessTrackBar.Value}%";
            };

            brightnessPanel.Controls.Add(brightnessTrackBar);
            brightnessPanel.Controls.Add(brightnessValueLabel);
            effectLayout.Controls.Add(brightnessPanel, 1, 0);

            // 对比度控制
            enableContrastCheckBox = new CheckBox { Text = "对比度", AutoSize = true, Checked = true, Enabled = enableImageEffectCheckBox.Checked };
            effectLayout.Controls.Add(enableContrastCheckBox, 0, 1);

            var contrastPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            contrastTrackBar = new TrackBar
            {
                Minimum = -100,
                Maximum = 100,
                TickFrequency = 10,
                Value = 0,
                Width = 150,
                Enabled = enableImageEffectCheckBox.Checked && enableContrastCheckBox.Checked
            };

            contrastValueLabel = new Label { Text = "0%", AutoSize = true, Width = 40 };

            contrastTrackBar.ValueChanged += (sender, e) =>
            {
                contrastValueLabel.Text = $"{contrastTrackBar.Value}%";
            };

            contrastPanel.Controls.Add(contrastTrackBar);
            contrastPanel.Controls.Add(contrastValueLabel);
            effectLayout.Controls.Add(contrastPanel, 1, 1);

            // 透明度控制
            enableTransparencyCheckBox = new CheckBox { Text = "透明度", AutoSize = true, Checked = true, Enabled = enableImageEffectCheckBox.Checked };
            effectLayout.Controls.Add(enableTransparencyCheckBox, 0, 2);

            var transparencyPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            transparencyTrackBar = new TrackBar
            {
                Minimum = 0,
                Maximum = 100,
                TickFrequency = 10,
                Value = 0,
                Width = 150,
                Enabled = enableImageEffectCheckBox.Checked && enableTransparencyCheckBox.Checked
            };

            transparencyValueLabel = new Label { Text = "0%", AutoSize = true, Width = 40 };

            transparencyTrackBar.ValueChanged += (sender, e) =>
            {
                transparencyValueLabel.Text = $"{transparencyTrackBar.Value}%";
            };

            transparencyPanel.Controls.Add(transparencyTrackBar);
            transparencyPanel.Controls.Add(transparencyValueLabel);
            effectLayout.Controls.Add(transparencyPanel, 1, 2);

            // 颜色模式
            enableColorModeCheckBox = new CheckBox { Text = "颜色模式", AutoSize = true, Checked = true, Enabled = enableImageEffectCheckBox.Checked };
            effectLayout.Controls.Add(enableColorModeCheckBox, 0, 3);

            colorModeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                DrawMode = DrawMode.OwnerDrawFixed,
                Enabled = enableImageEffectCheckBox.Checked && enableColorModeCheckBox.Checked
            };

            // 设置下拉框文字居中显示
            colorModeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = colorModeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            colorModeComboBox.Items.Add("自动");
            colorModeComboBox.Items.Add("彩色");
            colorModeComboBox.Items.Add("灰度");
            colorModeComboBox.Items.Add("黑白");
            colorModeComboBox.SelectedIndex = 0;

            effectLayout.Controls.Add(colorModeComboBox, 1, 3);

            // 添加图片效果事件处理
            enableImageEffectCheckBox.CheckedChanged += (sender, e) => UpdateImageEffectControlsEnabled();
            enableBrightnessCheckBox.CheckedChanged += (sender, e) => UpdateImageEffectControlsEnabled();
            enableContrastCheckBox.CheckedChanged += (sender, e) => UpdateImageEffectControlsEnabled();
            enableTransparencyCheckBox.CheckedChanged += (sender, e) => UpdateImageEffectControlsEnabled();
            enableColorModeCheckBox.CheckedChanged += (sender, e) => UpdateImageEffectControlsEnabled();

            effectMainLayout.Controls.Add(effectLayout, 0, 1);
            effectPanel.Controls.Add(effectMainLayout);
            mainLayout.Controls.Add(effectPanel, 0, 3);

            // 图片边框设置
            var borderPanel = new GroupBox
            {
                Text = "图片边框",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var borderLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true
            };

            enableImageBorderCheckBox = new CheckBox { Text = "启用边框", AutoSize = true };
            borderLayout.Controls.Add(enableImageBorderCheckBox, 0, 0);
            borderLayout.SetColumnSpan(enableImageBorderCheckBox, 2);

            borderLayout.Controls.Add(new Label { Text = "边框样式:", AutoSize = true }, 0, 1);

            imageBorderStyleComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            imageBorderStyleComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = imageBorderStyleComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            imageBorderStyleComboBox.Items.Add("实线");
            imageBorderStyleComboBox.Items.Add("虚线");
            imageBorderStyleComboBox.Items.Add("点划线");
            imageBorderStyleComboBox.SelectedIndex = 0;

            borderLayout.Controls.Add(imageBorderStyleComboBox, 1, 1);

            borderLayout.Controls.Add(new Label { Text = "边框宽度:", AutoSize = true }, 0, 2);

            imageBorderWidthNumeric = new NumericUpDown
            {
                Minimum = 0.25M,
                Maximum = 10M,
                DecimalPlaces = 2,
                Increment = 0.25M,
                Value = 1M,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };

            borderLayout.Controls.Add(imageBorderWidthNumeric, 1, 2);

            borderLayout.Controls.Add(new Label { Text = "边框颜色:", AutoSize = true }, 0, 3);

            var borderColorPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            imageBorderColorButton = new Button
            {
                Text = "选择...",
                Width = 80,
                Enabled = false
            };

            imageBorderColorPanel = new Panel
            {
                BackColor = Color.Black,
                Size = new Size(24, 24),
                BorderStyle = BorderStyle.FixedSingle
            };

            imageBorderColorButton.Click += (sender, e) =>
            {
                using var colorDialog = new ColorDialog
                {
                    Color = imageBorderColorPanel.BackColor,
                    FullOpen = true
                };

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    imageBorderColorPanel.BackColor = colorDialog.Color;
                    formatRule.ImageBorderColor = colorDialog.Color; // 立即更新格式规则对象
                }
            };

            clearImageBorderColorButton = new Button
            {
                Text = "清除",
                Width = 60,
                Enabled = false
            };

            clearImageBorderColorButton.Click += (sender, e) =>
            {
                // 重置为黑色
                imageBorderColorPanel.BackColor = Color.Black;
                formatRule.ImageBorderColor = Color.Black; // 立即更新格式规则对象
            };

            borderColorPanel.Controls.Add(imageBorderColorButton);
            borderColorPanel.Controls.Add(imageBorderColorPanel);
            borderColorPanel.Controls.Add(clearImageBorderColorButton);

            borderLayout.Controls.Add(borderColorPanel, 1, 3);

            // 启用/禁用边框相关控件
            enableImageBorderCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableImageBorderCheckBox.Checked;
                imageBorderStyleComboBox.Enabled = enabled;
                imageBorderWidthNumeric.Enabled = enabled;
                imageBorderColorButton.Enabled = enabled;
                clearImageBorderColorButton.Enabled = enabled;
            };

            borderPanel.Controls.Add(borderLayout);
            mainLayout.Controls.Add(borderPanel, 0, 4);

            // 图片位置设置
            var positionPanel = new GroupBox
            {
                Text = "图片位置",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var positionLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 6,
                AutoSize = true
            };

            enableImagePositionCheckBox = new CheckBox { Text = "启用位置设置", AutoSize = true };
            positionLayout.Controls.Add(enableImagePositionCheckBox, 0, 0);
            positionLayout.SetColumnSpan(enableImagePositionCheckBox, 2);

            positionLayout.Controls.Add(new Label { Text = "水平相对于:", AutoSize = true }, 0, 1);

            horizontalRelativeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            horizontalRelativeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = horizontalRelativeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            horizontalRelativeComboBox.Items.Add("页面");
            horizontalRelativeComboBox.Items.Add("页边距");
            horizontalRelativeComboBox.Items.Add("列");
            horizontalRelativeComboBox.SelectedIndex = 0;

            positionLayout.Controls.Add(horizontalRelativeComboBox, 1, 1);

            positionLayout.Controls.Add(new Label { Text = "垂直相对于:", AutoSize = true }, 0, 2);

            verticalRelativeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            verticalRelativeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = verticalRelativeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            verticalRelativeComboBox.Items.Add("页面");
            verticalRelativeComboBox.Items.Add("页边距");
            verticalRelativeComboBox.Items.Add("段落");
            verticalRelativeComboBox.SelectedIndex = 0;

            positionLayout.Controls.Add(verticalRelativeComboBox, 1, 2);

            // 对齐方式或精确位置选择
            useAlignmentRadio = new RadioButton { Text = "使用对齐方式", AutoSize = true, Checked = true, Enabled = false };
            positionLayout.Controls.Add(useAlignmentRadio, 0, 3);

            useExactPositionRadio = new RadioButton { Text = "使用精确位置", AutoSize = true, Enabled = false };
            positionLayout.Controls.Add(useExactPositionRadio, 1, 3);

            // 对齐方式选项
            var horizontalAlignPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            horizontalAlignPanel.Controls.Add(new Label { Text = "水平对齐:", Width = 70, TextAlign = ContentAlignment.MiddleLeft });

            // 创建水平对齐ComboBox
            horizontalAlignmentComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 100,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            horizontalAlignmentComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = horizontalAlignmentComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            horizontalAlignmentComboBox.Items.Add("左对齐");
            horizontalAlignmentComboBox.Items.Add("居中对齐");
            horizontalAlignmentComboBox.Items.Add("右对齐");
            horizontalAlignmentComboBox.SelectedIndex = 1; // 默认居中

            horizontalAlignPanel.Controls.Add(horizontalAlignmentComboBox);
            positionLayout.Controls.Add(horizontalAlignPanel, 0, 4);

            var verticalAlignPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            verticalAlignPanel.Controls.Add(new Label { Text = "垂直对齐:", Width = 70, TextAlign = ContentAlignment.MiddleLeft });

            // 创建垂直对齐ComboBox
            verticalAlignmentComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 100,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            verticalAlignmentComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = verticalAlignmentComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            verticalAlignmentComboBox.Items.Add("顶端对齐");
            verticalAlignmentComboBox.Items.Add("居中对齐");
            verticalAlignmentComboBox.Items.Add("底端对齐");
            verticalAlignmentComboBox.SelectedIndex = 1; // 默认居中

            verticalAlignPanel.Controls.Add(verticalAlignmentComboBox);
            positionLayout.Controls.Add(verticalAlignPanel, 1, 4);

            // 精确位置选项
            var horizontalPosPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            horizontalPosPanel.Controls.Add(new Label { Text = "水平位置:", Width = 70, TextAlign = ContentAlignment.MiddleLeft });

            horizontalPositionNumeric = new NumericUpDown
            {
                Minimum = -1000M,
                Maximum = 1000M,
                DecimalPlaces = 2,
                Value = 0M,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };

            horizontalPosPanel.Controls.Add(horizontalPositionNumeric);
            horizontalPosPanel.Controls.Add(new Label { Text = "厘米", AutoSize = true });
            positionLayout.Controls.Add(horizontalPosPanel, 0, 5);

            var verticalPosPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            verticalPosPanel.Controls.Add(new Label { Text = "垂直位置:", Width = 70, TextAlign = ContentAlignment.MiddleLeft });

            verticalPositionNumeric = new NumericUpDown
            {
                Minimum = -1000M,
                Maximum = 1000M,
                DecimalPlaces = 2,
                Value = 0M,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };

            verticalPosPanel.Controls.Add(verticalPositionNumeric);
            verticalPosPanel.Controls.Add(new Label { Text = "厘米", AutoSize = true });
            positionLayout.Controls.Add(verticalPosPanel, 1, 5);

            // 事件处理
            enableImagePositionCheckBox.CheckedChanged += (sender, e) => UpdatePositionControlsState();
            useAlignmentRadio.CheckedChanged += (sender, e) => UpdatePositionControlsState();
            useExactPositionRadio.CheckedChanged += (sender, e) => UpdatePositionControlsState();

            positionPanel.Controls.Add(positionLayout);
            mainLayout.Controls.Add(positionPanel, 0, 5);

            // 图片旋转和翻转
            var rotateFlipPanel = new GroupBox
            {
                Text = "旋转和翻转",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var rotateFlipLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true
            };

            enableImageRotateFlipCheckBox = new CheckBox { Text = "启用旋转和翻转", AutoSize = true };
            rotateFlipLayout.Controls.Add(enableImageRotateFlipCheckBox, 0, 0);
            rotateFlipLayout.SetColumnSpan(enableImageRotateFlipCheckBox, 2);

            // 旋转角度
            var rotationPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            rotationPanel.Controls.Add(new Label { Text = "旋转角度:", Width = 70, TextAlign = ContentAlignment.MiddleLeft });

            rotationAngleNumeric = new NumericUpDown
            {
                Minimum = 0M,
                Maximum = 359.9M,
                DecimalPlaces = 1,
                Increment = 0.5M,
                Value = 0M,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };

            rotationPanel.Controls.Add(rotationAngleNumeric);
            rotationPanel.Controls.Add(new Label { Text = "度", AutoSize = true });
            rotateFlipLayout.Controls.Add(rotationPanel, 0, 1);
            rotateFlipLayout.SetColumnSpan(rotationPanel, 2);

            // 翻转选项
            flipHorizontalCheckBox = new CheckBox { Text = "水平翻转", AutoSize = true, Enabled = false };
            rotateFlipLayout.Controls.Add(flipHorizontalCheckBox, 0, 2);

            flipVerticalCheckBox = new CheckBox { Text = "垂直翻转", AutoSize = true, Enabled = false };
            rotateFlipLayout.Controls.Add(flipVerticalCheckBox, 1, 2);

            // 事件处理
            enableImageRotateFlipCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableImageRotateFlipCheckBox.Checked;
                rotationAngleNumeric.Enabled = enabled;
                flipHorizontalCheckBox.Enabled = enabled;
                flipVerticalCheckBox.Enabled = enabled;
            };

            rotateFlipPanel.Controls.Add(rotateFlipLayout);
            mainLayout.Controls.Add(rotateFlipPanel, 0, 6);

            // 图片超链接设置
            var hyperlinkPanel = new GroupBox
            {
                Text = "超链接设置",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var hyperlinkLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 5,
                AutoSize = true
            };

            enableImageHyperlinkCheckBox = new CheckBox { Text = "启用超链接", AutoSize = true };
            hyperlinkLayout.Controls.Add(enableImageHyperlinkCheckBox, 0, 0);
            hyperlinkLayout.SetColumnSpan(enableImageHyperlinkCheckBox, 2);

            // 超链接类型
            hyperlinkLayout.Controls.Add(new Label { Text = "链接类型:", AutoSize = true }, 0, 1);

            hyperlinkTypeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            hyperlinkTypeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = hyperlinkTypeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            hyperlinkTypeComboBox.Items.Add("网址(URL)");
            hyperlinkTypeComboBox.Items.Add("书签");
            hyperlinkTypeComboBox.Items.Add("文件");
            hyperlinkTypeComboBox.SelectedIndex = 0;

            hyperlinkLayout.Controls.Add(hyperlinkTypeComboBox, 1, 1);

            // URL/文件路径
            hyperlinkLayout.Controls.Add(new Label { Text = "链接地址:", AutoSize = true }, 0, 2);

            hyperlinkUrlTextBox = new TextBox
            {
                Width = 250,
                Enabled = false,
                PlaceholderText = "输入网址、文件路径或书签名称"
            };

            hyperlinkLayout.Controls.Add(hyperlinkUrlTextBox, 1, 2);

            // 书签名称
            hyperlinkLayout.Controls.Add(new Label { Text = "书签名称:", AutoSize = true }, 0, 3);

            bookmarkNameTextBox = new TextBox
            {
                Width = 250,
                Enabled = false,
                PlaceholderText = "输入书签名称"
            };

            hyperlinkLayout.Controls.Add(bookmarkNameTextBox, 1, 3);

            // 工具提示
            hyperlinkLayout.Controls.Add(new Label { Text = "工具提示:", AutoSize = true }, 0, 4);

            var hyperlinkToolTipPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            hyperlinkToolTipTextBox = new TextBox
            {
                Width = 200,
                Enabled = false,
                PlaceholderText = "鼠标悬停时显示的提示文字"
            };

            openInNewWindowCheckBox = new CheckBox
            {
                Text = "在新窗口中打开",
                AutoSize = true,
                Enabled = false
            };

            hyperlinkToolTipPanel.Controls.Add(hyperlinkToolTipTextBox);
            hyperlinkToolTipPanel.Controls.Add(openInNewWindowCheckBox);

            hyperlinkLayout.Controls.Add(hyperlinkToolTipPanel, 1, 4);

            // 事件处理
            enableImageHyperlinkCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableImageHyperlinkCheckBox.Checked;
                hyperlinkTypeComboBox.Enabled = enabled;
                hyperlinkUrlTextBox.Enabled = enabled;
                bookmarkNameTextBox.Enabled = enabled;
                hyperlinkToolTipTextBox.Enabled = enabled;
                openInNewWindowCheckBox.Enabled = enabled;
            };

            hyperlinkPanel.Controls.Add(hyperlinkLayout);
            mainLayout.Controls.Add(hyperlinkPanel, 0, 7);

            // 图片锁定设置
            var lockingPanel = new GroupBox
            {
                Text = "锁定设置",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var lockingLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true
            };

            enableImageLockingCheckBox = new CheckBox { Text = "启用锁定功能", AutoSize = true };
            lockingLayout.Controls.Add(enableImageLockingCheckBox, 0, 0);
            lockingLayout.SetColumnSpan(enableImageLockingCheckBox, 2);

            // 锁定选项
            var lockingOptionsPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                WrapContents = true
            };

            lockPositionCheckBox = new CheckBox
            {
                Text = "锁定位置",
                AutoSize = true,
                Enabled = false
            };

            lockAspectRatioCheckBox = new CheckBox
            {
                Text = "锁定纵横比",
                AutoSize = true,
                Enabled = false
            };

            lockFormattingCheckBox = new CheckBox
            {
                Text = "锁定格式",
                AutoSize = true,
                Enabled = false
            };

            lockingOptionsPanel.Controls.Add(lockPositionCheckBox);
            lockingOptionsPanel.Controls.Add(lockAspectRatioCheckBox);
            lockingOptionsPanel.Controls.Add(lockFormattingCheckBox);

            lockingLayout.Controls.Add(lockingOptionsPanel, 0, 1);
            lockingLayout.SetColumnSpan(lockingOptionsPanel, 2);

            // 事件处理
            enableImageLockingCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableImageLockingCheckBox.Checked;
                lockPositionCheckBox.Enabled = enabled;
                lockAspectRatioCheckBox.Enabled = enabled;
                lockFormattingCheckBox.Enabled = enabled;
            };

            lockingPanel.Controls.Add(lockingLayout);
            mainLayout.Controls.Add(lockingPanel, 0, 8);

            groupBox.Controls.Add(mainLayout);
            return groupBox;
        }

        // 创建水印格式面板
        private GroupBox CreateWatermarkFormatPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "水印格式",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 5, // 5个主要区域: 文本水印、图片水印、位置、格式、其他设置
                AutoSize = true
            };

            // 文本水印设置
            var textWatermarkPanel = new GroupBox
            {
                Text = "文本水印",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var textWatermarkLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 6,
                AutoSize = true
            };

            // 启用水印
            enableWatermarkCheckBox = new CheckBox { Text = "启用文本水印", AutoSize = true };
            textWatermarkLayout.Controls.Add(enableWatermarkCheckBox, 0, 0);
            textWatermarkLayout.SetColumnSpan(enableWatermarkCheckBox, 2);

            // 水印文本
            textWatermarkLayout.Controls.Add(new Label { Text = "水印文本:", AutoSize = true }, 0, 1);

            watermarkTextBox = new TextBox
            {
                Width = 250,
                Enabled = false,
                Text = "CONFIDENTIAL"
            };

            textWatermarkLayout.Controls.Add(watermarkTextBox, 1, 1);

            // 字体设置
            textWatermarkLayout.Controls.Add(new Label { Text = "字体:", AutoSize = true }, 0, 2);

            watermarkFontFamilyComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            watermarkFontFamilyComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = watermarkFontFamilyComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            // 添加系统字体
            foreach (var fontFamily in System.Drawing.FontFamily.Families)
            {
                watermarkFontFamilyComboBox.Items.Add(fontFamily.Name);
            }

            // 设置默认字体
            for (int i = 0; i < watermarkFontFamilyComboBox.Items.Count; i++)
            {
                if (watermarkFontFamilyComboBox.Items[i].ToString() == "Arial")
                {
                    watermarkFontFamilyComboBox.SelectedIndex = i;
                    break;
                }
            }

            if (watermarkFontFamilyComboBox.SelectedIndex < 0 && watermarkFontFamilyComboBox.Items.Count > 0)
            {
                watermarkFontFamilyComboBox.SelectedIndex = 0;
            }

            textWatermarkLayout.Controls.Add(watermarkFontFamilyComboBox, 1, 2);

            // 字体大小
            textWatermarkLayout.Controls.Add(new Label { Text = "字体大小:", AutoSize = true }, 0, 3);

            watermarkFontSizeNumeric = new NumericUpDown
            {
                Minimum = 8M,
                Maximum = 144M,
                DecimalPlaces = 0,
                Value = 48M,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };

            var watermarkFontSizePanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };
            watermarkFontSizePanel.Controls.Add(watermarkFontSizeNumeric);
            watermarkFontSizePanel.Controls.Add(new Label { Text = "磅", AutoSize = true });

            textWatermarkLayout.Controls.Add(watermarkFontSizePanel, 1, 3);

            // 水印颜色
            textWatermarkLayout.Controls.Add(new Label { Text = "颜色:", AutoSize = true }, 0, 4);

            var watermarkColorFlowPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            watermarkColorButton = new Button
            {
                Text = "选择...",
                Width = 80,
                Enabled = false
            };

            watermarkColorPanel = new Panel
            {
                BackColor = Color.FromArgb(128, Color.Gray),
                Size = new Size(24, 24),
                BorderStyle = BorderStyle.FixedSingle
            };

            watermarkColorButton.Click += (sender, e) =>
            {
                using var colorDialog = new ColorDialog
                {
                    Color = watermarkColorPanel.BackColor,
                    FullOpen = true
                };

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    watermarkColorPanel.BackColor = colorDialog.Color;
                    formatRule.WatermarkColor = colorDialog.Color; // 立即更新格式规则对象
                }
            };

            clearWatermarkColorButton = new Button
            {
                Text = "清除",
                Width = 60,
                Enabled = false
            };

            clearWatermarkColorButton.Click += (sender, e) =>
            {
                // 重置为半透明灰色
                var defaultColor = Color.FromArgb(128, Color.Gray);
                watermarkColorPanel.BackColor = defaultColor;
                formatRule.WatermarkColor = defaultColor; // 立即更新格式规则对象
            };

            watermarkColorFlowPanel.Controls.Add(watermarkColorButton);
            watermarkColorFlowPanel.Controls.Add(watermarkColorPanel);
            watermarkColorFlowPanel.Controls.Add(clearWatermarkColorButton);

            textWatermarkLayout.Controls.Add(watermarkColorFlowPanel, 1, 4);

            // 不透明度
            textWatermarkLayout.Controls.Add(new Label { Text = "不透明度:", AutoSize = true }, 0, 5);

            var opacityPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            watermarkOpacityTrackBar = new TrackBar
            {
                Minimum = 0,
                Maximum = 100,
                TickFrequency = 10,
                Value = 30,
                Width = 150,
                Enabled = false
            };

            watermarkOpacityValueLabel = new Label { Text = "30%", AutoSize = true, Width = 40 };

            watermarkOpacityTrackBar.ValueChanged += (sender, e) =>
            {
                watermarkOpacityValueLabel.Text = $"{watermarkOpacityTrackBar.Value}%";
            };

            opacityPanel.Controls.Add(watermarkOpacityTrackBar);
            opacityPanel.Controls.Add(watermarkOpacityValueLabel);

            textWatermarkLayout.Controls.Add(opacityPanel, 1, 5);

            // 旋转角度
            var rotationPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            rotationPanel.Controls.Add(new Label { Text = "旋转角度:", Width = 80, TextAlign = ContentAlignment.MiddleLeft });

            watermarkRotationAngleNumeric = new NumericUpDown
            {
                Minimum = -360M,
                Maximum = 360M,
                DecimalPlaces = 1,
                Increment = 0.5M,
                Value = -45M, // 默认对角线方向
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };

            rotationPanel.Controls.Add(watermarkRotationAngleNumeric);
            rotationPanel.Controls.Add(new Label { Text = "度", AutoSize = true });

            textWatermarkLayout.Controls.Add(rotationPanel, 0, 6);
            textWatermarkLayout.SetColumnSpan(rotationPanel, 2);

            // 启用/禁用文本水印相关控件的事件处理
            enableWatermarkCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableWatermarkCheckBox.Checked;
                watermarkTextBox.Enabled = enabled;
                watermarkFontFamilyComboBox.Enabled = enabled;
                watermarkFontSizeNumeric.Enabled = enabled;
                watermarkColorButton.Enabled = enabled;
                clearWatermarkColorButton.Enabled = enabled;
                watermarkOpacityTrackBar.Enabled = enabled;
                watermarkRotationAngleNumeric.Enabled = enabled;
            };

            textWatermarkPanel.Controls.Add(textWatermarkLayout);
            mainLayout.Controls.Add(textWatermarkPanel, 0, 0);

            // 图片水印设置
            var imageWatermarkPanel = new GroupBox
            {
                Text = "图片水印",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var imageWatermarkLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true
            };

            // 启用图片水印
            enableImageWatermarkCheckBox = new CheckBox { Text = "启用图片水印", AutoSize = true };
            imageWatermarkLayout.Controls.Add(enableImageWatermarkCheckBox, 0, 0);
            imageWatermarkLayout.SetColumnSpan(enableImageWatermarkCheckBox, 2);

            // 图片路径
            imageWatermarkLayout.Controls.Add(new Label { Text = "图片路径:", AutoSize = true }, 0, 1);

            var imagePathPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            watermarkImagePathTextBox = new TextBox
            {
                Width = 200,
                Enabled = false
            };

            browseImageButton = new Button
            {
                Text = "浏览...",
                Width = 80,
                Enabled = false
            };

            browseImageButton.Click += (sender, e) =>
            {
                var openFileDialog = new OpenFileDialog
                {
                    Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif|所有文件|*.*",
                    Title = "选择水印图片"
                };

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    watermarkImagePathTextBox.Text = openFileDialog.FileName;
                }
            };

            imagePathPanel.Controls.Add(watermarkImagePathTextBox);
            imagePathPanel.Controls.Add(browseImageButton);

            imageWatermarkLayout.Controls.Add(imagePathPanel, 1, 1);

            // 水印布局
            imageWatermarkLayout.Controls.Add(new Label { Text = "水印布局:", AutoSize = true }, 0, 2);

            imageWatermarkLayoutComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            imageWatermarkLayoutComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = imageWatermarkLayoutComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            imageWatermarkLayoutComboBox.Items.Add("对角线");
            imageWatermarkLayoutComboBox.Items.Add("水平平铺");
            imageWatermarkLayoutComboBox.SelectedIndex = 0;

            imageWatermarkLayout.Controls.Add(imageWatermarkLayoutComboBox, 1, 2);

            // 启用/禁用图片水印相关控件的事件处理
            enableImageWatermarkCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableImageWatermarkCheckBox.Checked;
                watermarkImagePathTextBox.Enabled = enabled;
                browseImageButton.Enabled = enabled;
                imageWatermarkLayoutComboBox.Enabled = enabled;
            };

            imageWatermarkPanel.Controls.Add(imageWatermarkLayout);
            mainLayout.Controls.Add(imageWatermarkPanel, 0, 1);

            // 水印位置设置
            var positionPanel = new GroupBox
            {
                Text = "位置设置",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var positionLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 6,
                AutoSize = true
            };

            // 启用位置设置
            enableWatermarkPositionCheckBox = new CheckBox { Text = "启用位置设置", AutoSize = true };
            positionLayout.Controls.Add(enableWatermarkPositionCheckBox, 0, 0);
            positionLayout.SetColumnSpan(enableWatermarkPositionCheckBox, 2);

            // 水印位置选项
            var positionRadioPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            watermarkPositionCenterRadio = new RadioButton { Text = "页面中心", AutoSize = true, Checked = true, Enabled = false };
            watermarkPositionCustomRadio = new RadioButton { Text = "自定义位置", AutoSize = true, Enabled = false };

            positionRadioPanel.Controls.Add(watermarkPositionCenterRadio);
            positionRadioPanel.Controls.Add(watermarkPositionCustomRadio);

            positionLayout.Controls.Add(positionRadioPanel, 0, 1);
            positionLayout.SetColumnSpan(positionRadioPanel, 2);

            // 自定义位置 - 水平位置
            var horizontalPosPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            horizontalPosPanel.Controls.Add(new Label { Text = "水平位置:", Width = 70, TextAlign = ContentAlignment.MiddleLeft });

            watermarkHorizontalPositionNumeric = new NumericUpDown
            {
                Minimum = -1000M,
                Maximum = 1000M,
                DecimalPlaces = 2,
                Value = 0M,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };

            horizontalPosPanel.Controls.Add(watermarkHorizontalPositionNumeric);
            horizontalPosPanel.Controls.Add(new Label { Text = "厘米", AutoSize = true });

            positionLayout.Controls.Add(horizontalPosPanel, 0, 2);

            // 自定义位置 - 垂直位置
            var verticalPosPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            verticalPosPanel.Controls.Add(new Label { Text = "垂直位置:", Width = 70, TextAlign = ContentAlignment.MiddleLeft });

            watermarkVerticalPositionNumeric = new NumericUpDown
            {
                Minimum = -1000M,
                Maximum = 1000M,
                DecimalPlaces = 2,
                Value = 0M,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };

            verticalPosPanel.Controls.Add(watermarkVerticalPositionNumeric);
            verticalPosPanel.Controls.Add(new Label { Text = "厘米", AutoSize = true });

            positionLayout.Controls.Add(verticalPosPanel, 1, 2);

            // 水印应用页面选项
            var pagesRadioPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            watermarkAllPagesRadio = new RadioButton { Text = "所有页面", AutoSize = true, Checked = true, Enabled = false };
            watermarkSpecificPagesRadio = new RadioButton { Text = "指定页面", AutoSize = true, Enabled = false };

            pagesRadioPanel.Controls.Add(watermarkAllPagesRadio);
            pagesRadioPanel.Controls.Add(watermarkSpecificPagesRadio);

            positionLayout.Controls.Add(pagesRadioPanel, 0, 3);
            positionLayout.SetColumnSpan(pagesRadioPanel, 2);

            // 指定页面范围
            var pageRangePanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            pageRangePanel.Controls.Add(new Label { Text = "页面范围:", Width = 70, TextAlign = ContentAlignment.MiddleLeft });

            watermarkPageRangeTextBox = new TextBox
            {
                PlaceholderText = "例如: 1,3,5-7",
                Width = 150,
                Enabled = false
            };

            pageRangePanel.Controls.Add(watermarkPageRangeTextBox);

            positionLayout.Controls.Add(pageRangePanel, 0, 4);
            positionLayout.SetColumnSpan(pageRangePanel, 2);

            // 水印在内容背后
            watermarkBehindContentCheckBox = new CheckBox { Text = "水印在内容背后", AutoSize = true, Checked = true, Enabled = false };
            positionLayout.Controls.Add(watermarkBehindContentCheckBox, 0, 5);
            positionLayout.SetColumnSpan(watermarkBehindContentCheckBox, 2);

            // 事件处理
            enableWatermarkPositionCheckBox.CheckedChanged += (sender, e) => UpdateWatermarkPositionControlsState();
            watermarkPositionCenterRadio.CheckedChanged += (sender, e) => UpdateWatermarkPositionControlsState();
            watermarkPositionCustomRadio.CheckedChanged += (sender, e) => UpdateWatermarkPositionControlsState();
            watermarkSpecificPagesRadio.CheckedChanged += (sender, e) => UpdateWatermarkPositionControlsState();

            positionPanel.Controls.Add(positionLayout);
            mainLayout.Controls.Add(positionPanel, 0, 2);

            // 水印格式设置
            var formatPanel = new GroupBox
            {
                Text = "格式设置",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var formatLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 5,
                AutoSize = true
            };

            // 启用格式设置
            enableWatermarkFormatCheckBox = new CheckBox { Text = "启用格式设置", AutoSize = true };
            formatLayout.Controls.Add(enableWatermarkFormatCheckBox, 0, 0);
            formatLayout.SetColumnSpan(enableWatermarkFormatCheckBox, 2);

            // 水印边框
            watermarkHasBorderCheckBox = new CheckBox { Text = "显示边框", AutoSize = true, Enabled = false };
            formatLayout.Controls.Add(watermarkHasBorderCheckBox, 0, 1);
            formatLayout.SetColumnSpan(watermarkHasBorderCheckBox, 2);

            // 边框样式
            formatLayout.Controls.Add(new Label { Text = "边框样式:", AutoSize = true }, 0, 2);

            watermarkBorderStyleComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            watermarkBorderStyleComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = watermarkBorderStyleComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            watermarkBorderStyleComboBox.Items.Add("实线");
            watermarkBorderStyleComboBox.Items.Add("虚线");
            watermarkBorderStyleComboBox.Items.Add("点划线");
            watermarkBorderStyleComboBox.SelectedIndex = 0;

            formatLayout.Controls.Add(watermarkBorderStyleComboBox, 1, 2);

            // 边框宽度
            formatLayout.Controls.Add(new Label { Text = "边框宽度:", AutoSize = true }, 0, 3);

            watermarkBorderWidthNumeric = new NumericUpDown
            {
                Minimum = 0.25M,
                Maximum = 10M,
                DecimalPlaces = 2,
                Increment = 0.25M,
                Value = 1M,
                Width = 80,
                Enabled = false,
                TextAlign = HorizontalAlignment.Center
            };

            var watermarkBorderWidthPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };
            watermarkBorderWidthPanel.Controls.Add(watermarkBorderWidthNumeric);
            watermarkBorderWidthPanel.Controls.Add(new Label { Text = "磅", AutoSize = true });

            formatLayout.Controls.Add(watermarkBorderWidthPanel, 1, 3);

            // 边框颜色
            formatLayout.Controls.Add(new Label { Text = "边框颜色:", AutoSize = true }, 0, 4);

            var borderColorPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };

            watermarkBorderColorButton = new Button
            {
                Text = "选择...",
                Width = 80,
                Enabled = false
            };

            watermarkBorderColorPanel = new Panel
            {
                BackColor = Color.Black,
                Size = new Size(24, 24),
                BorderStyle = BorderStyle.FixedSingle
            };

            watermarkBorderColorButton.Click += (sender, e) =>
            {
                using var colorDialog = new ColorDialog
                {
                    Color = watermarkBorderColorPanel.BackColor,
                    FullOpen = true
                };

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    watermarkBorderColorPanel.BackColor = colorDialog.Color;
                    formatRule.WatermarkBorderColor = colorDialog.Color; // 立即更新格式规则对象
                }
            };

            clearWatermarkBorderColorButton = new Button
            {
                Text = "清除",
                Width = 60,
                Enabled = false
            };

            clearWatermarkBorderColorButton.Click += (sender, e) =>
            {
                // 重置为黑色
                watermarkBorderColorPanel.BackColor = Color.Black;
                formatRule.WatermarkBorderColor = Color.Black; // 立即更新格式规则对象
            };

            borderColorPanel.Controls.Add(watermarkBorderColorButton);
            borderColorPanel.Controls.Add(watermarkBorderColorPanel);
            borderColorPanel.Controls.Add(clearWatermarkBorderColorButton);

            formatLayout.Controls.Add(borderColorPanel, 1, 4);

            // 环绕方式
            formatLayout.Controls.Add(new Label { Text = "环绕方式:", AutoSize = true }, 0, 5);

            watermarkWrapTypeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Enabled = false,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            watermarkWrapTypeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = watermarkWrapTypeComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            watermarkWrapTypeComboBox.Items.Add("嵌入式");
            watermarkWrapTypeComboBox.Items.Add("四周型");
            watermarkWrapTypeComboBox.Items.Add("穿越型");
            watermarkWrapTypeComboBox.SelectedIndex = 0;

            formatLayout.Controls.Add(watermarkWrapTypeComboBox, 1, 5);

            // 事件处理
            enableWatermarkFormatCheckBox.CheckedChanged += (sender, e) => UpdateWatermarkFormatControlsState();
            watermarkHasBorderCheckBox.CheckedChanged += (sender, e) => UpdateWatermarkFormatControlsState();

            formatPanel.Controls.Add(formatLayout);
            mainLayout.Controls.Add(formatPanel, 0, 3);

            // 其他水印设置
            var otherPanel = new GroupBox
            {
                Text = "其他设置",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var otherLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true
            };

            // 锁定水印
            lockWatermarkCheckBox = new CheckBox { Text = "锁定水印（防止编辑）", AutoSize = true };
            otherLayout.Controls.Add(lockWatermarkCheckBox, 0, 0);
            otherLayout.SetColumnSpan(lockWatermarkCheckBox, 2);

            // 打印时显示水印
            showWatermarkInPrintCheckBox = new CheckBox { Text = "打印时显示水印", AutoSize = true, Checked = true };
            otherLayout.Controls.Add(showWatermarkInPrintCheckBox, 0, 1);
            otherLayout.SetColumnSpan(showWatermarkInPrintCheckBox, 2);

            otherPanel.Controls.Add(otherLayout);
            mainLayout.Controls.Add(otherPanel, 0, 4);

            groupBox.Controls.Add(mainLayout);
            return groupBox;
        }

        // 创建表格格式面板
        private GroupBox CreateTableFormatPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "表格格式",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4, // 4个主要区域：表格宽度自适应、表格布局、表格样式、单元格操作
                AutoSize = true
            };

            // 添加表格宽度自适应面板
            mainLayout.Controls.Add(CreateTableAutoFitPanel(), 0, 0);

            // 添加表格布局面板
            mainLayout.Controls.Add(CreateTableLayoutPanel(), 0, 1);

            // 添加表格样式面板
            mainLayout.Controls.Add(CreateTableStylePanel(), 0, 2);

            // 添加单元格操作面板
            mainLayout.Controls.Add(CreateCellFormatPanel(), 0, 3);

            groupBox.Controls.Add(mainLayout);
            return groupBox;
        }

        // 创建表格宽度自适应面板
        private GroupBox CreateTableAutoFitPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "表格宽度自适应",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 添加启用表格宽度自适应的复选框
            enableTableAutoFitCheckBox = new CheckBox
            {
                Text = "启用表格宽度自适应",
                AutoSize = true,
                Checked = formatRule.EnableTableAutoFit
            };

            enableTableAutoFitCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableTableAutoFitCheckBox.Checked;

                // 启用或禁用相关控件
                autoFitToContentsRadio.Enabled = enabled;
                autoFitToWindowRadio.Enabled = enabled;
                useFixedColumnWidthRadio.Enabled = enabled;
                preferredTableWidthNumeric.Enabled = enabled && useFixedColumnWidthRadio.Checked;
            };

            mainLayout.Controls.Add(enableTableAutoFitCheckBox, 0, 0);

            // 创建选项面板
            var optionsPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.TopDown,
                AutoSize = true,
                Margin = new Padding(20, 10, 0, 0)
            };

            // 添加自适应内容选项
            autoFitToContentsRadio = new RadioButton
            {
                Text = "自动调整到内容",
                AutoSize = true,
                Checked = formatRule.AutoFitToContents,
                Enabled = formatRule.EnableTableAutoFit
            };
            optionsPanel.Controls.Add(autoFitToContentsRadio);

            // 添加自适应窗口选项
            autoFitToWindowRadio = new RadioButton
            {
                Text = "自动调整到窗口",
                AutoSize = true,
                Checked = formatRule.AutoFitToWindow,
                Enabled = formatRule.EnableTableAutoFit
            };
            optionsPanel.Controls.Add(autoFitToWindowRadio);

            // 添加使用固定列宽选项
            useFixedColumnWidthRadio = new RadioButton
            {
                Text = "使用固定列宽",
                AutoSize = true,
                Checked = formatRule.UseFixedColumnWidth,
                Enabled = formatRule.EnableTableAutoFit
            };
            optionsPanel.Controls.Add(useFixedColumnWidthRadio);

            // 添加首选表格宽度设置
            var widthPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Margin = new Padding(20, 5, 0, 0)
            };

            widthPanel.Controls.Add(new Label
            {
                Text = "首选表格宽度:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft,
                Width = 120
            });

            preferredTableWidthNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.PreferredTableWidth,
                Width = 80,
                Enabled = formatRule.EnableTableAutoFit && formatRule.UseFixedColumnWidth,
                TextAlign = HorizontalAlignment.Center
            };
            widthPanel.Controls.Add(preferredTableWidthNumeric);

            widthPanel.Controls.Add(new Label
            {
                Text = "%",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            });

            optionsPanel.Controls.Add(widthPanel);

            // 添加选项更改事件
            useFixedColumnWidthRadio.CheckedChanged += (sender, e) =>
            {
                preferredTableWidthNumeric.Enabled = useFixedColumnWidthRadio.Checked && enableTableAutoFitCheckBox.Checked;
            };

            mainLayout.Controls.Add(optionsPanel, 0, 1);

            groupBox.Controls.Add(mainLayout);
            return groupBox;
        }

        // 创建表格布局面板
        private GroupBox CreateTableLayoutPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "表格布局",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 添加启用表格布局的复选框
            enableTableLayoutCheckBox = new CheckBox
            {
                Text = "启用表格布局",
                AutoSize = true,
                Checked = formatRule.EnableTableLayout
            };

            enableTableLayoutCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableTableLayoutCheckBox.Checked;

                // 启用或禁用相关控件
                tableAlignmentComboBox.Enabled = enabled;
                tableLeftIndentNumeric.Enabled = enabled;
                tableRightIndentNumeric.Enabled = enabled;
                tableTextWrappingComboBox.Enabled = enabled;
                tableAllowAutoFitCheckBox.Enabled = enabled;
                tableDefaultCellSpacingNumeric.Enabled = enabled;
            };

            mainLayout.Controls.Add(enableTableLayoutCheckBox, 0, 0);

            // 创建布局面板
            var layoutPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 6,
                AutoSize = true,
                Margin = new Padding(20, 10, 0, 0)
            };

            // 表格对齐方式
            layoutPanel.Controls.Add(new Label
            {
                Text = "对齐方式:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 0);

            tableAlignmentComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Enabled = formatRule.EnableTableLayout,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            tableAlignmentComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = tableAlignmentComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            tableAlignmentComboBox.Items.Add("左对齐");
            tableAlignmentComboBox.Items.Add("居中对齐");
            tableAlignmentComboBox.Items.Add("右对齐");

            switch (formatRule.TableAlignment)
            {
                case AW.Tables.TableAlignment.Left:
                    tableAlignmentComboBox.SelectedIndex = 0;
                    break;
                case AW.Tables.TableAlignment.Center:
                    tableAlignmentComboBox.SelectedIndex = 1;
                    break;
                case AW.Tables.TableAlignment.Right:
                    tableAlignmentComboBox.SelectedIndex = 2;
                    break;
                default:
                    tableAlignmentComboBox.SelectedIndex = 0;
                    break;
            }

            layoutPanel.Controls.Add(tableAlignmentComboBox, 1, 0);

            // 表格左缩进
            layoutPanel.Controls.Add(new Label
            {
                Text = "左缩进:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 1);

            var leftIndentPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            tableLeftIndentNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.TableLeftIndent,
                Width = 80,
                Enabled = formatRule.EnableTableLayout,
                TextAlign = HorizontalAlignment.Center
            };
            leftIndentPanel.Controls.Add(tableLeftIndentNumeric);

            leftIndentPanel.Controls.Add(new Label
            {
                Text = "磅",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            });

            layoutPanel.Controls.Add(leftIndentPanel, 1, 1);

            // 表格右缩进
            layoutPanel.Controls.Add(new Label
            {
                Text = "右缩进:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 2);

            var rightIndentPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            tableRightIndentNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.TableRightIndent,
                Width = 80,
                Enabled = formatRule.EnableTableLayout,
                TextAlign = HorizontalAlignment.Center
            };
            rightIndentPanel.Controls.Add(tableRightIndentNumeric);

            rightIndentPanel.Controls.Add(new Label
            {
                Text = "磅",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            });

            layoutPanel.Controls.Add(rightIndentPanel, 1, 2);

            // 文本环绕方式
            layoutPanel.Controls.Add(new Label
            {
                Text = "文本环绕方式:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 3);

            tableTextWrappingComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Enabled = formatRule.EnableTableLayout,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            tableTextWrappingComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = tableTextWrappingComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            tableTextWrappingComboBox.Items.Add("环绕");
            tableTextWrappingComboBox.Items.Add("无环绕");

            switch (formatRule.TableTextWrapping)
            {
                case AW.Tables.TextWrapping.Around:
                    tableTextWrappingComboBox.SelectedIndex = 0;
                    break;
                case AW.Tables.TextWrapping.None:
                    tableTextWrappingComboBox.SelectedIndex = 1;
                    break;
                default:
                    tableTextWrappingComboBox.SelectedIndex = 0;
                    break;
            }

            layoutPanel.Controls.Add(tableTextWrappingComboBox, 1, 3);

            // 允许自动调整
            layoutPanel.Controls.Add(new Label
            {
                Text = "允许自动调整:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 4);

            tableAllowAutoFitCheckBox = new CheckBox
            {
                Text = "启用",
                AutoSize = true,
                Checked = formatRule.TableAllowAutoFit,
                Enabled = formatRule.EnableTableLayout
            };
            layoutPanel.Controls.Add(tableAllowAutoFitCheckBox, 1, 4);

            // 默认单元格间距
            layoutPanel.Controls.Add(new Label
            {
                Text = "默认单元格间距:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 5);

            var cellSpacingPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            tableDefaultCellSpacingNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 20,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.TableDefaultCellSpacing,
                Width = 80,
                Enabled = formatRule.EnableTableLayout,
                TextAlign = HorizontalAlignment.Center
            };
            cellSpacingPanel.Controls.Add(tableDefaultCellSpacingNumeric);

            cellSpacingPanel.Controls.Add(new Label
            {
                Text = "磅",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            });

            layoutPanel.Controls.Add(cellSpacingPanel, 1, 5);

            mainLayout.Controls.Add(layoutPanel, 0, 1);

            groupBox.Controls.Add(mainLayout);
            return groupBox;
        }

        // 创建表格样式面板
        private GroupBox CreateTableStylePanel()
        {
            var groupBox = new GroupBox
            {
                Text = "表格样式",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 添加启用表格样式的复选框
            enableTableStyleCheckBox = new CheckBox
            {
                Text = "启用表格样式",
                AutoSize = true,
                Checked = formatRule.EnableTableStyle
            };

            enableTableStyleCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableTableStyleCheckBox.Checked;

                // 启用或禁用相关控件
                tableHasBordersCheckBox.Enabled = enabled;
                tableBorderStyleComboBox.Enabled = enabled && tableHasBordersCheckBox.Checked;
                tableBorderWidthNumeric.Enabled = enabled && tableHasBordersCheckBox.Checked;
                tableBorderColorButton.Enabled = enabled && tableHasBordersCheckBox.Checked;
                clearTableBorderColorButton.Enabled = enabled && tableHasBordersCheckBox.Checked;

                tableHasGridlinesCheckBox.Enabled = enabled;
                tableGridlineStyleComboBox.Enabled = enabled && tableHasGridlinesCheckBox.Checked;
                tableGridlineWidthNumeric.Enabled = enabled && tableHasGridlinesCheckBox.Checked;
                tableGridlineColorButton.Enabled = enabled && tableHasGridlinesCheckBox.Checked;
                clearTableGridlineColorButton.Enabled = enabled && tableHasGridlinesCheckBox.Checked;

                tableHasShadingCheckBox.Enabled = enabled;
                tableShadingColorButton.Enabled = enabled && tableHasShadingCheckBox.Checked;
                clearTableShadingColorButton.Enabled = enabled && tableHasShadingCheckBox.Checked;
            };

            mainLayout.Controls.Add(enableTableStyleCheckBox, 0, 0);

            // 创建样式面板
            var stylePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true,
                Margin = new Padding(0, 10, 0, 0)
            };

            // 边框设置区域
            var borderGroupBox = new GroupBox
            {
                Text = "边框设置",
                Dock = DockStyle.Fill,
                AutoSize = true
            };

            var borderLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(5)
            };

            tableHasBordersCheckBox = new CheckBox
            {
                Text = "表格边框",
                AutoSize = true,
                Checked = formatRule.TableHasBorders,
                Enabled = formatRule.EnableTableStyle
            };

            tableHasBordersCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = tableHasBordersCheckBox.Checked && enableTableStyleCheckBox.Checked;
                tableBorderStyleComboBox.Enabled = enabled;
                tableBorderWidthNumeric.Enabled = enabled;
                tableBorderColorButton.Enabled = enabled;
                clearTableBorderColorButton.Enabled = enabled;
            };

            borderLayout.Controls.Add(tableHasBordersCheckBox, 0, 0);
            borderLayout.SetColumnSpan(tableHasBordersCheckBox, 2);

            // 边框样式
            borderLayout.Controls.Add(new Label
            {
                Text = "样式:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 1);

            tableBorderStyleComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120,
                Enabled = formatRule.EnableTableStyle && formatRule.TableHasBorders,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            tableBorderStyleComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = tableBorderStyleComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            tableBorderStyleComboBox.Items.Add("单线");
            tableBorderStyleComboBox.Items.Add("双线");
            tableBorderStyleComboBox.Items.Add("虚线");
            tableBorderStyleComboBox.Items.Add("点线");

            switch (formatRule.TableBorderStyle)
            {
                case AW.LineStyle.Single:
                    tableBorderStyleComboBox.SelectedIndex = 0;
                    break;
                case AW.LineStyle.Double:
                    tableBorderStyleComboBox.SelectedIndex = 1;
                    break;
                case AW.LineStyle.DashSmallGap:
                    tableBorderStyleComboBox.SelectedIndex = 2;
                    break;
                case AW.LineStyle.Dot:
                    tableBorderStyleComboBox.SelectedIndex = 3;
                    break;
                default:
                    tableBorderStyleComboBox.SelectedIndex = 0;
                    break;
            }

            borderLayout.Controls.Add(tableBorderStyleComboBox, 1, 1);

            // 边框宽度
            borderLayout.Controls.Add(new Label
            {
                Text = "宽度:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 2);

            var borderWidthPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            tableBorderWidthNumeric = new NumericUpDown
            {
                Minimum = 0.25m,
                Maximum = 10m,
                DecimalPlaces = 2,
                Increment = 0.25m,
                Value = (decimal)formatRule.TableBorderWidth,
                Width = 70,
                Enabled = formatRule.EnableTableStyle && formatRule.TableHasBorders,
                TextAlign = HorizontalAlignment.Center
            };
            borderWidthPanel.Controls.Add(tableBorderWidthNumeric);

            borderWidthPanel.Controls.Add(new Label
            {
                Text = "磅",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            });

            borderLayout.Controls.Add(borderWidthPanel, 1, 2);

            // 边框颜色
            borderLayout.Controls.Add(new Label
            {
                Text = "颜色:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 3);

            var borderColorPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            tableBorderColorButton = new Button
            {
                Text = "选择...",
                Width = 70,
                Enabled = formatRule.EnableTableStyle && formatRule.TableHasBorders
            };

            tableBorderColorPanel = new Panel
            {
                BackColor = formatRule.TableBorderColor,
                Size = new Size(24, 24),
                BorderStyle = BorderStyle.FixedSingle
            };

            tableBorderColorButton.Click += (sender, e) =>
            {
                using var colorDialog = new ColorDialog
                {
                    Color = tableBorderColorPanel.BackColor,
                    FullOpen = true
                };

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    tableBorderColorPanel.BackColor = colorDialog.Color;
                    formatRule.TableBorderColor = colorDialog.Color; // 立即更新格式规则对象
                }
            };

            clearTableBorderColorButton = new Button
            {
                Text = "清除",
                Width = 60,
                Enabled = formatRule.EnableTableStyle && formatRule.TableHasBorders
            };

            clearTableBorderColorButton.Click += (sender, e) =>
            {
                // 重置为黑色
                tableBorderColorPanel.BackColor = Color.Black;
                formatRule.TableBorderColor = Color.Black; // 立即更新格式规则对象
            };

            borderColorPanel.Controls.Add(tableBorderColorButton);
            borderColorPanel.Controls.Add(tableBorderColorPanel);
            borderColorPanel.Controls.Add(clearTableBorderColorButton);

            borderLayout.Controls.Add(borderColorPanel, 1, 3);

            borderGroupBox.Controls.Add(borderLayout);
            stylePanel.Controls.Add(borderGroupBox, 0, 0);

            // 网格线设置区域
            var gridlineGroupBox = new GroupBox
            {
                Text = "网格线设置",
                Dock = DockStyle.Fill,
                AutoSize = true
            };

            var gridlineLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(5)
            };

            tableHasGridlinesCheckBox = new CheckBox
            {
                Text = "表格网格线",
                AutoSize = true,
                Checked = formatRule.TableHasGridlines,
                Enabled = formatRule.EnableTableStyle
            };

            tableHasGridlinesCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = tableHasGridlinesCheckBox.Checked && enableTableStyleCheckBox.Checked;
                tableGridlineStyleComboBox.Enabled = enabled;
                tableGridlineWidthNumeric.Enabled = enabled;
                tableGridlineColorButton.Enabled = enabled;
                clearTableGridlineColorButton.Enabled = enabled;
            };

            gridlineLayout.Controls.Add(tableHasGridlinesCheckBox, 0, 0);
            gridlineLayout.SetColumnSpan(tableHasGridlinesCheckBox, 2);

            // 网格线样式
            gridlineLayout.Controls.Add(new Label
            {
                Text = "样式:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 1);

            tableGridlineStyleComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120,
                Enabled = formatRule.EnableTableStyle && formatRule.TableHasGridlines,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            tableGridlineStyleComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = tableGridlineStyleComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            tableGridlineStyleComboBox.Items.Add("单线");
            tableGridlineStyleComboBox.Items.Add("双线");
            tableGridlineStyleComboBox.Items.Add("虚线");
            tableGridlineStyleComboBox.Items.Add("点线");

            switch (formatRule.TableGridlineStyle)
            {
                case AW.LineStyle.Single:
                    tableGridlineStyleComboBox.SelectedIndex = 0;
                    break;
                case AW.LineStyle.Double:
                    tableGridlineStyleComboBox.SelectedIndex = 1;
                    break;
                case AW.LineStyle.DashSmallGap:
                    tableGridlineStyleComboBox.SelectedIndex = 2;
                    break;
                case AW.LineStyle.Dot:
                    tableGridlineStyleComboBox.SelectedIndex = 3;
                    break;
                default:
                    tableGridlineStyleComboBox.SelectedIndex = 0;
                    break;
            }

            gridlineLayout.Controls.Add(tableGridlineStyleComboBox, 1, 1);

            // 网格线宽度
            gridlineLayout.Controls.Add(new Label
            {
                Text = "宽度:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 2);

            var gridlineWidthPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            tableGridlineWidthNumeric = new NumericUpDown
            {
                Minimum = 0.25m,
                Maximum = 10m,
                DecimalPlaces = 2,
                Increment = 0.25m,
                Value = (decimal)formatRule.TableGridlineWidth,
                Width = 70,
                Enabled = formatRule.EnableTableStyle && formatRule.TableHasGridlines,
                TextAlign = HorizontalAlignment.Center
            };
            gridlineWidthPanel.Controls.Add(tableGridlineWidthNumeric);

            gridlineWidthPanel.Controls.Add(new Label
            {
                Text = "磅",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            });

            gridlineLayout.Controls.Add(gridlineWidthPanel, 1, 2);

            // 网格线颜色
            gridlineLayout.Controls.Add(new Label
            {
                Text = "颜色:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 3);

            var gridlineColorPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            tableGridlineColorButton = new Button
            {
                Text = "选择...",
                Width = 70,
                Enabled = formatRule.EnableTableStyle && formatRule.TableHasGridlines
            };

            tableGridlineColorPanel = new Panel
            {
                BackColor = formatRule.TableGridlineColor,
                Size = new Size(24, 24),
                BorderStyle = BorderStyle.FixedSingle
            };

            tableGridlineColorButton.Click += (sender, e) =>
            {
                using var colorDialog = new ColorDialog
                {
                    Color = tableGridlineColorPanel.BackColor,
                    FullOpen = true
                };

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    tableGridlineColorPanel.BackColor = colorDialog.Color;
                    formatRule.TableGridlineColor = colorDialog.Color; // 立即更新格式规则对象
                }
            };

            clearTableGridlineColorButton = new Button
            {
                Text = "清除",
                Width = 60,
                Enabled = formatRule.EnableTableStyle && formatRule.TableHasGridlines
            };

            clearTableGridlineColorButton.Click += (sender, e) =>
            {
                // 重置为黑色
                tableGridlineColorPanel.BackColor = Color.Black;
                formatRule.TableGridlineColor = Color.Black; // 立即更新格式规则对象
            };

            gridlineColorPanel.Controls.Add(tableGridlineColorButton);
            gridlineColorPanel.Controls.Add(tableGridlineColorPanel);
            gridlineColorPanel.Controls.Add(clearTableGridlineColorButton);

            gridlineLayout.Controls.Add(gridlineColorPanel, 1, 3);

            gridlineGroupBox.Controls.Add(gridlineLayout);
            stylePanel.Controls.Add(gridlineGroupBox, 1, 0);

            // 底纹设置区域
            var shadingGroupBox = new GroupBox
            {
                Text = "底纹设置",
                Dock = DockStyle.Fill,
                AutoSize = true
            };

            var shadingLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(5)
            };

            tableHasShadingCheckBox = new CheckBox
            {
                Text = "表格底纹",
                AutoSize = true,
                Checked = formatRule.TableHasShading,
                Enabled = formatRule.EnableTableStyle
            };

            tableHasShadingCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = tableHasShadingCheckBox.Checked && enableTableStyleCheckBox.Checked;
                tableShadingColorButton.Enabled = enabled;
                clearTableShadingColorButton.Enabled = enabled;
            };

            shadingLayout.Controls.Add(tableHasShadingCheckBox, 0, 0);
            shadingLayout.SetColumnSpan(tableHasShadingCheckBox, 2);

            // 底纹颜色
            shadingLayout.Controls.Add(new Label
            {
                Text = "颜色:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 1);

            var shadingColorPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            tableShadingColorButton = new Button
            {
                Text = "选择...",
                Width = 70,
                Enabled = formatRule.EnableTableStyle && formatRule.TableHasShading
            };

            tableShadingColorPanel = new Panel
            {
                BackColor = formatRule.TableShadingColor,
                Size = new Size(24, 24),
                BorderStyle = BorderStyle.FixedSingle
            };

            tableShadingColorButton.Click += (sender, e) =>
            {
                using var colorDialog = new ColorDialog
                {
                    Color = tableShadingColorPanel.BackColor,
                    FullOpen = true
                };

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    tableShadingColorPanel.BackColor = colorDialog.Color;
                    formatRule.TableShadingColor = colorDialog.Color; // 立即更新格式规则对象
                }
            };

            clearTableShadingColorButton = new Button
            {
                Text = "清除",
                Width = 60,
                Enabled = formatRule.EnableTableStyle && formatRule.TableHasShading
            };

            clearTableShadingColorButton.Click += (sender, e) =>
            {
                // 重置为浅灰色
                tableShadingColorPanel.BackColor = Color.LightGray;
                formatRule.TableShadingColor = Color.LightGray; // 立即更新格式规则对象
            };

            shadingColorPanel.Controls.Add(tableShadingColorButton);
            shadingColorPanel.Controls.Add(tableShadingColorPanel);
            shadingColorPanel.Controls.Add(clearTableShadingColorButton);

            shadingLayout.Controls.Add(shadingColorPanel, 1, 1);

            shadingGroupBox.Controls.Add(shadingLayout);
            stylePanel.Controls.Add(shadingGroupBox, 0, 1);
            stylePanel.SetColumnSpan(shadingGroupBox, 2);

            mainLayout.Controls.Add(stylePanel, 0, 1);

            groupBox.Controls.Add(mainLayout);
            return groupBox;
        }

        // 创建单元格操作面板
        private GroupBox CreateCellFormatPanel()
        {
            var groupBox = new GroupBox
            {
                Text = "单元格操作",
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 添加启用单元格格式的复选框
            enableCellFormatCheckBox = new CheckBox
            {
                Text = "启用单元格格式",
                AutoSize = true,
                Checked = formatRule.EnableCellFormat
            };

            enableCellFormatCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableCellFormatCheckBox.Checked;

                // 启用或禁用相关控件
                cellverticalAlignmentComboBox.Enabled = enabled;
                cellMarginTopNumeric.Enabled = enabled;
                cellMarginBottomNumeric.Enabled = enabled;
                cellMarginLeftNumeric.Enabled = enabled;
                cellMarginRightNumeric.Enabled = enabled;

                cellHasShadingCheckBox.Enabled = enabled;
                cellShadingColorButton.Enabled = enabled && cellHasShadingCheckBox.Checked;
                clearCellShadingColorButton.Enabled = enabled && cellHasShadingCheckBox.Checked;

                cellHasBordersCheckBox.Enabled = enabled;
                cellBorderStyleComboBox.Enabled = enabled && cellHasBordersCheckBox.Checked;
                cellBorderWidthNumeric.Enabled = enabled && cellHasBordersCheckBox.Checked;
                cellBorderColorButton.Enabled = enabled && cellHasBordersCheckBox.Checked;
                clearCellBorderColorButton.Enabled = enabled && cellHasBordersCheckBox.Checked;
            };

            mainLayout.Controls.Add(enableCellFormatCheckBox, 0, 0);

            // 创建单元格格式面板
            var formatPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = true,
                Margin = new Padding(0, 10, 0, 0)
            };

            // 对齐和边距设置区域
            var alignMarginGroupBox = new GroupBox
            {
                Text = "对齐和边距",
                Dock = DockStyle.Fill,
                AutoSize = true
            };

            var alignMarginLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 6,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 垂直对齐方式
            alignMarginLayout.Controls.Add(new Label
            {
                Text = "垂直对齐方式:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 0);
            alignMarginLayout.SetColumnSpan(alignMarginLayout.GetControlFromPosition(0, 0), 4);

            cellverticalAlignmentComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Enabled = formatRule.EnableCellFormat
            };

            cellverticalAlignmentComboBox.Items.Add("顶端对齐");
            cellverticalAlignmentComboBox.Items.Add("居中对齐");
            cellverticalAlignmentComboBox.Items.Add("底端对齐");

            switch (formatRule.CellVerticalAlignment)
            {
                case AW.Tables.CellVerticalAlignment.Top:
                    cellverticalAlignmentComboBox.SelectedIndex = 0;
                    break;
                case AW.Tables.CellVerticalAlignment.Center:
                    cellverticalAlignmentComboBox.SelectedIndex = 1;
                    break;
                case AW.Tables.CellVerticalAlignment.Bottom:
                    cellverticalAlignmentComboBox.SelectedIndex = 2;
                    break;
                default:
                    cellverticalAlignmentComboBox.SelectedIndex = 1; // 默认居中对齐
                    break;
            }

            alignMarginLayout.Controls.Add(cellverticalAlignmentComboBox, 0, 1);
            alignMarginLayout.SetColumnSpan(alignMarginLayout.GetControlFromPosition(0, 1), 4);

            // 单元格边距
            alignMarginLayout.Controls.Add(new Label
            {
                Text = "单元格边距:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 2);
            alignMarginLayout.SetColumnSpan(alignMarginLayout.GetControlFromPosition(0, 2), 4);

            // 上边距
            alignMarginLayout.Controls.Add(new Label
            {
                Text = "上:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 3);

            cellMarginTopNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.CellMarginTop,
                Width = 70,
                Enabled = formatRule.EnableCellFormat,
                TextAlign = HorizontalAlignment.Center
            };
            alignMarginLayout.Controls.Add(cellMarginTopNumeric, 1, 3);

            // 下边距
            alignMarginLayout.Controls.Add(new Label
            {
                Text = "下:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 2, 3);

            cellMarginBottomNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.CellMarginBottom,
                Width = 70,
                Enabled = formatRule.EnableCellFormat,
                TextAlign = HorizontalAlignment.Center
            };
            alignMarginLayout.Controls.Add(cellMarginBottomNumeric, 3, 3);

            // 左边距
            alignMarginLayout.Controls.Add(new Label
            {
                Text = "左:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 4);

            cellMarginLeftNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.CellMarginLeft,
                Width = 70,
                Enabled = formatRule.EnableCellFormat,
                TextAlign = HorizontalAlignment.Center
            };
            alignMarginLayout.Controls.Add(cellMarginLeftNumeric, 1, 4);

            // 右边距
            alignMarginLayout.Controls.Add(new Label
            {
                Text = "右:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 2, 4);

            cellMarginRightNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = (decimal)formatRule.CellMarginRight,
                Width = 70,
                Enabled = formatRule.EnableCellFormat,
                TextAlign = HorizontalAlignment.Center
            };
            alignMarginLayout.Controls.Add(cellMarginRightNumeric, 3, 4);

            // 单元格内边距
            alignMarginLayout.Controls.Add(new Label
            {
                Text = "单元格内边距:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 5);
            alignMarginLayout.SetColumnSpan(alignMarginLayout.GetControlFromPosition(0, 5), 4);

            alignMarginGroupBox.Controls.Add(alignMarginLayout);
            formatPanel.Controls.Add(alignMarginGroupBox, 0, 0);
            formatPanel.SetColumnSpan(alignMarginGroupBox, 2);

            // 单元格底纹设置区域
            var cellShadingGroupBox = new GroupBox
            {
                Text = "单元格底纹",
                Dock = DockStyle.Fill,
                AutoSize = true
            };

            var cellShadingLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(5)
            };

            cellHasShadingCheckBox = new CheckBox
            {
                Text = "启用单元格底纹",
                AutoSize = true,
                Checked = formatRule.CellHasShading,
                Enabled = formatRule.EnableCellFormat
            };

            cellHasShadingCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = cellHasShadingCheckBox.Checked && enableCellFormatCheckBox.Checked;
                cellShadingColorButton.Enabled = enabled;
                clearCellShadingColorButton.Enabled = enabled;
            };

            cellShadingLayout.Controls.Add(cellHasShadingCheckBox, 0, 0);
            cellShadingLayout.SetColumnSpan(cellHasShadingCheckBox, 2);

            // 底纹颜色
            cellShadingLayout.Controls.Add(new Label
            {
                Text = "颜色:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 1);

            var cellShadingColorFlowPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            cellShadingColorButton = new Button
            {
                Text = "选择...",
                Width = 70,
                Enabled = formatRule.EnableCellFormat && formatRule.CellHasShading
            };

            cellShadingColorPanel = new Panel
            {
                BackColor = formatRule.CellShadingColor,
                Size = new Size(24, 24),
                BorderStyle = BorderStyle.FixedSingle
            };

            cellShadingColorButton.Click += (sender, e) =>
            {
                using var colorDialog = new ColorDialog
                {
                    Color = cellShadingColorPanel.BackColor,
                    FullOpen = true
                };

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    cellShadingColorPanel.BackColor = colorDialog.Color;
                    formatRule.CellShadingColor = colorDialog.Color; // 立即更新格式规则对象
                }
            };

            clearCellShadingColorButton = new Button
            {
                Text = "清除",
                Width = 60,
                Enabled = formatRule.EnableCellFormat && formatRule.CellHasShading
            };

            clearCellShadingColorButton.Click += (sender, e) =>
            {
                // 重置为浅灰色
                cellShadingColorPanel.BackColor = Color.LightGray;
                formatRule.CellShadingColor = Color.LightGray; // 立即更新格式规则对象
            };

            cellShadingColorFlowPanel.Controls.Add(cellShadingColorButton);
            cellShadingColorFlowPanel.Controls.Add(cellShadingColorPanel);
            cellShadingColorFlowPanel.Controls.Add(clearCellShadingColorButton);

            cellShadingLayout.Controls.Add(cellShadingColorFlowPanel, 1, 1);

            cellShadingGroupBox.Controls.Add(cellShadingLayout);
            formatPanel.Controls.Add(cellShadingGroupBox, 0, 1);

            // 单元格边框设置区域
            var cellBorderGroupBox = new GroupBox
            {
                Text = "单元格边框",
                Dock = DockStyle.Fill,
                AutoSize = true
            };

            var cellBorderLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(5)
            };

            cellHasBordersCheckBox = new CheckBox
            {
                Text = "启用单元格边框",
                AutoSize = true,
                Checked = formatRule.CellHasBorders,
                Enabled = formatRule.EnableCellFormat
            };

            cellHasBordersCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = cellHasBordersCheckBox.Checked && enableCellFormatCheckBox.Checked;
                cellBorderStyleComboBox.Enabled = enabled;
                cellBorderWidthNumeric.Enabled = enabled;
                cellBorderColorButton.Enabled = enabled;
                clearCellBorderColorButton.Enabled = enabled;
            };

            cellBorderLayout.Controls.Add(cellHasBordersCheckBox, 0, 0);
            cellBorderLayout.SetColumnSpan(cellHasBordersCheckBox, 2);

            // 边框样式
            cellBorderLayout.Controls.Add(new Label
            {
                Text = "样式:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 1);

            cellBorderStyleComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120,
                Enabled = formatRule.EnableCellFormat && formatRule.CellHasBorders,
                DrawMode = DrawMode.OwnerDrawFixed
            };

            // 设置下拉框文字居中显示
            cellBorderStyleComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    var text = cellBorderStyleComboBox.Items[e.Index].ToString();
                    var textBounds = new Rectangle(e.Bounds.X, e.Bounds.Y, e.Bounds.Width, e.Bounds.Height);
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor,
                        TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                    e.DrawFocusRectangle();
                }
            };

            cellBorderStyleComboBox.Items.Add("单线");
            cellBorderStyleComboBox.Items.Add("双线");
            cellBorderStyleComboBox.Items.Add("虚线");
            cellBorderStyleComboBox.Items.Add("点线");

            switch (formatRule.CellBorderStyle)
            {
                case AW.LineStyle.Single:
                    cellBorderStyleComboBox.SelectedIndex = 0;
                    break;
                case AW.LineStyle.Double:
                    cellBorderStyleComboBox.SelectedIndex = 1;
                    break;
                case AW.LineStyle.DashSmallGap:
                    cellBorderStyleComboBox.SelectedIndex = 2;
                    break;
                case AW.LineStyle.Dot:
                    cellBorderStyleComboBox.SelectedIndex = 3;
                    break;
                default:
                    cellBorderStyleComboBox.SelectedIndex = 0;
                    break;
            }

            cellBorderLayout.Controls.Add(cellBorderStyleComboBox, 1, 1);

            // 边框宽度
            cellBorderLayout.Controls.Add(new Label
            {
                Text = "宽度:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 2);

            var cellBorderWidthFlowPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            cellBorderWidthNumeric = new NumericUpDown
            {
                Minimum = 0.25m,
                Maximum = 10m,
                DecimalPlaces = 2,
                Increment = 0.25m,
                Value = (decimal)formatRule.CellBorderWidth,
                Width = 70,
                Enabled = formatRule.EnableCellFormat && formatRule.CellHasBorders,
                TextAlign = HorizontalAlignment.Center
            };
            cellBorderWidthFlowPanel.Controls.Add(cellBorderWidthNumeric);

            cellBorderWidthFlowPanel.Controls.Add(new Label
            {
                Text = "磅",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            });

            cellBorderLayout.Controls.Add(cellBorderWidthFlowPanel, 1, 2);

            // 边框颜色
            cellBorderLayout.Controls.Add(new Label
            {
                Text = "颜色:",
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleLeft
            }, 0, 3);

            var cellBorderColorFlowPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            cellBorderColorButton = new Button
            {
                Text = "选择...",
                Width = 70,
                Enabled = formatRule.EnableCellFormat && formatRule.CellHasBorders
            };

            cellBorderColorPanel = new Panel
            {
                BackColor = formatRule.CellBorderColor,
                Size = new Size(24, 24),
                BorderStyle = BorderStyle.FixedSingle
            };

            cellBorderColorButton.Click += (sender, e) =>
            {
                using var colorDialog = new ColorDialog
                {
                    Color = cellBorderColorPanel.BackColor,
                    FullOpen = true
                };

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    cellBorderColorPanel.BackColor = colorDialog.Color;
                    formatRule.CellBorderColor = colorDialog.Color; // 立即更新格式规则对象
                }
            };

            clearCellBorderColorButton = new Button
            {
                Text = "清除",
                Width = 60,
                Enabled = formatRule.EnableCellFormat && formatRule.CellHasBorders
            };

            clearCellBorderColorButton.Click += (sender, e) =>
            {
                // 重置为黑色
                cellBorderColorPanel.BackColor = Color.Black;
                formatRule.CellBorderColor = Color.Black; // 立即更新格式规则对象
            };

            cellBorderColorFlowPanel.Controls.Add(cellBorderColorButton);
            cellBorderColorFlowPanel.Controls.Add(cellBorderColorPanel);
            cellBorderColorFlowPanel.Controls.Add(clearCellBorderColorButton);

            cellBorderLayout.Controls.Add(cellBorderColorFlowPanel, 1, 3);

            cellBorderGroupBox.Controls.Add(cellBorderLayout);
            formatPanel.Controls.Add(cellBorderGroupBox, 1, 1);

            mainLayout.Controls.Add(formatPanel, 0, 1);

            groupBox.Controls.Add(mainLayout);
            return groupBox;
        }

        private static System.Drawing.FontStyle GetFontStyleFromIndex(int index)
        {
            return index switch
            {
                0 => System.Drawing.FontStyle.Regular,
                1 => System.Drawing.FontStyle.Bold,
                2 => System.Drawing.FontStyle.Italic,
                3 => System.Drawing.FontStyle.Bold | System.Drawing.FontStyle.Italic,
                _ => System.Drawing.FontStyle.Regular,
            };
        }

        private static int GetFontStyleIndex(System.Drawing.FontStyle fontStyle)
        {
            if (fontStyle.HasFlag(System.Drawing.FontStyle.Bold) && fontStyle.HasFlag(System.Drawing.FontStyle.Italic))
                return 3; // 粗体斜体
            else if (fontStyle.HasFlag(System.Drawing.FontStyle.Bold))
                return 1; // 粗体
            else if (fontStyle.HasFlag(System.Drawing.FontStyle.Italic))
                return 2; // 斜体
            else
                return 0; // 常规
        }

        // 单位转换工具类
        private static class UnitConverter
        {
            // 字符单位转换为磅 (1字符 = 12磅，这是Word中的标准转换)
            public static double CharactersToPoints(double characters)
            {
                return characters * 12.0;
            }

            // 磅转换为字符单位
            public static double PointsToCharacters(double points)
            {
                return points / 12.0;
            }

            // 厘米转换为磅 (1厘米 = 28.3464567磅)
            public static double CentimetersToPoints(double centimeters)
            {
                return centimeters * 28.3464567;
            }

            // 磅转换为厘米
            public static double PointsToCentimeters(double points)
            {
                return points / 28.3464567;
            }

            // 毫米转换为磅 (1毫米 = 2.83464567磅)
            public static double MillimetersToPoints(double millimeters)
            {
                return millimeters * 2.83464567;
            }

            // 磅转换为毫米
            public static double PointsToMillimeters(double points)
            {
                return points / 2.83464567;
            }

            // 英寸转换为磅 (1英寸 = 72磅)
            public static double InchesToPoints(double inches)
            {
                return inches * 72.0;
            }

            // 磅转换为英寸
            public static double PointsToInches(double points)
            {
                return points / 72.0;
            }

            // 根据单位类型转换为磅
            public static double ConvertToPoints(double value, IndentUnit unit)
            {
                return unit switch
                {
                    IndentUnit.Characters => CharactersToPoints(value),
                    IndentUnit.Centimeters => CentimetersToPoints(value),
                    IndentUnit.Millimeters => MillimetersToPoints(value),
                    IndentUnit.Points => value,
                    IndentUnit.Inches => InchesToPoints(value),
                    _ => value
                };
            }

            // 根据单位类型从磅转换
            public static double ConvertFromPoints(double points, IndentUnit unit)
            {
                return unit switch
                {
                    IndentUnit.Characters => PointsToCharacters(points),
                    IndentUnit.Centimeters => PointsToCentimeters(points),
                    IndentUnit.Millimeters => PointsToMillimeters(points),
                    IndentUnit.Points => points,
                    IndentUnit.Inches => PointsToInches(points),
                    _ => points
                };
            }

            // 根据单位类型转换为磅（行距单位）
            public static double ConvertToPoints(double value, LineSpacingUnit unit)
            {
                return unit switch
                {
                    LineSpacingUnit.Points => value,
                    LineSpacingUnit.Centimeters => CentimetersToPoints(value),
                    LineSpacingUnit.Millimeters => MillimetersToPoints(value),
                    LineSpacingUnit.Inches => InchesToPoints(value),
                    _ => value
                };
            }

            // 根据单位类型从磅转换（行距单位）
            public static double ConvertFromPoints(double points, LineSpacingUnit unit)
            {
                return unit switch
                {
                    LineSpacingUnit.Points => points,
                    LineSpacingUnit.Centimeters => PointsToCentimeters(points),
                    LineSpacingUnit.Millimeters => PointsToMillimeters(points),
                    LineSpacingUnit.Inches => PointsToInches(points),
                    _ => points
                };
            }

            // 获取单位显示文本
            public static string GetUnitText(IndentUnit unit)
            {
                return unit switch
                {
                    IndentUnit.Characters => "字符",
                    IndentUnit.Centimeters => "厘米",
                    IndentUnit.Millimeters => "毫米",
                    IndentUnit.Points => "磅",
                    IndentUnit.Inches => "英寸",
                    _ => "磅"
                };
            }

            // 获取单位显示文本（行距单位）
            public static string GetUnitText(LineSpacingUnit unit)
            {
                return unit switch
                {
                    LineSpacingUnit.Points => "磅",
                    LineSpacingUnit.Centimeters => "厘米",
                    LineSpacingUnit.Millimeters => "毫米",
                    LineSpacingUnit.Inches => "英寸",
                    _ => "磅"
                };
            }
        }

        // 缩进单位枚举
        public enum IndentUnit
        {
            Characters = 0,  // 字符
            Centimeters = 1, // 厘米
            Millimeters = 2, // 毫米
            Points = 3,      // 磅
            Inches = 4       // 英寸
        }

        // 行距单位枚举
        public enum LineSpacingUnit
        {
            Points = 0,      // 磅
            Centimeters = 1, // 厘米
            Millimeters = 2, // 毫米
            Inches = 3       // 英寸
        }

        // 中文字号枚举和转换
        public enum ChineseFontSize
        {
            Custom = -1,     // 自定义
            ChuHao = 0,      // 初号 = 42磅
            XiaoChu = 1,     // 小初 = 36磅
            YiHao = 2,       // 一号 = 26磅
            XiaoYi = 3,      // 小一 = 24磅
            ErHao = 4,       // 二号 = 22磅
            XiaoEr = 5,      // 小二 = 18磅
            SanHao = 6,      // 三号 = 16磅
            XiaoSan = 7,     // 小三 = 15磅
            SiHao = 8,       // 四号 = 14磅
            XiaoSi = 9,      // 小四 = 12磅
            WuHao = 10,      // 五号 = 10.5磅
            XiaoWu = 11,     // 小五 = 9磅
            LiuHao = 12,     // 六号 = 7.5磅
            XiaoLiu = 13,    // 小六 = 6.5磅
            QiHao = 14,      // 七号 = 5.5磅
            XiaoQi = 15,     // 小七 = 5磅
            BaHao = 16       // 八号 = 4.5磅
        }

        // 中文字号转换工具
        private static class ChineseFontSizeConverter
        {
            private static readonly Dictionary<ChineseFontSize, double> FontSizeMap = new()
            {
                { ChineseFontSize.ChuHao, 42.0 },    // 初号
                { ChineseFontSize.XiaoChu, 36.0 },   // 小初
                { ChineseFontSize.YiHao, 26.0 },     // 一号
                { ChineseFontSize.XiaoYi, 24.0 },    // 小一
                { ChineseFontSize.ErHao, 22.0 },     // 二号
                { ChineseFontSize.XiaoEr, 18.0 },    // 小二
                { ChineseFontSize.SanHao, 16.0 },    // 三号
                { ChineseFontSize.XiaoSan, 15.0 },   // 小三
                { ChineseFontSize.SiHao, 14.0 },     // 四号
                { ChineseFontSize.XiaoSi, 12.0 },    // 小四
                { ChineseFontSize.WuHao, 10.5 },     // 五号
                { ChineseFontSize.XiaoWu, 9.0 },     // 小五
                { ChineseFontSize.LiuHao, 7.5 },     // 六号
                { ChineseFontSize.XiaoLiu, 6.5 },    // 小六
                { ChineseFontSize.QiHao, 5.5 },      // 七号
                { ChineseFontSize.XiaoQi, 5.0 },     // 小七
                { ChineseFontSize.BaHao, 4.5 }       // 八号
            };

            private static readonly Dictionary<ChineseFontSize, string> FontSizeNames = new()
            {
                { ChineseFontSize.ChuHao, "初号" },
                { ChineseFontSize.XiaoChu, "小初" },
                { ChineseFontSize.YiHao, "一号" },
                { ChineseFontSize.XiaoYi, "小一" },
                { ChineseFontSize.ErHao, "二号" },
                { ChineseFontSize.XiaoEr, "小二" },
                { ChineseFontSize.SanHao, "三号" },
                { ChineseFontSize.XiaoSan, "小三" },
                { ChineseFontSize.SiHao, "四号" },
                { ChineseFontSize.XiaoSi, "小四" },
                { ChineseFontSize.WuHao, "五号" },
                { ChineseFontSize.XiaoWu, "小五" },
                { ChineseFontSize.LiuHao, "六号" },
                { ChineseFontSize.XiaoLiu, "小六" },
                { ChineseFontSize.QiHao, "七号" },
                { ChineseFontSize.XiaoQi, "小七" },
                { ChineseFontSize.BaHao, "八号" }
            };

            public static double GetPointSize(ChineseFontSize fontSize)
            {
                return FontSizeMap.TryGetValue(fontSize, out double size) ? size : 12.0;
            }

            public static string GetDisplayName(ChineseFontSize fontSize)
            {
                return FontSizeNames.TryGetValue(fontSize, out string? name) ? name : "自定义";
            }

            public static ChineseFontSize GetChineseFontSize(double pointSize)
            {
                foreach (var kvp in FontSizeMap)
                {
                    if (Math.Abs(kvp.Value - pointSize) < 0.1)
                    {
                        return kvp.Key;
                    }
                }
                return ChineseFontSize.Custom;
            }

            public static List<string> GetAllFontSizeNames()
            {
                var names = new List<string>();
                foreach (ChineseFontSize fontSize in Enum.GetValues<ChineseFontSize>())
                {
                    if (fontSize != ChineseFontSize.Custom)
                    {
                        names.Add(GetDisplayName(fontSize));
                    }
                }
                names.Add("自定义");
                return names;
            }
        }

        // 更新所有字体格式控件的启用状态
        private void UpdateFontFormatControlsEnabled()
        {
            bool mainEnabled = enableFontFormatCheckBox.Checked;

            // 中文字体控件
            bool chineseEnabled = mainEnabled && enableChineseFontCheckBox.Checked;
            if (chineseFontComboBox != null) chineseFontComboBox.Enabled = chineseEnabled;
            if (chineseFontStyleComboBox != null) chineseFontStyleComboBox.Enabled = chineseEnabled;
            if (chineseFontSizeComboBox != null) chineseFontSizeComboBox.Enabled = chineseEnabled;
            if (chineseFontSizeNumeric != null) chineseFontSizeNumeric.Enabled = chineseEnabled;

            // 西文字体控件
            bool westernEnabled = mainEnabled && enableWesternFontCheckBox.Checked;
            if (westernFontComboBox != null) westernFontComboBox.Enabled = westernEnabled;
            if (westernFontStyleComboBox != null) westernFontStyleComboBox.Enabled = westernEnabled;
            if (westernFontSizeComboBox != null) westernFontSizeComboBox.Enabled = westernEnabled;
            if (westernFontSizeNumeric != null) westernFontSizeNumeric.Enabled = westernEnabled;

            // 复杂文种字体控件
            bool complexEnabled = mainEnabled && enableComplexScriptFontCheckBox.Checked;
            if (complexScriptFontComboBox != null) complexScriptFontComboBox.Enabled = complexEnabled;
            if (complexScriptFontStyleComboBox != null) complexScriptFontStyleComboBox.Enabled = complexEnabled;
            if (complexScriptFontSizeComboBox != null) complexScriptFontSizeComboBox.Enabled = complexEnabled;
            if (complexScriptFontSizeNumeric != null) complexScriptFontSizeNumeric.Enabled = complexEnabled;

            // 字体颜色控件
            bool fontColorEnabled = mainEnabled && enableFontColorCheckBox.Checked;
            if (fontColorButton != null) fontColorButton.Enabled = fontColorEnabled;
            if (clearFontColorButton != null) clearFontColorButton.Enabled = fontColorEnabled;

            // 突出显示颜色控件
            bool highlightColorEnabled = mainEnabled && enableHighlightColorCheckBox.Checked;
            if (highlightColorButton != null) highlightColorButton.Enabled = highlightColorEnabled;
            if (clearHighlightColorButton != null) clearHighlightColorButton.Enabled = highlightColorEnabled;
        }

        // 更新所有基本段落格式控件的启用状态
        private void UpdateBasicFormatControlsEnabled()
        {
            bool mainEnabled = enableBasicFormatCheckBox.Checked;

            // 对齐方式控件
            bool alignmentEnabled = mainEnabled && enableAlignmentCheckBox.Checked;
            if (alignmentComboBox != null) alignmentComboBox.Enabled = alignmentEnabled;

            // 大纲级别控件
            bool outlineLevelEnabled = mainEnabled && enableOutlineLevelCheckBox.Checked;
            if (outlineLevelComboBox != null) outlineLevelComboBox.Enabled = outlineLevelEnabled;

            // 对齐方向控件
            bool textDirectionEnabled = mainEnabled && enableTextDirectionCheckBox.Checked;
            if (textDirectionComboBox != null) textDirectionComboBox.Enabled = textDirectionEnabled;

            // 缩进控件
            bool indentEnabled = mainEnabled && enableIndentCheckBox.Checked;
            if (leftIndentNumeric != null) leftIndentNumeric.Enabled = indentEnabled;
            if (rightIndentNumeric != null) rightIndentNumeric.Enabled = indentEnabled;
            if (indentTypeComboBox != null) indentTypeComboBox.Enabled = indentEnabled;
            if (indentValueNumeric != null && indentTypeComboBox != null)
                indentValueNumeric.Enabled = indentEnabled && indentTypeComboBox.SelectedIndex > 0;

            // 间距控件
            bool spacingEnabled = mainEnabled && enableSpacingCheckBox.Checked;
            if (beforeSpacingNumeric != null) beforeSpacingNumeric.Enabled = spacingEnabled;
            if (afterSpacingNumeric != null) afterSpacingNumeric.Enabled = spacingEnabled;
            if (lineSpacingTypeComboBox != null) lineSpacingTypeComboBox.Enabled = spacingEnabled;
            if (lineSpacingValueNumeric != null) lineSpacingValueNumeric.Enabled = spacingEnabled;
            if (adjustImageLineSpacingCheckBox != null) adjustImageLineSpacingCheckBox.Enabled = spacingEnabled;
        }

        // 更新所有图片尺寸控件的启用状态
        private void UpdateImageSizeControlsEnabled()
        {
            bool mainEnabled = enableImageFormatCheckBox.Checked && enableImageSizeCheckBox.Checked;

            // 基本控件启用状态
            if (preserveAspectRatioCheckBox != null) preserveAspectRatioCheckBox.Enabled = mainEnabled;
            if (useExactSizeRadio != null) useExactSizeRadio.Enabled = mainEnabled;
            if (useScaleRadio != null) useScaleRadio.Enabled = mainEnabled;

            // 精确尺寸控件
            bool useExact = mainEnabled && useExactSizeRadio != null && useExactSizeRadio.Checked;
            if (imageWidthNumeric != null) imageWidthNumeric.Enabled = useExact;
            if (imageHeightNumeric != null) imageHeightNumeric.Enabled = useExact;

            // 缩放比例控件
            bool useScale = mainEnabled && useScaleRadio != null && useScaleRadio.Checked;
            if (imageScaleXNumeric != null) imageScaleXNumeric.Enabled = useScale;

            bool preserveAspect = preserveAspectRatioCheckBox != null && preserveAspectRatioCheckBox.Checked;
            if (imageScaleYNumeric != null) imageScaleYNumeric.Enabled = useScale && !preserveAspect;
        }

        // 更新所有文本环绕控件的启用状态
        private void UpdateImageWrapControlsEnabled()
        {
            bool mainEnabled = enableImageFormatCheckBox.Checked && enableImageWrapCheckBox.Checked;

            // 环绕方式控件
            if (imageWrapTypeComboBox != null) imageWrapTypeComboBox.Enabled = mainEnabled;

            // 文本层次控件
            if (wrapTextNormalRadio != null) wrapTextNormalRadio.Enabled = mainEnabled;
            if (wrapTextBehindRadio != null) wrapTextBehindRadio.Enabled = mainEnabled;
            if (wrapTextFrontRadio != null) wrapTextFrontRadio.Enabled = mainEnabled;
        }

        // 更新所有图片格式控件的启用状态
        private void UpdateAllImageFormatControlsEnabled()
        {
            bool mainEnabled = enableImageFormatCheckBox.Checked;

            // 更新各个子功能区域
            UpdateImageSizeControlsEnabled();
            UpdateImageWrapControlsEnabled();
            UpdateImageEffectControlsEnabled();

            // 更新其他子功能的启用状态
            if (enableImageBorderCheckBox != null)
            {
                enableImageBorderCheckBox.Enabled = mainEnabled;
                bool borderEnabled = mainEnabled && enableImageBorderCheckBox.Checked;
                if (imageBorderStyleComboBox != null) imageBorderStyleComboBox.Enabled = borderEnabled;
                if (imageBorderWidthNumeric != null) imageBorderWidthNumeric.Enabled = borderEnabled;
                if (imageBorderColorButton != null) imageBorderColorButton.Enabled = borderEnabled;
                if (clearImageBorderColorButton != null) clearImageBorderColorButton.Enabled = borderEnabled;
            }

            if (enableImagePositionCheckBox != null)
            {
                enableImagePositionCheckBox.Enabled = mainEnabled;
                bool positionEnabled = mainEnabled && enableImagePositionCheckBox.Checked;
                if (horizontalRelativeComboBox != null) horizontalRelativeComboBox.Enabled = positionEnabled;
                if (verticalRelativeComboBox != null) verticalRelativeComboBox.Enabled = positionEnabled;
                if (horizontalAlignmentComboBox != null) horizontalAlignmentComboBox.Enabled = positionEnabled;
                if (verticalAlignmentComboBox != null) verticalAlignmentComboBox.Enabled = positionEnabled;
                if (horizontalPositionNumeric != null) horizontalPositionNumeric.Enabled = positionEnabled;
                if (verticalPositionNumeric != null) verticalPositionNumeric.Enabled = positionEnabled;
                if (useAlignmentRadio != null) useAlignmentRadio.Enabled = positionEnabled;
                if (useExactPositionRadio != null) useExactPositionRadio.Enabled = positionEnabled;
            }

            if (enableImageRotateFlipCheckBox != null)
            {
                enableImageRotateFlipCheckBox.Enabled = mainEnabled;
                bool rotateFlipEnabled = mainEnabled && enableImageRotateFlipCheckBox.Checked;
                if (rotationAngleNumeric != null) rotationAngleNumeric.Enabled = rotateFlipEnabled;
                if (flipHorizontalCheckBox != null) flipHorizontalCheckBox.Enabled = rotateFlipEnabled;
                if (flipVerticalCheckBox != null) flipVerticalCheckBox.Enabled = rotateFlipEnabled;
            }

            if (enableImageHyperlinkCheckBox != null)
            {
                enableImageHyperlinkCheckBox.Enabled = mainEnabled;
                bool hyperlinkEnabled = mainEnabled && enableImageHyperlinkCheckBox.Checked;
                if (hyperlinkTypeComboBox != null) hyperlinkTypeComboBox.Enabled = hyperlinkEnabled;
                if (hyperlinkUrlTextBox != null) hyperlinkUrlTextBox.Enabled = hyperlinkEnabled;
                if (bookmarkNameTextBox != null) bookmarkNameTextBox.Enabled = hyperlinkEnabled;
                if (hyperlinkToolTipTextBox != null) hyperlinkToolTipTextBox.Enabled = hyperlinkEnabled;
                if (openInNewWindowCheckBox != null) openInNewWindowCheckBox.Enabled = hyperlinkEnabled;
            }

            if (enableImageLockingCheckBox != null)
            {
                enableImageLockingCheckBox.Enabled = mainEnabled;
                bool lockingEnabled = mainEnabled && enableImageLockingCheckBox.Checked;
                if (lockPositionCheckBox != null) lockPositionCheckBox.Enabled = lockingEnabled;
                if (lockAspectRatioCheckBox != null) lockAspectRatioCheckBox.Enabled = lockingEnabled;
                if (lockFormattingCheckBox != null) lockFormattingCheckBox.Enabled = lockingEnabled;
            }
        }

        // 更新所有图片效果控件的启用状态
        private void UpdateImageEffectControlsEnabled()
        {
            bool mainEnabled = enableImageFormatCheckBox.Checked && enableImageEffectCheckBox.Checked;

            // 子功能启用复选框
            if (enableBrightnessCheckBox != null) enableBrightnessCheckBox.Enabled = mainEnabled;
            if (enableContrastCheckBox != null) enableContrastCheckBox.Enabled = mainEnabled;
            if (enableTransparencyCheckBox != null) enableTransparencyCheckBox.Enabled = mainEnabled;
            if (enableColorModeCheckBox != null) enableColorModeCheckBox.Enabled = mainEnabled;

            // 亮度控件
            bool brightnessEnabled = mainEnabled && enableBrightnessCheckBox != null && enableBrightnessCheckBox.Checked;
            if (brightnessTrackBar != null) brightnessTrackBar.Enabled = brightnessEnabled;

            // 对比度控件
            bool contrastEnabled = mainEnabled && enableContrastCheckBox != null && enableContrastCheckBox.Checked;
            if (contrastTrackBar != null) contrastTrackBar.Enabled = contrastEnabled;

            // 透明度控件
            bool transparencyEnabled = mainEnabled && enableTransparencyCheckBox != null && enableTransparencyCheckBox.Checked;
            if (transparencyTrackBar != null) transparencyTrackBar.Enabled = transparencyEnabled;

            // 颜色模式控件
            bool colorModeEnabled = mainEnabled && enableColorModeCheckBox != null && enableColorModeCheckBox.Checked;
            if (colorModeComboBox != null) colorModeComboBox.Enabled = colorModeEnabled;
        }

        private void UpdatePositionControlsState()
        {
            bool enabled = enableImagePositionCheckBox.Checked;
            horizontalRelativeComboBox.Enabled = enabled;
            verticalRelativeComboBox.Enabled = enabled;
            useAlignmentRadio.Enabled = enabled;
            useExactPositionRadio.Enabled = enabled;

            bool useAlignment = useAlignmentRadio.Checked && enabled;
            bool useExactPosition = useExactPositionRadio.Checked && enabled;

            horizontalAlignmentComboBox.Enabled = useAlignment;
            verticalAlignmentComboBox.Enabled = useAlignment;
            horizontalPositionNumeric.Enabled = useExactPosition;
            verticalPositionNumeric.Enabled = useExactPosition;
        }

        private void UpdateWatermarkPositionControlsState()
        {
            bool enabled = enableWatermarkPositionCheckBox.Checked;
            watermarkPositionCenterRadio.Enabled = enabled;
            watermarkPositionCustomRadio.Enabled = enabled;
            watermarkAllPagesRadio.Enabled = enabled;
            watermarkSpecificPagesRadio.Enabled = enabled;
            watermarkBehindContentCheckBox.Enabled = enabled;

            bool useCustomPosition = watermarkPositionCustomRadio.Checked && enabled;
            watermarkHorizontalPositionNumeric.Enabled = useCustomPosition;
            watermarkVerticalPositionNumeric.Enabled = useCustomPosition;

            bool useSpecificPages = watermarkSpecificPagesRadio.Checked && enabled;
            watermarkPageRangeTextBox.Enabled = useSpecificPages;
        }

        private void UpdateTableControlsState()
        {
            // 表格宽度自适应控件状态
            if (enableTableAutoFitCheckBox != null)
            {
                bool autoFitEnabled = enableTableAutoFitCheckBox.Checked;
                if (autoFitToContentsRadio != null) autoFitToContentsRadio.Enabled = autoFitEnabled;
                if (autoFitToWindowRadio != null) autoFitToWindowRadio.Enabled = autoFitEnabled;
                if (useFixedColumnWidthRadio != null) useFixedColumnWidthRadio.Enabled = autoFitEnabled;
                if (useFixedColumnWidthRadio != null && preferredTableWidthNumeric != null)
                    preferredTableWidthNumeric.Enabled = useFixedColumnWidthRadio.Checked && autoFitEnabled;
            }

            // 表格布局控件状态
            if (enableTableLayoutCheckBox != null)
            {
                bool layoutEnabled = enableTableLayoutCheckBox.Checked;
                if (tableAlignmentComboBox != null) tableAlignmentComboBox.Enabled = layoutEnabled;
                if (tableLeftIndentNumeric != null) tableLeftIndentNumeric.Enabled = layoutEnabled;
                if (tableRightIndentNumeric != null) tableRightIndentNumeric.Enabled = layoutEnabled;
                if (tableTextWrappingComboBox != null) tableTextWrappingComboBox.Enabled = layoutEnabled;
                if (tableAllowAutoFitCheckBox != null) tableAllowAutoFitCheckBox.Enabled = layoutEnabled;
                if (tableDefaultCellSpacingNumeric != null) tableDefaultCellSpacingNumeric.Enabled = layoutEnabled;
            }

            // 表格样式控件状态
            if (enableTableStyleCheckBox != null)
            {
                bool styleEnabled = enableTableStyleCheckBox.Checked;
                if (tableHasBordersCheckBox != null) tableHasBordersCheckBox.Enabled = styleEnabled;
                if (tableHasGridlinesCheckBox != null) tableHasGridlinesCheckBox.Enabled = styleEnabled;
                if (tableHasShadingCheckBox != null) tableHasShadingCheckBox.Enabled = styleEnabled;

                bool bordersEnabled = tableHasBordersCheckBox != null && tableHasBordersCheckBox.Checked && styleEnabled;
                if (tableBorderStyleComboBox != null) tableBorderStyleComboBox.Enabled = bordersEnabled;
                if (tableBorderWidthNumeric != null) tableBorderWidthNumeric.Enabled = bordersEnabled;
                if (tableBorderColorButton != null) tableBorderColorButton.Enabled = bordersEnabled;
                if (clearTableBorderColorButton != null) clearTableBorderColorButton.Enabled = bordersEnabled;

                bool gridlinesEnabled = tableHasGridlinesCheckBox != null && tableHasGridlinesCheckBox.Checked && styleEnabled;
                if (tableGridlineStyleComboBox != null) tableGridlineStyleComboBox.Enabled = gridlinesEnabled;
                if (tableGridlineWidthNumeric != null) tableGridlineWidthNumeric.Enabled = gridlinesEnabled;
                if (tableGridlineColorButton != null) tableGridlineColorButton.Enabled = gridlinesEnabled;
                if (clearTableGridlineColorButton != null) clearTableGridlineColorButton.Enabled = gridlinesEnabled;

                bool shadingEnabled = tableHasShadingCheckBox != null && tableHasShadingCheckBox.Checked && styleEnabled;
                if (tableShadingColorButton != null) tableShadingColorButton.Enabled = shadingEnabled;
                if (clearTableShadingColorButton != null) clearTableShadingColorButton.Enabled = shadingEnabled;
            }

            // 单元格格式控件状态
            if (enableCellFormatCheckBox != null)
            {
                bool cellFormatEnabled = enableCellFormatCheckBox.Checked;
                if (cellverticalAlignmentComboBox != null) cellverticalAlignmentComboBox.Enabled = cellFormatEnabled;
                if (cellMarginTopNumeric != null) cellMarginTopNumeric.Enabled = cellFormatEnabled;
                if (cellMarginBottomNumeric != null) cellMarginBottomNumeric.Enabled = cellFormatEnabled;
                if (cellMarginLeftNumeric != null) cellMarginLeftNumeric.Enabled = cellFormatEnabled;
                if (cellMarginRightNumeric != null) cellMarginRightNumeric.Enabled = cellFormatEnabled;
                if (cellPaddingTopNumeric != null) cellPaddingTopNumeric.Enabled = cellFormatEnabled;
                if (cellPaddingBottomNumeric != null) cellPaddingBottomNumeric.Enabled = cellFormatEnabled;
                if (cellPaddingLeftNumeric != null) cellPaddingLeftNumeric.Enabled = cellFormatEnabled;
                if (cellPaddingRightNumeric != null) cellPaddingRightNumeric.Enabled = cellFormatEnabled;
                if (cellHasShadingCheckBox != null) cellHasShadingCheckBox.Enabled = cellFormatEnabled;
                if (cellHasBordersCheckBox != null) cellHasBordersCheckBox.Enabled = cellFormatEnabled;

                bool cellShadingEnabled = cellHasShadingCheckBox != null && cellHasShadingCheckBox.Checked && cellFormatEnabled;
                if (cellShadingColorButton != null) cellShadingColorButton.Enabled = cellShadingEnabled;
                if (clearCellShadingColorButton != null) clearCellShadingColorButton.Enabled = cellShadingEnabled;

                bool cellBordersEnabled = cellHasBordersCheckBox != null && cellHasBordersCheckBox.Checked && cellFormatEnabled;
                if (cellBorderStyleComboBox != null) cellBorderStyleComboBox.Enabled = cellBordersEnabled;
                if (cellBorderWidthNumeric != null) cellBorderWidthNumeric.Enabled = cellBordersEnabled;
                if (cellBorderColorButton != null) cellBorderColorButton.Enabled = cellBordersEnabled;
                if (clearCellBorderColorButton != null) clearCellBorderColorButton.Enabled = cellBordersEnabled;
            }
        }
    }

    // 添加TabStopDialog类
    public class TabStopDialog : Form
    {
        private NumericUpDown positionNumeric;
        private ComboBox alignmentComboBox;
        private ComboBox leaderComboBox;

        public double TabStopPosition => (double)positionNumeric.Value;
        public AW.TabAlignment TabAlignment => GetTabAlignmentFromComboBox();
        public AW.TabLeader TabLeader => GetTabLeaderFromComboBox();

        public TabStopDialog()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "制表位设置";
            this.Size = new Size(350, 200);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 2,
                RowCount = 4
            };

            // 设置列宽
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120F));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

            // 位置
            layout.Controls.Add(new Label { Text = "位置:", AutoSize = true }, 0, 0);

            positionNumeric = new NumericUpDown
            {
                DecimalPlaces = 2,
                Increment = 0.5m,
                Minimum = 0,
                Maximum = 20,
                Value = 2.0m,
                Width = 80
            };

            var positionPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.LeftToRight
            };

            positionPanel.Controls.Add(positionNumeric);
            positionPanel.Controls.Add(new Label { Text = "厘米", AutoSize = true });

            layout.Controls.Add(positionPanel, 1, 0);

            // 对齐方式
            layout.Controls.Add(new Label { Text = "对齐方式:", AutoSize = true }, 0, 1);

            alignmentComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150
            };

            alignmentComboBox.Items.Add("左对齐");
            alignmentComboBox.Items.Add("居中对齐");
            alignmentComboBox.Items.Add("右对齐");
            alignmentComboBox.Items.Add("小数点对齐");
            alignmentComboBox.Items.Add("竖线对齐");
            alignmentComboBox.SelectedIndex = 0; // 默认左对齐

            layout.Controls.Add(alignmentComboBox, 1, 1);

            // 前导符
            layout.Controls.Add(new Label { Text = "前导符:", AutoSize = true }, 0, 2);

            leaderComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150
            };

            leaderComboBox.Items.Add("无");
            leaderComboBox.Items.Add("点号");
            leaderComboBox.Items.Add("短划线");
            leaderComboBox.Items.Add("下划线");
            leaderComboBox.SelectedIndex = 0; // 默认无前导符

            layout.Controls.Add(leaderComboBox, 1, 2);

            // 按钮
            var buttonPanel = new FlowLayoutPanel
            {
                AutoSize = true,
                FlowDirection = FlowDirection.RightToLeft,
                Dock = DockStyle.Fill
            };

            var cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                Width = 80
            };

            var okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK,
                Width = 80
            };

            buttonPanel.Controls.Add(cancelButton);
            buttonPanel.Controls.Add(okButton);

            layout.Controls.Add(buttonPanel, 1, 3);

            this.Controls.Add(layout);
            this.AcceptButton = okButton;
            this.CancelButton = cancelButton;
        }

        private AW.TabAlignment GetTabAlignmentFromComboBox()
        {
            return alignmentComboBox.SelectedIndex switch
            {
                0 => AW.TabAlignment.Left,
                1 => AW.TabAlignment.Center,
                2 => AW.TabAlignment.Right,
                3 => AW.TabAlignment.Decimal,
                4 => AW.TabAlignment.Bar,
                _ => AW.TabAlignment.Left
            };
        }

        private AW.TabLeader GetTabLeaderFromComboBox()
        {
            return leaderComboBox.SelectedIndex switch
            {
                0 => AW.TabLeader.None,
                1 => AW.TabLeader.Dots,
                2 => AW.TabLeader.Dashes,
                3 => AW.TabLeader.Line,
                _ => AW.TabLeader.None
            };
        }

        // 在TabStopDialog类中添加SetValues方法
        public void SetValues(double position, AW.TabAlignment alignment, AW.TabLeader leader)
        {
            positionNumeric.Value = (decimal)position;

            // 设置对齐方式
            alignmentComboBox.SelectedIndex = alignment switch
            {
                AW.TabAlignment.Left => 0,
                AW.TabAlignment.Center => 1,
                AW.TabAlignment.Right => 2,
                AW.TabAlignment.Decimal => 3,
                AW.TabAlignment.Bar => 4,
                _ => 0
            };

            // 设置前导符
            leaderComboBox.SelectedIndex = leader switch
            {
                AW.TabLeader.None => 0,
                AW.TabLeader.Dots => 1,
                AW.TabLeader.Dashes => 2,
                AW.TabLeader.Line => 3,
                _ => 0
            };
        }
    }
}



