/*
 * ========================================
 * 文件名: HeaderFooterSettings.cs
 * 功能描述: 页眉页脚设置数据模型
 * ========================================
 *
 * 主要功能:
 * 1. 定义页眉页脚的完整配置选项
 * 2. 支持页眉页脚的内容、格式和样式设置
 * 3. 提供页码格式和显示选项
 * 4. 包含字体、颜色和对齐方式配置
 * 5. 支持图片和文本的混合内容
 *
 * 核心设置分类:
 *
 * 通用设置:
 * - RemoveHeaderFooter: 移除所有页眉页脚
 * - RemoveHeader: 移除所有页眉
 * - RemoveFooter: 移除所有页脚
 * - EnableHeader: 启用页眉设置
 * - EnableFooter: 启用页脚设置
 * - DifferentFirstPage: 首页使用不同的页眉页脚
 * - ApplyToAllSections: 应用到所有节
 *
 * 距离设置:
 * - HeaderDistance: 页眉距顶部距离（厘米）
 * - FooterDistance: 页脚距底部距离（厘米）
 *
 * 页眉设置:
 * - HasHeader: 是否有页眉
 * - HeaderText: 页眉文本内容
 * - HasHeaderImage: 是否有页眉图片
 * - HeaderImageData: 页眉图片数据（字节数组）
 * - HeaderAlignment: 页眉对齐方式
 *
 * 页眉字体设置:
 * - FontName: 页眉字体名称
 * - FontSize: 页眉字体大小
 * - IsBold: 页眉是否粗体
 * - IsItalic: 页眉是否斜体
 * - FontColor: 页眉字体颜色
 * - HeaderBackgroundColor: 页眉背景颜色
 *
 * 页脚设置:
 * - HasFooter: 是否有页脚
 * - FooterText: 页脚文本内容
 * - HasFooterImage: 是否有页脚图片
 * - FooterImageData: 页脚图片数据（字节数组）
 * - FooterAlignment: 页脚对齐方式
 *
 * 页脚字体设置:
 * - FooterFontName: 页脚字体名称
 * - FooterFontSize: 页脚字体大小
 * - FooterIsBold: 页脚是否粗体
 * - FooterIsItalic: 页脚是否斜体
 * - FooterFontColor: 页脚字体颜色
 * - FooterBackgroundColor: 页脚背景颜色
 *
 * 页码设置:
 * - HasHeaderPageNumber: 页眉是否显示页码
 * - HeaderPageNumberFormat: 页眉页码格式（阿拉伯数字、罗马数字等）
 * - HeaderIncludePageCount: 页眉是否包含总页数
 * - HeaderPageNumberPrefix: 页眉页码前缀
 * - HeaderPageNumberSuffix: 页眉页码后缀
 * - HasFooterPageNumber: 页脚是否显示页码
 * - FooterPageNumberFormat: 页脚页码格式
 * - FooterIncludePageCount: 页脚是否包含总页数
 * - FooterPageNumberPrefix: 页脚页码前缀
 * - FooterPageNumberSuffix: 页脚页码后缀
 *
 * 颜色序列化:
 * - FontColorHex: 页眉字体颜色的十六进制表示
 * - HeaderBackgroundColorHex: 页眉背景颜色的十六进制表示
 * - FooterFontColorHex: 页脚字体颜色的十六进制表示
 * - FooterBackgroundColorHex: 页脚背景颜色的十六进制表示
 *
 * 页码格式选项:
 * - 0: 阿拉伯数字（1,2,3...）
 * - 1: 大写罗马数字（I,II,III...）
 * - 2: 小写罗马数字（i,ii,iii...）
 * - 3: 大写字母（A,B,C...）
 * - 4: 小写字母（a,b,c...）
 *
 * 注意事项:
 * - 支持Aspose.Words的所有页眉页脚选项
 * - 包含完整的颜色管理和序列化
 * - 支持图片和文本的混合内容
 * - 提供灵活的页码格式化选项
 */

using System;
using AW = Aspose.Words;
using System.Drawing;
using System.ComponentModel;

namespace AsposeWordFormatter.Models
{
    public class HeaderFooterSettings
    {
        // 通用设置
        public bool RemoveHeaderFooter { get; set; } = false;       // 移除所有页眉页脚
        public bool RemoveHeader { get; set; } = true;              // 新增：移除所有页眉
        public bool RemoveFooter { get; set; } = true;              // 新增：移除所有页脚
        public bool EnableHeader { get; set; } = false;             // 新增：启用页眉设置
        public bool EnableFooter { get; set; } = false;             // 新增：启用页脚设置
        public bool DifferentFirstPage { get; set; } = false;        // 首页使用不同的页眉页脚
        public bool ApplyToAllSections { get; set; } = true;        // 应用到所有节

        // 页眉页脚距离设置（厘米）
        public double HeaderDistance { get; set; } = 1.25;          // 页眉距顶部距离
        public double FooterDistance { get; set; } = 1.25;          // 页脚距底部距离

        // 页眉设置
        public bool HasHeader { get; set; } = false;
        public string HeaderText { get; set; } = string.Empty;
        public bool HasHeaderImage { get; set; } = false;
        public byte[]? HeaderImageData { get; set; } = null;
        public AW.ParagraphAlignment HeaderAlignment { get; set; } = AW.ParagraphAlignment.Center;

        // 页眉字体设置
        public string FontName { get; set; } = "宋体";
        public double FontSize { get; set; } = 10.5;
        public bool IsBold { get; set; } = false;
        public bool IsItalic { get; set; } = false;
        public Color FontColor { get; set; } = Color.Black;
        public Color? HeaderBackgroundColor { get; set; } = null;  // 新增：页眉文字背景色

        // 颜色序列化辅助属性
        [Browsable(false)]
        public string FontColorHex
        {
            get => ColorTranslator.ToHtml(FontColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                    FontColor = ColorTranslator.FromHtml(value);
            }
        }

        [Browsable(false)]
        public string HeaderBackgroundColorHex
        {
            get => HeaderBackgroundColor.HasValue ? ColorTranslator.ToHtml(HeaderBackgroundColor.Value) : string.Empty;
            set
            {
                if (string.IsNullOrEmpty(value))
                    HeaderBackgroundColor = null;
                else
                    HeaderBackgroundColor = ColorTranslator.FromHtml(value);
            }
        }

        // 页脚设置
        public bool HasFooter { get; set; } = false;
        public string FooterText { get; set; } = string.Empty;
        public bool HasFooterImage { get; set; } = false;
        public byte[]? FooterImageData { get; set; } = null;
        public AW.ParagraphAlignment FooterAlignment { get; set; } = AW.ParagraphAlignment.Center;

        // 页脚字体设置
        public string FooterFontName { get; set; } = "宋体";
        public double FooterFontSize { get; set; } = 10.5;
        public bool FooterIsBold { get; set; } = false;
        public bool FooterIsItalic { get; set; } = false;
        public Color FooterFontColor { get; set; } = Color.Black;
        public Color? FooterBackgroundColor { get; set; } = null;  // 新增：页脚文字背景色

        // 页脚颜色序列化辅助属性
        [Browsable(false)]
        public string FooterFontColorHex
        {
            get => ColorTranslator.ToHtml(FooterFontColor);
            set
            {
                if (!string.IsNullOrEmpty(value))
                    FooterFontColor = ColorTranslator.FromHtml(value);
            }
        }

        [Browsable(false)]
        public string FooterBackgroundColorHex
        {
            get => FooterBackgroundColor.HasValue ? ColorTranslator.ToHtml(FooterBackgroundColor.Value) : string.Empty;
            set
            {
                if (string.IsNullOrEmpty(value))
                    FooterBackgroundColor = null;
                else
                    FooterBackgroundColor = ColorTranslator.FromHtml(value);
            }
        }

        // 页眉页码设置
        public bool HasHeaderPageNumber { get; set; }
        public int HeaderPageNumberFormat { get; set; } = 0; // 0: 1,2,3..., 1: I,II,III..., 2: i,ii,iii..., 3: A,B,C..., 4: a,b,c...
        public bool HeaderIncludePageCount { get; set; } = false;
        public string HeaderPageNumberPrefix { get; set; } = "第";
        public string HeaderPageNumberSuffix { get; set; } = "页";

        // 页脚页码设置
        public bool HasFooterPageNumber { get; set; }
        public int FooterPageNumberFormat { get; set; } = 0;
        public bool FooterIncludePageCount { get; set; } = false;
        public string FooterPageNumberPrefix { get; set; } = "第";
        public string FooterPageNumberSuffix { get; set; } = "页";
    }
}