/*
 * ========================================
 * 文件名: DocumentFormatForm.cs
 * 功能描述: 文档格式配置窗体
 * ========================================
 *
 * 主要功能:
 * 1. 支持的文档格式选择和管理
 * 2. 文档格式的启用/禁用控制
 * 3. TXT文件特殊处理选项配置
 * 4. 格式分类和批量操作
 * 5. 文档格式设置的保存和加载
 *
 * 界面结构:
 * - 文档格式标签页：支持格式的复选框列表
 * - TXT处理标签页：TXT文件特殊处理选项
 *
 * 支持的文档格式:
 *
 * 文字处理格式:
 * - DOC, DOCX, DOCM: Microsoft Word文档
 * - DOT, DOTX, DOTM: Microsoft Word模板
 * - ODT, OTT: OpenDocument文本文档
 * - RTF: 富文本格式
 * - WPD: WordPerfect文档
 *
 * 文本和网页格式:
 * - TXT: 纯文本文件
 * - HTML, HTM: 网页文档
 * - MHTML: 单文件网页
 * - MD: Markdown文档
 * - XML: XML文档
 *
 * TXT处理选项:
 * - ConvertToWordBeforeProcessing: 处理前转换为Word格式
 * - PreserveOriginalEncoding: 保持原始编码
 * - AutoDetectEncoding: 自动检测编码
 * - DefaultEncoding: 默认编码设置
 *
 * 批量操作:
 * - 全部选择/取消
 * - 按分类选择（Word格式、文本格式等）
 * - 右键菜单快速操作
 *
 * 数据管理:
 * - 与SettingsManager集成
 * - 支持配置的保存和加载
 * - 实时更新文件列表
 *
 * 注意事项:
 * - 格式选择影响文件扫描范围
 * - TXT处理选项影响文本文件的处理方式
 * - 支持Aspose.Words的所有输入格式
 * - 包含格式兼容性检查
 */

using System;
using System.Collections.Generic;
using System.Windows.Forms;
using System.Drawing;
using System.Linq;
using System.Windows.Forms.VisualStyles;
using AsposeWordFormatter.Models;
using Aspose.Words;

namespace AsposeWordFormatter
{
    public partial class DocumentFormatForm : Form
    {
        private TabControl tabControl = null!;
        private TabPage formatsTabPage = null!;
        private TabPage txtProcessingTabPage = null!;
        private FlowLayoutPanel formatsPanel = null!;
        private Button saveButton = null!;
        private Button cancelButton = null!;
        private Dictionary<string, CheckBox> formatCheckboxes = new Dictionary<string, CheckBox>();

        // TXT处理相关控件
        private CheckBox convertTxtToWordCheckBox = null!;

        // TXT设置相关
        private TxtSettings txtSettings = null!;

        // 当前是否为批量转换模式
        public DocumentFormatForm()
        {
            // 先加载设置，确保txtSettings有值
            LoadSettings();
            InitializeComponent();
            InitializeControls();
            LoadFormats();

            // 不需要在OnLoad中再次调用LoadSettings，因为已经在构造函数中调用过了
        }

        private void InitializeComponent()
        {
            this.Text = "文档格式设置";
            this.Size = new System.Drawing.Size(700, 700); // 增加窗体大小
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Padding = new Padding(12);
            this.BackColor = SystemColors.Control;

            // 初始化tabControl和标签页
            tabControl = new TabControl();
            tabControl.Dock = DockStyle.Fill;
            tabControl.Location = new Point(0, 0);
            tabControl.Name = "tabControl";
            tabControl.SelectedIndex = 0;
            tabControl.Size = new Size(650, 420);
            tabControl.TabIndex = 0;

            // 创建格式标签页
            formatsTabPage = new TabPage();
            formatsTabPage.Text = "文档格式";
            formatsTabPage.Padding = new Padding(10);
            formatsTabPage.UseVisualStyleBackColor = true;

            // 创建TXT处理标签页
            txtProcessingTabPage = new TabPage();
            txtProcessingTabPage.Text = "TXT处理";
            txtProcessingTabPage.Padding = new Padding(10);
            txtProcessingTabPage.UseVisualStyleBackColor = true;

            // 确保txtSettings不为空
            if (txtSettings == null)
            {
                txtSettings = new TxtSettings();
            }

            txtProcessingTabPage.Controls.Add(CreateTxtProcessingPanel());

            // 创建格式组框
            var formatsGroupBox = new GroupBox();
            formatsGroupBox.Text = "可用格式";
            formatsGroupBox.Dock = DockStyle.Fill;
            formatsGroupBox.Padding = new Padding(10);

            // 创建格式面板（使用FlowLayoutPanel替代CheckedListBox）
            formatsPanel = new FlowLayoutPanel();
            formatsPanel.Dock = DockStyle.Fill;
            formatsPanel.Name = "formatsPanel";
            formatsPanel.AutoScroll = true;
            formatsPanel.FlowDirection = FlowDirection.TopDown;
            formatsPanel.WrapContents = false;
            formatsPanel.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, GraphicsUnit.Point);
            formatsPanel.Padding = new Padding(10, 5, 25, 5);
            formatsPanel.AutoScrollMinSize = new Size(550, 0);

            // 创建底部按钮面板
            var buttonPanel = new Panel();
            buttonPanel.Dock = DockStyle.Bottom;
            buttonPanel.Height = 50;
            buttonPanel.Padding = new Padding(10);

            // 创建保存按钮
            saveButton = new Button();
            saveButton.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            saveButton.Location = new Point(buttonPanel.Width - 180, 15);
            saveButton.Name = "saveButton";
            saveButton.Size = new Size(75, 30);
            saveButton.TabIndex = 1;
            saveButton.Text = "保存";
            saveButton.UseVisualStyleBackColor = true;
            saveButton.FlatStyle = FlatStyle.Flat;
            saveButton.BackColor = Color.FromArgb(0, 122, 204);
            saveButton.ForeColor = Color.White;
            saveButton.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            saveButton.Click += SaveButton_Click;

            // 创建取消按钮
            cancelButton = new Button();
            cancelButton.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            cancelButton.Location = new Point(buttonPanel.Width - 95, 15);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(75, 30);
            cancelButton.TabIndex = 2;
            cancelButton.Text = "取消";
            cancelButton.UseVisualStyleBackColor = true;
            cancelButton.FlatStyle = FlatStyle.Flat;
            cancelButton.BackColor = Color.White;
            cancelButton.ForeColor = Color.Black;
            cancelButton.Click += (sender, e) => Close();

            // 添加按钮到面板
            buttonPanel.Controls.Add(saveButton);
            buttonPanel.Controls.Add(cancelButton);

            // 添加控件到组框
            formatsGroupBox.Controls.Add(formatsPanel);

            // 添加组框到标签页
            formatsTabPage.Controls.Add(formatsGroupBox);

            // 添加标签页到标签控件
            tabControl.Controls.Add(formatsTabPage);
            tabControl.Controls.Add(txtProcessingTabPage);

            // 添加控件到窗体
            Controls.Add(tabControl);
            Controls.Add(buttonPanel);
        }

        private void InitializeControls()
        {
            // InitializeComponent已创建基本控件
            // 我们只设置任何剩余的配置

            // 添加右键菜单 - 移除不适用的格式类别
            ContextMenuStrip formatMenu = new ContextMenuStrip();
            formatMenu.Items.Add("全部选择", null, (sender, e) => SelectAllFormats());
            formatMenu.Items.Add("全部取消", null, (sender, e) => DeselectAllFormats());
            formatMenu.Items.Add("-"); // 分隔线
            formatMenu.Items.Add("选择Word处理格式", null, (sender, e) => SelectWordFormats());
            formatMenu.Items.Add("选择文本和网页格式", null, (sender, e) => SelectTextFormats());

            formatsPanel.ContextMenuStrip = formatMenu;
        }

        private void LoadFormats()
        {
            // 获取当前支持的格式
            var formats = SettingsManager.GetDocumentFormats();

            // 确保支持的格式列表包含所有Aspose.Words支持的文档格式
            EnsureAllFormatsSupported(formats);

            // 清空当前面板和字典
            formatsPanel.Controls.Clear();
            formatCheckboxes.Clear();

            // 定义格式分类
            var categoryFormats = new Dictionary<string, List<string[]>>
            {
                { "文字处理格式", new List<string[]> {
                    new string[] { "DOC", "DOCX", "DOCM" },
                    new string[] { "DOT", "DOTM", "DOTX" },
                    new string[] { "ODT", "OTT", "RTF" },
                    new string[] { "WPD" },
                }},
                { "文本和网页格式", new List<string[]> {
                    new string[] { "TXT", "HTML", "HTM" },
                    new string[] { "MHTML", "MD", "XML" },
                }}
            };

            // 为每个分类创建控件
            foreach (var category in categoryFormats)
            {
                // 添加分类标题
                Label categoryLabel = new Label
                {
                    Text = category.Key,
                    AutoSize = false,
                    Width = 550,
                    Height = 30,
                    Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold),
                    ForeColor = Color.DarkBlue,
                    BackColor = Color.FromArgb(235, 240, 250),
                    TextAlign = System.Drawing.ContentAlignment.MiddleLeft,
                    Padding = new Padding(10, 0, 0, 0),
                    Margin = new Padding(0, 10, 0, 5)
                };
                formatsPanel.Controls.Add(categoryLabel);

                // 添加该分类的格式
                foreach (var row in category.Value)
                {
                    // 创建一行格式复选框的容器
                    Panel rowPanel = new Panel
                    {
                        Width = 550, // 显式设置足够的宽度以容纳三列
                        Height = 35,
                        Margin = new Padding(5, 2, 0, 2),
                        Padding = new Padding(5, 0, 0, 0),
                        AutoSize = false
                    };

                    // 计算水平间距以均匀分布3个复选框
                    int checkboxWidth = 165; // 固定每个复选框宽度
                    int totalWidth = checkboxWidth * 3 + 20; // 总宽度

                    // 每行最多显示3个格式
                    for (int i = 0; i < row.Length; i++)
                    {
                        string format = row[i];

                        // 创建格式复选框
                        CheckBox formatCheckBox = new CheckBox
                        {
                            Text = format,
                            AutoSize = false,
                            Width = checkboxWidth,
                            Height = 25,
                            Location = new Point(i * checkboxWidth + 10, 5),
                            Checked = formats.ContainsKey(format) && formats[format],
                            TextAlign = System.Drawing.ContentAlignment.MiddleLeft,
                            Font = new System.Drawing.Font("微软雅黑", 9.5F, System.Drawing.FontStyle.Regular)
                        };

                        // 添加到面板和字典
                        rowPanel.Controls.Add(formatCheckBox);
                        formatCheckboxes[format] = formatCheckBox;
                    }

                    formatsPanel.Controls.Add(rowPanel);
                }

                // 添加分隔线
                Panel separatorPanel = new Panel
                {
                    Width = 550,
                    Height = 1,
                    BackColor = Color.FromArgb(230, 230, 230),
                    Margin = new Padding(5, 10, 0, 10)
                };
                formatsPanel.Controls.Add(separatorPanel);
            }
        }

        /// <summary>
        /// 确保所有Aspose.Words支持的文档格式都在列表中
        /// </summary>
        private void EnsureAllFormatsSupported(Dictionary<string, bool> formats)
        {
            // Aspose.Words支持的主要输入格式列表 - 移除不支持的格式
            string[] supportedFormats = new string[]
            {
                // 主要文字处理格式
                "DOC", "DOCX", "DOCM", "DOT", "DOTX", "DOTM", "RTF", "ODT", "OTT", "WPD",

                // 文本格式
                "TXT", "HTML", "HTM", "MHTML", "MD", "XML"
            };

            // 为每个支持的格式添加到字典中，如果尚未存在
            foreach (var format in supportedFormats)
            {
                if (!formats.ContainsKey(format))
                {
                    formats[format] = true; // 默认启用
                }
            }
        }

        private void SaveButton_Click(object? sender, EventArgs e)
        {
            // 获取当前设置
            Settings settings = SettingsManager.LoadSettings();

            // 保存格式设置
            var formats = new Dictionary<string, bool>();
            foreach (var kvp in formatCheckboxes)
            {
                formats[kvp.Key] = kvp.Value.Checked;
            }
            SettingsManager.SaveDocumentFormats(formats);

            // 保存TXT设置
            // 确保保存的是当前UI中的设置
            txtSettings.ConvertToWordBeforeProcessing = convertTxtToWordCheckBox.Checked;
            settings.TxtSettings = txtSettings;

            Console.WriteLine($"保存TXT设置: ConvertToWordBeforeProcessing = {txtSettings.ConvertToWordBeforeProcessing}");
            Console.WriteLine($"UI控件状态: convertTxtToWordCheckBox.Checked = {convertTxtToWordCheckBox.Checked}");

            // 保存设置
            SettingsManager.SaveSettings(settings);

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        // 添加格式选择相关方法
        private void SelectAllFormats()
        {
            foreach (var checkbox in formatCheckboxes.Values)
            {
                checkbox.Checked = true;
            }
        }

        private void DeselectAllFormats()
        {
            foreach (var checkbox in formatCheckboxes.Values)
            {
                checkbox.Checked = false;
            }
        }

        private void SelectWordFormats()
        {
            string[] wordFormats = { "DOC", "DOCX", "DOCM", "DOT", "DOTX", "DOTM", "RTF", "ODT", "OTT", "WPD" };
            SelectFormatsInList(wordFormats);
        }

        private void SelectTextFormats()
        {
            string[] textFormats = { "TXT", "HTML", "HTM", "MHTML", "XML", "MD" };
            SelectFormatsInList(textFormats);
        }

        private void SelectFormatsInList(string[] formats)
        {
            foreach (var kvp in formatCheckboxes)
            {
                kvp.Value.Checked = formats.Contains(kvp.Key);
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // 设置初始选中标签页
            tabControl.SelectedIndex = 0;
            // 不需要再次加载设置，因为已经在构造函数中加载过了
        }

        private void LoadSettings()
        {
            // 从SettingsManager加载TXT设置
            Settings settings = SettingsManager.LoadSettings();

            // 确保TxtSettings不为null，并且ConvertToWordBeforeProcessing的值正确
            if (settings.TxtSettings == null)
            {
                txtSettings = new TxtSettings();
                txtSettings.ConvertToWordBeforeProcessing = false; // 明确设置为false
                Console.WriteLine("创建新的TxtSettings实例，ConvertToWordBeforeProcessing = false");
            }
            else
            {
                txtSettings = settings.TxtSettings;
                Console.WriteLine($"使用已加载的TxtSettings实例，ConvertToWordBeforeProcessing = {txtSettings.ConvertToWordBeforeProcessing}");
            }

            Console.WriteLine($"加载TXT设置: ConvertToWordBeforeProcessing = {txtSettings.ConvertToWordBeforeProcessing}");

            // 如果控件已初始化，则更新UI
            if (convertTxtToWordCheckBox != null)
            {
                convertTxtToWordCheckBox.Checked = txtSettings.ConvertToWordBeforeProcessing;
                Console.WriteLine($"更新UI控件: convertTxtToWordCheckBox.Checked = {convertTxtToWordCheckBox.Checked}");
            }
        }

        // 创建TXT处理面板
        private Panel CreateTxtProcessingPanel()
        {
            // 创建主面板
            Panel contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(0)
            };

            // 创建容器面板用于包含所有设置组
            Panel settingsContainerPanel = new Panel
            {
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10)
            };

            contentPanel.Controls.Add(settingsContainerPanel);

            // 创建全局设置分组框
            GroupBox globalSettingsGroupBox = new GroupBox
            {
                Text = "全局设置",
                Width = 500,
                Height = 60,
                Location = new Point(10, 10)
            };

            // 创建全局启用TXT转Word的勾选框
            convertTxtToWordCheckBox = new CheckBox
            {
                Text = "将txt转换为word后再处理",
                Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold),
                AutoSize = true,
                Checked = txtSettings.ConvertToWordBeforeProcessing,
                Location = new Point(20, 25)
            };

            // 确保勾选框状态与设置一致
            Console.WriteLine($"TXT设置初始化: ConvertToWordBeforeProcessing = {txtSettings.ConvertToWordBeforeProcessing}");
            convertTxtToWordCheckBox.CheckedChanged += (sender, e) =>
            {
                txtSettings.ConvertToWordBeforeProcessing = convertTxtToWordCheckBox.Checked;

                // 获取所有子控件中的GroupBox并设置启用/禁用状态
                foreach (Control control in settingsContainerPanel.Controls)
                {
                    if (control is GroupBox && control != globalSettingsGroupBox)
                    {
                        control.Enabled = convertTxtToWordCheckBox.Checked;
                    }
                }
            };

            // 将复选框添加到GroupBox
            globalSettingsGroupBox.Controls.Add(convertTxtToWordCheckBox);

            // 将全局设置GroupBox添加到容器面板（与其他设置在同一层级）
            settingsContainerPanel.Controls.Add(globalSettingsGroupBox);

            // 添加各个TXT处理功能的分组框
            int yPos = globalSettingsGroupBox.Height + 20; // 从全局设置下方开始

            // 添加编码设置组
            var encodingGroup = CreateEncodingSettingsGroup(ref yPos);
            encodingGroup.Enabled = txtSettings.ConvertToWordBeforeProcessing;
            settingsContainerPanel.Controls.Add(encodingGroup);

            // 添加段落处理组
            var paragraphGroup = CreateParagraphProcessingGroup(ref yPos);
            paragraphGroup.Enabled = txtSettings.ConvertToWordBeforeProcessing;
            settingsContainerPanel.Controls.Add(paragraphGroup);

            // 添加列表处理组
            var listGroup = CreateListDetectionGroup(ref yPos);
            listGroup.Enabled = txtSettings.ConvertToWordBeforeProcessing;
            settingsContainerPanel.Controls.Add(listGroup);

            // 添加表格处理组
            var tableGroup = CreateTableDetectionGroup(ref yPos);
            tableGroup.Enabled = txtSettings.ConvertToWordBeforeProcessing;
            settingsContainerPanel.Controls.Add(tableGroup);

            // 添加标题处理组
            var headingGroup = CreateHeadingDetectionGroup(ref yPos);
            headingGroup.Enabled = txtSettings.ConvertToWordBeforeProcessing;
            settingsContainerPanel.Controls.Add(headingGroup);

            // 添加文本样式组
            var textStyleGroup = CreateTextStylingGroup(ref yPos);
            textStyleGroup.Enabled = txtSettings.ConvertToWordBeforeProcessing;
            settingsContainerPanel.Controls.Add(textStyleGroup);

            // 添加文档格式组
            var formatGroup = CreateDocumentFormatGroup(ref yPos);
            formatGroup.Enabled = txtSettings.ConvertToWordBeforeProcessing;
            settingsContainerPanel.Controls.Add(formatGroup);

            return contentPanel;
        }

        // 创建编码设置组
        private GroupBox CreateEncodingSettingsGroup(ref int yPos)
        {
            GroupBox groupBox = new GroupBox
            {
                Text = "编码设置",
                Width = 500,
                Height = 90,
                Location = new Point(10, yPos)  // 使用简单的位置，不加额外偏移
            };

            // 启用编码检测勾选框
            CheckBox enableEncodingDetectionCheckBox = new CheckBox
            {
                Text = "启用编码检测",
                AutoSize = true,
                Checked = txtSettings.EnableEncodingDetection,
                Location = new Point(20, 40)
            };
            enableEncodingDetectionCheckBox.CheckedChanged += (sender, e) =>
                txtSettings.EnableEncodingDetection = enableEncodingDetectionCheckBox.Checked;

            // 默认编码标签
            Label defaultEncodingLabel = new Label
            {
                Text = "默认编码:",
                AutoSize = true,
                Location = new Point(20, 60)
            };

            // 默认编码下拉框
            ComboBox defaultEncodingComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Location = new Point(120, 60)
            };

            string[] encodings = { "UTF-8", "GBK", "GB2312", "ASCII", "Unicode" };
            defaultEncodingComboBox.Items.AddRange(encodings);
            defaultEncodingComboBox.SelectedItem = txtSettings.DefaultEncoding;
            if (defaultEncodingComboBox.SelectedIndex == -1 && defaultEncodingComboBox.Items.Count > 0)
                defaultEncodingComboBox.SelectedIndex = 0;

            defaultEncodingComboBox.SelectedIndexChanged += (sender, e) =>
                txtSettings.DefaultEncoding = defaultEncodingComboBox.SelectedItem?.ToString() ?? "UTF-8";

            groupBox.Controls.Add(enableEncodingDetectionCheckBox);
            groupBox.Controls.Add(defaultEncodingLabel);
            groupBox.Controls.Add(defaultEncodingComboBox);

            yPos += groupBox.Height + 10;
            return groupBox;
        }

        // 创建段落处理组
        private GroupBox CreateParagraphProcessingGroup(ref int yPos)
        {
            GroupBox groupBox = new GroupBox
            {
                Text = "段落处理",
                Width = 500,
                Height = 140,
                Location = new Point(10, yPos)
            };

            // 启用段落处理勾选框
            CheckBox enableParagraphProcessingCheckBox = new CheckBox
            {
                Text = "启用段落处理",
                AutoSize = true,
                Checked = txtSettings.EnableParagraphProcessing,
                Location = new Point(20, 30)
            };
            enableParagraphProcessingCheckBox.CheckedChanged += (sender, e) =>
                txtSettings.EnableParagraphProcessing = enableParagraphProcessingCheckBox.Checked;

            // 连续空行作为段落分隔勾选框
            CheckBox blankLinesAsParagraphCheckBox = new CheckBox
            {
                Text = "将连续空行视为段落分隔",
                AutoSize = true,
                Checked = txtSettings.TreatConsecutiveBlankLinesAsParagraph,
                Location = new Point(20, 60)
            };
            blankLinesAsParagraphCheckBox.CheckedChanged += (sender, e) =>
                txtSettings.TreatConsecutiveBlankLinesAsParagraph = blankLinesAsParagraphCheckBox.Checked;

            // 空行阈值标签
            Label blankLinesThresholdLabel = new Label
            {
                Text = "空行阈值:",
                AutoSize = true,
                Location = new Point(20, 90)
            };

            // 空行阈值数字控件
            NumericUpDown blankLinesThresholdNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 10,
                Value = txtSettings.BlankLinesThreshold,
                Width = 60,
                Location = new Point(120, 90)
            };
            blankLinesThresholdNumeric.ValueChanged += (sender, e) =>
                txtSettings.BlankLinesThreshold = (int)blankLinesThresholdNumeric.Value;

            groupBox.Controls.Add(enableParagraphProcessingCheckBox);
            groupBox.Controls.Add(blankLinesAsParagraphCheckBox);
            groupBox.Controls.Add(blankLinesThresholdLabel);
            groupBox.Controls.Add(blankLinesThresholdNumeric);

            yPos += groupBox.Height + 10;
            return groupBox;
        }

        // 创建列表检测组
        private GroupBox CreateListDetectionGroup(ref int yPos)
        {
            GroupBox groupBox = new GroupBox
            {
                Text = "列表检测",
                Width = 500,
                Height = 120,
                Location = new Point(10, yPos)
            };

            // 启用列表检测勾选框
            CheckBox enableListDetectionCheckBox = new CheckBox
            {
                Text = "启用列表检测",
                AutoSize = true,
                Checked = txtSettings.EnableListDetection,
                Location = new Point(20, 30)
            };
            enableListDetectionCheckBox.CheckedChanged += (sender, e) =>
                txtSettings.EnableListDetection = enableListDetectionCheckBox.Checked;

            // 检测编号列表勾选框
            CheckBox detectNumberedListsCheckBox = new CheckBox
            {
                Text = "检测编号列表",
                AutoSize = true,
                Checked = txtSettings.DetectNumberedLists,
                Location = new Point(20, 60)
            };
            detectNumberedListsCheckBox.CheckedChanged += (sender, e) =>
                txtSettings.DetectNumberedLists = detectNumberedListsCheckBox.Checked;

            // 检测项目符号列表勾选框
            CheckBox detectBulletedListsCheckBox = new CheckBox
            {
                Text = "检测项目符号列表",
                AutoSize = true,
                Checked = txtSettings.DetectBulletedLists,
                Location = new Point(250, 60)
            };
            detectBulletedListsCheckBox.CheckedChanged += (sender, e) =>
                txtSettings.DetectBulletedLists = detectBulletedListsCheckBox.Checked;

            groupBox.Controls.Add(enableListDetectionCheckBox);
            groupBox.Controls.Add(detectNumberedListsCheckBox);
            groupBox.Controls.Add(detectBulletedListsCheckBox);

            yPos += groupBox.Height + 10;
            return groupBox;
        }

        // 创建表格检测组
        private GroupBox CreateTableDetectionGroup(ref int yPos)
        {
            GroupBox groupBox = new GroupBox
            {
                Text = "表格检测",
                Width = 500,
                Height = 120,
                Location = new Point(10, yPos)
            };

            // 启用表格检测勾选框
            CheckBox enableTableDetectionCheckBox = new CheckBox
            {
                Text = "启用表格检测",
                AutoSize = true,
                Checked = txtSettings.EnableTableDetection,
                Location = new Point(20, 30)
            };
            enableTableDetectionCheckBox.CheckedChanged += (sender, e) =>
                txtSettings.EnableTableDetection = enableTableDetectionCheckBox.Checked;

            // 表格分隔符标签
            Label tableDelimiterLabel = new Label
            {
                Text = "表格分隔符:",
                AutoSize = true,
                Location = new Point(20, 70)
            };

            // 表格分隔符下拉框
            ComboBox tableDelimiterComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Location = new Point(120, 70)
            };

            string[] delimiters = { "Tab键", "逗号", "空格", "分号", "自定义" };
            tableDelimiterComboBox.Items.AddRange(delimiters);
            int selectedIndex = 0;
            switch (txtSettings.TableDelimiter)
            {
                case "\t": selectedIndex = 0; break;
                case ",": selectedIndex = 1; break;
                case " ": selectedIndex = 2; break;
                case ";": selectedIndex = 3; break;
                default: selectedIndex = 4; break;
            }
            tableDelimiterComboBox.SelectedIndex = selectedIndex;

            // 自定义分隔符文本框
            TextBox customDelimiterTextBox = new TextBox
            {
                Width = 100,
                Location = new Point(280, 70),
                Visible = selectedIndex == 4,
                Text = selectedIndex == 4 ? txtSettings.TableDelimiter : ""
            };

            tableDelimiterComboBox.SelectedIndexChanged += (sender, e) =>
            {
                string delimiter = "";
                switch (tableDelimiterComboBox.SelectedIndex)
                {
                    case 0: delimiter = "\t"; break;
                    case 1: delimiter = ","; break;
                    case 2: delimiter = " "; break;
                    case 3: delimiter = ";"; break;
                    case 4: delimiter = customDelimiterTextBox.Text; break;
                }
                txtSettings.TableDelimiter = delimiter;
                customDelimiterTextBox.Visible = tableDelimiterComboBox.SelectedIndex == 4;
            };

            customDelimiterTextBox.TextChanged += (sender, e) =>
            {
                if (tableDelimiterComboBox.SelectedIndex == 4)
                    txtSettings.TableDelimiter = customDelimiterTextBox.Text;
            };

            groupBox.Controls.Add(enableTableDetectionCheckBox);
            groupBox.Controls.Add(tableDelimiterLabel);
            groupBox.Controls.Add(tableDelimiterComboBox);
            groupBox.Controls.Add(customDelimiterTextBox);

            yPos += groupBox.Height + 10;
            return groupBox;
        }

        // 创建标题检测组
        private GroupBox CreateHeadingDetectionGroup(ref int yPos)
        {
            GroupBox groupBox = new GroupBox
            {
                Text = "标题检测",
                Width = 500,
                Height = 120,
                Location = new Point(10, yPos)
            };

            // 启用标题检测勾选框
            CheckBox enableHeadingDetectionCheckBox = new CheckBox
            {
                Text = "启用标题检测",
                AutoSize = true,
                Checked = txtSettings.EnableHeadingDetection,
                Location = new Point(20, 30)
            };
            enableHeadingDetectionCheckBox.CheckedChanged += (sender, e) =>
                txtSettings.EnableHeadingDetection = enableHeadingDetectionCheckBox.Checked;

            // 最大标题级别标签
            Label maxHeadingLevelLabel = new Label
            {
                Text = "最大标题级别:",
                AutoSize = true,
                Location = new Point(20, 70)
            };

            // 最大标题级别数字控件
            NumericUpDown maxHeadingLevelNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 9,
                Value = txtSettings.MaxHeadingLevel,
                Width = 60,
                Location = new Point(120, 70)
            };
            maxHeadingLevelNumeric.ValueChanged += (sender, e) =>
                txtSettings.MaxHeadingLevel = (int)maxHeadingLevelNumeric.Value;

            groupBox.Controls.Add(enableHeadingDetectionCheckBox);
            groupBox.Controls.Add(maxHeadingLevelLabel);
            groupBox.Controls.Add(maxHeadingLevelNumeric);

            yPos += groupBox.Height + 10;
            return groupBox;
        }

        // 创建文本样式组
        private GroupBox CreateTextStylingGroup(ref int yPos)
        {
            GroupBox groupBox = new GroupBox
            {
                Text = "文本样式",
                Width = 500,
                Height = 150,
                Location = new Point(10, yPos)
            };

            // 启用文本样式勾选框
            CheckBox enableTextStylingCheckBox = new CheckBox
            {
                Text = "启用文本样式",
                AutoSize = true,
                Checked = txtSettings.EnableTextStyling,
                Location = new Point(20, 30)
            };
            enableTextStylingCheckBox.CheckedChanged += (sender, e) =>
                txtSettings.EnableTextStyling = enableTextStylingCheckBox.Checked;

            // 默认字体标签
            Label defaultFontLabel = new Label
            {
                Text = "默认字体:",
                AutoSize = true,
                Location = new Point(20, 70)
            };

            // 默认字体下拉框
            ComboBox defaultFontComboBox = new ComboBox
            {
                Width = 200,
                Location = new Point(120, 70)
            };

            // 添加系统字体
            foreach (FontFamily family in FontFamily.Families)
            {
                defaultFontComboBox.Items.Add(family.Name);
            }

            defaultFontComboBox.Text = txtSettings.DefaultFontName;
            defaultFontComboBox.TextChanged += (sender, e) =>
                txtSettings.DefaultFontName = defaultFontComboBox.Text;

            // 默认字体大小标签
            Label defaultFontSizeLabel = new Label
            {
                Text = "默认字体大小:",
                AutoSize = true,
                Location = new Point(20, 110)
            };

            // 默认字体大小数字控件
            NumericUpDown defaultFontSizeNumeric = new NumericUpDown
            {
                Minimum = 5,
                Maximum = 72,
                DecimalPlaces = 1,
                Increment = 0.5m,
                Value = (decimal)txtSettings.DefaultFontSize,
                Width = 60,
                Location = new Point(120, 110)
            };
            defaultFontSizeNumeric.ValueChanged += (sender, e) =>
                txtSettings.DefaultFontSize = (double)defaultFontSizeNumeric.Value;

            groupBox.Controls.Add(enableTextStylingCheckBox);
            groupBox.Controls.Add(defaultFontLabel);
            groupBox.Controls.Add(defaultFontComboBox);
            groupBox.Controls.Add(defaultFontSizeLabel);
            groupBox.Controls.Add(defaultFontSizeNumeric);

            yPos += groupBox.Height + 10;
            return groupBox;
        }

        // 创建文档格式组
        private GroupBox CreateDocumentFormatGroup(ref int yPos)
        {
            GroupBox groupBox = new GroupBox
            {
                Text = "文档格式",
                Width = 500,
                Height = 90,
                Location = new Point(10, yPos)
            };

            // 转换格式标签
            Label convertToFormatLabel = new Label
            {
                Text = "转换格式:",
                AutoSize = true,
                Location = new Point(20, 40)
            };

            // 转换格式下拉框
            ComboBox convertToFormatComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 150,
                Location = new Point(120, 40)
            };

            // 添加支持的格式
            convertToFormatComboBox.Items.Add("DOCX");
            convertToFormatComboBox.Items.Add("DOC");
            convertToFormatComboBox.Items.Add("RTF");

            // 选择当前格式
            int selectedIndex = 0;
            switch (txtSettings.ConvertToFormat)
            {
                case SaveFormat.Docx: selectedIndex = 0; break;
                case SaveFormat.Doc: selectedIndex = 1; break;
                case SaveFormat.Rtf: selectedIndex = 2; break;
                default: selectedIndex = 0; break;
            }
            convertToFormatComboBox.SelectedIndex = selectedIndex;

            convertToFormatComboBox.SelectedIndexChanged += (sender, e) =>
            {
                switch (convertToFormatComboBox.SelectedIndex)
                {
                    case 0: txtSettings.ConvertToFormat = SaveFormat.Docx; break;
                    case 1: txtSettings.ConvertToFormat = SaveFormat.Doc; break;
                    case 2: txtSettings.ConvertToFormat = SaveFormat.Rtf; break;
                }
            };

            groupBox.Controls.Add(convertToFormatLabel);
            groupBox.Controls.Add(convertToFormatComboBox);

            yPos += groupBox.Height + 10;
            return groupBox;
        }
    }
}
