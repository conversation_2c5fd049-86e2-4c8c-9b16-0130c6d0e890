// 创建PdfSettingsForm.cs

using System;
using System.Windows.Forms;
using System.Drawing;
using AsposeWordFormatter.Models;
using AW = Aspose.Words;

namespace AsposeWordFormatter
{
    public partial class PdfSettingsForm : Form
    {
        private readonly PdfSettings pdfSettings;
        private TabControl mainTabControl = null!;

        // 图像压缩选项控件
        private CheckBox enableImageCompressionCheckBox = null!;
        private RadioButton jpegCompressionRadio = null!;
        private RadioButton autoCompressionRadio = null!;
        private NumericUpDown jpegQualityNumeric = null!;
        private Label jpegQualityLabel = null!;

        // 分辨率设置控件
        private CheckBox enableResolutionSettingsCheckBox = null!;
        private NumericUpDown colorImageResolutionNumeric = null!;
        private NumericUpDown grayImageResolutionNumeric = null!;
        private NumericUpDown monoImageResolutionNumeric = null!;

        // 图像优化控件
        private CheckBox enableImageOptimizationCheckBox = null!;
        private CheckBox downsampleImagesCheckBox = null!;

        // 文本压缩选项控件
        private CheckBox enableTextCompressionCheckBox = null!;
        private ComboBox textCompressionComboBox = null!;

        // 文档结构控件
        private CheckBox enableDocumentStructureCheckBox = null!;
        private CheckBox exportDocumentStructureCheckBox = null!;

        // 字体设置控件
        private CheckBox enableFontSettingsCheckBox = null!;
        private CheckBox embedFullFontsCheckBox = null!;
        private ComboBox fontEmbeddingModeComboBox = null!;

        // 超链接设置控件
        private CheckBox enableHyperlinkSettingsCheckBox = null!;
        private CheckBox createNoteHyperlinksCheckBox = null!;
        private CheckBox openHyperlinksInNewWindowCheckBox = null!;

        // PDF兼容性设置控件
        private CheckBox enableComplianceSettingsCheckBox = null!;
        private ComboBox complianceComboBox = null!;

        // 渲染设置控件
        private CheckBox enableRenderingSettingsCheckBox = null!;
        private ComboBox dmlEffectsComboBox = null!;
        private CheckBox additionalTextPositioningCheckBox = null!;

        // 其他设置控件
        private CheckBox enableOtherSettingsCheckBox = null!;
        private CheckBox displayDocTitleCheckBox = null!;
        private ComboBox pageModeComboBox = null!;
        private ComboBox pageLayoutComboBox = null!;

        public PdfSettings PdfSettings => pdfSettings;

        public PdfSettingsForm(PdfSettings pdfSettings)
        {
            this.pdfSettings = pdfSettings ?? throw new ArgumentNullException(nameof(pdfSettings));
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "PDF转换设置";
            this.Size = new Size(600, 700);
            this.MinimumSize = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // 创建主TabControl
            mainTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Padding = new Point(5, 5),  // 减小内边距
                Multiline = true,  // 允许多行显示标签页
                SizeMode = TabSizeMode.FillToRight,  // 使用填充模式，让标签页填充整个宽度
                ItemSize = new Size(0, 40)  // 增加高度到40像素，确保文字完全显示
            };

            // 创建"图像处理设置"标签页
            var imageProcessingTab = new TabPage("图像处理设置");
            imageProcessingTab.Padding = new Padding(10);
            imageProcessingTab.UseVisualStyleBackColor = true;
            imageProcessingTab.AutoScroll = true;

            // 使用TableLayoutPanel进行单列布局
            var mainLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 3,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 设置行样式
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            // 1. 图像压缩选项区域
            var compressionGroup = new GroupBox
            {
                Text = "图像压缩选项",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var compressionLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 7, // 1个启用复选框 + 5个压缩选项 + 1个质量设置
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 创建启用图像压缩复选框
            enableImageCompressionCheckBox = new CheckBox
            {
                Text = "启用图像压缩",
                Checked = PdfSettings.EnableImageCompression,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };
            enableImageCompressionCheckBox.CheckedChanged += (sender, e) =>
            {
                // 更新相关控件的启用状态
                jpegCompressionRadio.Enabled = enableImageCompressionCheckBox.Checked;
                autoCompressionRadio.Enabled = enableImageCompressionCheckBox.Checked;
                jpegQualityNumeric.Enabled = enableImageCompressionCheckBox.Checked && jpegCompressionRadio.Checked;
                jpegQualityLabel.Enabled = enableImageCompressionCheckBox.Checked && jpegCompressionRadio.Checked;
            };

            // 创建压缩选项容器
            var compressionOptions = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 6,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(20, 0, 0, 0),  // 增加左侧内边距，形成缩进效果
                Margin = new Padding(0)
            };

            // 创建JPEG压缩单选按钮
            jpegCompressionRadio = new RadioButton
            {
                Text = "JPEG压缩",
                Checked = PdfSettings.ImageCompression == AW.Saving.PdfImageCompression.Jpeg,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5),
                Enabled = PdfSettings.EnableImageCompression
            };
            jpegCompressionRadio.CheckedChanged += (sender, e) =>
            {
                // 仅在JPEG压缩选中时启用质量设置
                jpegQualityNumeric.Enabled = jpegCompressionRadio.Checked && enableImageCompressionCheckBox.Checked;
                jpegQualityLabel.Enabled = jpegCompressionRadio.Checked && enableImageCompressionCheckBox.Checked;
            };

            // 创建自动压缩单选按钮
            autoCompressionRadio = new RadioButton
            {
                Text = "自动选择最佳压缩",
                Checked = PdfSettings.ImageCompression == AW.Saving.PdfImageCompression.Auto,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5),
                Enabled = PdfSettings.EnableImageCompression
            };

            // 创建JPEG质量行
            var jpegQualityPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 5),  // 增加左侧边距形成二级缩进
                Padding = new Padding(0)
            };

            jpegQualityLabel = new Label
            {
                Text = "JPEG质量(0-100):",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                TextAlign = ContentAlignment.MiddleLeft,
                Enabled = jpegCompressionRadio.Checked && PdfSettings.EnableImageCompression
            };

            jpegQualityNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 100,
                Value = PdfSettings.JpegQuality,
                Width = 100,
                Enabled = jpegCompressionRadio.Checked && PdfSettings.EnableImageCompression,
                TextAlign = HorizontalAlignment.Center
            };

            jpegQualityPanel.Controls.Add(jpegQualityLabel, 0, 0);
            jpegQualityPanel.Controls.Add(jpegQualityNumeric, 1, 0);

            // 添加所有控件到压缩选项容器
            compressionOptions.Controls.Add(jpegCompressionRadio);
            compressionOptions.Controls.Add(jpegQualityPanel);
            compressionOptions.Controls.Add(autoCompressionRadio);

            // 添加启用复选框和选项容器到压缩组布局
            compressionLayout.Controls.Add(enableImageCompressionCheckBox);
            compressionLayout.Controls.Add(compressionOptions);

            compressionGroup.Controls.Add(compressionLayout);
            mainLayout.Controls.Add(compressionGroup, 0, 0);

            // 2. 分辨率设置区域
            var resolutionGroup = new GroupBox
            {
                Text = "分辨率设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var resolutionLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 4, // 1个启用复选框 + 3个分辨率设置
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 启用分辨率设置复选框
            enableResolutionSettingsCheckBox = new CheckBox
            {
                Text = "启用分辨率设置",
                Checked = PdfSettings.EnableResolutionSettings,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 10)
            };

            // 彩色图像分辨率
            var colorResolutionPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 5)
            };

            var colorResolutionLabel = new Label
            {
                Text = "彩色图像分辨率 (DPI):",
                AutoSize = true,
                Anchor = AnchorStyles.Left
            };

            colorImageResolutionNumeric = new NumericUpDown
            {
                Minimum = 72,
                Maximum = 2400,
                Value = PdfSettings.ColorImageResolution > 0 ? PdfSettings.ColorImageResolution : 220,
                Width = 70,
                Margin = new Padding(5, 0, 0, 0),
                TextAlign = HorizontalAlignment.Center
            };

            colorResolutionPanel.Controls.Add(colorResolutionLabel, 0, 0);
            colorResolutionPanel.Controls.Add(colorImageResolutionNumeric, 1, 0);

            // 灰度图像分辨率
            var grayResolutionPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 5)
            };

            var grayResolutionLabel = new Label
            {
                Text = "灰度图像分辨率 (DPI):",
                AutoSize = true,
                Anchor = AnchorStyles.Left
            };

            grayImageResolutionNumeric = new NumericUpDown
            {
                Minimum = 72,
                Maximum = 2400,
                Value = PdfSettings.GrayImageResolution > 0 ? PdfSettings.GrayImageResolution : 220,
                Width = 70,
                Margin = new Padding(5, 0, 0, 0),
                TextAlign = HorizontalAlignment.Center
            };

            grayResolutionPanel.Controls.Add(grayResolutionLabel, 0, 0);
            grayResolutionPanel.Controls.Add(grayImageResolutionNumeric, 1, 0);

            // 单色图像分辨率
            var monoResolutionPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 0, 0, 0)
            };

            var monoResolutionLabel = new Label
            {
                Text = "单色图像分辨率 (DPI):",
                AutoSize = true,
                Anchor = AnchorStyles.Left
            };

            monoImageResolutionNumeric = new NumericUpDown
            {
                Minimum = 72,
                Maximum = 2400,
                Value = PdfSettings.MonoImageResolution > 0 ? PdfSettings.MonoImageResolution : 300,
                Width = 70,
                Margin = new Padding(5, 0, 0, 0),
                TextAlign = HorizontalAlignment.Center
            };

            monoResolutionPanel.Controls.Add(monoResolutionLabel, 0, 0);
            monoResolutionPanel.Controls.Add(monoImageResolutionNumeric, 1, 0);

            // 添加事件处理
            enableResolutionSettingsCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableResolutionSettingsCheckBox.Checked;
                colorResolutionPanel.Enabled = enabled;
                grayResolutionPanel.Enabled = enabled;
                monoResolutionPanel.Enabled = enabled;
            };

            // 添加控件到分辨率布局
            resolutionLayout.Controls.Add(enableResolutionSettingsCheckBox, 0, 0);
            resolutionLayout.Controls.Add(colorResolutionPanel, 0, 1);
            resolutionLayout.Controls.Add(grayResolutionPanel, 0, 2);
            resolutionLayout.Controls.Add(monoResolutionPanel, 0, 3);

            resolutionGroup.Controls.Add(resolutionLayout);
            mainLayout.Controls.Add(resolutionGroup, 0, 1);

            // 3. 图像优化区域
            var optimizationGroup = new GroupBox
            {
                Text = "图像优化",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var optimizationLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 2, // 1个启用复选框 + 1个优化选项
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5),
                Margin = new Padding(0)
            };

            // 创建启用图像优化复选框
            enableImageOptimizationCheckBox = new CheckBox
            {
                Text = "启用图像优化",
                Checked = PdfSettings.EnableImageOptimization,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5, 15, 5, 5)  // 增加上边距与上一部分分开
            };
            enableImageOptimizationCheckBox.CheckedChanged += (sender, e) =>
            {
                // 更新相关控件的启用状态
                downsampleImagesCheckBox.Enabled = enableImageOptimizationCheckBox.Checked;
            };

            // 创建图像优化选项容器
            var optimizationOptions = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(20, 0, 0, 0),  // 增加左侧内边距，形成缩进效果
                Margin = new Padding(0)
            };

            // 创建降采样复选框
            downsampleImagesCheckBox = new CheckBox
            {
                Text = "降采样图像 (减小文件大小但可能降低质量)",
                Checked = PdfSettings.DownsampleImages,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5),
                Enabled = PdfSettings.EnableImageOptimization
            };

            // 添加控件到优化选项容器
            optimizationOptions.Controls.Add(downsampleImagesCheckBox);

            // 添加启用复选框和选项容器到优化组布局
            optimizationLayout.Controls.Add(enableImageOptimizationCheckBox);
            optimizationLayout.Controls.Add(optimizationOptions);

            optimizationGroup.Controls.Add(optimizationLayout);
            mainLayout.Controls.Add(optimizationGroup, 0, 2);

            // 将主布局添加到标签页
            imageProcessingTab.Controls.Add(mainLayout);

            // 添加标签页到TabControl
            mainTabControl.TabPages.Add(imageProcessingTab);

            // 添加文本压缩设置标签页
            var textCompressionTab = CreateTextCompressionTab();
            mainTabControl.TabPages.Add(textCompressionTab);

            // 添加文档结构设置标签页
            var documentStructureTab = CreateDocumentStructureTab();
            mainTabControl.TabPages.Add(documentStructureTab);

            // 添加字体设置标签页
            var fontSettingsTab = CreateFontSettingsTab();
            mainTabControl.TabPages.Add(fontSettingsTab);

            // 添加超链接设置标签页
            var hyperlinkSettingsTab = CreateHyperlinkSettingsTab();
            mainTabControl.TabPages.Add(hyperlinkSettingsTab);

            // 添加PDF兼容性设置标签页
            var complianceSettingsTab = CreateComplianceSettingsTab();
            mainTabControl.TabPages.Add(complianceSettingsTab);

            // 添加渲染设置标签页
            var renderingSettingsTab = CreateRenderingSettingsTab();
            mainTabControl.TabPages.Add(renderingSettingsTab);

            // 添加其他设置标签页
            var otherSettingsTab = CreateOtherSettingsTab();
            mainTabControl.TabPages.Add(otherSettingsTab);

            // 添加安全设置标签页
            var securitySettingsTab = CreateSecuritySettingsTab();
            mainTabControl.TabPages.Add(securitySettingsTab);

            // 添加书签和大纲设置标签页
            var bookmarkSettingsTab = CreateBookmarkSettingsTab();
            mainTabControl.TabPages.Add(bookmarkSettingsTab);

            // 添加高级图像设置标签页
            var advancedImageTab = CreateAdvancedImageSettingsTab();
            mainTabControl.TabPages.Add(advancedImageTab);

            // 添加表单字段设置标签页
            var formFieldTab = CreateFormFieldSettingsTab();
            mainTabControl.TabPages.Add(formFieldTab);

            // 添加页面显示设置标签页
            var pageDisplayTab = CreatePageDisplaySettingsTab();
            mainTabControl.TabPages.Add(pageDisplayTab);

            // 添加元数据设置标签页
            var metadataTab = CreateMetadataSettingsTab();
            mainTabControl.TabPages.Add(metadataTab);

            // 按钮面板
            var buttonPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.RightToLeft,
                AutoSize = true,
                Dock = DockStyle.Bottom,
                Margin = new Padding(0, 10, 0, 0),
                Padding = new Padding(5)
            };

            var cancelButton = new Button
            {
                Text = "取消",
                AutoSize = true,
                Padding = new Padding(10, 5, 10, 5)
            };
            cancelButton.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            var okButton = new Button
            {
                Text = "确定",
                AutoSize = true,
                Padding = new Padding(10, 5, 10, 5),
                Margin = new Padding(10, 0, 0, 0)
            };
            okButton.Click += (s, e) =>
            {
                SaveSettings();
                this.DialogResult = DialogResult.OK;
            };

            buttonPanel.Controls.Add(cancelButton);
            buttonPanel.Controls.Add(okButton);

            // 创建主布局面板
            var formLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 2,
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            formLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            formLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            formLayout.Controls.Add(mainTabControl, 0, 0);
            formLayout.Controls.Add(buttonPanel, 0, 1);

            this.Controls.Add(formLayout);
        }

        private TabPage CreateTextCompressionTab()
        {
            var tab = new TabPage("文本压缩设置");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "文本压缩选项",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var compressionLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 3,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 添加启用文本压缩的复选框
            enableTextCompressionCheckBox = new CheckBox
            {
                Text = "启用文本压缩",
                Checked = PdfSettings.EnableTextCompression,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            textCompressionComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableTextCompression
            };
            textCompressionComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            textCompressionComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    using (var brush = new SolidBrush(e.ForeColor))
                    {
                        var text = textCompressionComboBox.Items[e.Index].ToString();
                        var textSize = e.Graphics.MeasureString(text, e.Font);
                        var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                        var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                        e.Graphics.DrawString(text, e.Font, brush, x, y);
                    }
                    e.DrawFocusRectangle();
                }
            };
            textCompressionComboBox.Items.AddRange(new object[]
            {
                "Flate压缩",
                "无压缩"
            });
            textCompressionComboBox.SelectedIndex = PdfSettings.TextCompression == AW.Saving.PdfTextCompression.Flate ? 0 : 1;

            // 添加启用状态变更事件
            enableTextCompressionCheckBox.CheckedChanged += (sender, e) =>
            {
                textCompressionComboBox.Enabled = enableTextCompressionCheckBox.Checked;
            };

            var descriptionLabel = new Label
            {
                Text = "Flate压缩：使用ZIP算法压缩文本，可以显著减小文件大小。\n无压缩：保持原始文本格式，文件较大但处理速度更快。",
                AutoSize = true,
                Margin = new Padding(5)
            };

            compressionLayout.Controls.Add(enableTextCompressionCheckBox);
            compressionLayout.Controls.Add(textCompressionComboBox);
            compressionLayout.Controls.Add(descriptionLabel);
            groupBox.Controls.Add(compressionLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private TabPage CreateDocumentStructureTab()
        {
            var tab = new TabPage("文档结构设置");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "文档结构选项",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var structureLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 3,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 添加启用文档结构功能的复选框
            enableDocumentStructureCheckBox = new CheckBox
            {
                Text = "启用文档结构功能",
                Checked = PdfSettings.EnableDocumentStructure,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            exportDocumentStructureCheckBox = new CheckBox
            {
                Text = "导出文档结构",
                Checked = PdfSettings.ExportDocumentStructure,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableDocumentStructure
            };

            // 添加启用状态变更事件
            enableDocumentStructureCheckBox.CheckedChanged += (sender, e) =>
            {
                exportDocumentStructureCheckBox.Enabled = enableDocumentStructureCheckBox.Checked;
            };

            var descriptionLabel = new Label
            {
                Text = "导出文档结构可以保留文档的层次结构，便于PDF阅读器显示文档大纲。\n启用此选项会增加文件大小，但提供更好的文档导航体验。",
                AutoSize = true,
                Margin = new Padding(5)
            };

            structureLayout.Controls.Add(enableDocumentStructureCheckBox);
            structureLayout.Controls.Add(exportDocumentStructureCheckBox);
            structureLayout.Controls.Add(descriptionLabel);
            groupBox.Controls.Add(structureLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private TabPage CreateFontSettingsTab()
        {
            var tab = new TabPage("字体设置");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "字体嵌入选项",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var fontLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 4,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 添加启用字体设置功能的复选框
            enableFontSettingsCheckBox = new CheckBox
            {
                Text = "启用字体设置功能",
                Checked = PdfSettings.EnableFontSettings,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            embedFullFontsCheckBox = new CheckBox
            {
                Text = "嵌入完整字体",
                Checked = PdfSettings.EmbedFullFonts,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableFontSettings
            };

            fontEmbeddingModeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableFontSettings
            };
            fontEmbeddingModeComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            fontEmbeddingModeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    using (var brush = new SolidBrush(e.ForeColor))
                    {
                        var text = fontEmbeddingModeComboBox.Items[e.Index].ToString();
                        var textSize = e.Graphics.MeasureString(text, e.Font);
                        var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                        var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                        e.Graphics.DrawString(text, e.Font, brush, x, y);
                    }
                    e.DrawFocusRectangle();
                }
            };
            fontEmbeddingModeComboBox.Items.AddRange(new object[]
            {
                "嵌入所有字体",
                "嵌入子集字体",
                "不嵌入字体"
            });
            fontEmbeddingModeComboBox.SelectedIndex = (int)PdfSettings.FontEmbeddingMode;

            // 添加启用状态变更事件
            enableFontSettingsCheckBox.CheckedChanged += (sender, e) =>
            {
                embedFullFontsCheckBox.Enabled = enableFontSettingsCheckBox.Checked;
                fontEmbeddingModeComboBox.Enabled = enableFontSettingsCheckBox.Checked;
            };

            var descriptionLabel = new Label
            {
                Text = "嵌入字体可以确保PDF文档在不同设备上显示一致。\n完整嵌入：包含所有字符，文件较大但显示最准确。\n子集嵌入：只包含文档中使用的字符，文件较小。\n不嵌入：依赖系统字体，文件最小但可能显示不一致。",
                AutoSize = true,
                Margin = new Padding(5)
            };

            fontLayout.Controls.Add(enableFontSettingsCheckBox);
            fontLayout.Controls.Add(embedFullFontsCheckBox);
            fontLayout.Controls.Add(fontEmbeddingModeComboBox);
            fontLayout.Controls.Add(descriptionLabel);
            groupBox.Controls.Add(fontLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private TabPage CreateHyperlinkSettingsTab()
        {
            var tab = new TabPage("超链接设置");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "超链接选项",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var hyperlinkLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 4,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 添加启用超链接设置功能的复选框
            enableHyperlinkSettingsCheckBox = new CheckBox
            {
                Text = "启用超链接设置功能",
                Checked = PdfSettings.EnableHyperlinkSettings,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            createNoteHyperlinksCheckBox = new CheckBox
            {
                Text = "创建注释超链接",
                Checked = PdfSettings.CreateNoteHyperlinks,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableHyperlinkSettings
            };

            openHyperlinksInNewWindowCheckBox = new CheckBox
            {
                Text = "在新窗口中打开超链接",
                Checked = PdfSettings.OpenHyperlinksInNewWindow,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableHyperlinkSettings
            };

            // 添加启用状态变更事件
            enableHyperlinkSettingsCheckBox.CheckedChanged += (sender, e) =>
            {
                createNoteHyperlinksCheckBox.Enabled = enableHyperlinkSettingsCheckBox.Checked;
                openHyperlinksInNewWindowCheckBox.Enabled = enableHyperlinkSettingsCheckBox.Checked;
            };

            var descriptionLabel = new Label
            {
                Text = "注释超链接：将文档中的注释转换为可点击的超链接。\n新窗口打开：点击超链接时在新窗口中打开目标。",
                AutoSize = true,
                Margin = new Padding(5)
            };

            hyperlinkLayout.Controls.Add(enableHyperlinkSettingsCheckBox);
            hyperlinkLayout.Controls.Add(createNoteHyperlinksCheckBox);
            hyperlinkLayout.Controls.Add(openHyperlinksInNewWindowCheckBox);
            hyperlinkLayout.Controls.Add(descriptionLabel);
            groupBox.Controls.Add(hyperlinkLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private TabPage CreateComplianceSettingsTab()
        {
            var tab = new TabPage("PDF兼容性设置");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "PDF兼容性选项",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var complianceLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 3,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 添加启用兼容性设置功能的复选框
            enableComplianceSettingsCheckBox = new CheckBox
            {
                Text = "启用PDF兼容性设置功能",
                Checked = true,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            complianceComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = true
            };
            complianceComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            complianceComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    using (var brush = new SolidBrush(e.ForeColor))
                    {
                        var text = complianceComboBox.Items[e.Index].ToString();
                        var textSize = e.Graphics.MeasureString(text, e.Font);
                        var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                        var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                        e.Graphics.DrawString(text, e.Font, brush, x, y);
                    }
                    e.DrawFocusRectangle();
                }
            };
            complianceComboBox.Items.AddRange(new object[]
            {
                "PDF 1.7",
                "PDF 标准",  // 使用None替代PDF 1.5
                "PDF/A-1a",
                "PDF/A-1b"
            });
            complianceComboBox.SelectedIndex = 0;

            // 添加启用状态变更事件
            enableComplianceSettingsCheckBox.CheckedChanged += (sender, e) =>
            {
                complianceComboBox.Enabled = enableComplianceSettingsCheckBox.Checked;
            };

            var descriptionLabel = new Label
            {
                Text = "选择PDF版本兼容性级别。\n较新的版本支持更多功能，但可能与旧版PDF阅读器不兼容。\n建议使用PDF 1.7以获得最佳兼容性。",
                AutoSize = true,
                Margin = new Padding(5)
            };

            complianceLayout.Controls.Add(enableComplianceSettingsCheckBox);
            complianceLayout.Controls.Add(complianceComboBox);
            complianceLayout.Controls.Add(descriptionLabel);
            groupBox.Controls.Add(complianceLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private TabPage CreateRenderingSettingsTab()
        {
            var tab = new TabPage("渲染设置");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "渲染选项",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var renderingLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 4,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 添加启用渲染设置功能的复选框
            enableRenderingSettingsCheckBox = new CheckBox
            {
                Text = "启用渲染设置功能",
                Checked = PdfSettings.EnableRenderingSettings,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            dmlEffectsComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableRenderingSettings
            };
            dmlEffectsComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            dmlEffectsComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    using (var brush = new SolidBrush(e.ForeColor))
                    {
                        var text = dmlEffectsComboBox.Items[e.Index].ToString();
                        var textSize = e.Graphics.MeasureString(text, e.Font);
                        var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                        var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                        e.Graphics.DrawString(text, e.Font, brush, x, y);
                    }
                    e.DrawFocusRectangle();
                }
            };
            dmlEffectsComboBox.Items.AddRange(new object[]
            {
                "精细渲染",
                "简化渲染"
            });
            int selectedIndex = (int)PdfSettings.DmlEffectsRenderingMode;
            if (selectedIndex >= 0 && selectedIndex < dmlEffectsComboBox.Items.Count)
            {
                dmlEffectsComboBox.SelectedIndex = selectedIndex;
            }
            else
            {
                dmlEffectsComboBox.SelectedIndex = 0; // 默认选择第一项
            }

            additionalTextPositioningCheckBox = new CheckBox
            {
                Text = "启用额外文本定位",
                Checked = PdfSettings.AdditionalTextPositioning,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableRenderingSettings
            };

            // 添加启用状态变更事件
            enableRenderingSettingsCheckBox.CheckedChanged += (sender, e) =>
            {
                dmlEffectsComboBox.Enabled = enableRenderingSettingsCheckBox.Checked;
                additionalTextPositioningCheckBox.Enabled = enableRenderingSettingsCheckBox.Checked;
            };

            var descriptionLabel = new Label
            {
                Text = "精细渲染：提供最佳的视觉效果，但文件较大。\n简化渲染：文件较小，但某些视觉效果可能简化。\n额外文本定位：提供更精确的文本位置控制。",
                AutoSize = true,
                Margin = new Padding(5)
            };

            renderingLayout.Controls.Add(enableRenderingSettingsCheckBox);
            renderingLayout.Controls.Add(dmlEffectsComboBox);
            renderingLayout.Controls.Add(additionalTextPositioningCheckBox);
            renderingLayout.Controls.Add(descriptionLabel);
            groupBox.Controls.Add(renderingLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private TabPage CreateOtherSettingsTab()
        {
            var tab = new TabPage("其他设置");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "其他选项",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var otherLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 5,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 添加启用其他设置功能的复选框
            enableOtherSettingsCheckBox = new CheckBox
            {
                Text = "启用其他设置功能",
                Checked = PdfSettings.EnableOtherSettings,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            displayDocTitleCheckBox = new CheckBox
            {
                Text = "显示文档标题",
                Checked = PdfSettings.DisplayDocTitle,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableOtherSettings
            };

            var pageModeLabel = new Label
            {
                Text = "初始显示模式：",
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5)
            };

            pageModeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableOtherSettings
            };
            pageModeComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            pageModeComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    using (var brush = new SolidBrush(e.ForeColor))
                    {
                        var text = pageModeComboBox.Items[e.Index].ToString();
                        var textSize = e.Graphics.MeasureString(text, e.Font);
                        var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                        var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                        e.Graphics.DrawString(text, e.Font, brush, x, y);
                    }
                    e.DrawFocusRectangle();
                }
            };
            pageModeComboBox.Items.AddRange(new object[]
            {
                "使用大纲",
                "使用缩略图",
                "全屏",
                "使用附件",
                "使用可选内容组",
                "使用书签"
            });
            pageModeComboBox.SelectedIndex = (int)PdfSettings.PageMode;

            var pageLayoutLabel = new Label
            {
                Text = "页面布局方式：",
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5)
            };

            pageLayoutComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableOtherSettings
            };
            pageLayoutComboBox.DrawMode = DrawMode.OwnerDrawFixed;
            pageLayoutComboBox.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    using (var brush = new SolidBrush(e.ForeColor))
                    {
                        var text = pageLayoutComboBox.Items[e.Index].ToString();
                        var textSize = e.Graphics.MeasureString(text, e.Font);
                        var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                        var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                        e.Graphics.DrawString(text, e.Font, brush, x, y);
                    }
                    e.DrawFocusRectangle();
                }
            };
            pageLayoutComboBox.Items.AddRange(new object[]
            {
                "单页",
                "单列",
                "双页",
                "双列",
                "双页连续",
                "双列连续"
            });
            pageLayoutComboBox.SelectedIndex = (int)PdfSettings.PageLayout;

            // 添加启用状态变更事件
            enableOtherSettingsCheckBox.CheckedChanged += (sender, e) =>
            {
                displayDocTitleCheckBox.Enabled = enableOtherSettingsCheckBox.Checked;
                pageModeComboBox.Enabled = enableOtherSettingsCheckBox.Checked;
                pageLayoutComboBox.Enabled = enableOtherSettingsCheckBox.Checked;
                pageModeLabel.Enabled = enableOtherSettingsCheckBox.Checked;
                pageLayoutLabel.Enabled = enableOtherSettingsCheckBox.Checked;
            };

            var descriptionLabel = new Label
            {
                Text = "文档标题：在PDF阅读器标题栏显示文档标题。\n页面模式：设置PDF打开时的初始显示模式。\n页面布局：设置PDF的页面布局方式。",
                AutoSize = true,
                Margin = new Padding(5)
            };

            otherLayout.Controls.Add(enableOtherSettingsCheckBox);
            otherLayout.Controls.Add(displayDocTitleCheckBox);
            otherLayout.Controls.Add(pageModeLabel);
            otherLayout.Controls.Add(pageModeComboBox);
            otherLayout.Controls.Add(pageLayoutLabel);
            otherLayout.Controls.Add(pageLayoutComboBox);
            otherLayout.Controls.Add(descriptionLabel);
            groupBox.Controls.Add(otherLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private TabPage CreateSecuritySettingsTab()
        {
            var tab = new TabPage("安全设置");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 2,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 1. 密码保护设置
            var passwordGroup = new GroupBox
            {
                Text = "密码保护设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 10)
            };

            var passwordLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 4,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 启用安全设置复选框
            var enableSecurityCheckBox = new CheckBox
            {
                Text = "启用安全设置",
                Checked = PdfSettings.EnableSecuritySettings,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            // 用户密码设置
            var userPasswordPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5)
            };

            var userPasswordLabel = new Label
            {
                Text = "用户密码:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var userPasswordTextBox = new TextBox
            {
                Text = PdfSettings.UserPassword,
                UseSystemPasswordChar = true,
                Width = 200,
                Enabled = PdfSettings.EnableSecuritySettings
            };

            userPasswordPanel.Controls.Add(userPasswordLabel, 0, 0);
            userPasswordPanel.Controls.Add(userPasswordTextBox, 1, 0);

            // 所有者密码设置
            var ownerPasswordPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5)
            };

            var ownerPasswordLabel = new Label
            {
                Text = "所有者密码:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var ownerPasswordTextBox = new TextBox
            {
                Text = PdfSettings.OwnerPassword,
                UseSystemPasswordChar = true,
                Width = 200,
                Enabled = PdfSettings.EnableSecuritySettings
            };

            ownerPasswordPanel.Controls.Add(ownerPasswordLabel, 0, 0);
            ownerPasswordPanel.Controls.Add(ownerPasswordTextBox, 1, 0);

            // 事件处理
            enableSecurityCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableSecurityCheckBox.Checked;
                userPasswordTextBox.Enabled = enabled;
                ownerPasswordTextBox.Enabled = enabled;
            };

            passwordLayout.Controls.Add(enableSecurityCheckBox, 0, 0);
            passwordLayout.Controls.Add(userPasswordPanel, 0, 1);
            passwordLayout.Controls.Add(ownerPasswordPanel, 0, 2);

            passwordGroup.Controls.Add(passwordLayout);
            layout.Controls.Add(passwordGroup, 0, 0);

            tab.Controls.Add(layout);
            return tab;
        }

        private TabPage CreateBookmarkSettingsTab()
        {
            var tab = new TabPage("书签和大纲");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "书签和大纲设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var bookmarkLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 7,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 启用书签设置复选框
            var enableBookmarkCheckBox = new CheckBox
            {
                Text = "启用书签和大纲设置",
                Checked = PdfSettings.EnableBookmarkSettings,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            // 标题大纲级别
            var headingLevelsPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5)
            };

            var headingLevelsLabel = new Label
            {
                Text = "标题大纲级别 (1-9):",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var headingLevelsNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 9,
                Value = PdfSettings.HeadingsOutlineLevels,
                Width = 80,
                TextAlign = HorizontalAlignment.Center,
                Enabled = PdfSettings.EnableBookmarkSettings
            };

            headingLevelsPanel.Controls.Add(headingLevelsLabel, 0, 0);
            headingLevelsPanel.Controls.Add(headingLevelsNumeric, 1, 0);

            // 展开的大纲级别
            var expandedLevelsPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5)
            };

            var expandedLevelsLabel = new Label
            {
                Text = "展开的大纲级别:",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var expandedLevelsNumeric = new NumericUpDown
            {
                Minimum = 0,
                Maximum = 9,
                Value = PdfSettings.ExpandedOutlineLevels,
                Width = 80,
                TextAlign = HorizontalAlignment.Center,
                Enabled = PdfSettings.EnableBookmarkSettings
            };

            expandedLevelsPanel.Controls.Add(expandedLevelsLabel, 0, 0);
            expandedLevelsPanel.Controls.Add(expandedLevelsNumeric, 1, 0);

            // 创建缺失的大纲级别
            var createMissingCheckBox = new CheckBox
            {
                Text = "创建缺失的大纲级别",
                Checked = PdfSettings.CreateMissingOutlineLevels,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableBookmarkSettings
            };

            // 为表格中的标题创建大纲
            var tableHeadingsCheckBox = new CheckBox
            {
                Text = "为表格中的标题创建大纲",
                Checked = PdfSettings.CreateOutlinesForHeadingsInTables,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableBookmarkSettings
            };

            // 事件处理
            enableBookmarkCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableBookmarkCheckBox.Checked;
                headingLevelsNumeric.Enabled = enabled;
                expandedLevelsNumeric.Enabled = enabled;
                createMissingCheckBox.Enabled = enabled;
                tableHeadingsCheckBox.Enabled = enabled;
            };

            bookmarkLayout.Controls.Add(enableBookmarkCheckBox, 0, 0);
            bookmarkLayout.Controls.Add(headingLevelsPanel, 0, 1);
            bookmarkLayout.Controls.Add(expandedLevelsPanel, 0, 2);
            bookmarkLayout.Controls.Add(createMissingCheckBox, 0, 3);
            bookmarkLayout.Controls.Add(tableHeadingsCheckBox, 0, 4);

            groupBox.Controls.Add(bookmarkLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private TabPage CreateAdvancedImageSettingsTab()
        {
            var tab = new TabPage("高级图像");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "高级图像设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var imageLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 4,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 启用高级图像设置复选框
            var enableAdvancedImageCheckBox = new CheckBox
            {
                Text = "启用高级图像设置",
                Checked = PdfSettings.EnableAdvancedImageSettings,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            // 图像插值
            var interpolateCheckBox = new CheckBox
            {
                Text = "启用图像插值（提高图像质量）",
                Checked = PdfSettings.InterpolateImages,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableAdvancedImageSettings
            };

            // 图像预混合
            var preblendCheckBox = new CheckBox
            {
                Text = "启用图像预混合（优化透明度处理）",
                Checked = PdfSettings.PreblendImages,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableAdvancedImageSettings
            };

            // 事件处理
            enableAdvancedImageCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableAdvancedImageCheckBox.Checked;
                interpolateCheckBox.Enabled = enabled;
                preblendCheckBox.Enabled = enabled;
            };

            imageLayout.Controls.Add(enableAdvancedImageCheckBox, 0, 0);
            imageLayout.Controls.Add(interpolateCheckBox, 0, 1);
            imageLayout.Controls.Add(preblendCheckBox, 0, 2);

            groupBox.Controls.Add(imageLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private TabPage CreateFormFieldSettingsTab()
        {
            var tab = new TabPage("表单字段");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "表单字段设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var formLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 3,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 启用表单字段设置复选框
            var enableFormFieldCheckBox = new CheckBox
            {
                Text = "启用表单字段设置",
                Checked = PdfSettings.EnableFormFieldSettings,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            // 保留表单字段
            var preserveFormFieldsCheckBox = new CheckBox
            {
                Text = "保留表单字段（保持交互性）",
                Checked = PdfSettings.PreserveFormFields,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5),
                Enabled = PdfSettings.EnableFormFieldSettings
            };

            // 事件处理
            enableFormFieldCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enableFormFieldCheckBox.Checked;
                preserveFormFieldsCheckBox.Enabled = enabled;
            };

            formLayout.Controls.Add(enableFormFieldCheckBox, 0, 0);
            formLayout.Controls.Add(preserveFormFieldsCheckBox, 0, 1);

            groupBox.Controls.Add(formLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private TabPage CreatePageDisplaySettingsTab()
        {
            var tab = new TabPage("页面显示");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "页面显示设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var displayLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 3,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 启用页面显示设置复选框
            var enablePageDisplayCheckBox = new CheckBox
            {
                Text = "启用页面显示设置",
                Checked = PdfSettings.EnablePageDisplaySettings,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            // 缩放因子设置
            var zoomPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(20, 5, 5, 5)
            };

            var zoomLabel = new Label
            {
                Text = "缩放因子 (%):",
                AutoSize = true,
                Anchor = AnchorStyles.Left,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var zoomNumeric = new NumericUpDown
            {
                Minimum = 10,
                Maximum = 1000,
                Value = PdfSettings.ZoomFactor,
                Width = 80,
                TextAlign = HorizontalAlignment.Center,
                Enabled = PdfSettings.EnablePageDisplaySettings
            };

            zoomPanel.Controls.Add(zoomLabel, 0, 0);
            zoomPanel.Controls.Add(zoomNumeric, 1, 0);

            // 事件处理
            enablePageDisplayCheckBox.CheckedChanged += (sender, e) =>
            {
                bool enabled = enablePageDisplayCheckBox.Checked;
                zoomNumeric.Enabled = enabled;
            };

            displayLayout.Controls.Add(enablePageDisplayCheckBox, 0, 0);
            displayLayout.Controls.Add(zoomPanel, 0, 1);

            groupBox.Controls.Add(displayLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private TabPage CreateMetadataSettingsTab()
        {
            var tab = new TabPage("元数据");
            tab.Padding = new Padding(10);
            tab.UseVisualStyleBackColor = true;
            tab.AutoScroll = true;

            var layout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 1,
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            var groupBox = new GroupBox
            {
                Text = "元数据设置",
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var metadataLayout = new TableLayoutPanel
            {
                ColumnCount = 1,
                RowCount = 2,
                Dock = DockStyle.Fill,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 启用元数据设置复选框
            var enableMetadataCheckBox = new CheckBox
            {
                Text = "启用元数据设置",
                Checked = PdfSettings.EnableMetadataSettings,
                AutoSize = true,
                Font = new System.Drawing.Font(this.Font.FontFamily, 9F, System.Drawing.FontStyle.Bold),
                Margin = new Padding(5)
            };

            metadataLayout.Controls.Add(enableMetadataCheckBox, 0, 0);

            groupBox.Controls.Add(metadataLayout);
            layout.Controls.Add(groupBox);
            tab.Controls.Add(layout);

            return tab;
        }

        private void SaveSettings()
        {
            // 图像处理相关设置部分
            // -----------------------------

            // 检查图像处理标签页下是否有任何子功能被启用
            bool anyImageProcessingEnabled = enableImageCompressionCheckBox.Checked ||
                                            enableResolutionSettingsCheckBox.Checked ||
                                            enableImageOptimizationCheckBox.Checked;

            // 如果图像处理标签页下没有任何子功能被启用，则跳过该标签页的所有设置
            if (anyImageProcessingEnabled)
            {
                // 尝试安全地保存图像压缩设置
                try
                {
                    // 保存图像压缩选项 - 始终保存当前选择，无论是否启用
                    if (jpegCompressionRadio.Checked)
                        PdfSettings.ImageCompression = AW.Saving.PdfImageCompression.Jpeg;
                    else // 默认使用自动压缩
                        PdfSettings.ImageCompression = AW.Saving.PdfImageCompression.Auto;

                    // 保存JPEG质量，无论是否启用图像压缩
                    PdfSettings.JpegQuality = (int)jpegQualityNumeric.Value;

                    // 保存是否启用图像压缩的总开关设置
                    PdfSettings.EnableImageCompression = enableImageCompressionCheckBox.Checked;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"保存图像压缩设置时出错: {ex.Message}");
                    MessageBox.Show($"保存图像压缩设置时出错: {ex.Message}", "保存设置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // 尝试安全地保存图像优化设置
                try
                {
                    // 保存图像优化设置，无论是否启用
                    PdfSettings.DownsampleImages = downsampleImagesCheckBox.Checked;

                    // 保存是否启用图像优化的总开关设置
                    PdfSettings.EnableImageOptimization = enableImageOptimizationCheckBox.Checked;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"保存图像优化设置时出错: {ex.Message}");
                    MessageBox.Show($"保存图像优化设置时出错: {ex.Message}", "保存设置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // 尝试安全地保存分辨率设置
                try
                {
                    // 保存分辨率设置 - 存储当前值无论是否启用
                    PdfSettings.EnableResolutionSettings = enableResolutionSettingsCheckBox.Checked;
                    PdfSettings.ColorImageResolution = (int)colorImageResolutionNumeric.Value;
                    PdfSettings.GrayImageResolution = (int)grayImageResolutionNumeric.Value;
                    PdfSettings.MonoImageResolution = (int)monoImageResolutionNumeric.Value;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"保存分辨率设置时出错: {ex.Message}");
                    MessageBox.Show($"保存分辨率设置时出错: {ex.Message}", "保存设置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            else
            {
                // 如果图像处理标签页下没有任何子功能被启用，则禁用所有相关设置
                PdfSettings.EnableImageCompression = false;
                PdfSettings.EnableImageOptimization = false;
                PdfSettings.EnableResolutionSettings = false;
                System.Diagnostics.Debug.WriteLine("图像处理标签页下没有任何子功能被启用，跳过该标签页的所有设置");
            }

            // 文档格式相关设置部分
            // -----------------------------

            // 检查文本压缩标签页下是否有任何子功能被启用
            bool textCompressionEnabled = enableTextCompressionCheckBox.Checked;

            // 如果文本压缩标签页下有子功能被启用，则保存相关设置
            if (textCompressionEnabled)
            {
                // 尝试安全地保存文本压缩设置
                try
                {
                    // 保存文本压缩设置
                    PdfSettings.EnableTextCompression = enableTextCompressionCheckBox.Checked;
                    PdfSettings.TextCompression = textCompressionComboBox.SelectedIndex == 0 ?
                        AW.Saving.PdfTextCompression.Flate : AW.Saving.PdfTextCompression.None;
                }
                catch (Exception ex)
                {
                    // 记录错误但继续保存其他设置
                    System.Diagnostics.Debug.WriteLine($"保存文本压缩设置时出错: {ex.Message}");
                    MessageBox.Show($"保存文本压缩设置时出错: {ex.Message}", "保存设置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            else
            {
                // 如果文本压缩标签页下没有任何子功能被启用，则禁用所有相关设置
                PdfSettings.EnableTextCompression = false;
                System.Diagnostics.Debug.WriteLine("文本压缩标签页下没有任何子功能被启用，跳过该标签页的所有设置");
            }

            // 检查文档结构标签页下是否有任何子功能被启用
            bool documentStructureEnabled = enableDocumentStructureCheckBox.Checked;

            // 如果文档结构标签页下有子功能被启用，则保存相关设置
            if (documentStructureEnabled)
            {
                // 尝试安全地保存文档结构设置
                try
                {
                    // 保存文档结构设置
                    PdfSettings.EnableDocumentStructure = enableDocumentStructureCheckBox.Checked;
                    PdfSettings.ExportDocumentStructure = exportDocumentStructureCheckBox.Checked;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"保存文档结构设置时出错: {ex.Message}");
                    MessageBox.Show($"保存文档结构设置时出错: {ex.Message}", "保存设置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            else
            {
                // 如果文档结构标签页下没有任何子功能被启用，则禁用所有相关设置
                PdfSettings.EnableDocumentStructure = false;
                System.Diagnostics.Debug.WriteLine("文档结构标签页下没有任何子功能被启用，跳过该标签页的所有设置");
            }

            // 检查字体设置标签页下是否有任何子功能被启用
            bool fontSettingsEnabled = enableFontSettingsCheckBox.Checked;

            // 如果字体设置标签页下有子功能被启用，则保存相关设置
            if (fontSettingsEnabled)
            {
                // 尝试安全地保存字体设置
                try
                {
                    // 保存字体设置
                    PdfSettings.EnableFontSettings = enableFontSettingsCheckBox.Checked;
                    PdfSettings.EmbedFullFonts = embedFullFontsCheckBox.Checked;
                    PdfSettings.FontEmbeddingMode = (AW.Saving.PdfFontEmbeddingMode)fontEmbeddingModeComboBox.SelectedIndex;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"保存字体设置时出错: {ex.Message}");
                    MessageBox.Show($"保存字体设置时出错: {ex.Message}", "保存设置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            else
            {
                // 如果字体设置标签页下没有任何子功能被启用，则禁用所有相关设置
                PdfSettings.EnableFontSettings = false;
                System.Diagnostics.Debug.WriteLine("字体设置标签页下没有任何子功能被启用，跳过该标签页的所有设置");
            }

            // 检查超链接设置标签页下是否有任何子功能被启用
            bool hyperlinkSettingsEnabled = enableHyperlinkSettingsCheckBox.Checked;

            // 如果超链接设置标签页下有子功能被启用，则保存相关设置
            if (hyperlinkSettingsEnabled)
            {
                // 尝试安全地保存超链接设置
                try
                {
                    // 保存超链接设置
                    PdfSettings.EnableHyperlinkSettings = enableHyperlinkSettingsCheckBox.Checked;
                    PdfSettings.CreateNoteHyperlinks = createNoteHyperlinksCheckBox.Checked;
                    PdfSettings.OpenHyperlinksInNewWindow = openHyperlinksInNewWindowCheckBox.Checked;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"保存超链接设置时出错: {ex.Message}");
                    MessageBox.Show($"保存超链接设置时出错: {ex.Message}", "保存设置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            else
            {
                // 如果超链接设置标签页下没有任何子功能被启用，则禁用所有相关设置
                PdfSettings.EnableHyperlinkSettings = false;
                System.Diagnostics.Debug.WriteLine("超链接设置标签页下没有任何子功能被启用，跳过该标签页的所有设置");
            }

            // 检查PDF兼容性设置标签页下是否有任何子功能被启用
            bool complianceSettingsEnabled = enableComplianceSettingsCheckBox.Checked;

            // 如果PDF兼容性设置标签页下有子功能被启用，则保存相关设置
            if (complianceSettingsEnabled)
            {
                // 尝试安全地保存PDF兼容性设置
                try
                {
                    // 保存PDF兼容性设置
                    PdfSettings.EnableComplianceSettings = enableComplianceSettingsCheckBox.Checked;

                    // 正确映射PDF兼容性设置
                    // 根据Aspose.Words的实际可用枚举值进行映射
                    // 确保使用正确的枚举值
                    var complianceMap = new Dictionary<int, AW.Saving.PdfCompliance>
                    {
                        { 0, AW.Saving.PdfCompliance.Pdf17 },    // PDF 1.7
                        { 1, AW.Saving.PdfCompliance.Pdf17 },    // PDF 标准 (使用Pdf17替代，因为没有对应的枚举值)
                        { 2, AW.Saving.PdfCompliance.PdfA1a },   // PDF/A-1a
                        { 3, AW.Saving.PdfCompliance.PdfA1b }    // PDF/A-1b
                    };

                    if (complianceMap.TryGetValue(complianceComboBox.SelectedIndex, out var compliance))
                    {
                        PdfSettings.Compliance = compliance;
                    }
                    else
                    {
                        // 默认使用PDF 1.7
                        PdfSettings.Compliance = AW.Saving.PdfCompliance.Pdf17;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"保存PDF兼容性设置时出错: {ex.Message}");
                    MessageBox.Show($"保存PDF兼容性设置时出错: {ex.Message}", "保存设置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            else
            {
                // 如果PDF兼容性设置标签页下没有任何子功能被启用，则禁用所有相关设置
                PdfSettings.EnableComplianceSettings = false;
                System.Diagnostics.Debug.WriteLine("PDF兼容性设置标签页下没有任何子功能被启用，跳过该标签页的所有设置");
            }

            // 检查渲染设置标签页下是否有任何子功能被启用
            bool renderingSettingsEnabled = enableRenderingSettingsCheckBox.Checked;

            // 如果渲染设置标签页下有子功能被启用，则保存相关设置
            if (renderingSettingsEnabled)
            {
                // 尝试安全地保存渲染设置
                try
                {
                    // 保存渲染设置
                    PdfSettings.EnableRenderingSettings = enableRenderingSettingsCheckBox.Checked;
                    PdfSettings.DmlEffectsRenderingMode = (AW.Saving.DmlEffectsRenderingMode)dmlEffectsComboBox.SelectedIndex;
                    PdfSettings.AdditionalTextPositioning = additionalTextPositioningCheckBox.Checked;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"保存渲染设置时出错: {ex.Message}");
                    MessageBox.Show($"保存渲染设置时出错: {ex.Message}", "保存设置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            else
            {
                // 如果渲染设置标签页下没有任何子功能被启用，则禁用所有相关设置
                PdfSettings.EnableRenderingSettings = false;
                System.Diagnostics.Debug.WriteLine("渲染设置标签页下没有任何子功能被启用，跳过该标签页的所有设置");
            }

            // 检查其他设置标签页下是否有任何子功能被启用
            bool otherSettingsEnabled = enableOtherSettingsCheckBox.Checked;

            // 如果其他设置标签页下有子功能被启用，则保存相关设置
            if (otherSettingsEnabled)
            {
                // 尝试安全地保存其他设置
                try
                {
                    // 保存其他设置 - 包括显示模式和布局
                    PdfSettings.EnableOtherSettings = enableOtherSettingsCheckBox.Checked;
                    PdfSettings.DisplayDocTitle = displayDocTitleCheckBox.Checked;
                    PdfSettings.PageMode = (AW.Saving.PdfPageMode)pageModeComboBox.SelectedIndex;
                    PdfSettings.PageLayout = (AW.Saving.PdfPageLayout)pageLayoutComboBox.SelectedIndex;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"保存其他设置时出错: {ex.Message}");
                    MessageBox.Show($"保存其他设置时出错: {ex.Message}", "保存设置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            else
            {
                // 如果其他设置标签页下没有任何子功能被启用，则禁用所有相关设置
                PdfSettings.EnableOtherSettings = false;
                System.Diagnostics.Debug.WriteLine("其他设置标签页下没有任何子功能被启用，跳过该标签页的所有设置");
            }

            // 保存新增的标签页设置
            // 注意：由于这些是新添加的标签页，我们需要通过查找控件来获取值
            // 这里使用简化的保存方式，实际应用中可能需要更复杂的控件引用管理

            // 保存安全设置（如果标签页存在）
            try
            {
                var securityTab = mainTabControl.TabPages.Cast<TabPage>().FirstOrDefault(t => t.Text == "安全设置");
                if (securityTab != null)
                {
                    var enableSecurityCheckBox = FindControlByName<CheckBox>(securityTab, "启用安全设置");
                    if (enableSecurityCheckBox != null)
                    {
                        PdfSettings.EnableSecuritySettings = enableSecurityCheckBox.Checked;

                        // 查找密码文本框并保存值
                        var userPasswordTextBox = FindControlByType<TextBox>(securityTab).FirstOrDefault();
                        var ownerPasswordTextBox = FindControlByType<TextBox>(securityTab).Skip(1).FirstOrDefault();

                        if (userPasswordTextBox != null) PdfSettings.UserPassword = userPasswordTextBox.Text;
                        if (ownerPasswordTextBox != null) PdfSettings.OwnerPassword = ownerPasswordTextBox.Text;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存安全设置时出错: {ex.Message}");
            }

            // 保存书签设置
            try
            {
                var bookmarkTab = mainTabControl.TabPages.Cast<TabPage>().FirstOrDefault(t => t.Text == "书签和大纲");
                if (bookmarkTab != null)
                {
                    var enableBookmarkCheckBox = FindControlByName<CheckBox>(bookmarkTab, "启用书签和大纲设置");
                    if (enableBookmarkCheckBox != null)
                    {
                        PdfSettings.EnableBookmarkSettings = enableBookmarkCheckBox.Checked;

                        // 查找数值控件并保存值
                        var numericControls = FindControlByType<NumericUpDown>(bookmarkTab).ToList();
                        if (numericControls.Count >= 2)
                        {
                            PdfSettings.HeadingsOutlineLevels = (int)numericControls[0].Value;
                            PdfSettings.ExpandedOutlineLevels = (int)numericControls[1].Value;
                        }

                        // 查找复选框并保存值
                        var checkBoxes = FindControlByType<CheckBox>(bookmarkTab).Skip(1).ToList();
                        if (checkBoxes.Count >= 2)
                        {
                            PdfSettings.CreateMissingOutlineLevels = checkBoxes[0].Checked;
                            PdfSettings.CreateOutlinesForHeadingsInTables = checkBoxes[1].Checked;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存书签设置时出错: {ex.Message}");
            }

            // 记录设置保存完成
            System.Diagnostics.Debug.WriteLine("PDF设置保存完成");

            // 检查是否所有标签页都被禁用
            if (!anyImageProcessingEnabled && !textCompressionEnabled && !documentStructureEnabled &&
                !fontSettingsEnabled && !hyperlinkSettingsEnabled && !complianceSettingsEnabled &&
                !renderingSettingsEnabled && !otherSettingsEnabled)
            {
                // 所有标签页都被禁用，提示用户
                MessageBox.Show("所有PDF设置标签页都被禁用，将使用默认设置进行PDF转换。",
                                "设置提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        // 辅助方法：根据文本查找控件
        private T? FindControlByName<T>(Control parent, string text) where T : Control
        {
            foreach (Control control in parent.Controls)
            {
                if (control is T targetControl && control.Text == text)
                {
                    return targetControl;
                }

                var found = FindControlByName<T>(control, text);
                if (found != null)
                {
                    return found;
                }
            }
            return null;
        }

        // 辅助方法：根据类型查找控件
        private IEnumerable<T> FindControlByType<T>(Control parent) where T : Control
        {
            foreach (Control control in parent.Controls)
            {
                if (control is T targetControl)
                {
                    yield return targetControl;
                }

                foreach (var found in FindControlByType<T>(control))
                {
                    yield return found;
                }
            }
        }
    }
}
