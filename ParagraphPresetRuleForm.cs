/*
 * ========================================
 * 文件名: ParagraphPresetRuleForm.cs
 * 功能描述: 段落预设规则配置窗体
 * ========================================
 *
 * 主要功能:
 * 1. 提供段落预设规则的高级配置界面
 * 2. 支持复杂的条件组合和逻辑关系设置
 * 3. 包含完整的段落和字体格式预设配置
 * 4. 提供多种匹配条件类型的组合使用
 * 5. 支持格式应用范围的精确控制
 *
 * 界面结构:
 * - 匹配条件标签页：条件组合、匹配方式、逻辑关系等
 * - 段落格式标签页：对齐、缩进、间距、行距等完整设置
 * - 字体格式标签页：多语言字体、样式、效果等高级设置
 *
 * 条件组合功能:
 * - 条件列表管理：添加、编辑、删除多个匹配条件
 * - 逻辑关系设置：AND（与）、OR（或）逻辑组合
 * - 条件类型支持：开头匹配、包含匹配、结尾匹配、正则表达式、位置匹配
 * - 实时条件预览：显示当前配置的所有条件
 *
 * 匹配方式类型:
 * - 段落开头匹配：检查段落是否以指定文本开头
 * - 段落包含匹配：检查段落是否包含指定关键词
 * - 段落结尾匹配：检查段落是否以指定文本结尾
 * - 正则表达式匹配：支持复杂的正则表达式模式
 * - 段落位置匹配：按段落在文档中的序号匹配
 *
 * 段落格式设置:
 * - 对齐方式：左对齐、居中、右对齐、两端对齐
 * - 大纲级别：正文、标题1-9级别设置
 * - 文本方向：从左到右、从右到左
 * - 缩进设置：首行缩进、悬挂缩进、左右缩进
 * - 间距设置：段前距、段后距、行距类型和数值
 * - 分页控制：段前分页符设置
 *
 * 字体格式设置:
 * - 多语言字体：中文、西文、复杂脚本独立设置
 * - 字体样式：常规、粗体、斜体、粗斜体
 * - 字体大小：支持精确的字号设置
 * - 字体颜色：前景色和背景高亮色
 * - 文本效果：下划线、删除线等
 *
 * 格式应用范围:
 * - 整个段落：应用到段落的全部内容
 * - 到句号为止：应用到第一个句号之前的内容
 * - 到换行为止：应用到第一个换行符之前的内容
 * - 到指定字符：应用到指定字符之前或之后的内容
 * - 字符区间：在指定字符之间应用格式
 *
 * 高级特性:
 * - 中文版式特性：标点压缩、网格对齐等
 * - 文本间距与缩放：字符间距、文本缩放比例
 * - 条件逻辑验证：确保条件组合的有效性
 * - 预设标题显示：显示当前编辑的预设名称
 * - 实时格式预览：提供格式效果的即时反馈
 *
 * 交互特性:
 * - 分组控件管理：相关设置分组显示
 * - 启用状态控制：每个格式类别可独立启用/禁用
 * - 智能控件联动：相关控件状态自动更新
 * - 用户友好提示：提供详细的使用说明和帮助
 *
 * 数据管理:
 * - 与ParagraphMatchRule模型完全集成
 * - 支持复杂规则的序列化和反序列化
 * - 提供规则验证和错误检查
 * - 支持预设规则的导入导出
 *
 * 注意事项:
 * - 支持Aspose.Words的所有高级格式选项
 * - 包含完整的条件逻辑处理
 * - 实现了复杂的用户界面交互
 * - 提供了专业级的格式设置功能
 */

#nullable enable
using System;
using System.Windows.Forms;
using System.Drawing;
using System.Linq;
using System.Collections.Generic;
using AsposeWordFormatter.Models;
using AW = Aspose.Words;

namespace AsposeWordFormatter
{
    #pragma warning disable CS0169, CS0414 // 禁用未使用的字段和赋值但未使用的字段警告
    public partial class ParagraphPresetRuleForm : Form
    {
        private readonly ParagraphMatchRule rule;
        private readonly string presetTitle;

        // 匹配条件控件
        private TextBox? startPatternsTextBox;
        private TextBox? containPatternsTextBox;
        private TextBox? endPatternsTextBox;
        private TextBox? regexPatternTextBox;
        private NumericUpDown? paragraphPositionNumeric;
        private CheckBox? caseSensitiveCheckBox;
        private NumericUpDown? minLengthNumeric;
        private NumericUpDown? maxLengthNumeric;

        // 添加单选按钮
        private RadioButton? startsWithRadio;
        private RadioButton? containsRadio;
        private RadioButton? endsWithRadio;
        private RadioButton? regexRadio;
        private RadioButton? paragraphPositionRadio;

        // 添加条件组合控件
        private ListView? conditionListView;
        private ComboBox? conditionLogicComboBox;
        private Button? addConditionButton;
        private Button? removeConditionButton;
        private Button? editConditionButton;

        // 段落格式控件
        private ComboBox? alignmentComboBox;
        private ComboBox? outlineLevelComboBox;
        private ComboBox? textDirectionComboBox;
        private NumericUpDown? beforeSpacingNumeric;
        private NumericUpDown? afterSpacingNumeric;
        private ComboBox? indentTypeComboBox;
        private NumericUpDown? indentValueNumeric;
        private ComboBox? indentUnitComboBox;
        private NumericUpDown? leftIndentNumeric;
        private NumericUpDown? rightIndentNumeric;
        private ComboBox? lineSpacingTypeComboBox;
        private NumericUpDown? lineSpacingValueNumeric;
        private ComboBox? lineSpacingUnitComboBox;
        private Label? lineSpacingUnitLabel;

        // 字体格式控件
        private ComboBox? chineseFontComboBox;
        private ComboBox? chineseFontStyleComboBox;
        private ComboBox? chineseFontSizeComboBox;
        private NumericUpDown? chineseFontSizeNumeric;
        private Label? chineseFontSizeUnitLabel;
        private ComboBox? westernFontComboBox;
        private ComboBox? westernFontStyleComboBox;
        private ComboBox? westernFontSizeComboBox;
        private NumericUpDown? westernFontSizeNumeric;
        private Label? westernFontSizeUnitLabel;
        private ComboBox? complexScriptFontComboBox;
        private ComboBox? complexScriptFontStyleComboBox;
        private ComboBox? complexScriptFontSizeComboBox;
        private NumericUpDown? complexScriptFontSizeNumeric;
        private Label? complexScriptFontSizeUnitLabel;
        private Button? fontColorButton;
        private Panel? fontColorPanel;
        // 新增的背景色控件
        private Button? highlightColorButton;
        private Panel? highlightColorPanel;

        // 格式应用范围控件
        private RadioButton? wholeParagraphRadio;
        private RadioButton? untilPeriodRadio;
        private RadioButton? untilLineBreakRadio;
        private RadioButton? untilSpecificCharInclusiveRadio;
        private RadioButton? untilSpecificCharExclusiveRadio;
        private TextBox? inclusiveCharTextBox;
        private TextBox? exclusiveCharTextBox;

        // 新增控件
        private RadioButton? betweenPeriodsRadio;
        private RadioButton? fromStartToMatchInclusiveRadio;
        private RadioButton? fromStartToMatchExclusiveRadio;
        private RadioButton? fromCharToMatchInclusiveRadio;
        private RadioButton? fromCharToMatchExclusiveRadio;
        private TextBox? startingCharInclusiveTextBox;
        private TextBox? startingCharExclusiveTextBox;

        // 添加文本效果面板引用
        private TableLayoutPanel? textEffectsPanel;

        // 文本间距与缩放控件
        private readonly CheckBox? characterSpacingCheckBox;
        private readonly ComboBox? characterSpacingComboBox;
        private readonly CheckBox? textScalingCheckBox;
        private readonly NumericUpDown? textScalingNumeric;

        // 中文版式特性控件
        private readonly CheckBox? chineseTypographyCheckBox;
        private readonly RadioButton? horizontalTextRadio;
        private readonly RadioButton? verticalTextRadio;
        private readonly CheckBox? punctuationCompressionCheckBox;
        private readonly ComboBox? punctuationCompressionComboBox;
        private readonly CheckBox? gridAlignmentCheckBox;
        private readonly ComboBox? gridAlignmentComboBox;

        // 文本效果相关控件 - 初始化这些字段以解决CS0649警告
        private readonly CheckBox? textEffectsCheckBox = null;
        private readonly CheckBox? underlineCheckBox = null;
        private readonly CheckBox? strikethroughCheckBox = null;

        // 段落格式启用控件
        private CheckBox? alignmentEnableCheckBox;
        private CheckBox? outlineLevelEnableCheckBox;
        private CheckBox? textDirectionEnableCheckBox;
        private CheckBox? indentEnableCheckBox;
        private CheckBox? spacingEnableCheckBox;
        private CheckBox? pageBreakBeforeCheckBox; // 添加段落前分页符控件

        // 字体格式启用控件
        private CheckBox? chineseFontEnableCheckBox;
        private CheckBox? westernFontEnableCheckBox;
        private CheckBox? complexScriptFontEnableCheckBox;
        private CheckBox? fontColorEnableCheckBox;
        private CheckBox? highlightColorEnableCheckBox;

        public ParagraphPresetRuleForm(ParagraphMatchRule rule, string presetTitle)
        {
            this.rule = rule ?? throw new ArgumentNullException(nameof(rule));
            this.presetTitle = presetTitle ?? throw new ArgumentNullException(nameof(presetTitle));
            InitializeComponent();
            LoadSettings();
        }

        private void InitializeComponent()
        {
            this.Text = $"段落匹配规则 - {presetTitle}";
            // 设置合适的窗体尺寸
            this.Size = new Size(800, 900); // 增加窗体高度
            this.MinimumSize = new Size(800, 900); // 增加最小高度
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.AutoScroll = true;

            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(5)
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(10),
                AutoSize = false // 改为false以避免尺寸计算问题
            };

            // 设置行高比例
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 90F)); // 标签页占90%
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 5F));  // 说明标签占5%
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 5F));  // 按钮面板占5%

            // 创建标签页控件
            var tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Height = 800, // 增加高度
                AutoSize = false // 改为false以避免尺寸计算问题
            };

            // 创建匹配条件标签页
            var matchConditionsTab = new TabPage("匹配条件");
            matchConditionsTab.Controls.Add(CreateMatchPanel());

            // 创建段落格式标签页
            var paragraphFormatTab = new TabPage("段落格式");
            paragraphFormatTab.Controls.Add(CreateParagraphFormatPanel());

            // 创建字体格式标签页
            var fontFormatTab = new TabPage("字体格式");
            fontFormatTab.Controls.Add(CreateFontFormatPanel());

            // 创建高级格式标签页
            var advancedFormatTab = new TabPage("高级格式");
            advancedFormatTab.Controls.Add(CreateAdvancedFormatPanel());

            // 创建中文排版标签页
            var chineseTypographyTab = new TabPage("中文排版");
            chineseTypographyTab.Controls.Add(CreateChineseTypographyPanel());

            // 创建字符单位标签页
            var characterUnitTab = new TabPage("字符单位");
            characterUnitTab.Controls.Add(CreateCharacterUnitPanel());

            // 添加标签页到控件（按顺序）
            tabControl.Controls.Add(matchConditionsTab);
            tabControl.Controls.Add(paragraphFormatTab);
            tabControl.Controls.Add(fontFormatTab);
            tabControl.Controls.Add(advancedFormatTab);
            tabControl.Controls.Add(chineseTypographyTab);
            tabControl.Controls.Add(characterUnitTab);

            mainLayout.Controls.Add(tabControl, 0, 0);

            // 添加匹配说明
            var noteLabel = new Label
            {
                Text = "注：本预设会设置段落格式、字体格式和对齐方式等，匹配条件满足时，将应用此预设格式。",
                AutoSize = true,
                ForeColor = Color.Red,
                Margin = new Padding(10)
            };

            mainLayout.Controls.Add(noteLabel, 0, 1);

            // 添加按钮面板
            var buttonPanel = CreateButtonPanel();
            mainLayout.Controls.Add(buttonPanel, 0, 2);

            contentPanel.Controls.Add(mainLayout);
            this.Controls.Add(contentPanel);
        }

        private Panel CreateMatchPanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill, AutoScroll = true };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 5, // 增加一行用于条件组合
                Padding = new Padding(10),
                AutoSize = true,
                MinimumSize = new Size(0, 650) // 设置最小高度
            };

            // 设置行高均匀
            for (int i = 0; i < 5; i++)
            {
                layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 添加启用规则复选框 - 作为第一个选项
            var enableRulePanel = CreateLabeledControl("启用规则：", () => {
                var enableRuleCheckBox = new CheckBox
                {
                    Checked = rule.IsEnabled,
                    AutoSize = true,
                    Text = ""
                };
                enableRuleCheckBox.CheckedChanged += (s, e) => rule.IsEnabled = enableRuleCheckBox.Checked;
                return enableRuleCheckBox;
            });
            layout.Controls.Add(enableRulePanel, 0, 0);

            // 创建条件组合面板
            var conditionGroupPanel = new GroupBox
            {
                Text = "", // 清空原标题
                Dock = DockStyle.Fill,
                Padding = new Padding(10, 15, 10, 5), // ，减小底部边距
                Margin = new Padding(0, 5, 0, 5), // 减小上下边距
                Height = 190, // 减小高度
                AutoSize = false
            };

            // 添加红色加粗的标题标签
            var conditionGroupLabel = new Label
            {
                Text = "条件组合",
                ForeColor = Color.Red,
                Font = new Font(Font.FontFamily, Font.Size, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(10, 0), // 位置在GroupBox左上角
                BackColor = Color.Transparent
            };
            conditionGroupPanel.Controls.Add(conditionGroupLabel);

            var conditionLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                AutoSize = false
            };

            // 添加条件列表视图
            conditionListView = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = false,
                Height = 90 // 减小高度
            };
            conditionListView.Columns.Add("类型", 100);
            conditionListView.Columns.Add("条件", 300);
            conditionListView.Columns.Add("逻辑", 80);

            // 添加逻辑选择和按钮面板
            var controlPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Bottom,
                ColumnCount = 4,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 5, 0, 0)
            };

            conditionLogicComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 100,
                DrawMode = DrawMode.OwnerDrawFixed
            };
            conditionLogicComboBox.Items.Add("与(AND)");
            conditionLogicComboBox.Items.Add("或(OR)");
            conditionLogicComboBox.SelectedIndex = 0;
            conditionLogicComboBox.DrawItem += ComboBox_DrawItem;

            addConditionButton = new Button
            {
                Text = "添加条件",
                AutoSize = true
            };

            editConditionButton = new Button
            {
                Text = "编辑条件",
                AutoSize = true,
                Enabled = false
            };

            removeConditionButton = new Button
            {
                Text = "删除条件",
                AutoSize = true,
                Enabled = false
            };

            var relationshipLabel = new Label
            {
                Text = "条件之间的关系:",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top | AnchorStyles.Bottom,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 1, 5, 0) // 添加上边距5像素
            };
            controlPanel.Controls.Add(relationshipLabel, 0, 0);
            controlPanel.Controls.Add(conditionLogicComboBox, 1, 0);
            controlPanel.Controls.Add(addConditionButton, 2, 0);

            var buttonsPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Dock = DockStyle.Fill
            };

            buttonsPanel.Controls.Add(editConditionButton);
            buttonsPanel.Controls.Add(removeConditionButton);

            controlPanel.Controls.Add(buttonsPanel, 3, 0);

            conditionLayout.Controls.Add(conditionListView, 0, 0);
            conditionLayout.Controls.Add(controlPanel, 0, 1);

            // 删除之前添加的使用说明标签
            // 在buttonsPanel中添加使用说明按钮
            var helpButton = new Button
            {
                Text = "使用说明",
                AutoSize = true,
                Margin = new Padding(5, 3, 0, 0)
            };
            helpButton.Click += HelpButton_Click;
            buttonsPanel.Controls.Add(helpButton);

            conditionGroupPanel.Controls.Add(conditionLayout);

            // 添加事件处理
            addConditionButton.Click += AddConditionButton_Click;
            editConditionButton.Click += EditConditionButton_Click;
            removeConditionButton.Click += RemoveConditionButton_Click;
            conditionListView.SelectedIndexChanged += ConditionListView_SelectedIndexChanged;

            layout.Controls.Add(conditionGroupPanel, 0, 1);

            // 创建匹配方式分组框
            var matchTypeGroup = new GroupBox
            {
                Text = "单个匹配条件设置",
                Dock = DockStyle.Fill,
                Padding = new Padding(10, 15, 10, 5), // 减小底部边距
                Margin = new Padding(0, 5, 0, 5), // 减小上下边距
                Height = 320, // 减小固定高度
                AutoSize = false // 关闭自动调整大小
            };

            // 添加红色加粗的提示标签
            var instructionLabel = new Label
            {
                Text = "（点击\"添加条件\"添加到条件组合中,否则条件无法生效）",
                ForeColor = Color.Red,
                Font = new Font(Font.FontFamily, Font.Size, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(100, 0), // 位置在GroupBox标题右侧
                BackColor = Color.Transparent
            };
            matchTypeGroup.Controls.Add(instructionLabel);

            var matchTypeLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 5, // 每种匹配方式一行，增加段落位置匹配
                AutoSize = false // 关闭自动调整大小
            };

            // 设置行高，确保每行有足够空间
            for (int i = 0; i < 5; i++)
            {
                matchTypeLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 20)); // 均分空间，5行每行20%
            }

            // 1. 段落匹配开头 - 整合为一个紧凑的面板
            var startsWithPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 3, 0, 3)
            };

            startsWithRadio = new RadioButton
            {
                Text = "匹配段落开头",
                AutoSize = true,
                Checked = rule.StartWithPatterns.Count > 0
            };

            var startsWithControlPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            startPatternsTextBox = new TextBox
            {
                Multiline = true,
                Height = 40, // 减小文本框高度
                ScrollBars = ScrollBars.Vertical,
                Dock = DockStyle.Fill,
                Enabled = startsWithRadio.Checked,
                Margin = new Padding(0, 3, 0, 3)
            };

            var startHelp = new Label
            {
                Text = "多个关键词用===分隔，满足其中一个即可",
                Font = new Font(Font.FontFamily, 8),
                ForeColor = Color.Gray,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 3),
                Height = 20 // 恢复高度确保文本完整显示
            };

            startsWithControlPanel.Controls.Add(startPatternsTextBox, 0, 0);
            startsWithControlPanel.Controls.Add(startHelp, 0, 1);

            startsWithPanel.Controls.Add(startsWithRadio, 0, 0);
            startsWithPanel.Controls.Add(startsWithControlPanel, 1, 0);
            startsWithPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            startsWithPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

            // 调整控制面板的行高
            startsWithControlPanel.RowStyles.Clear();
            startsWithControlPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 70)); // 恢复原来的比例
            startsWithControlPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 30)); // 确保帮助文本有足够空间

            // 2. 段落包含关键词
            var containsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 3, 0, 3)
            };

            containsRadio = new RadioButton
            {
                Text = "段落包含关键词",
                AutoSize = true,
                Checked = !startsWithRadio.Checked && rule.ContainsPatterns.Count > 0
            };

            var containsControlPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            containPatternsTextBox = new TextBox
            {
                Multiline = true,
                Height = 40, // 减小文本框高度
                ScrollBars = ScrollBars.Vertical,
                Dock = DockStyle.Fill,
                Enabled = containsRadio.Checked,
                Margin = new Padding(0, 3, 0, 3)
            };

            var containHelp = new Label
            {
                Text = "多个关键词用===分隔，满足其中一个即可",
                Font = new Font(Font.FontFamily, 8),
                ForeColor = Color.Gray,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 3),
                Height = 20 // 恢复高度确保文本完整显示
            };

            containsControlPanel.Controls.Add(containPatternsTextBox, 0, 0);
            containsControlPanel.Controls.Add(containHelp, 0, 1);

            containsPanel.Controls.Add(containsRadio, 0, 0);
            containsPanel.Controls.Add(containsControlPanel, 1, 0);
            containsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            containsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

            // 调整控制面板的行高
            containsControlPanel.RowStyles.Clear();
            containsControlPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 70));
            containsControlPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 30));

            // 3. 段落匹配结尾
            var endsWithPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 3, 0, 3)
            };

            endsWithRadio = new RadioButton
            {
                Text = "匹配段落结尾",
                AutoSize = true,
                Checked = !startsWithRadio.Checked && !containsRadio.Checked && rule.EndWithPatterns.Count > 0
            };

            var endsWithControlPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            endPatternsTextBox = new TextBox
            {
                Multiline = true,
                Height = 40, // 减小文本框高度
                ScrollBars = ScrollBars.Vertical,
                Dock = DockStyle.Fill,
                Enabled = endsWithRadio.Checked,
                Margin = new Padding(0, 3, 0, 3)
            };

            var endHelp = new Label
            {
                Text = "多个关键词用===分隔，满足其中一个即可",
                Font = new Font(Font.FontFamily, 8),
                ForeColor = Color.Gray,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 3),
                Height = 20 // 恢复高度确保文本完整显示
            };

            endsWithControlPanel.Controls.Add(endPatternsTextBox, 0, 0);
            endsWithControlPanel.Controls.Add(endHelp, 0, 1);

            endsWithPanel.Controls.Add(endsWithRadio, 0, 0);
            endsWithPanel.Controls.Add(endsWithControlPanel, 1, 0);
            endsWithPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            endsWithPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

            // 调整控制面板的行高
            endsWithControlPanel.RowStyles.Clear();
            endsWithControlPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 70));
            endsWithControlPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 30));

            // 4. 正则表达式匹配
            var regexPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 3, 0, 3)
            };

            regexRadio = new RadioButton
            {
                Text = "使用正则表达式",
                AutoSize = true,
                Checked = !startsWithRadio.Checked && !containsRadio.Checked && !endsWithRadio.Checked && !string.IsNullOrEmpty(rule.Pattern)
            };

            regexPatternTextBox = new TextBox
            {
                Height = 40, // 减小文本框高度
                Dock = DockStyle.Fill,
                Enabled = regexRadio.Checked,
                Margin = new Padding(0, 3, 0, 3)
            };

            regexPanel.Controls.Add(regexRadio, 0, 0);
            regexPanel.Controls.Add(regexPatternTextBox, 1, 0);
            regexPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            regexPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

            // 5. 段落位置匹配
            var paragraphPositionPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 3, 0, 3)
            };

            paragraphPositionRadio = new RadioButton
            {
                Text = "匹配段落数",
                AutoSize = true,
                Checked = rule.UseParagraphPosition
            };

            var positionControlPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true
            };

            paragraphPositionNumeric = new NumericUpDown
            {
                Minimum = 1,
                Maximum = 9999,
                Value = rule.ParagraphPosition,
                Width = 80,
                Enabled = paragraphPositionRadio.Checked,
                Margin = new Padding(0, 3, 5, 3),
                TextAlign = HorizontalAlignment.Center
            };

            var positionLabel = new Label
            {
                Text = "第",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top | AnchorStyles.Bottom,
                Margin = new Padding(0, 6, 0, 3)
            };

            var positionLabel2 = new Label
            {
                Text = "段",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top | AnchorStyles.Bottom,
                Margin = new Padding(0, 6, 0, 3)
            };

            positionControlPanel.Controls.Add(positionLabel, 0, 0);
            positionControlPanel.Controls.Add(paragraphPositionNumeric, 1, 0);
            positionControlPanel.Controls.Add(positionLabel2, 2, 0);
            positionControlPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            positionControlPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            positionControlPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            paragraphPositionPanel.Controls.Add(paragraphPositionRadio, 0, 0);
            paragraphPositionPanel.Controls.Add(positionControlPanel, 1, 0);
            paragraphPositionPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            paragraphPositionPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

            // 添加所有匹配面板到匹配类型布局
            matchTypeLayout.Controls.Add(startsWithPanel, 0, 0);
            matchTypeLayout.Controls.Add(containsPanel, 0, 1);
            matchTypeLayout.Controls.Add(endsWithPanel, 0, 2);
            matchTypeLayout.Controls.Add(regexPanel, 0, 3);
            matchTypeLayout.Controls.Add(paragraphPositionPanel, 0, 4);

            matchTypeGroup.Controls.Add(matchTypeLayout);
            layout.Controls.Add(matchTypeGroup, 0, 1);

            // 5. 区分大小写和段落字符数限制
            var optionsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5) // 减小底部边距
            };

            // 区分大小写复选框
            caseSensitiveCheckBox = new CheckBox
            {
                Text = "区分大小写",
                AutoSize = true
            };

            optionsPanel.Controls.Add(caseSensitiveCheckBox, 0, 0);

            // 添加字符数限制控件
            var limitPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Dock = DockStyle.Fill
            };

            limitPanel.Controls.Add(new Label { Text = "字符数限制：", AutoSize = true });

            minLengthNumeric = new NumericUpDown { Minimum = 0, Maximum = 99999, Width = 100, TextAlign = HorizontalAlignment.Center };
            limitPanel.Controls.Add(minLengthNumeric);

            limitPanel.Controls.Add(new Label { Text = " 到 ", AutoSize = true });

            maxLengthNumeric = new NumericUpDown { Minimum = 0, Maximum = 99999, Width = 100, TextAlign = HorizontalAlignment.Center };
            limitPanel.Controls.Add(maxLengthNumeric);

            optionsPanel.Controls.Add(limitPanel, 1, 0);

            layout.Controls.Add(optionsPanel, 0, 2);

            // 6. 添加格式应用范围选项
            var scopePanel = new GroupBox
            {
                Text = "格式应用范围",
                Dock = DockStyle.Top,
                AutoSize = false,
                Height = 360,
                Padding = new Padding(10),
                Margin = new Padding(0, 0, 0, 0)
            };

            var scopeScrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,  // 添加滚动条
                Padding = new Padding(5),
                AutoSize = false
            };

            // 创建单选按钮组布局
            var scopeRadioPanel = new Panel
            {
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(5)
            };

            // 创建单选按钮组 - 并排成一列
            wholeParagraphRadio = new RadioButton
            {
                Text = "设定整段格式（默认）",
                AutoSize = true,
                Checked = rule.ApplyScope == FormatApplyScope.WholeParagrph,
                Location = new Point(5, 5)
            };

            // 添加分隔线1
            var separator1 = new Label
            {
                Text = "─────────────────── 向后匹配选项 ───────────────────",
                AutoSize = true,
                Location = new Point(5, 30)
            };

            untilPeriodRadio = new RadioButton
            {
                Text = "设定格式到匹配部分到第一个句号结束（含句号）",
                AutoSize = true,
                Checked = rule.ApplyScope == FormatApplyScope.UntilFirstPeriod,
                Location = new Point(5, 55)
            };

            untilLineBreakRadio = new RadioButton
            {
                Text = "设定格式到匹配部分直至换行结束",
                AutoSize = true,
                Checked = rule.ApplyScope == FormatApplyScope.UntilLineBreak,
                Location = new Point(5, 80)
            };

            // 自定义字符选项（包含）
            untilSpecificCharInclusiveRadio = new RadioButton
            {
                Text = "设定格式到匹配部分直至特定字符（包含）：",
                AutoSize = true,
                Checked = rule.ApplyScope == FormatApplyScope.UntilSpecificCharInclusive,
                Location = new Point(5, 105)
            };

            inclusiveCharTextBox = new TextBox
            {
                Width = 150,
                Text = rule.ApplyScope == FormatApplyScope.UntilSpecificCharInclusive ? rule.EndingChar : "",
                Enabled = rule.ApplyScope == FormatApplyScope.UntilSpecificCharInclusive,
                Location = new Point(325, 105)
            };

            // 自定义字符选项（不包含）
            untilSpecificCharExclusiveRadio = new RadioButton
            {
                Text = "设定格式到匹配部分直至特定字符（不包含）：",
                AutoSize = true,
                Checked = rule.ApplyScope == FormatApplyScope.UntilSpecificCharExclusive,
                Location = new Point(5, 130)
            };

            exclusiveCharTextBox = new TextBox
            {
                Width = 150,
                Text = rule.ApplyScope == FormatApplyScope.UntilSpecificCharExclusive ? rule.EndingChar : "",
                Enabled = rule.ApplyScope == FormatApplyScope.UntilSpecificCharExclusive,
                Location = new Point(325, 130)
            };

            // 添加分隔线2
            var separator2 = new Label
            {
                Text = "─────────────────── 向前匹配选项 ───────────────────",
                AutoSize = true,
                Location = new Point(5, 155)
            };

            // 添加句号之间选项
            betweenPeriodsRadio = new RadioButton
            {
                Text = "设定格式为匹配部分的前一个句号到后一个句号之间部分（含后一个句号）",
                AutoSize = true,
                Checked = rule.ApplyScope == FormatApplyScope.BetweenPeriods,
                Location = new Point(5, 180)
            };

            // 从段落开头到匹配部分（含匹配部分）
            fromStartToMatchInclusiveRadio = new RadioButton
            {
                Text = "设定格式为本段开头到匹配部分结束（含匹配部分）",
                AutoSize = true,
                Checked = rule.ApplyScope == FormatApplyScope.FromStartToMatchInclusive,
                Location = new Point(5, 205)
            };

            // 从段落开头到匹配部分（不含匹配部分）
            fromStartToMatchExclusiveRadio = new RadioButton
            {
                Text = "设定格式为本段开头到匹配部分结束（不含匹配部分）",
                AutoSize = true,
                Checked = rule.ApplyScope == FormatApplyScope.FromStartToMatchExclusive,
                Location = new Point(5, 230)
            };

            // 从特定字符到匹配部分（含匹配部分）
            fromCharToMatchInclusiveRadio = new RadioButton
            {
                Text = "设定格式为特定字符到匹配部分结束（含匹配部分）：",
                AutoSize = true,
                Checked = rule.ApplyScope == FormatApplyScope.FromCharToMatchInclusive,
                Location = new Point(5, 255)
            };

            startingCharInclusiveTextBox = new TextBox
            {
                Width = 150,
                Text = rule.ApplyScope == FormatApplyScope.FromCharToMatchInclusive ? rule.StartingChar : "",
                Enabled = rule.ApplyScope == FormatApplyScope.FromCharToMatchInclusive,
                Location = new Point(325, 255)
            };

            // 从特定字符到匹配部分（不含匹配部分）
            fromCharToMatchExclusiveRadio = new RadioButton
            {
                Text = "设定格式为特定字符到匹配部分结束（不含匹配部分）：",
                AutoSize = true,
                Checked = rule.ApplyScope == FormatApplyScope.FromCharToMatchExclusive,
                Location = new Point(5, 280)
            };

            startingCharExclusiveTextBox = new TextBox
            {
                Width = 150,
                Text = rule.ApplyScope == FormatApplyScope.FromCharToMatchExclusive ? rule.StartingChar : "",
                Enabled = rule.ApplyScope == FormatApplyScope.FromCharToMatchExclusive,
                Location = new Point(325, 280)
            };

            // 设置事件处理
            wholeParagraphRadio.CheckedChanged += (s, e) => {
                if (wholeParagraphRadio.Checked)
                {
                    rule.ApplyScope = FormatApplyScope.WholeParagrph;
                    UpdateTextBoxStates();
                }
            };

            if (untilPeriodRadio != null)
            {
                untilPeriodRadio.CheckedChanged += (s, e) => {
                    if (untilPeriodRadio?.Checked == true)
                    {
                        rule.ApplyScope = FormatApplyScope.UntilFirstPeriod;
                        UpdateTextBoxStates();
                    }
                };
            }

            if (untilLineBreakRadio != null)
            {
                untilLineBreakRadio.CheckedChanged += (s, e) => {
                    if (untilLineBreakRadio?.Checked == true)
                    {
                        rule.ApplyScope = FormatApplyScope.UntilLineBreak;
                        UpdateTextBoxStates();
                    }
                };
            }

            if (untilSpecificCharInclusiveRadio != null)
            {
                untilSpecificCharInclusiveRadio.CheckedChanged += (s, e) => {
                    if (untilSpecificCharInclusiveRadio?.Checked == true)
                    {
                        rule.ApplyScope = FormatApplyScope.UntilSpecificCharInclusive;
                        UpdateTextBoxStates();
                    }
                };
            }

            if (inclusiveCharTextBox != null && untilSpecificCharInclusiveRadio != null)
            {
                inclusiveCharTextBox.TextChanged += (s, e) => {
                    if (untilSpecificCharInclusiveRadio?.Checked == true)
                        rule.EndingChar = inclusiveCharTextBox.Text;
                };
            }

            if (untilSpecificCharExclusiveRadio != null)
            {
                untilSpecificCharExclusiveRadio.CheckedChanged += (s, e) => {
                    if (untilSpecificCharExclusiveRadio?.Checked == true)
                    {
                        rule.ApplyScope = FormatApplyScope.UntilSpecificCharExclusive;
                        UpdateTextBoxStates();
                    }
                };
            }

            if (exclusiveCharTextBox != null && untilSpecificCharExclusiveRadio != null)
            {
                exclusiveCharTextBox.TextChanged += (s, e) => {
                    if (untilSpecificCharExclusiveRadio?.Checked == true)
                        rule.EndingChar = exclusiveCharTextBox.Text;
                };
            }

            if (betweenPeriodsRadio != null)
            {
                betweenPeriodsRadio.CheckedChanged += (s, e) => {
                    if (betweenPeriodsRadio?.Checked == true)
                    {
                        rule.ApplyScope = FormatApplyScope.BetweenPeriods;
                        UpdateTextBoxStates();
                    }
                };
            }

            if (fromStartToMatchInclusiveRadio != null)
            {
                fromStartToMatchInclusiveRadio.CheckedChanged += (s, e) => {
                    if (fromStartToMatchInclusiveRadio?.Checked == true)
                    {
                        rule.ApplyScope = FormatApplyScope.FromStartToMatchInclusive;
                        UpdateTextBoxStates();
                    }
                };
            }

            if (fromStartToMatchExclusiveRadio != null)
            {
                fromStartToMatchExclusiveRadio.CheckedChanged += (s, e) => {
                    if (fromStartToMatchExclusiveRadio?.Checked == true)
                    {
                        rule.ApplyScope = FormatApplyScope.FromStartToMatchExclusive;
                        UpdateTextBoxStates();
                    }
                };
            }

            if (fromCharToMatchInclusiveRadio != null)
            {
                fromCharToMatchInclusiveRadio.CheckedChanged += (s, e) => {
                    if (fromCharToMatchInclusiveRadio?.Checked == true)
                    {
                        rule.ApplyScope = FormatApplyScope.FromCharToMatchInclusive;
                        UpdateTextBoxStates();
                    }
                };
            }

            if (startingCharInclusiveTextBox != null && fromCharToMatchInclusiveRadio != null)
            {
                startingCharInclusiveTextBox.TextChanged += (s, e) => {
                    if (fromCharToMatchInclusiveRadio?.Checked == true)
                        rule.StartingChar = startingCharInclusiveTextBox.Text;
                };
            }

            if (fromCharToMatchExclusiveRadio != null)
            {
                fromCharToMatchExclusiveRadio.CheckedChanged += (s, e) => {
                    if (fromCharToMatchExclusiveRadio?.Checked == true)
                    {
                        rule.ApplyScope = FormatApplyScope.FromCharToMatchExclusive;
                        UpdateTextBoxStates();
                    }
                };
            }

            if (startingCharExclusiveTextBox != null && fromCharToMatchExclusiveRadio != null)
            {
                startingCharExclusiveTextBox.TextChanged += (s, e) => {
                    if (fromCharToMatchExclusiveRadio?.Checked == true)
                        rule.StartingChar = startingCharExclusiveTextBox.Text;
                };
            }

            // 添加控件到布局
            if (scopeRadioPanel != null)
            {
                if (wholeParagraphRadio != null)
                    scopeRadioPanel.Controls.Add(wholeParagraphRadio);
                if (separator1 != null)
                    scopeRadioPanel.Controls.Add(separator1);
                if (untilPeriodRadio != null)
                    scopeRadioPanel.Controls.Add(untilPeriodRadio);
                if (untilLineBreakRadio != null)
                    scopeRadioPanel.Controls.Add(untilLineBreakRadio);
                if (untilSpecificCharInclusiveRadio != null)
                    scopeRadioPanel.Controls.Add(untilSpecificCharInclusiveRadio);
                if (inclusiveCharTextBox != null)
                    scopeRadioPanel.Controls.Add(inclusiveCharTextBox);
                if (untilSpecificCharExclusiveRadio != null)
                    scopeRadioPanel.Controls.Add(untilSpecificCharExclusiveRadio);
                if (exclusiveCharTextBox != null)
                    scopeRadioPanel.Controls.Add(exclusiveCharTextBox);
                if (separator2 != null)
                    scopeRadioPanel.Controls.Add(separator2);
                if (betweenPeriodsRadio != null)
                    scopeRadioPanel.Controls.Add(betweenPeriodsRadio);
                if (fromStartToMatchInclusiveRadio != null)
                    scopeRadioPanel.Controls.Add(fromStartToMatchInclusiveRadio);
                if (fromStartToMatchExclusiveRadio != null)
                    scopeRadioPanel.Controls.Add(fromStartToMatchExclusiveRadio);
                if (fromCharToMatchInclusiveRadio != null)
                    scopeRadioPanel.Controls.Add(fromCharToMatchInclusiveRadio);
                if (startingCharInclusiveTextBox != null)
                    scopeRadioPanel.Controls.Add(startingCharInclusiveTextBox);
                if (fromCharToMatchExclusiveRadio != null)
                    scopeRadioPanel.Controls.Add(fromCharToMatchExclusiveRadio);
                if (startingCharExclusiveTextBox != null)
                    scopeRadioPanel.Controls.Add(startingCharExclusiveTextBox);
            }

            // 组装控件层次结构
            if (scopeScrollPanel != null && scopeRadioPanel != null)
                scopeScrollPanel.Controls.Add(scopeRadioPanel);
            if (scopePanel != null && scopeScrollPanel != null)
                scopePanel.Controls.Add(scopeScrollPanel);
            if (layout != null && scopePanel != null)
                layout.Controls.Add(scopePanel, 0, 3);

            // 添加RadioButton事件处理程序
            if (startsWithRadio != null && containsRadio != null && endsWithRadio != null &&
                regexRadio != null && paragraphPositionRadio != null && startPatternsTextBox != null &&
                containPatternsTextBox != null && endPatternsTextBox != null && regexPatternTextBox != null &&
                paragraphPositionNumeric != null)
            {
                startsWithRadio.CheckedChanged += (s, e) => {
                    if (startsWithRadio?.Checked == true) {
                        // 禁用其他选项
                        if (containsRadio != null)
                            containsRadio.Checked = false;
                        if (endsWithRadio != null)
                            endsWithRadio.Checked = false;
                        if (regexRadio != null)
                            regexRadio.Checked = false;
                        if (paragraphPositionRadio != null)
                            paragraphPositionRadio.Checked = false;

                        // 更新规则设置
                        rule.UseParagraphPosition = false;

                        if (startPatternsTextBox != null)
                            startPatternsTextBox.Enabled = true;
                        if (containPatternsTextBox != null)
                            containPatternsTextBox.Enabled = false;
                        if (endPatternsTextBox != null)
                            endPatternsTextBox.Enabled = false;
                        if (regexPatternTextBox != null)
                            regexPatternTextBox.Enabled = false;
                        if (paragraphPositionNumeric != null)
                            paragraphPositionNumeric.Enabled = false;
                    }
                };
            }

            if (containsRadio != null && startsWithRadio != null && endsWithRadio != null &&
                regexRadio != null && paragraphPositionRadio != null && startPatternsTextBox != null &&
                containPatternsTextBox != null && endPatternsTextBox != null && regexPatternTextBox != null &&
                paragraphPositionNumeric != null)
            {
                containsRadio.CheckedChanged += (s, e) => {
                    if (containsRadio?.Checked == true) {
                        // 禁用其他选项
                        if (startsWithRadio != null)
                            startsWithRadio.Checked = false;
                        if (endsWithRadio != null)
                            endsWithRadio.Checked = false;
                        if (regexRadio != null)
                            regexRadio.Checked = false;
                        if (paragraphPositionRadio != null)
                            paragraphPositionRadio.Checked = false;

                        // 更新规则设置
                        rule.UseParagraphPosition = false;

                        if (startPatternsTextBox != null)
                            startPatternsTextBox.Enabled = false;
                        if (containPatternsTextBox != null)
                            containPatternsTextBox.Enabled = true;
                        if (endPatternsTextBox != null)
                            endPatternsTextBox.Enabled = false;
                        if (regexPatternTextBox != null)
                            regexPatternTextBox.Enabled = false;
                        if (paragraphPositionNumeric != null)
                            paragraphPositionNumeric.Enabled = false;
                    }
                };
            }

            if (endsWithRadio != null && startsWithRadio != null && containsRadio != null &&
                regexRadio != null && paragraphPositionRadio != null && startPatternsTextBox != null &&
                containPatternsTextBox != null && endPatternsTextBox != null && regexPatternTextBox != null &&
                paragraphPositionNumeric != null)
            {
                endsWithRadio.CheckedChanged += (s, e) => {
                    if (endsWithRadio?.Checked == true) {
                        // 禁用其他选项
                        if (startsWithRadio != null)
                            startsWithRadio.Checked = false;
                        if (containsRadio != null)
                            containsRadio.Checked = false;
                        if (regexRadio != null)
                            regexRadio.Checked = false;
                        if (paragraphPositionRadio != null)
                            paragraphPositionRadio.Checked = false;

                        // 更新规则设置
                        rule.UseParagraphPosition = false;

                        if (startPatternsTextBox != null)
                            startPatternsTextBox.Enabled = false;
                        if (containPatternsTextBox != null)
                            containPatternsTextBox.Enabled = false;
                        if (endPatternsTextBox != null)
                            endPatternsTextBox.Enabled = true;
                        if (regexPatternTextBox != null)
                            regexPatternTextBox.Enabled = false;
                        if (paragraphPositionNumeric != null)
                            paragraphPositionNumeric.Enabled = false;
                    }
                };
            }

            if (regexRadio != null && startsWithRadio != null && containsRadio != null &&
                endsWithRadio != null && paragraphPositionRadio != null && startPatternsTextBox != null &&
                containPatternsTextBox != null && endPatternsTextBox != null && regexPatternTextBox != null &&
                paragraphPositionNumeric != null)
            {
                regexRadio.CheckedChanged += (s, e) => {
                    if (regexRadio?.Checked == true) {
                        // 禁用其他选项
                        if (startsWithRadio != null)
                            startsWithRadio.Checked = false;
                        if (containsRadio != null)
                            containsRadio.Checked = false;
                        if (endsWithRadio != null)
                            endsWithRadio.Checked = false;
                        if (paragraphPositionRadio != null)
                            paragraphPositionRadio.Checked = false;

                        // 更新规则设置
                        rule.UseParagraphPosition = false;

                        if (startPatternsTextBox != null)
                            startPatternsTextBox.Enabled = false;
                        if (containPatternsTextBox != null)
                            containPatternsTextBox.Enabled = false;
                        if (endPatternsTextBox != null)
                            endPatternsTextBox.Enabled = false;
                        if (regexPatternTextBox != null)
                            regexPatternTextBox.Enabled = true;
                        if (paragraphPositionNumeric != null)
                            paragraphPositionNumeric.Enabled = false;
                    }
                };
            }

            // 添加段落位置匹配的事件处理程序
            if (paragraphPositionRadio != null && startsWithRadio != null && containsRadio != null &&
                endsWithRadio != null && regexRadio != null && startPatternsTextBox != null &&
                containPatternsTextBox != null && endPatternsTextBox != null && regexPatternTextBox != null &&
                paragraphPositionNumeric != null)
            {
                paragraphPositionRadio.CheckedChanged += (s, e) => {
                    if (paragraphPositionRadio?.Checked == true) {
                        // 禁用其他选项
                        if (startsWithRadio != null)
                            startsWithRadio.Checked = false;
                        if (containsRadio != null)
                            containsRadio.Checked = false;
                        if (endsWithRadio != null)
                            endsWithRadio.Checked = false;
                        if (regexRadio != null)
                            regexRadio.Checked = false;

                        // 更新规则设置
                        rule.UseParagraphPosition = true;

                        if (startPatternsTextBox != null)
                            startPatternsTextBox.Enabled = false;
                        if (containPatternsTextBox != null)
                            containPatternsTextBox.Enabled = false;
                        if (endPatternsTextBox != null)
                            endPatternsTextBox.Enabled = false;
                        if (regexPatternTextBox != null)
                            regexPatternTextBox.Enabled = false;
                        if (paragraphPositionNumeric != null)
                            paragraphPositionNumeric.Enabled = true;
                    }
                };

                // 添加段落位置数值变化事件
                paragraphPositionNumeric.ValueChanged += (s, e) => {
                    rule.ParagraphPosition = (int)paragraphPositionNumeric.Value;
                };
            }

            if (panel != null && layout != null)
                panel.Controls.Add(layout);
            return panel ?? new Panel();
        }

        private Panel CreateParagraphFormatPanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill, AutoScroll = true };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 6, // 增加行数以容纳新添加的控件
                Padding = new Padding(10),
                AutoSize = true,
                MinimumSize = new Size(0, 600) // 减少最小高度
            };

            // 设置行高均匀
            for (int i = 0; i < 6; i++) // 增加循环次数
            {
                layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 段落前添加分页符 - 作为第一个选项
            var pageBreakPanel = CreateLabeledControl("新页开始：", () => {
                pageBreakBeforeCheckBox = new CheckBox
                {
                    Text = "段落前添加分页符",
                    AutoSize = true,
                    Checked = rule.PageBreakBefore
                };
                pageBreakBeforeCheckBox.CheckedChanged += (s, e) => {
                    rule.PageBreakBefore = pageBreakBeforeCheckBox.Checked;
                };
                return pageBreakBeforeCheckBox;
            });
            layout.Controls.Add(pageBreakPanel, 0, 0);

            // 1. 对齐方式
            var alignmentPanel = CreateLabeledControl("对齐方式：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true,
                    Dock = DockStyle.Fill
                };

                // 设置列宽比例，减少勾选框与下拉框的距离
                container.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
                container.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

                alignmentEnableCheckBox = new CheckBox
                {
                    Text = "",
                    Checked = rule.EnableAlignment,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(0, 0, 5, 0) // 减少右边距
                };

                alignmentComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 150,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Enabled = rule.EnableAlignment,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                alignmentComboBox.Items.Add("左对齐");
                alignmentComboBox.Items.Add("居中对齐");
                alignmentComboBox.Items.Add("右对齐");
                alignmentComboBox.Items.Add("两端对齐");
                alignmentComboBox.Items.Add("分散对齐");
                alignmentComboBox.SelectedIndex = 0; // 默认左对齐
                alignmentComboBox.DrawItem += ComboBox_DrawItem;

                // 设置启用复选框事件
                alignmentEnableCheckBox.CheckedChanged += (s, e) =>
                {
                    alignmentComboBox.Enabled = alignmentEnableCheckBox.Checked;
                    rule.EnableAlignment = alignmentEnableCheckBox.Checked;
                };

                container.Controls.Add(alignmentEnableCheckBox, 0, 0);
                container.Controls.Add(alignmentComboBox, 1, 0);

                return container;
            });
            layout.Controls.Add(alignmentPanel, 0, 1);

            // 2. 大纲级别
            var outlineLevelPanel = CreateLabeledControl("大纲级别：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true,
                    Dock = DockStyle.Fill
                };

                // 设置列宽比例，减少勾选框与下拉框的距离
                container.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
                outlineLevelEnableCheckBox = new CheckBox
                {
                    Text = "",
                    Checked = rule.EnableOutlineLevel,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(0, 0, 5, 0) // 减少右边距
                };

                outlineLevelComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 150,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Enabled = rule.EnableOutlineLevel,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                outlineLevelComboBox.Items.Add("正文文本");
                outlineLevelComboBox.Items.Add("1级");
                outlineLevelComboBox.Items.Add("2级");
                outlineLevelComboBox.Items.Add("3级");
                outlineLevelComboBox.Items.Add("4级");
                outlineLevelComboBox.Items.Add("5级");
                outlineLevelComboBox.Items.Add("6级");
                outlineLevelComboBox.Items.Add("7级");
                outlineLevelComboBox.Items.Add("8级");
                outlineLevelComboBox.SelectedIndex = 0; // 默认正文文本
                outlineLevelComboBox.DrawItem += ComboBox_DrawItem;

                // 添加大纲级别改变事件，自动更新字体格式
                outlineLevelComboBox.SelectedIndexChanged += OutlineLevelComboBox_SelectedIndexChanged;

                // 设置启用复选框事件
                outlineLevelEnableCheckBox.CheckedChanged += (s, e) =>
                {
                    outlineLevelComboBox.Enabled = outlineLevelEnableCheckBox.Checked;
                    rule.EnableOutlineLevel = outlineLevelEnableCheckBox.Checked;
                };

                container.Controls.Add(outlineLevelEnableCheckBox, 0, 0);
                container.Controls.Add(outlineLevelComboBox, 1, 0);

                return container;
            });
            layout.Controls.Add(outlineLevelPanel, 0, 2);

            // 3. 对齐方向
            var textDirectionPanel = CreateLabeledControl("对齐方向：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true,
                    Dock = DockStyle.Fill
                };

                textDirectionEnableCheckBox = new CheckBox
                {
                    Text = "",
                    Checked = rule.EnableTextDirection,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(0, 0, 5, 0) // 减少右边距
                };

                textDirectionComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 150,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Enabled = rule.EnableTextDirection,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                textDirectionComboBox.Items.Add("从左向右");
                textDirectionComboBox.Items.Add("从右向左");
                textDirectionComboBox.SelectedIndex = 0; // 默认从左向右
                textDirectionComboBox.DrawItem += ComboBox_DrawItem;

                // 设置启用复选框事件
                textDirectionEnableCheckBox.CheckedChanged += (s, e) =>
                {
                    textDirectionComboBox.Enabled = textDirectionEnableCheckBox.Checked;
                    rule.EnableTextDirection = textDirectionEnableCheckBox.Checked;
                };

                container.Controls.Add(textDirectionEnableCheckBox, 0, 0);
                container.Controls.Add(textDirectionComboBox, 1, 0);

                return container;
            });
            layout.Controls.Add(textDirectionPanel, 0, 3);

            // 4. 缩进
            var indentPanel = CreateLabeledControl("缩进：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true,
                    Dock = DockStyle.Fill
                };

                indentEnableCheckBox = new CheckBox
                {
                    Text = "",
                    Checked = rule.EnableIndent,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(0, 0, 5, 0) // 减少右边距
                };

                var fullIndentPanel = new FlowLayoutPanel
                {
                    FlowDirection = FlowDirection.TopDown,
                    AutoSize = true,
                    Dock = DockStyle.Fill,
                    Enabled = rule.EnableIndent
                };

                // 基本缩进部分
                var basicIndentPanel = new TableLayoutPanel
                {
                    ColumnCount = 3,
                    RowCount = 2,
                    AutoSize = true,
                    Dock = DockStyle.Top
                };

                // 左缩进
                basicIndentPanel.Controls.Add(new Label { Text = "文本之前：", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft, Anchor = AnchorStyles.Left | AnchorStyles.Top }, 0, 0);
                leftIndentNumeric = new NumericUpDown
                {
                    Minimum = 0,
                    Maximum = 100,
                    Width = 60,
                    Value = 0,
                    Height = 23, // 减小高度
                    TextAlign = HorizontalAlignment.Center
                };
                basicIndentPanel.Controls.Add(leftIndentNumeric, 1, 0);
                basicIndentPanel.Controls.Add(new Label { Text = "磅", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft, Anchor = AnchorStyles.Left | AnchorStyles.Top }, 2, 0);

                // 右缩进
                basicIndentPanel.Controls.Add(new Label { Text = "文本之后：", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft, Anchor = AnchorStyles.Left | AnchorStyles.Top }, 0, 1);
                rightIndentNumeric = new NumericUpDown
                {
                    Minimum = 0,
                    Maximum = 100,
                    Width = 60,
                    Value = 0,
                    Height = 23, // 减小高度
                    TextAlign = HorizontalAlignment.Center
                };
                basicIndentPanel.Controls.Add(rightIndentNumeric, 1, 1);
                basicIndentPanel.Controls.Add(new Label { Text = "磅", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft, Anchor = AnchorStyles.Left | AnchorStyles.Top }, 2, 1);

                fullIndentPanel.Controls.Add(basicIndentPanel);

                // 特殊缩进部分
                var specialIndentPanel = new TableLayoutPanel
                {
                    ColumnCount = 3,
                    RowCount = 2,
                    AutoSize = true,
                    Margin = new Padding(0, 5, 0, 0) // 减小上边距
                };

                // 特殊格式
                specialIndentPanel.Controls.Add(new Label { Text = "特殊格式：", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft, Anchor = AnchorStyles.Left | AnchorStyles.Top }, 0, 0);
                indentTypeComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 150,
                    Height = 23, // 减小高度
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                indentTypeComboBox.Items.Add("无");
                indentTypeComboBox.Items.Add("首行缩进");
                indentTypeComboBox.Items.Add("悬挂缩进");
                indentTypeComboBox.SelectedIndex = 1; // 默认首行缩进
                indentTypeComboBox.DrawItem += ComboBox_DrawItem;
                specialIndentPanel.Controls.Add(indentTypeComboBox, 1, 0);

                // 缩进值
                specialIndentPanel.Controls.Add(new Label { Text = "缩进值：", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft, Anchor = AnchorStyles.Left | AnchorStyles.Top }, 0, 1);

                var indentValuePanel = new FlowLayoutPanel
                {
                    FlowDirection = FlowDirection.LeftToRight,
                    AutoSize = true,
                    Margin = new Padding(0)
                };

                indentValueNumeric = new NumericUpDown
                {
                    Minimum = 0,
                    Maximum = 100,
                    DecimalPlaces = 2,
                    Increment = 0.1m,
                    Width = 60,
                    Value = 2.0m, // 默认2字符
                    Enabled = true, // 启用，因为选择了首行缩进
                    Height = 23, // 减小高度
                    TextAlign = HorizontalAlignment.Center
                };

                indentUnitComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 60,
                    Height = 23,
                    DrawMode = DrawMode.OwnerDrawFixed
                };
                indentUnitComboBox.Items.Add("字符");
                indentUnitComboBox.Items.Add("厘米");
                indentUnitComboBox.Items.Add("毫米");
                indentUnitComboBox.Items.Add("磅");
                indentUnitComboBox.Items.Add("英寸");
                indentUnitComboBox.SelectedIndex = 0; // 默认字符单位
                indentUnitComboBox.Tag = IndentUnit.Characters; // 保存当前单位
                indentUnitComboBox.DrawItem += ComboBox_DrawItem;

                indentValuePanel.Controls.Add(indentValueNumeric);
                indentValuePanel.Controls.Add(indentUnitComboBox);

                specialIndentPanel.Controls.Add(indentValuePanel, 1, 1);

                // 设置特殊缩进选择事件
                indentTypeComboBox.SelectedIndexChanged += indentTypeComboBox_SelectedIndexChanged;

                // 设置特殊缩进单位改变事件
                indentUnitComboBox.SelectedIndexChanged += (s, e) =>
                {
                    if (indentUnitComboBox.SelectedIndex >= 0)
                    {
                        // 获取当前磅值
                        var currentUnit = (IndentUnit)(indentUnitComboBox.Tag ?? IndentUnit.Characters);
                        var newUnit = (IndentUnit)indentUnitComboBox.SelectedIndex;

                        if (currentUnit != newUnit)
                        {
                            // 先转换为磅，再转换为新单位
                            double pointsValue = UnitConverter.ConvertToPoints((double)indentValueNumeric.Value, currentUnit);
                            double newValue = UnitConverter.ConvertFromPoints(pointsValue, newUnit);

                            indentValueNumeric.Value = (decimal)Math.Round(newValue, 2);
                            indentUnitComboBox.Tag = newUnit; // 保存当前单位
                        }
                    }
                };

                fullIndentPanel.Controls.Add(specialIndentPanel);

                // 设置启用复选框事件
                indentEnableCheckBox.CheckedChanged += (s, e) =>
                {
                    fullIndentPanel.Enabled = indentEnableCheckBox.Checked;
                    rule.EnableIndent = indentEnableCheckBox.Checked;
                };

                container.Controls.Add(indentEnableCheckBox, 0, 0);
                container.Controls.Add(fullIndentPanel, 1, 0);

                return container;
            });
            layout.Controls.Add(indentPanel, 0, 4);

            // 5. 间距
            var spacingPanel = CreateLabeledControl("间距：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true,
                    Dock = DockStyle.Fill
                };

                spacingEnableCheckBox = new CheckBox
                {
                    Text = "",
                    Checked = rule.EnableSpacing,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(0, 0, 5, 0) // 减少右边距
                };

                var fullSpacingPanel = new FlowLayoutPanel
                {
                    FlowDirection = FlowDirection.TopDown,
                    AutoSize = true,
                    Dock = DockStyle.Fill,
                    Enabled = rule.EnableSpacing
                };

                // 基本间距
                var basicSpacingPanel = new TableLayoutPanel
                {
                    ColumnCount = 3,
                    RowCount = 2,
                    AutoSize = true,
                    Dock = DockStyle.Top
                };

                // 段前间距
                basicSpacingPanel.Controls.Add(new Label { Text = "段前距离：", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft, Anchor = AnchorStyles.Left | AnchorStyles.Top }, 0, 0);
                beforeSpacingNumeric = new NumericUpDown
                {
                    Minimum = 0,
                    Maximum = 100,
                    DecimalPlaces = 1,
                    Increment = 0.1m,
                    Width = 60,
                    Value = 0,
                    Height = 23, // 减小高度
                    TextAlign = HorizontalAlignment.Center
                };
                basicSpacingPanel.Controls.Add(beforeSpacingNumeric, 1, 0);
                basicSpacingPanel.Controls.Add(new Label { Text = "磅", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft, Anchor = AnchorStyles.Left | AnchorStyles.Top }, 2, 0);

                // 段后间距
                basicSpacingPanel.Controls.Add(new Label { Text = "段后距离：", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft, Anchor = AnchorStyles.Left | AnchorStyles.Top }, 0, 1);
                afterSpacingNumeric = new NumericUpDown
                {
                    Minimum = 0,
                    Maximum = 100,
                    DecimalPlaces = 1,
                    Increment = 0.1m,
                    Width = 60,
                    Value = 0,
                    Height = 23, // 减小高度
                    TextAlign = HorizontalAlignment.Center
                };
                basicSpacingPanel.Controls.Add(afterSpacingNumeric, 1, 1);
                basicSpacingPanel.Controls.Add(new Label { Text = "磅", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft, Anchor = AnchorStyles.Left | AnchorStyles.Top }, 2, 1);

                fullSpacingPanel.Controls.Add(basicSpacingPanel);

                // 行距
                var lineSpacingContainer = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true,
                    Margin = new Padding(0, 5, 0, 0) // 减小上边距
                };

                lineSpacingContainer.Controls.Add(new Label { Text = "行距：", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft, Anchor = AnchorStyles.Left | AnchorStyles.Top }, 0, 0);

                var lineSpacingPanel = new FlowLayoutPanel
                {
                    FlowDirection = FlowDirection.LeftToRight,
                    AutoSize = true
                };

                lineSpacingTypeComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 100,
                    Height = 23, // 减小高度
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                lineSpacingTypeComboBox.Items.Add("单倍行距");
                lineSpacingTypeComboBox.Items.Add("1.5倍行距");
                lineSpacingTypeComboBox.Items.Add("2倍行距");
                lineSpacingTypeComboBox.Items.Add("多倍行距");
                lineSpacingTypeComboBox.Items.Add("最小值");
                lineSpacingTypeComboBox.Items.Add("固定值");
                lineSpacingTypeComboBox.SelectedIndex = 5; // 默认固定值
                lineSpacingTypeComboBox.DrawItem += ComboBox_DrawItem;

                lineSpacingPanel.Controls.Add(lineSpacingTypeComboBox);

                lineSpacingValueNumeric = new NumericUpDown
                {
                    Minimum = 1,
                    Maximum = 100,
                    DecimalPlaces = 2,
                    Increment = 0.1m,
                    Width = 60,
                    Value = 28, // 默认28磅
                    Enabled = true, // 默认启用（因为默认选择固定值）
                    Height = 23, // 减小高度
                    TextAlign = HorizontalAlignment.Center
                };

                lineSpacingUnitComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 60,
                    Height = 23,
                    Enabled = true, // 默认启用（因为默认选择固定值）
                    DrawMode = DrawMode.OwnerDrawFixed
                };
                lineSpacingUnitComboBox.Items.Add("磅");
                lineSpacingUnitComboBox.Items.Add("厘米");
                lineSpacingUnitComboBox.Items.Add("毫米");
                lineSpacingUnitComboBox.Items.Add("英寸");
                lineSpacingUnitComboBox.SelectedIndex = 0; // 默认磅单位
                lineSpacingUnitComboBox.Tag = LineSpacingUnit.Points; // 保存当前单位
                lineSpacingUnitComboBox.DrawItem += ComboBox_DrawItem;

                lineSpacingPanel.Controls.Add(lineSpacingValueNumeric);
                lineSpacingPanel.Controls.Add(lineSpacingUnitComboBox);

                // 设置行距类型选择事件
                lineSpacingTypeComboBox.SelectedIndexChanged += lineSpacingTypeComboBox_SelectedIndexChanged;

                // 设置行距单位改变事件
                lineSpacingUnitComboBox.SelectedIndexChanged += (s, e) =>
                {
                    if (lineSpacingUnitComboBox.SelectedIndex >= 0)
                    {
                        // 获取当前单位
                        var currentUnit = (LineSpacingUnit)(lineSpacingUnitComboBox.Tag ?? LineSpacingUnit.Points);
                        var newUnit = (LineSpacingUnit)lineSpacingUnitComboBox.SelectedIndex;

                        if (currentUnit != newUnit)
                        {
                            // 先转换为磅，再转换为新单位
                            double pointsValue = UnitConverter.ConvertToPoints((double)lineSpacingValueNumeric.Value, currentUnit);
                            double newValue = UnitConverter.ConvertFromPoints(pointsValue, newUnit);

                            lineSpacingValueNumeric.Value = (decimal)Math.Round(newValue, 2);
                            lineSpacingUnitComboBox.Tag = newUnit; // 保存当前单位
                        }
                    }
                };

                lineSpacingContainer.Controls.Add(lineSpacingPanel, 1, 0);
                fullSpacingPanel.Controls.Add(lineSpacingContainer);

                // 设置启用复选框事件
                spacingEnableCheckBox.CheckedChanged += (s, e) =>
                {
                    fullSpacingPanel.Enabled = spacingEnableCheckBox.Checked;
                    rule.EnableSpacing = spacingEnableCheckBox.Checked;
                };

                container.Controls.Add(spacingEnableCheckBox, 0, 0);
                container.Controls.Add(fullSpacingPanel, 1, 0);

                return container;
            });
            layout.Controls.Add(spacingPanel, 0, 5);

            // 最重要的步骤 - 确保将layout添加到panel
            panel.Controls.Add(layout);

            return panel;
        }

        private TableLayoutPanel CreateButtonPanel()
        {
            TableLayoutPanel panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));

            Button okButton = new Button
            {
                Text = "确定",
                AutoSize = true,
                Margin = new Padding(5),
                Padding = new Padding(5)
            };

            okButton.Click += (s, e) => {
                SaveSettings(); // 确保保存设置
                this.DialogResult = DialogResult.OK;
                this.Close();
            };

            Button cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                AutoSize = true,
                Margin = new Padding(5),
                Padding = new Padding(5)
            };

            panel.Controls.Add(new Control(), 0, 0);
            panel.Controls.Add(okButton, 1, 0);
            panel.Controls.Add(cancelButton, 2, 0);

            okButton.Click += OkButton_Click;

            return panel;
        }

        private void OkButton_Click(object? sender, EventArgs e)
        {
            // 保存所有设置到规则对象
            SaveSettings();
        }

        private void fontColorButton_Click(object? sender, EventArgs e)
        {
            if (fontColorPanel == null) return;

            using (ColorDialog colorDialog = new ColorDialog())
            {
                colorDialog.Color = fontColorPanel.BackColor != Color.Transparent ?
                    fontColorPanel.BackColor : Color.Black;

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    fontColorPanel.BackColor = colorDialog.Color;
                    // 立即更新规则对象的FontColor属性
                    rule.FontColor = colorDialog.Color;
                }
            }
        }

        private void highlightColorButton_Click(object? sender, EventArgs e)
        {
            if (highlightColorPanel == null) return;

            using (ColorDialog colorDialog = new ColorDialog())
            {
                colorDialog.Color = highlightColorPanel.BackColor != Color.Transparent ?
                    highlightColorPanel.BackColor : Color.Yellow; // 使用黄色作为默认高亮色

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    highlightColorPanel.BackColor = colorDialog.Color;
                    // 立即更新规则对象的HighlightColor属性
                    rule.HighlightColor = colorDialog.Color;
                }
            }
        }

        private void indentTypeComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (indentTypeComboBox == null || indentValueNumeric == null) return;

            // 只有当选择了首行缩进或悬挂缩进时才启用缩进值控件
            indentValueNumeric.Enabled = indentTypeComboBox.SelectedIndex > 0;

            // 当选择首行缩进或悬挂缩进时，如果当前值为0，则设置默认值为2字符
            if (indentTypeComboBox.SelectedIndex > 0 && indentValueNumeric.Value == 0)
            {
                indentValueNumeric.Value = 2.0m; // 默认2字符
            }
        }

        private void lineSpacingTypeComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (lineSpacingTypeComboBox == null || lineSpacingValueNumeric == null || lineSpacingUnitComboBox == null) return;

            int selectedIndex = lineSpacingTypeComboBox.SelectedIndex;

            switch (selectedIndex)
            {
                case 0: // 单倍行距
                    lineSpacingValueNumeric.Value = 1;
                    lineSpacingValueNumeric.Enabled = false;
                    lineSpacingUnitComboBox.Enabled = false;
                    // 创建一个临时的"倍"标签来替换单位下拉框
                    ShowTimesUnit();
                    break;
                case 1: // 1.5倍行距
                    lineSpacingValueNumeric.Value = 1.5m;
                    lineSpacingValueNumeric.Enabled = false;
                    lineSpacingUnitComboBox.Enabled = false;
                    ShowTimesUnit();
                    break;
                case 2: // 2倍行距
                    lineSpacingValueNumeric.Value = 2;
                    lineSpacingValueNumeric.Enabled = false;
                    lineSpacingUnitComboBox.Enabled = false;
                    ShowTimesUnit();
                    break;
                case 3: // 多倍行距
                    // 多倍行距显示倍数值，不是磅值
                    lineSpacingValueNumeric.Value = 3;
                    lineSpacingValueNumeric.Enabled = true;
                    lineSpacingUnitComboBox.Enabled = false;
                    ShowTimesUnit();
                    break;
                case 4: // 最小值
                    lineSpacingValueNumeric.Value = 28;
                    lineSpacingValueNumeric.Enabled = true;
                    lineSpacingUnitComboBox.Enabled = true;
                    ShowUnitComboBox();
                    break;
                case 5: // 固定值
                    lineSpacingValueNumeric.Value = 28;
                    lineSpacingValueNumeric.Enabled = true;
                    lineSpacingUnitComboBox.Enabled = true;
                    ShowUnitComboBox();
                    break;
            }
        }

        private void ShowTimesUnit()
        {
            // 隐藏单位下拉框，显示"倍"标签
            if (lineSpacingUnitComboBox != null)
            {
                lineSpacingUnitComboBox.Visible = false;
            }

            // 查找或创建"倍"标签
            var parent = lineSpacingUnitComboBox?.Parent;
            if (parent != null)
            {
                var timesLabel = parent.Controls.OfType<Label>().FirstOrDefault(l => l.Text == "倍");
                if (timesLabel == null)
                {
                    timesLabel = new Label
                    {
                        Text = "倍",
                        AutoSize = true,
                        TextAlign = ContentAlignment.MiddleLeft,
                        Anchor = AnchorStyles.Left | AnchorStyles.Top,
                        Location = lineSpacingUnitComboBox?.Location ?? new Point(0, 0),
                        Name = "timesLabel"
                    };
                    parent.Controls.Add(timesLabel);
                }
                timesLabel.Visible = true;
            }
        }

        private void ShowUnitComboBox()
        {
            // 显示单位下拉框，隐藏"倍"标签
            if (lineSpacingUnitComboBox != null)
            {
                lineSpacingUnitComboBox.Visible = true;
            }

            // 隐藏"倍"标签
            var parent = lineSpacingUnitComboBox?.Parent;
            if (parent != null)
            {
                var timesLabel = parent.Controls.OfType<Label>().FirstOrDefault(l => l.Text == "倍");
                if (timesLabel != null)
                {
                    timesLabel.Visible = false;
                }
            }
        }

        private void OutlineLevelComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (outlineLevelComboBox == null || alignmentComboBox == null) return;

            int selectedLevel = outlineLevelComboBox.SelectedIndex;

            // 如果是正文文本，应用默认格式
            if (selectedLevel == 0)
            {
                // 正文默认格式
                rule.FontSize = 12;
                rule.Bold = false;
                rule.Italic = false;
                alignmentComboBox.SelectedIndex = 0; // 左对齐

                // 更新字体格式控件
                if (chineseFontSizeNumeric != null) chineseFontSizeNumeric.Value = 12;
                if (westernFontSizeNumeric != null) westernFontSizeNumeric.Value = 12;
                if (complexScriptFontSizeNumeric != null) complexScriptFontSizeNumeric.Value = 12;

                // 更新字体样式
                if (chineseFontStyleComboBox != null) chineseFontStyleComboBox.SelectedIndex = 0; // 常规
                if (westernFontStyleComboBox != null) westernFontStyleComboBox.SelectedIndex = 0;
                if (complexScriptFontStyleComboBox != null) complexScriptFontStyleComboBox.SelectedIndex = 0;

                return;
            }

            // 应用标题格式，级别越高，字体越大，最高1级
            switch (selectedLevel)
            {
                case 1: // 1级
                    rule.FontSize = 22;
                    rule.Bold = true;
                    alignmentComboBox.SelectedIndex = 1; // 居中对齐
                    break;
                case 2: // 2级
                    rule.FontSize = 20;
                    rule.Bold = true;
                    alignmentComboBox.SelectedIndex = 0; // 左对齐
                    break;
                case 3: // 3级
                    rule.FontSize = 18;
                    rule.Bold = true;
                    break;
                case 4: // 4级
                    rule.FontSize = 16;
                    rule.Bold = true;
                    break;
                case 5: // 5级
                    rule.FontSize = 14;
                    rule.Bold = true;
                    break;
                case 6: // 6级
                    rule.FontSize = 13;
                    rule.Bold = true;
                    break;
                case 7: // 7级
                    rule.FontSize = 12;
                    rule.Bold = true;
                    break;
                case 8: // 8级
                    rule.FontSize = 12;
                    rule.Bold = true;
                    rule.Italic = true;  // 8级使用加粗斜体
                    break;
            }

            // 更新字体大小控件
            if (chineseFontSizeNumeric != null) chineseFontSizeNumeric.Value = (decimal)rule.FontSize;
            if (westernFontSizeNumeric != null) westernFontSizeNumeric.Value = (decimal)rule.FontSize;
            if (complexScriptFontSizeNumeric != null) complexScriptFontSizeNumeric.Value = (decimal)rule.FontSize;

            // 更新字体样式控件
            int fontStyleIndex = 0;
            if (rule.Bold && rule.Italic) fontStyleIndex = 3; // 加粗倾斜
            else if (rule.Bold) fontStyleIndex = 2; // 加粗
            else if (rule.Italic) fontStyleIndex = 1; // 倾斜

            if (chineseFontStyleComboBox != null) chineseFontStyleComboBox.SelectedIndex = fontStyleIndex;
            if (westernFontStyleComboBox != null) westernFontStyleComboBox.SelectedIndex = fontStyleIndex;
            if (complexScriptFontStyleComboBox != null) complexScriptFontStyleComboBox.SelectedIndex = fontStyleIndex;
        }

        private void LoadSettings()
        {
            try
            {
                // 加载匹配条件
                if (startPatternsTextBox != null)
                    startPatternsTextBox.Text = rule.StartWithPatterns.Count > 0 ? string.Join("===", rule.StartWithPatterns) : "";

                if (containPatternsTextBox != null)
                    containPatternsTextBox.Text = rule.ContainsPatterns.Count > 0 ? string.Join("===", rule.ContainsPatterns) : "";

                if (endPatternsTextBox != null)
                    endPatternsTextBox.Text = rule.EndWithPatterns.Count > 0 ? string.Join("===", rule.EndWithPatterns) : "";

                if (regexPatternTextBox != null)
                    regexPatternTextBox.Text = rule.Pattern ?? "";

                // 加载条件组合
                if (conditionListView != null)
                {
                    conditionListView.Items.Clear();

                    // 遍历规则中的条件项
                    foreach (var item in rule.ConditionItems)
                    {
                        string conditionType = "";
                        string conditionValue = item.Value;
                        string logicText = item.Logic == LogicOperator.And ? "与(AND)" : "或(OR)";

                        // 根据条件类型设置显示文本
                        switch (item.Type)
                        {
                            case ConditionType.StartsWith:
                                conditionType = "段落匹配开头";
                                break;
                            case ConditionType.Contains:
                                conditionType = "段落包含关键词";
                                break;
                            case ConditionType.EndsWith:
                                conditionType = "段落匹配结尾";
                                break;
                            case ConditionType.Regex:
                                conditionType = "使用正则表达式";
                                break;
                            case ConditionType.ParagraphPosition:
                                conditionType = "匹配段落数";
                                // 将数字值转换为显示格式，例如"3"转换为"第3段"
                                if (int.TryParse(item.Value, out int position))
                                {
                                    conditionValue = $"第{position}段";
                                }
                                break;
                        }

                        // 创建列表项
                        var listItem = new ListViewItem(new[] {
                            conditionType,
                            conditionValue,
                            conditionListView.Items.Count > 0 ? logicText : "-"
                        });

                        // 保存条件数据到Tag属性中，以便后续编辑
                        var conditionData = new ConditionData
                        {
                            Type = conditionType,
                            Value = conditionValue,
                            Logic = conditionListView.Items.Count > 0 ? (item.Logic == LogicOperator.And ? "AND" : "OR") : ""
                        };
                        listItem.Tag = conditionData;

                        conditionListView.Items.Add(listItem);
                    }

                    // 更新按钮状态
                    UpdateConditionButtonState();
                }

                // 确定选择哪个单选按钮
                if (startsWithRadio != null && containsRadio != null && endsWithRadio != null && regexRadio != null && paragraphPositionRadio != null)
                {
                    if (rule.StartWithPatterns.Count > 0) {
                        startsWithRadio.Checked = true;
                    }
                    else if (rule.ContainsPatterns.Count > 0) {
                        containsRadio.Checked = true;
                    }
                    else if (rule.EndWithPatterns.Count > 0) {
                        endsWithRadio.Checked = true;
                    }
                    else if (!string.IsNullOrEmpty(rule.Pattern)) {
                        regexRadio.Checked = true;
                    }
                    else if (rule.UseParagraphPosition && rule.ParagraphPosition > 0) {
                        paragraphPositionRadio.Checked = true;
                        if (paragraphPositionNumeric != null) {
                            paragraphPositionNumeric.Value = rule.ParagraphPosition;
                        }
                    }
                    else {
                        // 默认选择起始匹配
                        startsWithRadio.Checked = true;
                    }
                }

                if (caseSensitiveCheckBox != null)
                    caseSensitiveCheckBox.Checked = rule.CaseSensitive;

                if (minLengthNumeric != null)
                    minLengthNumeric.Value = rule.MinLength;

                if (maxLengthNumeric != null)
                    maxLengthNumeric.Value = Math.Min(rule.MaxLength, 9999); // 限制最大值

                // 设置格式应用范围的单选按钮状态
                if (wholeParagraphRadio != null)
                    wholeParagraphRadio.Checked = rule.ApplyScope == FormatApplyScope.WholeParagrph;

                if (untilPeriodRadio != null)
                    untilPeriodRadio.Checked = rule.ApplyScope == FormatApplyScope.UntilFirstPeriod;

                if (untilLineBreakRadio != null)
                    untilLineBreakRadio.Checked = rule.ApplyScope == FormatApplyScope.UntilLineBreak;

                if (untilSpecificCharInclusiveRadio != null)
                    untilSpecificCharInclusiveRadio.Checked = rule.ApplyScope == FormatApplyScope.UntilSpecificCharInclusive;

                if (untilSpecificCharExclusiveRadio != null)
                    untilSpecificCharExclusiveRadio.Checked = rule.ApplyScope == FormatApplyScope.UntilSpecificCharExclusive;

                if (betweenPeriodsRadio != null)
                    betweenPeriodsRadio.Checked = rule.ApplyScope == FormatApplyScope.BetweenPeriods;

                if (fromStartToMatchInclusiveRadio != null)
                    fromStartToMatchInclusiveRadio.Checked = rule.ApplyScope == FormatApplyScope.FromStartToMatchInclusive;

                if (fromStartToMatchExclusiveRadio != null)
                    fromStartToMatchExclusiveRadio.Checked = rule.ApplyScope == FormatApplyScope.FromStartToMatchExclusive;

                if (fromCharToMatchInclusiveRadio != null)
                    fromCharToMatchInclusiveRadio.Checked = rule.ApplyScope == FormatApplyScope.FromCharToMatchInclusive;

                if (fromCharToMatchExclusiveRadio != null)
                    fromCharToMatchExclusiveRadio.Checked = rule.ApplyScope == FormatApplyScope.FromCharToMatchExclusive;

                // 加载结束字符到相应的文本框
                if (inclusiveCharTextBox != null && rule.ApplyScope == FormatApplyScope.UntilSpecificCharInclusive)
                {
                    inclusiveCharTextBox.Text = rule.EndingChar ?? "";
                }

                if (exclusiveCharTextBox != null && rule.ApplyScope == FormatApplyScope.UntilSpecificCharExclusive)
                {
                    exclusiveCharTextBox.Text = rule.EndingChar ?? "";
                }

                if (startingCharInclusiveTextBox != null && rule.ApplyScope == FormatApplyScope.FromCharToMatchInclusive)
                {
                    startingCharInclusiveTextBox.Text = rule.StartingChar ?? "";
                }

                if (startingCharExclusiveTextBox != null && rule.ApplyScope == FormatApplyScope.FromCharToMatchExclusive)
                {
                    startingCharExclusiveTextBox.Text = rule.StartingChar ?? "";
                }

                // 加载段落格式
                // 对齐方式
                if (alignmentComboBox != null)
                    alignmentComboBox.SelectedIndex = (int)rule.Alignment;

                // 大纲级别
                if (outlineLevelComboBox != null)
                {
                    outlineLevelComboBox.SelectedIndexChanged -= OutlineLevelComboBox_SelectedIndexChanged;

                    // 设置大纲级别
                    int outlineLevelIndex = 0;
                    if (rule.OutlineLevel != AW.OutlineLevel.BodyText)
                    {
                        outlineLevelIndex = (int)rule.OutlineLevel + 1;
                    }
                    outlineLevelComboBox.SelectedIndex = outlineLevelIndex;

                    // 重新绑定事件
                    outlineLevelComboBox.SelectedIndexChanged += OutlineLevelComboBox_SelectedIndexChanged;
                }

                // 对齐方向
                if (textDirectionComboBox != null)
                    textDirectionComboBox.SelectedIndex = rule.TextDirection == Models.TextDirection.RightToLeft ? 1 : 0;

                // 缩进
                if (leftIndentNumeric != null)
                    leftIndentNumeric.Value = Math.Min((decimal)rule.LeftIndent, leftIndentNumeric.Maximum);

                if (rightIndentNumeric != null)
                    rightIndentNumeric.Value = Math.Min((decimal)rule.RightIndent, rightIndentNumeric.Maximum);

                // 特殊缩进
                if (indentTypeComboBox != null && indentValueNumeric != null && indentUnitComboBox != null)
                {
                    if (rule.SpecialIndent == Models.SpecialIndent.FirstLine)
                    {
                        indentTypeComboBox.SelectedIndex = 1; // 首行缩进

                        // 如果规则中的值为0或默认值14.4磅，则设置为2字符
                        if (rule.SpecialIndentValue == 0 || Math.Abs(rule.SpecialIndentValue - 14.4) < 0.1)
                        {
                            indentValueNumeric.Value = 2.0m; // 默认2字符
                            indentUnitComboBox.SelectedIndex = 0; // 字符单位
                            indentUnitComboBox.Tag = IndentUnit.Characters;
                        }
                        else
                        {
                            // 将磅值转换为字符值显示
                            double charactersValue = UnitConverter.ConvertFromPoints(rule.SpecialIndentValue, IndentUnit.Characters);
                            indentValueNumeric.Value = Math.Min((decimal)charactersValue, indentValueNumeric.Maximum);
                            indentUnitComboBox.SelectedIndex = 0; // 默认字符单位
                            indentUnitComboBox.Tag = IndentUnit.Characters;
                        }
                    }
                    else if (rule.SpecialIndent == Models.SpecialIndent.Hanging)
                    {
                        indentTypeComboBox.SelectedIndex = 2; // 悬挂缩进

                        // 如果规则中的值为0或默认值14.4磅，则设置为2字符
                        if (rule.SpecialIndentValue == 0 || Math.Abs(rule.SpecialIndentValue - 14.4) < 0.1)
                        {
                            indentValueNumeric.Value = 2.0m; // 默认2字符
                            indentUnitComboBox.SelectedIndex = 0; // 字符单位
                            indentUnitComboBox.Tag = IndentUnit.Characters;
                        }
                        else
                        {
                            // 将磅值转换为字符值显示
                            double charactersValue = UnitConverter.ConvertFromPoints(rule.SpecialIndentValue, IndentUnit.Characters);
                            indentValueNumeric.Value = Math.Min((decimal)charactersValue, indentValueNumeric.Maximum);
                            indentUnitComboBox.SelectedIndex = 0; // 默认字符单位
                            indentUnitComboBox.Tag = IndentUnit.Characters;
                        }
                    }
                    else
                    {
                        indentTypeComboBox.SelectedIndex = 0; // 无
                        indentValueNumeric.Value = 2.0m; // 即使选择"无"，也设置默认值为2字符，以便用户切换时有合适的默认值
                        indentUnitComboBox.SelectedIndex = 0; // 字符单位
                        indentUnitComboBox.Tag = IndentUnit.Characters;
                    }
                }

                // 加载间距
                if (beforeSpacingNumeric != null)
                    beforeSpacingNumeric.Value = Math.Min((decimal)rule.SpaceBefore, beforeSpacingNumeric.Maximum);

                if (afterSpacingNumeric != null)
                    afterSpacingNumeric.Value = Math.Min((decimal)rule.SpaceAfter, afterSpacingNumeric.Maximum);

                // 行距
                if (lineSpacingTypeComboBox != null && lineSpacingValueNumeric != null)
                {
                    if (rule.LineSpacingRule == AW.LineSpacingRule.Multiple)
                    {
                        // 直接使用倍数值
                        if (Math.Abs(rule.LineSpacing - 1.0) < 0.1)
                        {
                            lineSpacingTypeComboBox.SelectedIndex = 0; // 单倍行距
                            lineSpacingValueNumeric.Value = 12.0m; // 显示对应的磅值
                        }
                        else if (Math.Abs(rule.LineSpacing - 1.5) < 0.1)
                        {
                            lineSpacingTypeComboBox.SelectedIndex = 1; // 1.5倍行距
                            lineSpacingValueNumeric.Value = 18.0m; // 显示对应的磅值
                        }
                        else if (Math.Abs(rule.LineSpacing - 2.0) < 0.1)
                        {
                            lineSpacingTypeComboBox.SelectedIndex = 2; // 2倍行距
                            lineSpacingValueNumeric.Value = 24.0m; // 显示对应的磅值
                        }
                        else
                        {
                            lineSpacingTypeComboBox.SelectedIndex = 3; // 多倍行距
                            // 多倍行距直接显示倍数值
                            decimal multipleValue = Math.Min((decimal)rule.LineSpacing, lineSpacingValueNumeric.Maximum);
                            lineSpacingValueNumeric.Value = multipleValue > 0 ? multipleValue : 1.0m;
                        }
                    }
                    else if (rule.LineSpacingRule == AW.LineSpacingRule.Exactly ||
                             rule.LineSpacingRule == AW.LineSpacingRule.AtLeast)
                    {
                        // 固定磅值或最小磅值的情况
                        double lineSpacingValue = rule.LineSpacing;

                        // 根据LineSpacingRule确定选择哪个选项
                        if (rule.LineSpacingRule == AW.LineSpacingRule.Exactly)
                        {
                            lineSpacingTypeComboBox.SelectedIndex = 5; // 固定值
                        }
                        else if (rule.LineSpacingRule == AW.LineSpacingRule.AtLeast)
                        {
                            lineSpacingTypeComboBox.SelectedIndex = 4; // 最小值
                        }
                        else
                        {
                            lineSpacingTypeComboBox.SelectedIndex = 5; // 默认固定值
                        }
                        decimal pointValue = Math.Min((decimal)lineSpacingValue, lineSpacingValueNumeric.Maximum);
                        lineSpacingValueNumeric.Value = pointValue > 0 ? pointValue : 28.0m; // 默认28磅
                    }
                    else
                    {
                        // 默认使用单倍行距
                        lineSpacingTypeComboBox.SelectedIndex = 0;
                        lineSpacingValueNumeric.Value = 12.0m;
                    }
                }

                // 加载字体格式
                // 字体
                if (!string.IsNullOrEmpty(rule.FontName))
                {
                    if (chineseFontComboBox != null && chineseFontComboBox.Items.Count > 0)
                    {
                        int index = chineseFontComboBox.Items.IndexOf(rule.FontName);
                        if (index >= 0)
                        {
                            chineseFontComboBox.SelectedIndex = index;

                            if (westernFontComboBox != null && westernFontComboBox.Items.Count > index)
                                westernFontComboBox.SelectedIndex = index;

                            if (complexScriptFontComboBox != null && complexScriptFontComboBox.Items.Count > index)
                                complexScriptFontComboBox.SelectedIndex = index;
                        }
                    }
                }

                // 字体大小
                if (rule.FontSize > 0)
                {
                    // 中文字体大小处理
                    if (chineseFontSizeComboBox != null && chineseFontSizeNumeric != null)
                    {
                        double fontSize = rule.ChineseFontSize > 0 ? rule.ChineseFontSize : rule.FontSize;
                        chineseFontSizeNumeric.Value = Math.Min((decimal)fontSize, chineseFontSizeNumeric.Maximum);

                        // 检查是否匹配预设字号
                        string matchedFontName = GetChineseFontSizeName(fontSize);
                        if (!char.IsDigit(matchedFontName[0])) // 如果是中文字号名称
                        {
                            // 查找并选择对应的预设字号
                            for (int i = 0; i < chineseFontSizeComboBox.Items.Count - 1; i++) // 排除"自定义"项
                            {
                                if (chineseFontSizeComboBox.Items[i]?.ToString() == matchedFontName)
                                {
                                    chineseFontSizeComboBox.SelectedIndex = i;
                                    chineseFontSizeNumeric.Visible = false;
                                    if (chineseFontSizeUnitLabel != null)
                                        chineseFontSizeUnitLabel.Visible = false;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            // 使用自定义字号
                            chineseFontSizeComboBox.SelectedIndex = chineseFontSizeComboBox.Items.Count - 1; // 选择"自定义"
                            chineseFontSizeNumeric.Visible = true;
                            if (chineseFontSizeUnitLabel != null)
                                chineseFontSizeUnitLabel.Visible = true;
                        }
                    }

                    // 西文字体大小处理
                    if (westernFontSizeComboBox != null && westernFontSizeNumeric != null)
                    {
                        double fontSize = rule.WesternFontSize > 0 ? rule.WesternFontSize : rule.FontSize;
                        westernFontSizeNumeric.Value = Math.Min((decimal)fontSize, westernFontSizeNumeric.Maximum);

                        // 检查是否匹配预设字号
                        string matchedFontName = GetChineseFontSizeName(fontSize);
                        if (!char.IsDigit(matchedFontName[0])) // 如果是中文字号名称
                        {
                            // 查找并选择对应的预设字号
                            for (int i = 0; i < westernFontSizeComboBox.Items.Count - 1; i++) // 排除"自定义"项
                            {
                                if (westernFontSizeComboBox.Items[i]?.ToString() == matchedFontName)
                                {
                                    westernFontSizeComboBox.SelectedIndex = i;
                                    westernFontSizeNumeric.Visible = false;
                                    if (westernFontSizeUnitLabel != null)
                                        westernFontSizeUnitLabel.Visible = false;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            // 使用自定义字号
                            westernFontSizeComboBox.SelectedIndex = westernFontSizeComboBox.Items.Count - 1; // 选择"自定义"
                            westernFontSizeNumeric.Visible = true;
                            if (westernFontSizeUnitLabel != null)
                                westernFontSizeUnitLabel.Visible = true;
                        }
                    }

                    // 复杂文字字体大小处理
                    if (complexScriptFontSizeComboBox != null && complexScriptFontSizeNumeric != null)
                    {
                        double fontSize = rule.ComplexScriptFontSize > 0 ? rule.ComplexScriptFontSize : rule.FontSize;
                        complexScriptFontSizeNumeric.Value = Math.Min((decimal)fontSize, complexScriptFontSizeNumeric.Maximum);

                        // 检查是否匹配预设字号
                        string matchedFontName = GetChineseFontSizeName(fontSize);
                        if (!char.IsDigit(matchedFontName[0])) // 如果是中文字号名称
                        {
                            // 查找并选择对应的预设字号
                            for (int i = 0; i < complexScriptFontSizeComboBox.Items.Count - 1; i++) // 排除"自定义"项
                            {
                                if (complexScriptFontSizeComboBox.Items[i]?.ToString() == matchedFontName)
                                {
                                    complexScriptFontSizeComboBox.SelectedIndex = i;
                                    complexScriptFontSizeNumeric.Visible = false;
                                    if (complexScriptFontSizeUnitLabel != null)
                                        complexScriptFontSizeUnitLabel.Visible = false;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            // 使用自定义字号
                            complexScriptFontSizeComboBox.SelectedIndex = complexScriptFontSizeComboBox.Items.Count - 1; // 选择"自定义"
                            complexScriptFontSizeNumeric.Visible = true;
                            if (complexScriptFontSizeUnitLabel != null)
                                complexScriptFontSizeUnitLabel.Visible = true;
                        }
                    }
                }

                // 字体样式
                int fontStyleIndex = 0;
                if (rule.Bold && rule.Italic)
                {
                    fontStyleIndex = 3; // 加粗倾斜
                }
                else if (rule.Bold)
                {
                    fontStyleIndex = 2; // 加粗
                }
                else if (rule.Italic)
                {
                    fontStyleIndex = 1; // 倾斜
                }

                if (chineseFontStyleComboBox != null && chineseFontStyleComboBox.Items.Count > fontStyleIndex)
                    chineseFontStyleComboBox.SelectedIndex = fontStyleIndex;

                if (westernFontStyleComboBox != null && westernFontStyleComboBox.Items.Count > fontStyleIndex)
                    westernFontStyleComboBox.SelectedIndex = fontStyleIndex;

                if (complexScriptFontStyleComboBox != null && complexScriptFontStyleComboBox.Items.Count > fontStyleIndex)
                    complexScriptFontStyleComboBox.SelectedIndex = fontStyleIndex;

                // 字体颜色
                if (rule.FontColor.HasValue && fontColorPanel != null)
                {
                    fontColorPanel.BackColor = rule.FontColor.Value;
                }

                // 加载文字突出显示颜色（背景色）
                if (rule.HighlightColor.HasValue && highlightColorPanel != null)
                {
                    highlightColorPanel.BackColor = rule.HighlightColor.Value;
                }

                // 加载文本效果与装饰设置
                try
                {
                    if (textEffectsPanel != null && textEffectsPanel.Controls.Count > 1)
                    {
                        // 获取实际的effectsContainer控件
                        var container = textEffectsPanel.Controls[1] as TableLayoutPanel;
                        if (container != null && container.Controls.Count > 0)
                        {
                            // 查找下划线CheckBox和ComboBox - 第一项
                            if (container.Controls.Count > 0)
                            {
                                var underlinePanel = container.Controls[0] as TableLayoutPanel;
                                if (underlinePanel != null && underlinePanel.Controls.Count >= 2)
                                {
                                    var underlineCheckBox = underlinePanel.Controls[0] as CheckBox;
                                    var underlineStyleComboBox = underlinePanel.Controls[1] as ComboBox;
                                    if (underlineCheckBox != null && underlineStyleComboBox != null)
                                    {
                                        underlineCheckBox.Checked = rule.UnderlineEnabled;
                                        underlineStyleComboBox.Enabled = rule.UnderlineEnabled;
                                        underlineStyleComboBox.SelectedIndex = rule.UnderlineStyle;
                                    }
                                }
                            }

                            // 查找删除线CheckBox和ComboBox - 第二项
                            if (container.Controls.Count > 1)
                            {
                                var strikethroughPanel = container.Controls[1] as TableLayoutPanel;
                                if (strikethroughPanel != null && strikethroughPanel.Controls.Count >= 2)
                                {
                                    var strikethroughCheckBox = strikethroughPanel.Controls[0] as CheckBox;
                                    var strikethroughStyleComboBox = strikethroughPanel.Controls[1] as ComboBox;
                                    if (strikethroughCheckBox != null && strikethroughStyleComboBox != null)
                                    {
                                        strikethroughCheckBox.Checked = rule.Strikethrough;
                                        strikethroughStyleComboBox.Enabled = rule.Strikethrough;
                                        strikethroughStyleComboBox.SelectedIndex = rule.StrikethroughStyle;
                                    }
                                }
                            }

                            // 查找小型大写字母CheckBox - 第三项
                            if (container.Controls.Count > 2)
                            {
                                var smallCapsPanel = container.Controls[2] as TableLayoutPanel;
                                if (smallCapsPanel != null && smallCapsPanel.Controls.Count >= 1)
                                {
                                    var smallCapsCheckBox = smallCapsPanel.Controls[0] as CheckBox;
                                    if (smallCapsCheckBox != null)
                                    {
                                        smallCapsCheckBox.Checked = rule.SmallCaps;
                                    }
                                }
                            }

                            // 查找隐藏文本CheckBox - 第四项
                            if (container.Controls.Count > 3)
                            {
                                var hiddenTextPanel = container.Controls[3] as TableLayoutPanel;
                                if (hiddenTextPanel != null && hiddenTextPanel.Controls.Count >= 1)
                                {
                                    var hiddenTextCheckBox = hiddenTextPanel.Controls[0] as CheckBox;
                                    if (hiddenTextCheckBox != null)
                                    {
                                        hiddenTextCheckBox.Checked = rule.Hidden;
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 安全处理任何可能的异常，避免加载设置失败
                    System.Diagnostics.Debug.WriteLine($"加载文本效果设置时出错: {ex.Message}");
                }

                // 在LoadSettings方法中添加以下代码，位置在其他控件状态加载的位置附近
                // 加载段落格式各功能的启用状态
                if (alignmentEnableCheckBox != null)
                    alignmentEnableCheckBox.Checked = rule.EnableAlignment;

                if (outlineLevelEnableCheckBox != null)
                    outlineLevelEnableCheckBox.Checked = rule.EnableOutlineLevel;

                if (textDirectionEnableCheckBox != null)
                    textDirectionEnableCheckBox.Checked = rule.EnableTextDirection;

                if (indentEnableCheckBox != null)
                    indentEnableCheckBox.Checked = rule.EnableIndent;

                if (spacingEnableCheckBox != null)
                    spacingEnableCheckBox.Checked = rule.EnableSpacing;

                // 加载字体格式各功能的启用状态
                if (chineseFontEnableCheckBox != null)
                    chineseFontEnableCheckBox.Checked = rule.EnableChineseFont;

                if (westernFontEnableCheckBox != null)
                    westernFontEnableCheckBox.Checked = rule.EnableWesternFont;

                if (complexScriptFontEnableCheckBox != null)
                    complexScriptFontEnableCheckBox.Checked = rule.EnableComplexScriptFont;

                if (fontColorEnableCheckBox != null)
                    fontColorEnableCheckBox.Checked = rule.EnableFontColor;

                if (highlightColorEnableCheckBox != null)
                    highlightColorEnableCheckBox.Checked = rule.EnableHighlightColor;

                // 加载段落前分页符设置
                if (pageBreakBeforeCheckBox != null)
                    pageBreakBeforeCheckBox.Checked = rule.PageBreakBefore;

                // 更新格式应用范围控件状态
                UpdateFormatApplyScopeControls();
            }
            catch (Exception ex)
            {
                // 全局异常捕获，确保不会因为控件初始化问题导致表单加载失败
                System.Diagnostics.Debug.WriteLine($"加载设置时发生错误: {ex.Message}");
                System.Windows.Forms.MessageBox.Show($"加载设置时发生错误: {ex.Message}", "错误", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        private void SaveSettings()
        {
            // 首先保存条件组合，确保条件组合被正确保存
            SaveConditionsToRule();

            // 如果没有条件组合，则使用单个条件
            if (conditionListView?.Items.Count == 0)
            {
                // 保存匹配条件，根据选中的单选按钮决定保存哪个条件
                if (startsWithRadio?.Checked == true) {
                    rule.StartWithPatterns = SplitPatterns(startPatternsTextBox?.Text ?? string.Empty);
                    rule.ContainsPatterns = new List<string>(); // 清空其他条件
                    rule.EndWithPatterns = new List<string>();
                    rule.Pattern = string.Empty;
                }
                else if (containsRadio?.Checked == true) {
                    rule.StartWithPatterns = new List<string>();
                    rule.ContainsPatterns = SplitPatterns(containPatternsTextBox?.Text ?? string.Empty);
                    rule.EndWithPatterns = new List<string>();
                    rule.Pattern = string.Empty;
                }
                else if (endsWithRadio?.Checked == true) {
                    rule.StartWithPatterns = new List<string>();
                    rule.ContainsPatterns = new List<string>();
                    rule.EndWithPatterns = SplitPatterns(endPatternsTextBox?.Text ?? string.Empty);
                    rule.Pattern = string.Empty;
                }
                else if (regexRadio?.Checked == true) {
                    rule.StartWithPatterns = new List<string>();
                    rule.ContainsPatterns = new List<string>();
                    rule.EndWithPatterns = new List<string>();
                    rule.Pattern = regexPatternTextBox?.Text ?? string.Empty;
                }
                else if (paragraphPositionRadio?.Checked == true) {
                    rule.StartWithPatterns = new List<string>();
                    rule.ContainsPatterns = new List<string>();
                    rule.EndWithPatterns = new List<string>();
                    rule.Pattern = string.Empty;
                    rule.UseParagraphPosition = true;
                    rule.ParagraphPosition = (int)(paragraphPositionNumeric?.Value ?? 1);
                }
            }

            rule.CaseSensitive = caseSensitiveCheckBox?.Checked ?? false;
            rule.MinLength = (int)(minLengthNumeric?.Value ?? 0);
            rule.MaxLength = (maxLengthNumeric?.Value ?? 0) >= (maxLengthNumeric?.Maximum ?? 0) ? int.MaxValue : (int)(maxLengthNumeric?.Value ?? 0);

            // 保存格式应用范围
            if (wholeParagraphRadio?.Checked == true)
                rule.ApplyScope = FormatApplyScope.WholeParagrph;
            else if (untilPeriodRadio?.Checked == true)
                rule.ApplyScope = FormatApplyScope.UntilFirstPeriod;
            else if (untilLineBreakRadio?.Checked == true)
                rule.ApplyScope = FormatApplyScope.UntilLineBreak;
            else if (untilSpecificCharInclusiveRadio?.Checked == true)
            {
                rule.ApplyScope = FormatApplyScope.UntilSpecificCharInclusive;
                rule.EndingChar = inclusiveCharTextBox?.Text ?? string.Empty;
            }
            else if (untilSpecificCharExclusiveRadio?.Checked == true)
            {
                rule.ApplyScope = FormatApplyScope.UntilSpecificCharExclusive;
                rule.EndingChar = exclusiveCharTextBox?.Text ?? string.Empty;
            }
            else if (betweenPeriodsRadio?.Checked == true)
                rule.ApplyScope = FormatApplyScope.BetweenPeriods;
            else if (fromStartToMatchInclusiveRadio?.Checked == true)
                rule.ApplyScope = FormatApplyScope.FromStartToMatchInclusive;
            else if (fromStartToMatchExclusiveRadio?.Checked == true)
                rule.ApplyScope = FormatApplyScope.FromStartToMatchExclusive;
            else if (fromCharToMatchInclusiveRadio?.Checked == true)
            {
                rule.ApplyScope = FormatApplyScope.FromCharToMatchInclusive;
                rule.StartingChar = startingCharInclusiveTextBox?.Text ?? string.Empty;
            }
            else if (fromCharToMatchExclusiveRadio?.Checked == true)
            {
                rule.ApplyScope = FormatApplyScope.FromCharToMatchExclusive;
                rule.StartingChar = startingCharExclusiveTextBox?.Text ?? string.Empty;
            }

            // 保存段落格式
            // 对齐方式
            if (alignmentComboBox != null)
                rule.Alignment = (AW.ParagraphAlignment)alignmentComboBox.SelectedIndex;

            // 大纲级别
            if (outlineLevelComboBox != null)
            {
                if (outlineLevelComboBox.SelectedIndex == 0)
                {
                    rule.OutlineLevel = AW.OutlineLevel.BodyText;
                }
                else
                {
                    rule.OutlineLevel = (AW.OutlineLevel)(outlineLevelComboBox.SelectedIndex - 1);
                }
            }

            // 对齐方向
            if (textDirectionComboBox != null)
                rule.TextDirection = textDirectionComboBox.SelectedIndex == 1 ?
                    Models.TextDirection.RightToLeft : Models.TextDirection.LeftToRight;

            // 缩进
            if (leftIndentNumeric != null)
                rule.LeftIndent = (double)leftIndentNumeric.Value;
            if (rightIndentNumeric != null)
                rule.RightIndent = (double)rightIndentNumeric.Value;

            // 特殊缩进
            if (indentTypeComboBox != null && indentValueNumeric != null && indentUnitComboBox != null)
            {
                if (indentTypeComboBox.SelectedIndex == 1)
                {
                    rule.SpecialIndent = Models.SpecialIndent.FirstLine;
                    // 将UI中的值转换为磅值保存
                    var currentUnit = (IndentUnit)(indentUnitComboBox.Tag ?? IndentUnit.Characters);
                    rule.SpecialIndentValue = UnitConverter.ConvertToPoints((double)indentValueNumeric.Value, currentUnit);
                }
                else if (indentTypeComboBox.SelectedIndex == 2)
                {
                    rule.SpecialIndent = Models.SpecialIndent.Hanging;
                    // 将UI中的值转换为磅值保存
                    var currentUnit = (IndentUnit)(indentUnitComboBox.Tag ?? IndentUnit.Characters);
                    rule.SpecialIndentValue = UnitConverter.ConvertToPoints((double)indentValueNumeric.Value, currentUnit);
                }
                else
                {
                    rule.SpecialIndent = Models.SpecialIndent.None;
                    rule.SpecialIndentValue = 0;
                }
            }

            // 保存间距
            if (beforeSpacingNumeric != null)
                rule.SpaceBefore = (double)beforeSpacingNumeric.Value;
            if (afterSpacingNumeric != null)
                rule.SpaceAfter = (double)afterSpacingNumeric.Value;

            // 行距
            if (lineSpacingTypeComboBox != null && lineSpacingValueNumeric != null)
            {
                switch (lineSpacingTypeComboBox.SelectedIndex)
                {
                    case 0: // 单倍行距
                        rule.LineSpacingRule = AW.LineSpacingRule.Multiple;
                        rule.LineSpacing = 1.0; // 单倍行距使用倍数值1.0
                        break;
                    case 1: // 1.5倍行距
                        rule.LineSpacingRule = AW.LineSpacingRule.Multiple;
                        rule.LineSpacing = 1.5; // 1.5倍行距使用倍数值1.5
                        break;
                    case 2: // 2倍行距
                        rule.LineSpacingRule = AW.LineSpacingRule.Multiple;
                        rule.LineSpacing = 2.0; // 2倍行距使用倍数值2.0
                        break;
                    case 3: // 多倍行距
                        rule.LineSpacingRule = AW.LineSpacingRule.Multiple;
                        // 多倍行距直接使用倍数值，UI中显示的就是倍数值
                        rule.LineSpacing = Math.Max(0.1, (double)lineSpacingValueNumeric.Value);
                        break;
                    case 4: // 最小值
                        rule.LineSpacingRule = AW.LineSpacingRule.AtLeast;
                        // 将UI中的值转换为磅值保存
                        if (lineSpacingUnitComboBox != null)
                        {
                            var currentUnit = (LineSpacingUnit)(lineSpacingUnitComboBox.Tag ?? LineSpacingUnit.Points);
                            rule.LineSpacing = UnitConverter.ConvertToPoints((double)lineSpacingValueNumeric.Value, currentUnit);
                        }
                        else
                        {
                            rule.LineSpacing = Math.Max(0.1, (double)lineSpacingValueNumeric.Value); // 默认按磅处理
                        }
                        break;
                    case 5: // 固定值
                        rule.LineSpacingRule = AW.LineSpacingRule.Exactly;
                        // 将UI中的值转换为磅值保存
                        if (lineSpacingUnitComboBox != null)
                        {
                            var currentUnit = (LineSpacingUnit)(lineSpacingUnitComboBox.Tag ?? LineSpacingUnit.Points);
                            rule.LineSpacing = UnitConverter.ConvertToPoints((double)lineSpacingValueNumeric.Value, currentUnit);
                        }
                        else
                        {
                            rule.LineSpacing = Math.Max(0.1, (double)lineSpacingValueNumeric.Value); // 默认按磅处理
                        }
                        break;
                }
            }

            // 保存字体格式
            // 中文字体设置
            if (chineseFontComboBox?.SelectedIndex >= 0 && chineseFontComboBox.SelectedItem != null)
            {
                rule.ChineseFontName = chineseFontComboBox.SelectedItem.ToString() ?? "宋体";
                rule.FontName = chineseFontComboBox.SelectedItem.ToString() ?? "宋体"; // 兼容性设置
            }

            // 中文字体大小
            if (chineseFontSizeNumeric != null)
            {
                rule.ChineseFontSize = (double)chineseFontSizeNumeric.Value;
                rule.FontSize = (double)chineseFontSizeNumeric.Value; // 兼容性设置
            }

            // 中文字体样式
            if (chineseFontStyleComboBox != null)
            {
                // 设置ChineseFontStyle属性
                switch (chineseFontStyleComboBox.SelectedIndex)
                {
                    case 1: // 倾斜
                        rule.ChineseFontStyle = FontStyle.Italic;
                        break;
                    case 2: // 加粗
                        rule.ChineseFontStyle = FontStyle.Bold;
                        break;
                    case 3: // 加粗倾斜
                        rule.ChineseFontStyle = FontStyle.Bold | FontStyle.Italic;
                        break;
                    default: // 常规
                        rule.ChineseFontStyle = FontStyle.Regular;
                        break;
                }

                // 同时设置兼容性属性Bold和Italic
                switch (chineseFontStyleComboBox.SelectedIndex)
                {
                    case 1: // 倾斜
                        rule.Italic = true;
                        rule.Bold = false;
                        break;
                    case 2: // 加粗
                        rule.Italic = false;
                        rule.Bold = true;
                        break;
                    case 3: // 加粗倾斜
                        rule.Italic = true;
                        rule.Bold = true;
                        break;
                    default: // 常规
                        rule.Italic = false;
                        rule.Bold = false;
                        break;
                }
            }

            // 西文字体设置
            if (westernFontComboBox?.SelectedIndex >= 0 && westernFontComboBox.SelectedItem != null)
            {
                rule.WesternFontName = westernFontComboBox.SelectedItem.ToString() ?? "Times New Roman";
            }

            // 西文字体大小
            if (westernFontSizeNumeric != null)
            {
                rule.WesternFontSize = (double)westernFontSizeNumeric.Value;
            }

            // 西文字体样式
            if (westernFontStyleComboBox != null)
            {
                switch (westernFontStyleComboBox.SelectedIndex)
                {
                    case 1: // 倾斜
                        rule.WesternFontStyle = FontStyle.Italic;
                        break;
                    case 2: // 加粗
                        rule.WesternFontStyle = FontStyle.Bold;
                        break;
                    case 3: // 加粗倾斜
                        rule.WesternFontStyle = FontStyle.Bold | FontStyle.Italic;
                        break;
                    default: // 常规
                        rule.WesternFontStyle = FontStyle.Regular;
                        break;
                }
            }

            // 复杂文字字体设置
            if (complexScriptFontComboBox?.SelectedIndex >= 0 && complexScriptFontComboBox.SelectedItem != null)
            {
                rule.ComplexScriptFontName = complexScriptFontComboBox.SelectedItem.ToString() ?? "Arial";
            }

            // 复杂文字字体大小
            if (complexScriptFontSizeNumeric != null)
            {
                rule.ComplexScriptFontSize = (double)complexScriptFontSizeNumeric.Value;
            }

            // 复杂文字字体样式
            if (complexScriptFontStyleComboBox != null)
            {
                switch (complexScriptFontStyleComboBox.SelectedIndex)
                {
                    case 1: // 倾斜
                        rule.ComplexScriptFontStyle = FontStyle.Italic;
                        break;
                    case 2: // 加粗
                        rule.ComplexScriptFontStyle = FontStyle.Bold;
                        break;
                    case 3: // 加粗倾斜
                        rule.ComplexScriptFontStyle = FontStyle.Bold | FontStyle.Italic;
                        break;
                    default: // 常规
                        rule.ComplexScriptFontStyle = FontStyle.Regular;
                        break;
                }
            }

            // 字体颜色
            if (fontColorPanel != null)
            {
                if (fontColorPanel.BackColor == Color.Transparent)
                {
                    rule.FontColor = Color.Black; // 如果是透明，设置为黑色
                }
                else
                {
                    rule.FontColor = fontColorPanel.BackColor;
                }
            }

            // 保存文字突出显示颜色（背景色）
            if (highlightColorPanel != null)
            {
                if (highlightColorPanel.BackColor == Color.Transparent)
                {
                    rule.HighlightColor = null; // 如果是透明，设置为null（无色）
                }
                else
                {
                    rule.HighlightColor = highlightColorPanel.BackColor;
                }
            }

            // 确保规则名称与预设标题一致
            rule.Name = presetTitle;

            // 在SaveSettings方法中添加以下代码，位置在其他控件状态保存的位置附近
            // 保存段落格式各功能的启用状态
            rule.EnableAlignment = alignmentEnableCheckBox?.Checked ?? false;
            rule.EnableOutlineLevel = outlineLevelEnableCheckBox?.Checked ?? false;
            rule.EnableTextDirection = textDirectionEnableCheckBox?.Checked ?? false;
            rule.EnableIndent = indentEnableCheckBox?.Checked ?? false;
            rule.EnableSpacing = spacingEnableCheckBox?.Checked ?? false;

            // 保存字体格式各功能的启用状态
            rule.EnableChineseFont = chineseFontEnableCheckBox?.Checked ?? false;
            rule.EnableWesternFont = westernFontEnableCheckBox?.Checked ?? false;
            rule.EnableComplexScriptFont = complexScriptFontEnableCheckBox?.Checked ?? false;
            rule.EnableFontColor = fontColorEnableCheckBox?.Checked ?? false;
            rule.EnableHighlightColor = highlightColorEnableCheckBox?.Checked ?? false;

            // 保存段落前分页符设置
            rule.PageBreakBefore = pageBreakBeforeCheckBox?.Checked ?? false;

            // 保存字体格式总启用状态（注意：这里不再从文本效果复选框获取，而是从字体格式总启用控制获取）
            // rule.EnableFontFormat 已经在事件处理中实时更新，这里不需要重复设置

            // 保存文本效果与装饰设置
            if (textEffectsPanel != null && textEffectsPanel.Controls.Count > 0)
            {
                // 获取启用文本效果的复选框（这个只控制文本效果，不控制整个字体格式）
                var enableEffectsCheckBox = textEffectsPanel.Controls[0] as CheckBox;
                if (enableEffectsCheckBox != null)
                {
                    // 这里可以添加专门的文本效果启用属性，如果需要的话
                    // rule.EnableTextEffects = enableEffectsCheckBox.Checked;
                }

                // 获取效果容器
                if (textEffectsPanel.Controls.Count > 1 && textEffectsPanel.Controls[1] is TableLayoutPanel effectsContainer)
                {
                    // 遍历效果容器中的所有面板
                    foreach (Control control in effectsContainer.Controls)
                    {
                        if (control is TableLayoutPanel panel)
                        {
                            // 根据面板的标签文本确定要保存的属性
                            var label = panel.Controls[0] as Label;
                            if (label != null && panel.Controls.Count > 1)
                            {
                                var controlPanel = panel.Controls[1];

                                // 下划线设置
                                if (label.Text.Contains("下划线"))
                                {
                                    if (controlPanel is TableLayoutPanel underlinePanel && underlinePanel.Controls.Count >= 2)
                                    {
                                        var checkbox = underlinePanel.Controls[0] as CheckBox;
                                        var combobox = underlinePanel.Controls[1] as ComboBox;

                                        if (checkbox != null)
                                        {
                                            rule.UnderlineEnabled = checkbox.Checked;

                                            if (combobox != null && combobox.SelectedIndex >= 0)
                                            {
                                                rule.UnderlineStyle = combobox.SelectedIndex;
                                            }
                                        }
                                    }
                                }
                                // 删除线设置
                                else if (label.Text.Contains("删除线"))
                                {
                                    if (controlPanel is TableLayoutPanel strikethroughPanel && strikethroughPanel.Controls.Count >= 2)
                                    {
                                        var checkbox = strikethroughPanel.Controls[0] as CheckBox;
                                        var combobox = strikethroughPanel.Controls[1] as ComboBox;

                                        if (checkbox != null)
                                        {
                                            rule.Strikethrough = checkbox.Checked;

                                            if (combobox != null && combobox.SelectedIndex >= 0)
                                            {
                                                rule.StrikethroughStyle = combobox.SelectedIndex;
                                            }
                                        }
                                    }
                                }
                                // 小型大写字母
                                else if (label.Text.Contains("小型大写字母"))
                                {
                                    var checkbox = controlPanel as CheckBox;
                                    if (checkbox != null)
                                    {
                                        rule.SmallCaps = checkbox.Checked;
                                    }
                                }
                                // 隐藏文本
                                else if (label.Text.Contains("隐藏文本"))
                                {
                                    var checkbox = controlPanel as CheckBox;
                                    if (checkbox != null)
                                    {
                                        rule.Hidden = checkbox.Checked;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 保存文本间距与缩放设置
            // 字符间距
            var characterSpacingCheckbox = FindCheckBoxInPanel("字符间距");
            var characterSpacingComboBox = FindComboBoxInPanel("字符间距");
            if (characterSpacingCheckbox != null)
            {
                rule.EnableCharacterSpacing = characterSpacingCheckbox.Checked;

                if (characterSpacingComboBox != null && characterSpacingComboBox.SelectedIndex >= 0)
                {
                    // 根据选择设置CharacterSpacing的值
                    if (characterSpacingComboBox.SelectedIndex == 0) // 正常
                        rule.CharacterSpacing = 0;
                    else if (characterSpacingComboBox.SelectedIndex == 1) // 加宽
                        rule.CharacterSpacing = 2.0f; // 典型的加宽值
                    else if (characterSpacingComboBox.SelectedIndex == 2) // 紧缩
                        rule.CharacterSpacing = -1.0f; // 典型的紧缩值
                }
            }

            // 文本缩放
            var textScalingCheckbox = FindCheckBoxInPanel("文本缩放");
            var textScalingNumeric = FindNumericUpDownInPanel("文本缩放");
            if (textScalingCheckbox != null)
            {
                rule.EnableTextScaling = textScalingCheckbox.Checked;

                if (textScalingNumeric != null)
                {
                    rule.TextScaling = (float)textScalingNumeric.Value;
                }
            }

            // 保存中文排版标签页设置
            // 注意：中文排版标签页的设置已经通过事件处理器实时保存到rule中
            // 这里只需要保存悬挂标点设置（因为它在高级格式设置中被重复处理）
            var hangingPunctuationCheckBox = FindCheckBoxInPanel("允许标点悬挂");
            if (hangingPunctuationCheckBox != null)
            {
                rule.HangingPunctuation = hangingPunctuationCheckBox.Checked;
            }

            // 保存高级格式设置
            SaveAdvancedFormatSettings();

            // 保存字符单位设置
            SaveCharacterUnitSettings();
        }

        // 保存高级格式设置
        private void SaveAdvancedFormatSettings()
        {
            // 保存中西文间距设置
            var alphaCheckBox = FindCheckBoxInPanel("中文与西文之间自动加空格");
            var digitCheckBox = FindCheckBoxInPanel("中文与数字之间自动加空格");

            if (alphaCheckBox != null)
            {
                rule.AddSpaceBetweenFarEastAndAlpha = alphaCheckBox.Checked;
            }

            if (digitCheckBox != null)
            {
                rule.AddSpaceBetweenFarEastAndDigit = digitCheckBox.Checked;
            }

            // 保存基线对齐设置
            var baselineCheckBox = FindCheckBoxInPanel("基线对齐");
            var baselineComboBox = FindComboBoxInPanel("基线对齐");
            if (baselineCheckBox != null && baselineComboBox != null)
            {
                if (baselineCheckBox.Checked && baselineComboBox.SelectedIndex >= 0)
                {
                    rule.BaselineAlignment = (AW.BaselineAlignment)baselineComboBox.SelectedIndex;
                }
            }

            // 保存首字下沉设置
            var dropCapCheckBox = FindCheckBoxInPanel("首字下沉");
            var dropCapPositionComboBox = FindComboBoxInPanel("首字下沉位置");
            var dropCapLinesNumeric = FindNumericUpDownInPanel("首字下沉行数");
            if (dropCapCheckBox != null)
            {
                rule.EnableDropCap = dropCapCheckBox.Checked;

                if (dropCapPositionComboBox != null && dropCapPositionComboBox.SelectedIndex >= 0)
                {
                    rule.DropCapPosition = (AW.DropCapPosition)dropCapPositionComboBox.SelectedIndex;
                }

                if (dropCapLinesNumeric != null)
                {
                    rule.LinesToDrop = (int)dropCapLinesNumeric.Value;
                }
            }

            // 保存自动间距设置
            var spaceBeforeAutoCheckBox = FindCheckBoxInPanel("段前间距自动");
            var spaceAfterAutoCheckBox = FindCheckBoxInPanel("段后间距自动");

            if (spaceBeforeAutoCheckBox != null)
            {
                rule.SpaceBeforeAuto = spaceBeforeAutoCheckBox.Checked;
            }

            if (spaceAfterAutoCheckBox != null)
            {
                rule.SpaceAfterAuto = spaceAfterAutoCheckBox.Checked;
            }

            // 保存高级选项设置
            var suppressHyphensCheckBox = FindCheckBoxInPanel("禁止自动断字");
            if (suppressHyphensCheckBox != null)
            {
                rule.SuppressAutoHyphens = suppressHyphensCheckBox.Checked;
            }

            var snapToGridCheckBox = FindCheckBoxInPanel("对齐到网格");
            if (snapToGridCheckBox != null)
            {
                rule.SnapToGrid = snapToGridCheckBox.Checked;
            }

            var wordWrapCheckBox = FindCheckBoxInPanel("单词换行");
            if (wordWrapCheckBox != null)
            {
                rule.WordWrap = wordWrapCheckBox.Checked;
            }

            var farEastLineBreakCheckBox = FindCheckBoxInPanel("远东换行规则");
            if (farEastLineBreakCheckBox != null)
            {
                rule.FarEastLineBreakControl = farEastLineBreakCheckBox.Checked;
            }

            var mirrorIndentsCheckBox = FindCheckBoxInPanel("镜像缩进");
            if (mirrorIndentsCheckBox != null)
            {
                rule.MirrorIndents = mirrorIndentsCheckBox.Checked;
            }

            var suppressLineNumbersCheckBox = FindCheckBoxInPanel("禁止行号");
            if (suppressLineNumbersCheckBox != null)
            {
                rule.SuppressLineNumbers = suppressLineNumbersCheckBox.Checked;
            }

            // 保存行单位间距设置
            var lineUnitSpacingCheckBox = FindCheckBoxInPanel("行单位间距");
            var lineUnitBeforeNumeric = FindNumericUpDownInPanel("行单位段前");
            var lineUnitAfterNumeric = FindNumericUpDownInPanel("行单位段后");
            if (lineUnitSpacingCheckBox != null)
            {
                rule.EnableLineUnitSpacing = lineUnitSpacingCheckBox.Checked;

                if (lineUnitBeforeNumeric != null)
                {
                    rule.LineUnitBefore = (double)lineUnitBeforeNumeric.Value;
                }

                if (lineUnitAfterNumeric != null)
                {
                    rule.LineUnitAfter = (double)lineUnitAfterNumeric.Value;
                }
            }

            // 注意：悬挂标点设置已经在中文排版标签页中通过事件处理器实时保存
            // 这里不需要重复处理
        }

        // 保存字符单位设置
        private void SaveCharacterUnitSettings()
        {
            // 保存字符单位缩进设置
            var charUnitIndentCheckBox = FindCheckBoxInPanel("启用字符单位缩进");

            if (charUnitIndentCheckBox != null)
            {
                rule.EnableCharacterUnitIndent = charUnitIndentCheckBox.Checked;
            }

            // 保存字符单位间距设置
            var charUnitSpacingCheckBox = FindCheckBoxInPanel("启用字符单位间距");

            if (charUnitSpacingCheckBox != null)
            {
                rule.EnableCharacterUnitSpacing = charUnitSpacingCheckBox.Checked;
            }

            // 保存字符单位行距设置
            var enableLineSpacingCheckBox = FindCheckBoxInPanel("启用字符单位行距");
            if (enableLineSpacingCheckBox != null)
            {
                rule.EnableCharacterUnitLineSpacing = enableLineSpacingCheckBox.Checked;
            }

            // 注意：具体的数值设置已经通过事件处理器实时保存到rule中，这里只需要保存启用状态
        }

        private List<string> SplitPatterns(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return new List<string>();

            return input.Split(new[] { "===" }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => s.Trim())
                        .Where(s => !string.IsNullOrWhiteSpace(s))
                        .ToList();
        }

        // 辅助方法：创建带标签的控件
        private TableLayoutPanel CreateLabeledControl(string labelText, Func<Control> createControlFunc)
        {
            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 3, 0, 3) // 减小外边距
            };

            // 设置列宽比例
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80)); // 减小标签固定宽度
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100)); // 控件占用剩余空间

            // 添加标签
            var label = new Label
            {
                Text = labelText,
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(0, 2, 0, 0) // 减小内边距
            };
            panel.Controls.Add(label, 0, 0);

            // 添加控件
            var control = createControlFunc();
            panel.Controls.Add(control, 1, 0);

            return panel;
        }

        // 辅助方法：更新文本框状态
        private void UpdateTextBoxStates()
        {
            // 禁用所有文本框
            if (inclusiveCharTextBox != null) inclusiveCharTextBox.Enabled = false;
            if (exclusiveCharTextBox != null) exclusiveCharTextBox.Enabled = false;
            if (startingCharInclusiveTextBox != null) startingCharInclusiveTextBox.Enabled = false;
            if (startingCharExclusiveTextBox != null) startingCharExclusiveTextBox.Enabled = false;

            // 根据选中的单选按钮启用相应的文本框
            if (untilSpecificCharInclusiveRadio?.Checked == true && inclusiveCharTextBox != null)
                inclusiveCharTextBox.Enabled = true;
            else if (untilSpecificCharExclusiveRadio?.Checked == true && exclusiveCharTextBox != null)
                exclusiveCharTextBox.Enabled = true;
            else if (fromCharToMatchInclusiveRadio?.Checked == true && startingCharInclusiveTextBox != null)
                startingCharInclusiveTextBox.Enabled = true;
            else if (fromCharToMatchExclusiveRadio?.Checked == true && startingCharExclusiveTextBox != null)
                startingCharExclusiveTextBox.Enabled = true;
        }

        // 添加新的条件操作方法
        private void AddConditionButton_Click(object? sender, EventArgs e)
        {
            // 如果正在编辑条件，则保存编辑
            if (editingConditionItem != null)
            {
                SaveEditedCondition();
                return;
            }

            // 验证是否有选择匹配条件
            if ((startsWithRadio?.Checked != true) && (containsRadio?.Checked != true) &&
                (endsWithRadio?.Checked != true) && (regexRadio?.Checked != true) &&
                (paragraphPositionRadio?.Checked != true))
            {
                MessageBox.Show("请先选择一种匹配条件类型", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            string conditionType = "";
            string conditionValue = "";

            if (startsWithRadio?.Checked == true && startPatternsTextBox != null)
            {
                conditionType = "段落匹配开头";
                conditionValue = startPatternsTextBox.Text;
            }
            else if (containsRadio?.Checked == true && containPatternsTextBox != null)
            {
                conditionType = "段落包含关键词";
                conditionValue = containPatternsTextBox.Text;
            }
            else if (endsWithRadio?.Checked == true && endPatternsTextBox != null)
            {
                conditionType = "段落匹配结尾";
                conditionValue = endPatternsTextBox.Text;
            }
            else if (regexRadio?.Checked == true && regexPatternTextBox != null)
            {
                conditionType = "使用正则表达式";
                conditionValue = regexPatternTextBox.Text;
            }
            else if (paragraphPositionRadio?.Checked == true && paragraphPositionNumeric != null)
            {
                conditionType = "匹配段落数";
                conditionValue = $"第{paragraphPositionNumeric.Value}段";
            }

            if (string.IsNullOrWhiteSpace(conditionValue))
            {
                MessageBox.Show("请输入匹配内容", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 添加新条件
            if (conditionListView != null && conditionLogicComboBox != null)
            {
                var item = new ListViewItem(new[] {
                    conditionType,
                    conditionValue,
                    conditionListView.Items.Count > 0 ? (conditionLogicComboBox.SelectedIndex == 0 ? "与(AND)" : "或(OR)") : "-"
                });

                // 保存条件数据到Tag属性中，以便后续编辑
                var conditionData = new ConditionData
                {
                    Type = conditionType,
                    Value = conditionValue,
                    Logic = conditionListView.Items.Count > 0 ? (conditionLogicComboBox.SelectedIndex == 0 ? "AND" : "OR") : ""
                };
                item.Tag = conditionData;

                conditionListView.Items.Add(item);

                // 清空输入字段
                ClearConditionInputs();

                UpdateConditionButtonState();

                // 将更新后的条件组合保存到规则中
                SaveConditionsToRule();
            }
        }

        // 用于存储正在编辑的条件项
        private ListViewItem? editingConditionItem = null;

        private void EditConditionButton_Click(object? sender, EventArgs e)
        {
            // 如果正在编辑，则取消编辑
            if (editingConditionItem != null)
            {
                CancelEditing();
                return;
            }

            if (conditionListView?.SelectedItems.Count == 0) return;

            var selectedItem = conditionListView?.SelectedItems[0];
            var conditionData = selectedItem?.Tag as ConditionData;

            if (conditionData == null) return;

            // 保存正在编辑的条件项
            editingConditionItem = selectedItem;

            // 根据条件类型设置相应的输入框
            switch (conditionData.Type)
            {
                case "段落匹配开头":
                    if (startsWithRadio != null && startPatternsTextBox != null)
                    {
                        startsWithRadio.Checked = true;
                        startPatternsTextBox.Text = conditionData.Value ?? "";
                    }
                    break;
                case "段落包含关键词":
                    if (containsRadio != null && containPatternsTextBox != null)
                    {
                        containsRadio.Checked = true;
                        containPatternsTextBox.Text = conditionData.Value ?? "";
                    }
                    break;
                case "段落匹配结尾":
                    if (endsWithRadio != null && endPatternsTextBox != null)
                    {
                        endsWithRadio.Checked = true;
                        endPatternsTextBox.Text = conditionData.Value ?? "";
                    }
                    break;
                case "使用正则表达式":
                    if (regexRadio != null && regexPatternTextBox != null)
                    {
                        regexRadio.Checked = true;
                        regexPatternTextBox.Text = conditionData.Value ?? "";
                    }
                    break;
                case "匹配段落数":
                    if (paragraphPositionRadio != null && paragraphPositionNumeric != null)
                    {
                        paragraphPositionRadio.Checked = true;
                        // 从显示值中提取数字，例如"第3段"提取出3
                        var valueText = conditionData.Value ?? "";
                        if (valueText.StartsWith("第") && valueText.EndsWith("段"))
                        {
                            var numberText = valueText.Substring(1, valueText.Length - 2);
                            if (int.TryParse(numberText, out int position))
                            {
                                paragraphPositionNumeric.Value = position;
                            }
                        }
                    }
                    break;
            }

            // 更新按钮状态 - 显示保存编辑按钮，隐藏添加条件按钮
            UpdateEditingButtonState(true);
        }

        // 更新编辑状态下的按钮显示
        private void UpdateEditingButtonState(bool isEditing)
        {
            if (addConditionButton != null)
            {
                addConditionButton.Text = isEditing ? "保存编辑" : "添加条件";
            }

            // 控制编辑和删除按钮的状态
            if (editConditionButton != null)
            {
                editConditionButton.Text = isEditing ? "取消编辑" : "编辑条件";
            }

            // 在编辑状态下禁用删除按钮，避免混乱
            if (removeConditionButton != null)
            {
                removeConditionButton.Enabled = !isEditing && (conditionListView?.SelectedItems.Count > 0);
            }
        }

        // 保存编辑的条件
        private void SaveEditedCondition()
        {
            if (editingConditionItem == null || conditionListView == null) return;

            var conditionData = editingConditionItem.Tag as ConditionData;
            if (conditionData == null) return;

            // 获取当前选择的条件类型和值
            string? conditionType = null;
            string? conditionValue = null;

            if (startsWithRadio?.Checked == true && startPatternsTextBox != null && !string.IsNullOrWhiteSpace(startPatternsTextBox.Text))
            {
                conditionType = "段落匹配开头";
                conditionValue = startPatternsTextBox.Text.Trim();
            }
            else if (containsRadio?.Checked == true && containPatternsTextBox != null && !string.IsNullOrWhiteSpace(containPatternsTextBox.Text))
            {
                conditionType = "段落包含关键词";
                conditionValue = containPatternsTextBox.Text.Trim();
            }
            else if (endsWithRadio?.Checked == true && endPatternsTextBox != null && !string.IsNullOrWhiteSpace(endPatternsTextBox.Text))
            {
                conditionType = "段落匹配结尾";
                conditionValue = endPatternsTextBox.Text.Trim();
            }
            else if (regexRadio?.Checked == true && regexPatternTextBox != null && !string.IsNullOrWhiteSpace(regexPatternTextBox.Text))
            {
                conditionType = "使用正则表达式";
                conditionValue = regexPatternTextBox.Text.Trim();
            }
            else if (paragraphPositionRadio?.Checked == true && paragraphPositionNumeric != null)
            {
                conditionType = "匹配段落数";
                conditionValue = $"第{paragraphPositionNumeric.Value}段";
            }

            if (conditionType != null && conditionValue != null)
            {
                // 更新条件数据
                conditionData.Type = conditionType;
                conditionData.Value = conditionValue;

                // 更新ListView显示
                editingConditionItem.SubItems[0].Text = conditionType;
                editingConditionItem.SubItems[1].Text = conditionValue;

                // 清空输入框
                ClearConditionInputs();

                // 重置编辑状态
                editingConditionItem = null;
                UpdateEditingButtonState(false);

                // 保存到规则
                SaveConditionsToRule();
            }
        }

        // 清空条件输入框
        private void ClearConditionInputs()
        {
            if (startPatternsTextBox != null) startPatternsTextBox.Text = "";
            if (containPatternsTextBox != null) containPatternsTextBox.Text = "";
            if (endPatternsTextBox != null) endPatternsTextBox.Text = "";
            if (regexPatternTextBox != null) regexPatternTextBox.Text = "";
            if (paragraphPositionNumeric != null) paragraphPositionNumeric.Value = 1;

            // 取消所有单选按钮的选择
            if (startsWithRadio != null) startsWithRadio.Checked = false;
            if (containsRadio != null) containsRadio.Checked = false;
            if (endsWithRadio != null) endsWithRadio.Checked = false;
            if (regexRadio != null) regexRadio.Checked = false;
            if (paragraphPositionRadio != null) paragraphPositionRadio.Checked = false;
        }

        // 取消编辑
        private void CancelEditing()
        {
            // 清空输入框
            ClearConditionInputs();

            // 重置编辑状态
            editingConditionItem = null;
            UpdateEditingButtonState(false);
        }

        private void RemoveConditionButton_Click(object? sender, EventArgs e)
        {
            if (conditionListView == null || conditionListView.SelectedItems.Count == 0) return;

            var selectedItem = conditionListView.SelectedItems[0];
            if (selectedItem == null) return;

            conditionListView.Items.Remove(selectedItem);

            // 如果删除后列表为空，需要更新按钮状态
            UpdateConditionButtonState();

            // 重新排序逻辑关系标签（第一个条件不应有逻辑标签）
            if (conditionListView.Items.Count > 0)
            {
                conditionListView.Items[0].SubItems[2].Text = "-";
                var conditionData = conditionListView.Items[0].Tag as ConditionData;
                if (conditionData != null)
                {
                    conditionData.Logic = "";
                }
            }

            // 将更新后的条件组合保存到规则中
            SaveConditionsToRule();
        }

        private void ConditionListView_SelectedIndexChanged(object? sender, EventArgs e)
        {
            UpdateConditionButtonState();
        }

        private void UpdateConditionButtonState()
        {
            if (conditionListView != null && editConditionButton != null && removeConditionButton != null)
            {
                bool hasSelection = conditionListView.SelectedItems.Count > 0;
                bool isEditing = editingConditionItem != null;

                // 编辑按钮：有选择时启用，或者正在编辑时启用（用于取消编辑）
                editConditionButton.Enabled = hasSelection || isEditing;

                // 删除按钮：有选择且不在编辑状态时启用
                removeConditionButton.Enabled = hasSelection && !isEditing;
            }
        }

        private void SaveConditionsToRule()
        {
            if (rule == null) return;

            // 更新rule对象中的条件组合
            rule.ClearConditions();

            // 如果条件列表为空或不可用，则根据单选按钮状态添加单个条件
            if (conditionListView == null || conditionListView.Items.Count == 0)
            {
                if (startsWithRadio?.Checked == true && startPatternsTextBox != null && !string.IsNullOrWhiteSpace(startPatternsTextBox.Text))
                {
                    foreach (var pattern in SplitPatterns(startPatternsTextBox.Text))
                    {
                        rule.AddStartWithPattern(pattern, "AND");
                    }
                }
                else if (containsRadio?.Checked == true && containPatternsTextBox != null && !string.IsNullOrWhiteSpace(containPatternsTextBox.Text))
                {
                    foreach (var pattern in SplitPatterns(containPatternsTextBox.Text))
                    {
                        rule.AddContainsPattern(pattern, "AND");
                    }
                }
                else if (endsWithRadio?.Checked == true && endPatternsTextBox != null && !string.IsNullOrWhiteSpace(endPatternsTextBox.Text))
                {
                    foreach (var pattern in SplitPatterns(endPatternsTextBox.Text))
                    {
                        rule.AddEndWithPattern(pattern, "AND");
                    }
                }
                else if (regexRadio?.Checked == true && regexPatternTextBox != null && !string.IsNullOrWhiteSpace(regexPatternTextBox.Text))
                {
                    rule.AddRegexPattern(regexPatternTextBox.Text, "AND");
                }
                return;
            }

            // 处理条件列表中的条件
            for (int i = 0; i < conditionListView.Items.Count; i++)
            {
                var item = conditionListView.Items[i];
                if (item == null) continue;

                var conditionData = item.Tag as ConditionData;

                if (conditionData == null || conditionData.Type == null) continue;

                switch (conditionData.Type)
                {
                    case "段落匹配开头":
                        rule.AddStartWithPattern(conditionData.Value ?? "", conditionData.Logic ?? "AND");
                        break;
                    case "段落包含关键词":
                        rule.AddContainsPattern(conditionData.Value ?? "", conditionData.Logic ?? "AND");
                        break;
                    case "段落匹配结尾":
                        rule.AddEndWithPattern(conditionData.Value ?? "", conditionData.Logic ?? "AND");
                        break;
                    case "使用正则表达式":
                        rule.AddRegexPattern(conditionData.Value ?? "", conditionData.Logic ?? "AND");
                        break;
                    case "匹配段落数":
                        // 从显示值中提取数字，例如"第3段"提取出3
                        var valueText = conditionData.Value ?? "";
                        if (valueText.StartsWith("第") && valueText.EndsWith("段"))
                        {
                            var numberText = valueText.Substring(1, valueText.Length - 2);
                            if (int.TryParse(numberText, out int position))
                            {
                                rule.AddParagraphPositionPattern(position, conditionData.Logic ?? "AND");
                            }
                        }
                        break;
                }
            }

            // 更新格式应用范围控件状态
            UpdateFormatApplyScopeControls();
        }

        // 更新格式应用范围控件状态
        private void UpdateFormatApplyScopeControls()
        {
            if (conditionListView == null) return;

            // 检查条件数量
            int conditionCount = conditionListView.Items.Count;

            // 如果有多个条件，只允许选择"设定整段格式"
            bool hasMultipleConditions = conditionCount > 1;

            if (hasMultipleConditions)
            {
                // 强制选择"设定整段格式"
                if (wholeParagraphRadio != null)
                {
                    wholeParagraphRadio.Checked = true;
                    rule.ApplyScope = FormatApplyScope.WholeParagrph;
                }

                // 禁用其他选项
                if (untilPeriodRadio != null) untilPeriodRadio.Enabled = false;
                if (untilLineBreakRadio != null) untilLineBreakRadio.Enabled = false;
                if (untilSpecificCharInclusiveRadio != null) untilSpecificCharInclusiveRadio.Enabled = false;
                if (untilSpecificCharExclusiveRadio != null) untilSpecificCharExclusiveRadio.Enabled = false;
                if (betweenPeriodsRadio != null) betweenPeriodsRadio.Enabled = false;
                if (fromStartToMatchInclusiveRadio != null) fromStartToMatchInclusiveRadio.Enabled = false;
                if (fromStartToMatchExclusiveRadio != null) fromStartToMatchExclusiveRadio.Enabled = false;
                if (fromCharToMatchInclusiveRadio != null) fromCharToMatchInclusiveRadio.Enabled = false;
                if (fromCharToMatchExclusiveRadio != null) fromCharToMatchExclusiveRadio.Enabled = false;

                // 禁用相关的文本框
                if (inclusiveCharTextBox != null) inclusiveCharTextBox.Enabled = false;
                if (exclusiveCharTextBox != null) exclusiveCharTextBox.Enabled = false;
                if (startingCharInclusiveTextBox != null) startingCharInclusiveTextBox.Enabled = false;
                if (startingCharExclusiveTextBox != null) startingCharExclusiveTextBox.Enabled = false;
            }
            else
            {
                // 启用所有选项
                if (untilPeriodRadio != null) untilPeriodRadio.Enabled = true;
                if (untilLineBreakRadio != null) untilLineBreakRadio.Enabled = true;
                if (untilSpecificCharInclusiveRadio != null) untilSpecificCharInclusiveRadio.Enabled = true;
                if (untilSpecificCharExclusiveRadio != null) untilSpecificCharExclusiveRadio.Enabled = true;
                if (betweenPeriodsRadio != null) betweenPeriodsRadio.Enabled = true;
                if (fromStartToMatchInclusiveRadio != null) fromStartToMatchInclusiveRadio.Enabled = true;
                if (fromStartToMatchExclusiveRadio != null) fromStartToMatchExclusiveRadio.Enabled = true;
                if (fromCharToMatchInclusiveRadio != null) fromCharToMatchInclusiveRadio.Enabled = true;
                if (fromCharToMatchExclusiveRadio != null) fromCharToMatchExclusiveRadio.Enabled = true;

                // 根据当前选择状态启用相关的文本框
                UpdateTextBoxStates();
            }
        }

        // 新增用于保存条件数据的类
        private class ConditionData
        {
            public string? Type { get; set; }
            public string? Value { get; set; }
            public string? Logic { get; set; } // AND 或 OR
        }

        // 辅助方法：查找指定标签文本的面板中的CheckBox控件
        private CheckBox? FindCheckBoxInPanel(string labelText)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Panel panel)
                {
                    var result = FindCheckBoxInControlByLabel(panel, labelText);
                    if (result != null)
                        return result;
                }
            }
            return null;
        }

        // 辅助方法：查找指定标签文本的面板中的ComboBox控件
        private ComboBox? FindComboBoxInPanel(string labelText)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Panel panel)
                {
                    var result = FindComboBoxInControlByLabel(panel, labelText);
                    if (result != null)
                        return result;
                }
            }
            return null;
        }

        // 辅助方法：查找指定标签文本的面板中的NumericUpDown控件
        private NumericUpDown? FindNumericUpDownInPanel(string labelText)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Panel panel)
                {
                    var result = FindNumericUpDownInControlByLabel(panel, labelText);
                    if (result != null)
                        return result;
                }
            }
            return null;
        }

        // 辅助方法：查找指定标签文本和文本内容的RadioButton控件
        private RadioButton? FindRadioButtonInPanel(string labelText, string radioText)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Panel panel)
                {
                    var result = FindRadioButtonInControlByLabelAndText(panel, labelText, radioText);
                    if (result != null)
                        return result;
                }
            }
            return null;
        }

        // 递归查找控件中的CheckBox
        private CheckBox? FindCheckBoxInControlByLabel(Control control, string labelText)
        {
            // 检查当前控件是否是TableLayoutPanel，且第一个控件是Label，且Label文本包含指定文本
            if (control is TableLayoutPanel panel && panel.Controls.Count > 1 &&
                panel.Controls[0] is Label label && label.Text.Contains(labelText))
            {
                // 检查第二个控件是否包含CheckBox
                if (panel.Controls[1] is CheckBox checkBox)
                    return checkBox;

                // 如果第二个控件是容器，则在其中查找CheckBox
                if (panel.Controls[1] is Control container)
                {
                    foreach (Control child in container.Controls)
                    {
                        if (child is CheckBox cb)
                            return cb;

                        // 递归查找
                        var result = FindCheckBoxInControlByLabel(child, labelText);
                        if (result != null)
                            return result;
                    }
                }
            }

            // 递归查找子控件
            foreach (Control child in control.Controls)
            {
                var result = FindCheckBoxInControlByLabel(child, labelText);
                if (result != null)
                    return result;
            }

            return null;
        }

        // 递归查找控件中的ComboBox
        private ComboBox? FindComboBoxInControlByLabel(Control control, string labelText)
        {
            // 检查当前控件是否是TableLayoutPanel，且第一个控件是Label，且Label文本包含指定文本
            if (control is TableLayoutPanel panel && panel.Controls.Count > 1 &&
                panel.Controls[0] is Label label && label.Text.Contains(labelText))
            {
                // 检查第二个控件是否包含ComboBox
                if (panel.Controls[1] is ComboBox comboBox)
                    return comboBox;

                // 如果第二个控件是容器，则在其中查找ComboBox
                if (panel.Controls[1] is Control container)
                {
                    foreach (Control child in container.Controls)
                    {
                        if (child is ComboBox cb)
                            return cb;

                        // 递归查找
                        var result = FindComboBoxInControlByLabel(child, labelText);
                        if (result != null)
                            return result;
                    }
                }
            }

            // 递归查找子控件
            foreach (Control child in control.Controls)
            {
                var result = FindComboBoxInControlByLabel(child, labelText);
                if (result != null)
                    return result;
            }

            return null;
        }

        // 递归查找控件中的NumericUpDown
        private NumericUpDown? FindNumericUpDownInControlByLabel(Control control, string labelText)
        {
            // 检查当前控件是否是TableLayoutPanel，且第一个控件是Label，且Label文本包含指定文本
            if (control is TableLayoutPanel panel && panel.Controls.Count > 1 &&
                panel.Controls[0] is Label label && label.Text.Contains(labelText))
            {
                // 检查第二个控件是否包含NumericUpDown
                if (panel.Controls[1] is NumericUpDown numericUpDown)
                    return numericUpDown;

                // 如果第二个控件是容器，则在其中查找NumericUpDown
                if (panel.Controls[1] is Control container)
                {
                    foreach (Control child in container.Controls)
                    {
                        if (child is NumericUpDown nud)
                            return nud;

                        // 递归查找
                        var result = FindNumericUpDownInControlByLabel(child, labelText);
                        if (result != null)
                            return result;
                    }
                }
            }

            // 递归查找子控件
            foreach (Control child in control.Controls)
            {
                var result = FindNumericUpDownInControlByLabel(child, labelText);
                if (result != null)
                    return result;
            }

            return null;
        }

        // 递归查找控件中的RadioButton
        private RadioButton? FindRadioButtonInControlByLabelAndText(Control control, string labelText, string radioText)
        {
            // 检查当前控件是否是TableLayoutPanel，且第一个控件是Label，且Label文本包含指定文本
            if (control is TableLayoutPanel panel && panel.Controls.Count > 1 &&
                panel.Controls[0] is Label label && label.Text.Contains(labelText))
            {
                // 在第二个控件中查找指定文本的RadioButton
                if (panel.Controls[1] is Control container)
                {
                    foreach (Control child in container.Controls)
                    {
                        if (child is RadioButton rb && rb.Text.Contains(radioText))
                            return rb;

                        // 递归查找
                        var result = FindRadioButtonInControlByLabelAndText(child, labelText, radioText);
                        if (result != null)
                            return result;
                    }
                }
            }

            // 递归查找子控件
            foreach (Control child in control.Controls)
            {
                var result = FindRadioButtonInControlByLabelAndText(child, labelText, radioText);
                if (result != null)
                    return result;
            }

            return null;
        }

        // 添加使用说明按钮的点击事件处理程序
        private void HelpButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show(
                "条件组合使用方法：\n\n" +
                "1. 在\"单个匹配条件设置\"区域中选择一种匹配类型（段落匹配开头、包含关键词、匹配结尾或正则表达式）\n\n" +
                "2. 输入相应的匹配文本\n\n" +
                "3. 选择\"条件之间的关系\"（与(AND)/或(OR)）- 这决定了新添加的条件与已有条件的逻辑关系\n\n" +
                "4. 点击\"添加条件\"将此条件添加到条件列表中\n\n" +
                "5. 重复上述步骤添加多个条件\n\n" +
                "6. 已添加的条件会显示在条件列表中，可以通过选择后点击\"编辑条件\"或\"删除条件\"进行管理\n\n" +
                "示例：\n" +
                "- 段落包含\"重要\"（第一个条件）\n" +
                "- 与(AND)\n" +
                "- 段落匹配开头\"注意\"（第二个条件）\n\n" +
                "这将匹配同时包含\"重要\"且以\"注意\"开头的段落。",
                "条件组合使用说明",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information
            );
        }

        private Panel CreateFontFormatPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 5, // 增加一行用于总启用控制
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 设置行高为自动调整
            for (int i = 0; i < 5; i++)
            {
                mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 添加字体格式总启用控制
            var fontFormatEnablePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 10)
            };

            var fontFormatEnableCheckBox = new CheckBox
            {
                Text = "启用字体格式",
                AutoSize = true,
                Checked = rule.EnableFontFormat,
                Font = new Font(Font.FontFamily, Font.Size, FontStyle.Bold),
                ForeColor = Color.Red
            };

            fontFormatEnablePanel.Controls.Add(fontFormatEnableCheckBox, 0, 0);
            mainLayout.Controls.Add(fontFormatEnablePanel, 0, 0);

            // 添加基本字体设置
            var fontBasicGroup = new GroupBox
            {
                Text = "基本字体设置",
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 5, 0, 5),
                AutoSize = true,
                Enabled = rule.EnableFontFormat
            };

            var fontBasicPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4, // 中文字体、西文字体、复杂文字、颜色
                AutoSize = true
            };

            // 1. 中文字体
            var chineseFontPanel = CreateLabeledControl("中文字体:", () =>
            {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true,
                    Dock = DockStyle.Fill
                };

                chineseFontEnableCheckBox = new CheckBox
                {
                    Text = "",
                    Checked = rule.EnableChineseFont,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(0, 0, 5, 0) // 减少右边距
                };

                var panel = new TableLayoutPanel
                {
                    ColumnCount = 5, // 字体、样式、大小下拉框、自定义输入框、单位
                    RowCount = 1,
                    AutoSize = true,
                    Enabled = rule.EnableChineseFont
                };

                // 字体
                chineseFontComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 150,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                // 从系统加载所有已安装的字体
                LoadSystemFonts(chineseFontComboBox);
                // 设置默认选择为微软雅黑（如果存在）
                SetDefaultFont(chineseFontComboBox, "微软雅黑");
                chineseFontComboBox.DrawItem += ComboBox_DrawItem;

                // 字体样式
                chineseFontStyleComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 100,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                chineseFontStyleComboBox.Items.Add("常规");
                chineseFontStyleComboBox.Items.Add("倾斜");
                chineseFontStyleComboBox.Items.Add("加粗");
                chineseFontStyleComboBox.Items.Add("加粗倾斜");
                chineseFontStyleComboBox.SelectedIndex = 0;
                chineseFontStyleComboBox.DrawItem += ComboBox_DrawItem;

                // 字体大小 - 使用预设字号下拉框
                chineseFontSizeComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList, // 只允许选择预设项
                    Width = 80,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                // 添加中文预设字号
                chineseFontSizeComboBox.Items.Add("初号");      // 42磅
                chineseFontSizeComboBox.Items.Add("小初");      // 36磅
                chineseFontSizeComboBox.Items.Add("一号");      // 26磅
                chineseFontSizeComboBox.Items.Add("小一");      // 24磅
                chineseFontSizeComboBox.Items.Add("二号");      // 22磅
                chineseFontSizeComboBox.Items.Add("小二");      // 18磅
                chineseFontSizeComboBox.Items.Add("三号");      // 16磅
                chineseFontSizeComboBox.Items.Add("小三");      // 15磅
                chineseFontSizeComboBox.Items.Add("四号");      // 14磅
                chineseFontSizeComboBox.Items.Add("小四");      // 12磅
                chineseFontSizeComboBox.Items.Add("五号");      // 10.5磅
                chineseFontSizeComboBox.Items.Add("小五");      // 9磅
                chineseFontSizeComboBox.Items.Add("六号");      // 7.5磅
                chineseFontSizeComboBox.Items.Add("小六");      // 6.5磅
                chineseFontSizeComboBox.Items.Add("七号");      // 5.5磅
                chineseFontSizeComboBox.Items.Add("小七");      // 5磅
                chineseFontSizeComboBox.Items.Add("八号");      // 4.5磅
                chineseFontSizeComboBox.Items.Add("小八");      // 4磅
                chineseFontSizeComboBox.Items.Add("自定义");     // 自定义选项

                chineseFontSizeComboBox.SelectedIndex = 10; // 默认五号字体
                chineseFontSizeComboBox.DrawItem += ComboBox_DrawItem;

                // 自定义字号输入框
                chineseFontSizeNumeric = new NumericUpDown
                {
                    Minimum = 1,
                    Maximum = 100,
                    Value = 10.5m, // 默认10.5磅（五号）
                    Width = 50,
                    TextAlign = HorizontalAlignment.Center,
                    Visible = false // 初始隐藏
                };

                // 字体大小单位标签（磅）
                chineseFontSizeUnitLabel = new Label
                {
                    Text = "磅",
                    AutoSize = true,
                    TextAlign = ContentAlignment.MiddleLeft,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(2, 3, 0, 0),
                    Visible = false // 初始隐藏，与输入框同步显示
                };

                // 设置字体大小选择事件
                EventHandler chineseFontSizeComboBoxHandler = (s, e) =>
                {
                    if (chineseFontSizeComboBox.SelectedIndex == chineseFontSizeComboBox.Items.Count - 1) // 自定义
                    {
                        // 显示自定义输入框和单位标签
                        chineseFontSizeNumeric.Visible = true;
                        chineseFontSizeUnitLabel.Visible = true;
                        rule.ChineseFontSize = (double)chineseFontSizeNumeric.Value;
                        rule.FontSize = (double)chineseFontSizeNumeric.Value; // 兼容性设置
                    }
                    else
                    {
                        // 使用预设字号，隐藏自定义输入框和单位标签
                        chineseFontSizeNumeric.Visible = false;
                        chineseFontSizeUnitLabel.Visible = false;
                        double pointSize = GetPointSizeFromChineseFontName(chineseFontSizeComboBox.SelectedItem?.ToString() ?? "五号");
                        chineseFontSizeNumeric.Value = (decimal)pointSize;
                        rule.ChineseFontSize = pointSize;
                        rule.FontSize = pointSize; // 兼容性设置
                    }
                };

                chineseFontSizeComboBox.SelectedIndexChanged += chineseFontSizeComboBoxHandler;

                // 自定义字号输入框值改变时的处理
                chineseFontSizeNumeric.ValueChanged += (sender, e) =>
                {
                    // 检查是否匹配预设字号
                    double currentSize = (double)chineseFontSizeNumeric.Value;
                    string matchedFontName = GetChineseFontSizeName(currentSize);

                    // 如果匹配到预设字号且不是数字格式，自动选择对应的预设项
                    if (!char.IsDigit(matchedFontName[0]))
                    {
                        chineseFontSizeComboBox.SelectedIndexChanged -= chineseFontSizeComboBoxHandler;
                        for (int i = 0; i < chineseFontSizeComboBox.Items.Count - 1; i++) // 排除"自定义"项
                        {
                            if (chineseFontSizeComboBox.Items[i]?.ToString() == matchedFontName)
                            {
                                chineseFontSizeComboBox.SelectedIndex = i;
                                chineseFontSizeNumeric.Visible = false;
                                chineseFontSizeUnitLabel.Visible = false;
                                break;
                            }
                        }
                        chineseFontSizeComboBox.SelectedIndexChanged += chineseFontSizeComboBoxHandler;
                    }

                    rule.ChineseFontSize = currentSize;
                    rule.FontSize = currentSize; // 兼容性设置
                };

                panel.Controls.Add(chineseFontComboBox, 0, 0);
                panel.Controls.Add(chineseFontStyleComboBox, 1, 0);
                panel.Controls.Add(chineseFontSizeComboBox, 2, 0);
                panel.Controls.Add(chineseFontSizeNumeric, 3, 0);
                panel.Controls.Add(chineseFontSizeUnitLabel, 4, 0);

                // 添加启用复选框事件
                chineseFontEnableCheckBox.CheckedChanged += (s, e) =>
                {
                    panel.Enabled = chineseFontEnableCheckBox.Checked;
                    rule.EnableChineseFont = chineseFontEnableCheckBox.Checked;
                };

                container.Controls.Add(chineseFontEnableCheckBox, 0, 0);
                container.Controls.Add(panel, 1, 0);

                return container;
            });

            // 2. 西文字体
            var westernFontPanel = CreateLabeledControl("西文字体:", () =>
            {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true,
                    Dock = DockStyle.Fill
                };

                westernFontEnableCheckBox = new CheckBox
                {
                    Text = "",
                    Checked = rule.EnableWesternFont,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(0, 0, 5, 0) // 减少右边距
                };

                var panel = new TableLayoutPanel
                {
                    ColumnCount = 5, // 字体、样式、大小下拉框、自定义输入框、单位
                    RowCount = 1,
                    AutoSize = true,
                    Enabled = rule.EnableWesternFont
                };

                // 字体
                westernFontComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 150,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                // 从系统加载所有已安装的字体
                LoadSystemFonts(westernFontComboBox);
                // 设置默认选择为Arial（如果存在）
                SetDefaultFont(westernFontComboBox, "Arial");
                westernFontComboBox.DrawItem += ComboBox_DrawItem;

                // 字体样式
                westernFontStyleComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 100,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                westernFontStyleComboBox.Items.Add("常规");
                westernFontStyleComboBox.Items.Add("倾斜");
                westernFontStyleComboBox.Items.Add("加粗");
                westernFontStyleComboBox.Items.Add("加粗倾斜");
                westernFontStyleComboBox.SelectedIndex = 0;
                westernFontStyleComboBox.DrawItem += ComboBox_DrawItem;

                // 字体大小 - 使用预设字号下拉框
                westernFontSizeComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList, // 只允许选择预设项
                    Width = 80,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                // 添加中文预设字号
                westernFontSizeComboBox.Items.Add("初号");      // 42磅
                westernFontSizeComboBox.Items.Add("小初");      // 36磅
                westernFontSizeComboBox.Items.Add("一号");      // 26磅
                westernFontSizeComboBox.Items.Add("小一");      // 24磅
                westernFontSizeComboBox.Items.Add("二号");      // 22磅
                westernFontSizeComboBox.Items.Add("小二");      // 18磅
                westernFontSizeComboBox.Items.Add("三号");      // 16磅
                westernFontSizeComboBox.Items.Add("小三");      // 15磅
                westernFontSizeComboBox.Items.Add("四号");      // 14磅
                westernFontSizeComboBox.Items.Add("小四");      // 12磅
                westernFontSizeComboBox.Items.Add("五号");      // 10.5磅
                westernFontSizeComboBox.Items.Add("小五");      // 9磅
                westernFontSizeComboBox.Items.Add("六号");      // 7.5磅
                westernFontSizeComboBox.Items.Add("小六");      // 6.5磅
                westernFontSizeComboBox.Items.Add("七号");      // 5.5磅
                westernFontSizeComboBox.Items.Add("小七");      // 5磅
                westernFontSizeComboBox.Items.Add("八号");      // 4.5磅
                westernFontSizeComboBox.Items.Add("小八");      // 4磅
                westernFontSizeComboBox.Items.Add("自定义");     // 自定义选项

                westernFontSizeComboBox.SelectedIndex = 10; // 默认五号字体
                westernFontSizeComboBox.DrawItem += ComboBox_DrawItem;

                // 自定义字号输入框
                westernFontSizeNumeric = new NumericUpDown
                {
                    Minimum = 1,
                    Maximum = 100,
                    Value = 10.5m, // 默认10.5磅（五号）
                    Width = 50,
                    TextAlign = HorizontalAlignment.Center,
                    Visible = false // 初始隐藏
                };

                // 字体大小单位标签（磅）
                westernFontSizeUnitLabel = new Label
                {
                    Text = "磅",
                    AutoSize = true,
                    TextAlign = ContentAlignment.MiddleLeft,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(2, 3, 0, 0),
                    Visible = false // 初始隐藏，与输入框同步显示
                };

                // 设置字体大小选择事件
                EventHandler westernFontSizeComboBoxHandler = (s, e) =>
                {
                    if (westernFontSizeComboBox.SelectedIndex == westernFontSizeComboBox.Items.Count - 1) // 自定义
                    {
                        // 显示自定义输入框和单位标签
                        westernFontSizeNumeric.Visible = true;
                        westernFontSizeUnitLabel.Visible = true;
                        rule.WesternFontSize = (double)westernFontSizeNumeric.Value;
                        rule.FontSize = (double)westernFontSizeNumeric.Value; // 兼容性设置
                    }
                    else
                    {
                        // 使用预设字号，隐藏自定义输入框和单位标签
                        westernFontSizeNumeric.Visible = false;
                        westernFontSizeUnitLabel.Visible = false;
                        double pointSize = GetPointSizeFromChineseFontName(westernFontSizeComboBox.SelectedItem?.ToString() ?? "五号");
                        westernFontSizeNumeric.Value = (decimal)pointSize;
                        rule.WesternFontSize = pointSize;
                        rule.FontSize = pointSize; // 兼容性设置
                    }
                };

                westernFontSizeComboBox.SelectedIndexChanged += westernFontSizeComboBoxHandler;

                // 自定义字号输入框值改变时的处理
                westernFontSizeNumeric.ValueChanged += (sender, e) =>
                {
                    // 检查是否匹配预设字号
                    double currentSize = (double)westernFontSizeNumeric.Value;
                    string matchedFontName = GetChineseFontSizeName(currentSize);

                    // 如果匹配到预设字号且不是数字格式，自动选择对应的预设项
                    if (!char.IsDigit(matchedFontName[0]))
                    {
                        westernFontSizeComboBox.SelectedIndexChanged -= westernFontSizeComboBoxHandler;
                        for (int i = 0; i < westernFontSizeComboBox.Items.Count - 1; i++) // 排除"自定义"项
                        {
                            if (westernFontSizeComboBox.Items[i]?.ToString() == matchedFontName)
                            {
                                westernFontSizeComboBox.SelectedIndex = i;
                                westernFontSizeNumeric.Visible = false;
                                westernFontSizeUnitLabel.Visible = false;
                                break;
                            }
                        }
                        westernFontSizeComboBox.SelectedIndexChanged += westernFontSizeComboBoxHandler;
                    }

                    rule.WesternFontSize = currentSize;
                    rule.FontSize = currentSize; // 兼容性设置
                };

                panel.Controls.Add(westernFontComboBox, 0, 0);
                panel.Controls.Add(westernFontStyleComboBox, 1, 0);
                panel.Controls.Add(westernFontSizeComboBox, 2, 0);
                panel.Controls.Add(westernFontSizeNumeric, 3, 0);
                panel.Controls.Add(westernFontSizeUnitLabel, 4, 0);

                // 添加启用复选框事件
                westernFontEnableCheckBox.CheckedChanged += (s, e) =>
                {
                    panel.Enabled = westernFontEnableCheckBox.Checked;
                    rule.EnableWesternFont = westernFontEnableCheckBox.Checked;
                };

                container.Controls.Add(westernFontEnableCheckBox, 0, 0);
                container.Controls.Add(panel, 1, 0);

                return container;
            });

            // 3. 复杂文字字体
            var complexScriptFontPanel = CreateLabeledControl("复杂文字字体:", () =>
            {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true,
                    Dock = DockStyle.Fill
                };

                complexScriptFontEnableCheckBox = new CheckBox
                {
                    Text = "",
                    Checked = rule.EnableComplexScriptFont,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(0, 0, 5, 0) // 减少右边距
                };

                var panel = new TableLayoutPanel
                {
                    ColumnCount = 5, // 字体、样式、大小下拉框、自定义输入框、单位
                    RowCount = 1,
                    AutoSize = true,
                    Enabled = rule.EnableComplexScriptFont
                };

                // 字体
                complexScriptFontComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 150,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                // 从系统加载所有已安装的字体
                LoadSystemFonts(complexScriptFontComboBox);
                // 设置默认选择为Arial（如果存在）
                SetDefaultFont(complexScriptFontComboBox, "Arial");
                complexScriptFontComboBox.DrawItem += ComboBox_DrawItem;

                // 字体样式
                complexScriptFontStyleComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 100,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                complexScriptFontStyleComboBox.Items.Add("常规");
                complexScriptFontStyleComboBox.Items.Add("倾斜");
                complexScriptFontStyleComboBox.Items.Add("加粗");
                complexScriptFontStyleComboBox.Items.Add("加粗倾斜");
                complexScriptFontStyleComboBox.SelectedIndex = 0;
                complexScriptFontStyleComboBox.DrawItem += ComboBox_DrawItem;

                // 字体大小 - 使用预设字号下拉框
                complexScriptFontSizeComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList, // 只允许选择预设项
                    Width = 80,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                // 添加中文预设字号
                complexScriptFontSizeComboBox.Items.Add("初号");      // 42磅
                complexScriptFontSizeComboBox.Items.Add("小初");      // 36磅
                complexScriptFontSizeComboBox.Items.Add("一号");      // 26磅
                complexScriptFontSizeComboBox.Items.Add("小一");      // 24磅
                complexScriptFontSizeComboBox.Items.Add("二号");      // 22磅
                complexScriptFontSizeComboBox.Items.Add("小二");      // 18磅
                complexScriptFontSizeComboBox.Items.Add("三号");      // 16磅
                complexScriptFontSizeComboBox.Items.Add("小三");      // 15磅
                complexScriptFontSizeComboBox.Items.Add("四号");      // 14磅
                complexScriptFontSizeComboBox.Items.Add("小四");      // 12磅
                complexScriptFontSizeComboBox.Items.Add("五号");      // 10.5磅
                complexScriptFontSizeComboBox.Items.Add("小五");      // 9磅
                complexScriptFontSizeComboBox.Items.Add("六号");      // 7.5磅
                complexScriptFontSizeComboBox.Items.Add("小六");      // 6.5磅
                complexScriptFontSizeComboBox.Items.Add("七号");      // 5.5磅
                complexScriptFontSizeComboBox.Items.Add("小七");      // 5磅
                complexScriptFontSizeComboBox.Items.Add("八号");      // 4.5磅
                complexScriptFontSizeComboBox.Items.Add("小八");      // 4磅
                complexScriptFontSizeComboBox.Items.Add("自定义");     // 自定义选项

                complexScriptFontSizeComboBox.SelectedIndex = 10; // 默认五号字体
                complexScriptFontSizeComboBox.DrawItem += ComboBox_DrawItem;

                // 自定义字号输入框
                complexScriptFontSizeNumeric = new NumericUpDown
                {
                    Minimum = 1,
                    Maximum = 100,
                    Value = 10.5m, // 默认10.5磅（五号）
                    Width = 50,
                    TextAlign = HorizontalAlignment.Center,
                    Visible = false // 初始隐藏
                };

                // 字体大小单位标签（磅）
                complexScriptFontSizeUnitLabel = new Label
                {
                    Text = "磅",
                    AutoSize = true,
                    TextAlign = ContentAlignment.MiddleLeft,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(2, 3, 0, 0),
                    Visible = false // 初始隐藏，与输入框同步显示
                };

                // 设置字体大小选择事件
                EventHandler complexScriptFontSizeComboBoxHandler = (s, e) =>
                {
                    if (complexScriptFontSizeComboBox.SelectedIndex == complexScriptFontSizeComboBox.Items.Count - 1) // 自定义
                    {
                        // 显示自定义输入框和单位标签
                        complexScriptFontSizeNumeric.Visible = true;
                        complexScriptFontSizeUnitLabel.Visible = true;
                        rule.ComplexScriptFontSize = (double)complexScriptFontSizeNumeric.Value;
                        rule.FontSize = (double)complexScriptFontSizeNumeric.Value; // 兼容性设置
                    }
                    else
                    {
                        // 使用预设字号，隐藏自定义输入框和单位标签
                        complexScriptFontSizeNumeric.Visible = false;
                        complexScriptFontSizeUnitLabel.Visible = false;
                        double pointSize = GetPointSizeFromChineseFontName(complexScriptFontSizeComboBox.SelectedItem?.ToString() ?? "五号");
                        complexScriptFontSizeNumeric.Value = (decimal)pointSize;
                        rule.ComplexScriptFontSize = pointSize;
                        rule.FontSize = pointSize; // 兼容性设置
                    }
                };

                complexScriptFontSizeComboBox.SelectedIndexChanged += complexScriptFontSizeComboBoxHandler;

                // 自定义字号输入框值改变时的处理
                complexScriptFontSizeNumeric.ValueChanged += (sender, e) =>
                {
                    // 检查是否匹配预设字号
                    double currentSize = (double)complexScriptFontSizeNumeric.Value;
                    string matchedFontName = GetChineseFontSizeName(currentSize);

                    // 如果匹配到预设字号且不是数字格式，自动选择对应的预设项
                    if (!char.IsDigit(matchedFontName[0]))
                    {
                        complexScriptFontSizeComboBox.SelectedIndexChanged -= complexScriptFontSizeComboBoxHandler;
                        for (int i = 0; i < complexScriptFontSizeComboBox.Items.Count - 1; i++) // 排除"自定义"项
                        {
                            if (complexScriptFontSizeComboBox.Items[i]?.ToString() == matchedFontName)
                            {
                                complexScriptFontSizeComboBox.SelectedIndex = i;
                                complexScriptFontSizeNumeric.Visible = false;
                                complexScriptFontSizeUnitLabel.Visible = false;
                                break;
                            }
                        }
                        complexScriptFontSizeComboBox.SelectedIndexChanged += complexScriptFontSizeComboBoxHandler;
                    }

                    rule.ComplexScriptFontSize = currentSize;
                    rule.FontSize = currentSize; // 兼容性设置
                };

                panel.Controls.Add(complexScriptFontComboBox, 0, 0);
                panel.Controls.Add(complexScriptFontStyleComboBox, 1, 0);
                panel.Controls.Add(complexScriptFontSizeComboBox, 2, 0);
                panel.Controls.Add(complexScriptFontSizeNumeric, 3, 0);
                panel.Controls.Add(complexScriptFontSizeUnitLabel, 4, 0);

                // 添加启用复选框事件
                complexScriptFontEnableCheckBox.CheckedChanged += (s, e) =>
                {
                    panel.Enabled = complexScriptFontEnableCheckBox.Checked;
                    rule.EnableComplexScriptFont = complexScriptFontEnableCheckBox.Checked;
                };

                container.Controls.Add(complexScriptFontEnableCheckBox, 0, 0);
                container.Controls.Add(panel, 1, 0);

                return container;
            });

            // 4. 颜色设置
            var colorPanel = CreateLabeledControl("颜色设置:", () =>
            {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 1,
                    RowCount = 2,
                    AutoSize = true,
                    Dock = DockStyle.Fill
                };

                // 字体颜色行
                var fontColorRow = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    Height = 40,
                    Dock = DockStyle.Fill,
                    Margin = new Padding(0, 0, 0, 5)
                };

                // 设置列宽比例，减少勾选框与控件的距离
                fontColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
                fontColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

                fontColorEnableCheckBox = new CheckBox
                {
                    Text = "字体颜色",
                    Checked = rule.EnableFontColor,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(0, 0, 5, 0) // 减少右边距
                };

                var fontColorControlsPanel = new FlowLayoutPanel
                {
                    FlowDirection = FlowDirection.LeftToRight,
                    AutoSize = true,
                    Enabled = rule.EnableFontColor
                };

                // 字体颜色面板
                fontColorPanel = new Panel
                {
                    Width = 30,
                    Height = 20,
                    BorderStyle = BorderStyle.FixedSingle,
                    BackColor = rule.FontColor.HasValue ? rule.FontColor.Value : Color.Black
                };

                fontColorButton = new Button
                {
                    Text = "选择颜色",
                    AutoSize = true
                };

                var clearFontColorButton = new Button
                {
                    Text = "清除",
                    AutoSize = true,
                    Margin = new Padding(5, 0, 0, 0)
                };

                fontColorButton.Click += fontColorButton_Click;
                clearFontColorButton.Click += (s, e) => {
                    fontColorPanel.BackColor = Color.Black; // 清除时恢复为黑色
                    // 立即更新规则对象的FontColor属性
                    rule.FontColor = Color.Black;
                };

                fontColorControlsPanel.Controls.Add(fontColorPanel);
                fontColorControlsPanel.Controls.Add(fontColorButton);
                fontColorControlsPanel.Controls.Add(clearFontColorButton);

                // 设置启用复选框事件
                fontColorEnableCheckBox.CheckedChanged += (s, e) =>
                {
                    fontColorControlsPanel.Enabled = fontColorEnableCheckBox.Checked;
                    rule.EnableFontColor = fontColorEnableCheckBox.Checked;
                };

                fontColorRow.Controls.Add(fontColorEnableCheckBox, 0, 0);
                fontColorRow.Controls.Add(fontColorControlsPanel, 1, 0);

                // 高亮颜色行
                var highlightColorRow = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    Height = 40,
                    Dock = DockStyle.Fill
                };

                // 设置列宽比例，减少勾选框与控件的距离
                highlightColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
                highlightColorRow.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

                highlightColorEnableCheckBox = new CheckBox
                {
                    Text = "高亮颜色",
                    Checked = rule.EnableHighlightColor,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Margin = new Padding(0, 0, 5, 0) // 减少右边距
                };

                var highlightColorControlsPanel = new FlowLayoutPanel
                {
                    FlowDirection = FlowDirection.LeftToRight,
                    AutoSize = true,
                    Enabled = rule.EnableHighlightColor
                };

                // 高亮颜色面板
                highlightColorPanel = new Panel
                {
                    Width = 30,
                    Height = 20,
                    BorderStyle = BorderStyle.FixedSingle,
                    BackColor = rule.HighlightColor.HasValue ? rule.HighlightColor.Value : Color.Transparent
                };

                highlightColorButton = new Button
                {
                    Text = "选择颜色",
                    AutoSize = true
                };

                var clearHighlightColorButton = new Button
                {
                    Text = "清除",
                    AutoSize = true,
                    Margin = new Padding(5, 0, 0, 0)
                };

                highlightColorButton.Click += highlightColorButton_Click;
                clearHighlightColorButton.Click += (s, e) => {
                    highlightColorPanel.BackColor = Color.Transparent; // 清除时恢复为无色（透明）
                    // 立即更新规则对象的HighlightColor属性
                    rule.HighlightColor = null;
                };

                highlightColorControlsPanel.Controls.Add(highlightColorPanel);
                highlightColorControlsPanel.Controls.Add(highlightColorButton);
                highlightColorControlsPanel.Controls.Add(clearHighlightColorButton);

                // 设置启用复选框事件
                highlightColorEnableCheckBox.CheckedChanged += (s, e) =>
                {
                    highlightColorControlsPanel.Enabled = highlightColorEnableCheckBox.Checked;
                    rule.EnableHighlightColor = highlightColorEnableCheckBox.Checked;
                };

                highlightColorRow.Controls.Add(highlightColorEnableCheckBox, 0, 0);
                highlightColorRow.Controls.Add(highlightColorControlsPanel, 1, 0);

                // 添加到主容器
                container.Controls.Add(fontColorRow, 0, 0);
                container.Controls.Add(highlightColorRow, 0, 1);

                return container;
            });

            // 添加所有字体面板到布局
            fontBasicPanel.Controls.Add(chineseFontPanel, 0, 0);
            fontBasicPanel.Controls.Add(westernFontPanel, 0, 1);
            fontBasicPanel.Controls.Add(complexScriptFontPanel, 0, 2);
            fontBasicPanel.Controls.Add(colorPanel, 0, 3);

            fontBasicGroup.Controls.Add(fontBasicPanel);
            mainLayout.Controls.Add(fontBasicGroup, 0, 1);

            // 创建文本效果与装饰分组框
            var textEffectsGroup = new GroupBox
            {
                Text = "文本效果与装饰",
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 5, 0, 5),
                AutoSize = true,
                Enabled = rule.EnableFontFormat
            };

            // 创建文本效果面板
            textEffectsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 启用文本效果的复选框
            var enableEffectsCheckBox = new CheckBox
            {
                Text = "启用文本效果与装饰",
                AutoSize = true,
                Checked = false // 默认不启用
            };

            textEffectsPanel.Controls.Add(enableEffectsCheckBox, 0, 0);

            // 创建效果容器
            var effectsContainer = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4, // 下划线、删除线、小型大写字母、隐藏文本
                AutoSize = true,
                Margin = new Padding(0, 5, 0, 0)
            };

            // 1. 下划线设置
            var underlinePanel = CreateLabeledControl("下划线:", () =>
            {
                var panel = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true
                };

                var checkbox = new CheckBox
                {
                    AutoSize = true,
                    Text = "",
                    Checked = rule.UnderlineEnabled
                };

                var styleCombo = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 120,
                    Enabled = checkbox.Checked,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                styleCombo.Items.AddRange(new string[] {
                    "单线", "双线", "虚线", "点线", "波浪线"
                });

                if (styleCombo.Items.Count > rule.UnderlineStyle && rule.UnderlineStyle >= 0)
                    styleCombo.SelectedIndex = rule.UnderlineStyle;
                else
                    styleCombo.SelectedIndex = 0;

                styleCombo.DrawItem += ComboBox_DrawItem;

                checkbox.CheckedChanged += (s, e) =>
                {
                    styleCombo.Enabled = checkbox.Checked;
                };

                panel.Controls.Add(checkbox, 0, 0);
                panel.Controls.Add(styleCombo, 1, 0);

                return panel;
            });

            // 2. 删除线设置
            var strikethroughPanel = CreateLabeledControl("删除线:", () =>
            {
                var panel = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true
                };

                var checkbox = new CheckBox
                {
                    AutoSize = true,
                    Text = "",
                    Checked = rule.Strikethrough
                };

                var styleCombo = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 120,
                    Enabled = checkbox.Checked,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                styleCombo.Items.AddRange(new string[] { "单线", "双线" });

                if (styleCombo.Items.Count > rule.StrikethroughStyle && rule.StrikethroughStyle >= 0)
                    styleCombo.SelectedIndex = rule.StrikethroughStyle;
                else
                    styleCombo.SelectedIndex = 0;

                styleCombo.DrawItem += ComboBox_DrawItem;

                checkbox.CheckedChanged += (s, e) =>
                {
                    styleCombo.Enabled = checkbox.Checked;
                };

                panel.Controls.Add(checkbox, 0, 0);
                panel.Controls.Add(styleCombo, 1, 0);

                return panel;
            });

            // 3. 小型大写字母
            var smallCapsPanel = CreateLabeledControl("小型大写字母:", () =>
            {
                var checkbox = new CheckBox
                {
                    AutoSize = true,
                    Text = "",
                    Checked = rule.SmallCaps
                };

                return checkbox;
            });

            // 4. 隐藏文本
            var hiddenTextPanel = CreateLabeledControl("隐藏文本:", () =>
            {
                var checkbox = new CheckBox
                {
                    AutoSize = true,
                    Text = "",
                    Checked = rule.Hidden
                };

                return checkbox;
            });

            // 添加各效果面板到容器
            effectsContainer.Controls.Add(underlinePanel, 0, 0);
            effectsContainer.Controls.Add(strikethroughPanel, 0, 1);
            effectsContainer.Controls.Add(smallCapsPanel, 0, 2);
            effectsContainer.Controls.Add(hiddenTextPanel, 0, 3);

            // 将容器添加到主效果面板
            textEffectsPanel.Controls.Add(effectsContainer, 0, 1);

            // 添加到分组框
            textEffectsGroup.Controls.Add(textEffectsPanel);

            // 添加分组框到主布局（现在是第二项，索引为1）
            mainLayout.Controls.Add(textEffectsGroup, 0, 2);

            // 添加文本间距与缩放分组框
            var textSpacingGroup = new GroupBox
            {
                Text = "文本间距与缩放",
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 5, 0, 5),
                AutoSize = true,
                Enabled = rule.EnableFontFormat
            };

            var spacingPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true
            };

            // 1. 字符间距
            var characterSpacingPanel = CreateLabeledControl("字符间距:", () =>
            {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true
                };

                var checkbox = new CheckBox
                {
                    AutoSize = true,
                    Text = "",
                    Checked = rule.EnableCharacterSpacing
                };

                var comboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 120,
                    Enabled = checkbox.Checked,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                comboBox.Items.AddRange(new string[] { "正常", "加宽", "紧缩" });
                // 根据CharacterSpacing的值设置下拉框选项
                if (rule.CharacterSpacing == 0)
                    comboBox.SelectedIndex = 0; // 正常
                else if (rule.CharacterSpacing > 0)
                    comboBox.SelectedIndex = 1; // 加宽
                else
                    comboBox.SelectedIndex = 2; // 紧缩

                comboBox.DrawItem += ComboBox_DrawItem;

                checkbox.CheckedChanged += (s, e) =>
                {
                    comboBox.Enabled = checkbox.Checked;
                    rule.EnableCharacterSpacing = checkbox.Checked;
                };

                comboBox.SelectedIndexChanged += (s, e) =>
                {
                    // 根据选择设置CharacterSpacing的值
                    if (comboBox.SelectedIndex == 0) // 正常
                        rule.CharacterSpacing = 0;
                    else if (comboBox.SelectedIndex == 1) // 加宽
                        rule.CharacterSpacing = 2.0f; // 典型的加宽值
                    else if (comboBox.SelectedIndex == 2) // 紧缩
                        rule.CharacterSpacing = -1.0f; // 典型的紧缩值
                };

                container.Controls.Add(checkbox, 0, 0);
                container.Controls.Add(comboBox, 1, 0);

                return container;
            });

            // 2. 文本缩放
            var textScalingPanel = CreateLabeledControl("文本缩放:", () =>
            {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true
                };

                var checkbox = new CheckBox
                {
                    AutoSize = true,
                    Text = "",
                    Checked = rule.EnableTextScaling
                };

                var numericUpDown = new NumericUpDown
                {
                    Minimum = 1,
                    Maximum = 600,
                    Value = (decimal)rule.TextScaling,
                    Width = 60,
                    Enabled = checkbox.Checked
                };

                checkbox.CheckedChanged += (s, e) =>
                {
                    numericUpDown.Enabled = checkbox.Checked;
                    rule.EnableTextScaling = checkbox.Checked;
                };

                numericUpDown.ValueChanged += (s, e) =>
                {
                    rule.TextScaling = (float)numericUpDown.Value;
                };

                container.Controls.Add(checkbox, 0, 0);

                var scaling = new FlowLayoutPanel
                {
                    FlowDirection = FlowDirection.LeftToRight,
                    AutoSize = true
                };

                scaling.Controls.Add(numericUpDown);
                scaling.Controls.Add(new Label { Text = "%", AutoSize = true });

                container.Controls.Add(scaling, 1, 0);

                return container;
            });

            spacingPanel.Controls.Add(characterSpacingPanel, 0, 0);
            spacingPanel.Controls.Add(textScalingPanel, 0, 1);

            textSpacingGroup.Controls.Add(spacingPanel);
            mainLayout.Controls.Add(textSpacingGroup, 0, 3);

            // 添加中文版式特性分组框
            var chineseTypographyGroup = new GroupBox
            {
                Text = "中文版式特性",
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 5, 0, 5),
                AutoSize = true,
                Enabled = rule.EnableFontFormat
            };

            var typographyPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                AutoSize = true
            };

            // 1. 启用中文版式特性
            var enableTypographyPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = true,
                Margin = new Padding(0, 0, 0, 5)
            };

            var enableTypographyCheckBox = new CheckBox
            {
                Text = "启用中文版式特性",
                AutoSize = true,
                Checked = rule.EnableChineseTypography
            };

            enableTypographyPanel.Controls.Add(enableTypographyCheckBox, 0, 0);

            // 文字方向选项
            var directionPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Enabled = enableTypographyCheckBox.Checked
            };

            var horizontalTextRadio = new RadioButton
            {
                Text = "横排",
                AutoSize = true,
                Checked = !rule.VerticalText
            };

            var verticalTextRadio = new RadioButton
            {
                Text = "直排",
                AutoSize = true,
                Checked = rule.VerticalText,
                Margin = new Padding(10, 0, 0, 0)
            };

            horizontalTextRadio.CheckedChanged += (s, e) =>
            {
                if (horizontalTextRadio.Checked)
                    rule.VerticalText = false;
            };

            verticalTextRadio.CheckedChanged += (s, e) =>
            {
                if (verticalTextRadio.Checked)
                    rule.VerticalText = true;
            };

            directionPanel.Controls.Add(horizontalTextRadio);
            directionPanel.Controls.Add(verticalTextRadio);

            enableTypographyPanel.Controls.Add(directionPanel, 1, 0);

            // 2. 标点压缩
            var punctuationPanel = CreateLabeledControl("标点压缩:", () =>
            {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true
                };

                var punctCheckBox = new CheckBox
                {
                    AutoSize = true,
                    Text = "",
                    Checked = rule.EnablePunctuationCompression,
                    Enabled = enableTypographyCheckBox.Checked
                };

                var punctComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 200,
                    Enabled = punctCheckBox.Checked && enableTypographyCheckBox.Checked,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                punctComboBox.Items.AddRange(new string[] { "无", "简易（仅压缩标点）", "完全（压缩标点和部分字符）" });
                punctComboBox.SelectedIndex = rule.PunctuationCompressionLevel;
                punctComboBox.DrawItem += ComboBox_DrawItem;

                punctCheckBox.CheckedChanged += (s, e) =>
                {
                    punctComboBox.Enabled = punctCheckBox.Checked && enableTypographyCheckBox.Checked;
                    rule.EnablePunctuationCompression = punctCheckBox.Checked;
                };

                punctComboBox.SelectedIndexChanged += (s, e) =>
                {
                    rule.PunctuationCompressionLevel = punctComboBox.SelectedIndex;
                };

                container.Controls.Add(punctCheckBox, 0, 0);
                container.Controls.Add(punctComboBox, 1, 0);

                return container;
            });

            // 3. 网格对齐方式
            var gridAlignmentPanel = CreateLabeledControl("网格对齐:", () =>
            {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true
                };

                var gridCheckBox = new CheckBox
                {
                    AutoSize = true,
                    Text = "",
                    Checked = rule.EnableChineseGridAlignment,
                    Enabled = enableTypographyCheckBox.Checked
                };

                var gridComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 120,
                    Enabled = gridCheckBox.Checked && enableTypographyCheckBox.Checked,
                    DrawMode = DrawMode.OwnerDrawFixed
                };

                gridComboBox.Items.AddRange(new string[] { "无", "对齐到字符", "对齐到网格线" });
                gridComboBox.SelectedIndex = rule.GridAlignmentLevel;
                gridComboBox.DrawItem += ComboBox_DrawItem;

                gridCheckBox.CheckedChanged += (s, e) =>
                {
                    gridComboBox.Enabled = gridCheckBox.Checked && enableTypographyCheckBox.Checked;
                    rule.EnableChineseGridAlignment = gridCheckBox.Checked;
                };

                gridComboBox.SelectedIndexChanged += (s, e) =>
                {
                    rule.GridAlignmentLevel = gridComboBox.SelectedIndex;
                };

                container.Controls.Add(gridCheckBox, 0, 0);
                container.Controls.Add(gridComboBox, 1, 0);

                return container;
            });

            // 中文版式特性选项启用/禁用事件
            enableTypographyCheckBox.CheckedChanged += (s, e) =>
            {
                bool enabled = enableTypographyCheckBox.Checked;
                directionPanel.Enabled = enabled;
                rule.EnableChineseTypography = enabled;

                // 更新依赖控件状态
                foreach (Control control in punctuationPanel.Controls)
                {
                    if (control is TableLayoutPanel tlp && tlp.Controls.Count > 0)
                    {
                        var checkbox = tlp.Controls[0] as CheckBox;
                        var combobox = tlp.Controls[1] as ComboBox;

                        if (checkbox != null)
                        {
                            checkbox.Enabled = enabled;
                            if (combobox != null)
                                combobox.Enabled = enabled && checkbox.Checked;
                        }
                    }
                }

                foreach (Control control in gridAlignmentPanel.Controls)
                {
                    if (control is TableLayoutPanel tlp && tlp.Controls.Count > 0)
                    {
                        var checkbox = tlp.Controls[0] as CheckBox;
                        var combobox = tlp.Controls[1] as ComboBox;

                        if (checkbox != null)
                        {
                            checkbox.Enabled = enabled;
                            if (combobox != null)
                                combobox.Enabled = enabled && checkbox.Checked;
                        }
                    }
                }
            };

            typographyPanel.Controls.Add(enableTypographyPanel, 0, 0);
            typographyPanel.Controls.Add(punctuationPanel, 0, 1);
            typographyPanel.Controls.Add(gridAlignmentPanel, 0, 2);

            chineseTypographyGroup.Controls.Add(typographyPanel);
            mainLayout.Controls.Add(chineseTypographyGroup, 0, 4);

            // 添加字体格式总启用控制的事件处理
            fontFormatEnableCheckBox.CheckedChanged += (s, e) =>
            {
                bool enabled = fontFormatEnableCheckBox.Checked;
                rule.EnableFontFormat = enabled;

                // 控制所有字体格式相关分组的启用状态
                fontBasicGroup.Enabled = enabled;
                textEffectsGroup.Enabled = enabled;
                textSpacingGroup.Enabled = enabled;
                chineseTypographyGroup.Enabled = enabled;
            };

            // 将主布局添加到面板
            panel.Controls.Add(mainLayout);

            return panel;
        }

        // ComboBox居中绘制事件处理程序
        private void ComboBox_DrawItem(object? sender, DrawItemEventArgs e)
        {
            if (sender is not ComboBox comboBox || e.Index < 0 || e.Index >= comboBox.Items.Count) return;

            e.DrawBackground();

            string text = comboBox.Items[e.Index]?.ToString() ?? "";

            // 使用控件字体或默认字体
            Font font = e.Font ?? comboBox.Font ?? SystemFonts.DefaultFont;

            // 计算文本居中位置
            var textSize = e.Graphics.MeasureString(text, font);
            var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
            var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;

            // 绘制文本
            using (var brush = new SolidBrush(e.ForeColor))
            {
                e.Graphics.DrawString(text, font, brush, x, y);
            }

            e.DrawFocusRectangle();
        }

        // 从系统加载所有已安装的字体到ComboBox
        private void LoadSystemFonts(ComboBox fontComboBox)
        {
            try
            {
                fontComboBox.Items.Clear();

                // 获取系统中所有已安装的字体
                var fontFamilies = System.Drawing.FontFamily.Families;

                // 按字体名称排序并添加到ComboBox
                var sortedFonts = fontFamilies
                    .Select(f => f.Name)
                    .OrderBy(name => name)
                    .ToArray();

                fontComboBox.Items.AddRange(sortedFonts);
            }
            catch (Exception ex)
            {
                // 如果加载系统字体失败，添加一些常用字体作为备选
                fontComboBox.Items.Clear();
                fontComboBox.Items.AddRange(new string[]
                {
                    "宋体", "黑体", "微软雅黑", "Arial", "Times New Roman", "Calibri"
                });

                System.Diagnostics.Debug.WriteLine($"加载系统字体失败: {ex.Message}");
            }
        }

        // 设置ComboBox的默认字体选择
        private void SetDefaultFont(ComboBox fontComboBox, string preferredFont)
        {
            if (fontComboBox.Items.Count == 0) return;

            // 尝试找到首选字体
            for (int i = 0; i < fontComboBox.Items.Count; i++)
            {
                if (fontComboBox.Items[i]?.ToString() == preferredFont)
                {
                    fontComboBox.SelectedIndex = i;
                    return;
                }
            }

            // 如果没有找到首选字体，选择第一个
            fontComboBox.SelectedIndex = 0;
        }



        // 从磅值获取对应的中文字号名称
        private string GetChineseFontSizeName(double points)
        {
            // 根据磅值返回最接近的中文字号
            if (Math.Abs(points - 42) < 0.1) return "初号";
            if (Math.Abs(points - 36) < 0.1) return "小初";
            if (Math.Abs(points - 26) < 0.1) return "一号";
            if (Math.Abs(points - 24) < 0.1) return "小一";
            if (Math.Abs(points - 22) < 0.1) return "二号";
            if (Math.Abs(points - 18) < 0.1) return "小二";
            if (Math.Abs(points - 16) < 0.1) return "三号";
            if (Math.Abs(points - 15) < 0.1) return "小三";
            if (Math.Abs(points - 14) < 0.1) return "四号";
            if (Math.Abs(points - 12) < 0.1) return "小四";
            if (Math.Abs(points - 10.5) < 0.1) return "五号";
            if (Math.Abs(points - 9) < 0.1) return "小五";
            if (Math.Abs(points - 7.5) < 0.1) return "六号";
            if (Math.Abs(points - 6.5) < 0.1) return "小六";
            if (Math.Abs(points - 5.5) < 0.1) return "七号";
            if (Math.Abs(points - 5) < 0.1) return "小七";
            if (Math.Abs(points - 4.5) < 0.1) return "八号";
            if (Math.Abs(points - 4) < 0.1) return "小八";

            // 如果不是标准字号，返回磅值
            return points.ToString("0.#");
        }

        // 从中文字号名称获取对应的磅值
        private double GetPointSizeFromChineseFontName(string fontName)
        {
            return fontName switch
            {
                "初号" => 42,
                "小初" => 36,
                "一号" => 26,
                "小一" => 24,
                "二号" => 22,
                "小二" => 18,
                "三号" => 16,
                "小三" => 15,
                "四号" => 14,
                "小四" => 12,
                "五号" => 10.5,
                "小五" => 9,
                "六号" => 7.5,
                "小六" => 6.5,
                "七号" => 5.5,
                "小七" => 5,
                "八号" => 4.5,
                "小八" => 4,
                _ => 10.5 // 默认五号字体
            };
        }

        // 创建高级格式面板
        private Panel CreateAdvancedFormatPanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill, AutoScroll = true };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 6, // 增加行数以容纳新增控件
                Padding = new Padding(10),
                AutoSize = true
            };

            // 设置行高
            for (int i = 0; i < 6; i++)
            {
                layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 1. 基线对齐
            var baselineAlignmentPanel = CreateLabeledControl("基线对齐：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 2,
                    RowCount = 1,
                    AutoSize = true
                };

                var enableCheckBox = new CheckBox
                {
                    Text = "启用",
                    AutoSize = true,
                    Checked = rule.BaselineAlignment != AW.BaselineAlignment.Auto
                };

                var comboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 150,
                    Enabled = enableCheckBox.Checked,
                    DrawMode = DrawMode.OwnerDrawFixed
                };
                comboBox.Items.AddRange(new string[] { "自动", "顶部", "居中", "基线", "底部" });
                comboBox.SelectedIndex = (int)rule.BaselineAlignment;
                comboBox.DrawItem += ComboBox_DrawItem;

                enableCheckBox.CheckedChanged += (s, e) => {
                    comboBox.Enabled = enableCheckBox.Checked;
                    if (!enableCheckBox.Checked)
                    {
                        rule.BaselineAlignment = AW.BaselineAlignment.Auto;
                        comboBox.SelectedIndex = 0;
                    }
                };

                comboBox.SelectedIndexChanged += (s, e) => {
                    rule.BaselineAlignment = (AW.BaselineAlignment)comboBox.SelectedIndex;
                };

                container.Controls.Add(enableCheckBox, 0, 0);
                container.Controls.Add(comboBox, 1, 0);

                return container;
            });
            layout.Controls.Add(baselineAlignmentPanel, 0, 0);

            // 2. 首字下沉
            var dropCapPanel = CreateLabeledControl("首字下沉：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 4,
                    RowCount = 1,
                    AutoSize = true
                };

                var enableCheckBox = new CheckBox
                {
                    Text = "启用",
                    AutoSize = true,
                    Checked = rule.EnableDropCap
                };

                var positionComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 80,
                    Enabled = rule.EnableDropCap,
                    DrawMode = DrawMode.OwnerDrawFixed
                };
                positionComboBox.Items.AddRange(new string[] { "无", "下沉", "悬挂" });
                positionComboBox.SelectedIndex = (int)rule.DropCapPosition;
                positionComboBox.DrawItem += ComboBox_DrawItem;

                var linesLabel = new Label
                {
                    Text = "行数:",
                    AutoSize = true,
                    Enabled = rule.EnableDropCap
                };

                var linesNumeric = new NumericUpDown
                {
                    Minimum = 1,
                    Maximum = 10,
                    Value = rule.LinesToDrop,
                    Width = 60,
                    Enabled = rule.EnableDropCap,
                    TextAlign = HorizontalAlignment.Center
                };

                enableCheckBox.CheckedChanged += (s, e) => {
                    bool enabled = enableCheckBox.Checked;
                    positionComboBox.Enabled = enabled;
                    linesLabel.Enabled = enabled;
                    linesNumeric.Enabled = enabled;
                    rule.EnableDropCap = enabled;

                    if (!enabled)
                    {
                        rule.DropCapPosition = AW.DropCapPosition.None;
                        positionComboBox.SelectedIndex = 0;
                    }
                };

                positionComboBox.SelectedIndexChanged += (s, e) => {
                    rule.DropCapPosition = (AW.DropCapPosition)positionComboBox.SelectedIndex;
                };

                linesNumeric.ValueChanged += (s, e) => {
                    rule.LinesToDrop = (int)linesNumeric.Value;
                };

                container.Controls.Add(enableCheckBox, 0, 0);
                container.Controls.Add(positionComboBox, 1, 0);
                container.Controls.Add(linesLabel, 2, 0);
                container.Controls.Add(linesNumeric, 3, 0);

                return container;
            });
            layout.Controls.Add(dropCapPanel, 0, 1);

            // 3. 段落间距自动调整
            var autoSpacingPanel = CreateLabeledControl("自动间距：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 3,
                    RowCount = 1,
                    AutoSize = true
                };

                bool autoSpacingEnabled = rule.SpaceBeforeAuto || rule.SpaceAfterAuto;

                var enableCheckBox = new CheckBox
                {
                    Text = "启用",
                    AutoSize = true,
                    Checked = autoSpacingEnabled
                };

                var beforeCheckBox = new CheckBox
                {
                    Text = "段前自动",
                    AutoSize = true,
                    Enabled = autoSpacingEnabled,
                    Checked = rule.SpaceBeforeAuto
                };

                var afterCheckBox = new CheckBox
                {
                    Text = "段后自动",
                    AutoSize = true,
                    Enabled = autoSpacingEnabled,
                    Checked = rule.SpaceAfterAuto
                };

                enableCheckBox.CheckedChanged += (s, e) => {
                    bool enabled = enableCheckBox.Checked;
                    beforeCheckBox.Enabled = enabled;
                    afterCheckBox.Enabled = enabled;

                    if (!enabled)
                    {
                        rule.SpaceBeforeAuto = false;
                        rule.SpaceAfterAuto = false;
                        beforeCheckBox.Checked = false;
                        afterCheckBox.Checked = false;
                    }
                };

                beforeCheckBox.CheckedChanged += (s, e) => {
                    rule.SpaceBeforeAuto = beforeCheckBox.Checked;
                };

                afterCheckBox.CheckedChanged += (s, e) => {
                    rule.SpaceAfterAuto = afterCheckBox.Checked;
                };

                container.Controls.Add(enableCheckBox, 0, 0);
                container.Controls.Add(beforeCheckBox, 1, 0);
                container.Controls.Add(afterCheckBox, 2, 0);

                return container;
            });
            layout.Controls.Add(autoSpacingPanel, 0, 2);

            // 4. 其他高级选项
            var advancedOptionsPanel = CreateLabeledControl("高级选项：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 1,
                    RowCount = 7, // 增加行数以容纳新增控件
                    AutoSize = true
                };

                bool advancedEnabled = rule.SuppressAutoHyphens || rule.SnapToGrid ||
                                     rule.WordWrap || rule.FarEastLineBreakControl ||
                                     rule.MirrorIndents || rule.SuppressLineNumbers;

                var enableCheckBox = new CheckBox
                {
                    Text = "启用高级选项",
                    AutoSize = true,
                    Checked = advancedEnabled
                };

                var suppressHyphensCheckBox = new CheckBox
                {
                    Text = "禁止自动断字",
                    AutoSize = true,
                    Enabled = advancedEnabled,
                    Checked = rule.SuppressAutoHyphens
                };

                var snapToGridCheckBox = new CheckBox
                {
                    Text = "对齐到网格",
                    AutoSize = true,
                    Enabled = advancedEnabled,
                    Checked = rule.SnapToGrid
                };

                var wordWrapCheckBox = new CheckBox
                {
                    Text = "允许西文在单词中间换行",
                    AutoSize = true,
                    Enabled = advancedEnabled,
                    Checked = rule.WordWrap
                };

                var lineBreakControlCheckBox = new CheckBox
                {
                    Text = "允许标点溢出边界",
                    AutoSize = true,
                    Enabled = advancedEnabled,
                    Checked = rule.FarEastLineBreakControl
                };

                var mirrorIndentsCheckBox = new CheckBox
                {
                    Text = "镜像缩进（双面打印）",
                    AutoSize = true,
                    Enabled = advancedEnabled,
                    Checked = rule.MirrorIndents
                };

                var suppressLineNumbersCheckBox = new CheckBox
                {
                    Text = "禁止行号显示",
                    AutoSize = true,
                    Enabled = advancedEnabled,
                    Checked = rule.SuppressLineNumbers
                };

                enableCheckBox.CheckedChanged += (s, e) => {
                    bool enabled = enableCheckBox.Checked;
                    suppressHyphensCheckBox.Enabled = enabled;
                    snapToGridCheckBox.Enabled = enabled;
                    wordWrapCheckBox.Enabled = enabled;
                    lineBreakControlCheckBox.Enabled = enabled;
                    mirrorIndentsCheckBox.Enabled = enabled;
                    suppressLineNumbersCheckBox.Enabled = enabled;

                    if (!enabled)
                    {
                        rule.SuppressAutoHyphens = false;
                        rule.SnapToGrid = false;
                        rule.WordWrap = false; // 默认启用
                        rule.FarEastLineBreakControl = false;
                        rule.MirrorIndents = false;
                        rule.SuppressLineNumbers = false;

                        suppressHyphensCheckBox.Checked = false;
                        snapToGridCheckBox.Checked = false;
                        wordWrapCheckBox.Checked = true;
                        lineBreakControlCheckBox.Checked = false;
                        mirrorIndentsCheckBox.Checked = false;
                        suppressLineNumbersCheckBox.Checked = false;
                    }
                };

                suppressHyphensCheckBox.CheckedChanged += (s, e) => {
                    rule.SuppressAutoHyphens = suppressHyphensCheckBox.Checked;
                };

                snapToGridCheckBox.CheckedChanged += (s, e) => {
                    rule.SnapToGrid = snapToGridCheckBox.Checked;
                };

                wordWrapCheckBox.CheckedChanged += (s, e) => {
                    rule.WordWrap = wordWrapCheckBox.Checked;
                };

                lineBreakControlCheckBox.CheckedChanged += (s, e) => {
                    rule.FarEastLineBreakControl = lineBreakControlCheckBox.Checked;
                };

                mirrorIndentsCheckBox.CheckedChanged += (s, e) => {
                    rule.MirrorIndents = mirrorIndentsCheckBox.Checked;
                };

                suppressLineNumbersCheckBox.CheckedChanged += (s, e) => {
                    rule.SuppressLineNumbers = suppressLineNumbersCheckBox.Checked;
                };

                container.Controls.Add(enableCheckBox, 0, 0);
                container.Controls.Add(suppressHyphensCheckBox, 0, 1);
                container.Controls.Add(snapToGridCheckBox, 0, 2);
                container.Controls.Add(wordWrapCheckBox, 0, 3);
                container.Controls.Add(lineBreakControlCheckBox, 0, 4);
                container.Controls.Add(mirrorIndentsCheckBox, 0, 5);
                container.Controls.Add(suppressLineNumbersCheckBox, 0, 6);

                return container;
            });
            layout.Controls.Add(advancedOptionsPanel, 0, 3);

            // 5. 行单位间距
            var lineUnitSpacingPanel = CreateLabeledControl("行单位间距：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 1,
                    RowCount = 2,
                    AutoSize = true
                };

                var enableCheckBox = new CheckBox
                {
                    Text = "启用行单位间距",
                    AutoSize = true,
                    Checked = rule.EnableLineUnitSpacing
                };

                var spacingContainer = new TableLayoutPanel
                {
                    ColumnCount = 6,
                    RowCount = 1,
                    AutoSize = true,
                    Enabled = rule.EnableLineUnitSpacing
                };

                var beforeLabel = new Label { Text = "段前:", AutoSize = true };
                var beforeNumeric = new NumericUpDown
                {
                    Minimum = 0,
                    Maximum = 100,
                    DecimalPlaces = 1,
                    Increment = 0.5m,
                    Value = (decimal)rule.LineUnitBefore,
                    Width = 80,
                    TextAlign = HorizontalAlignment.Center
                };
                var beforeUnitLabel = new Label { Text = "行", AutoSize = true };

                var afterLabel = new Label { Text = "段后:", AutoSize = true };
                var afterNumeric = new NumericUpDown
                {
                    Minimum = 0,
                    Maximum = 100,
                    DecimalPlaces = 1,
                    Increment = 0.5m,
                    Value = (decimal)rule.LineUnitAfter,
                    Width = 80,
                    TextAlign = HorizontalAlignment.Center
                };
                var afterUnitLabel = new Label { Text = "行", AutoSize = true };

                enableCheckBox.CheckedChanged += (s, e) => {
                    spacingContainer.Enabled = enableCheckBox.Checked;
                    rule.EnableLineUnitSpacing = enableCheckBox.Checked;

                    if (!enableCheckBox.Checked)
                    {
                        rule.LineUnitBefore = 0;
                        rule.LineUnitAfter = 0;
                        beforeNumeric.Value = 0;
                        afterNumeric.Value = 0;
                    }
                };

                beforeNumeric.ValueChanged += (s, e) => {
                    rule.LineUnitBefore = (double)beforeNumeric.Value;
                };

                afterNumeric.ValueChanged += (s, e) => {
                    rule.LineUnitAfter = (double)afterNumeric.Value;
                };

                spacingContainer.Controls.Add(beforeLabel, 0, 0);
                spacingContainer.Controls.Add(beforeNumeric, 1, 0);
                spacingContainer.Controls.Add(beforeUnitLabel, 2, 0);
                spacingContainer.Controls.Add(afterLabel, 3, 0);
                spacingContainer.Controls.Add(afterNumeric, 4, 0);
                spacingContainer.Controls.Add(afterUnitLabel, 5, 0);

                container.Controls.Add(enableCheckBox, 0, 0);
                container.Controls.Add(spacingContainer, 0, 1);

                return container;
            });
            layout.Controls.Add(lineUnitSpacingPanel, 0, 4);

            panel.Controls.Add(layout);
            return panel;
        }

        // 创建中文排版面板
        private Panel CreateChineseTypographyPanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill, AutoScroll = true };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 4,
                Padding = new Padding(10),
                AutoSize = true
            };

            // 设置行高
            for (int i = 0; i < 4; i++)
            {
                layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 1. 中西文间距
            var spacingPanel = CreateLabeledControl("中西文间距：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 3,
                    RowCount = 1,
                    AutoSize = true
                };

                // 中西文间距的启用状态基于是否有任一子选项被启用
                bool spacingEnabled = rule.AddSpaceBetweenFarEastAndAlpha || rule.AddSpaceBetweenFarEastAndDigit;

                var enableCheckBox = new CheckBox
                {
                    Text = "启用",
                    AutoSize = true,
                    Checked = spacingEnabled
                };

                var alphaCheckBox = new CheckBox
                {
                    Text = "中文与西文之间自动加空格",
                    AutoSize = true,
                    Enabled = spacingEnabled,
                    Checked = rule.AddSpaceBetweenFarEastAndAlpha
                };

                var digitCheckBox = new CheckBox
                {
                    Text = "中文与数字之间自动加空格",
                    AutoSize = true,
                    Enabled = spacingEnabled,
                    Checked = rule.AddSpaceBetweenFarEastAndDigit
                };

                enableCheckBox.CheckedChanged += (s, e) => {
                    bool enabled = enableCheckBox.Checked;
                    alphaCheckBox.Enabled = enabled;
                    digitCheckBox.Enabled = enabled;

                    if (!enabled)
                    {
                        rule.AddSpaceBetweenFarEastAndAlpha = false;
                        rule.AddSpaceBetweenFarEastAndDigit = false;
                        alphaCheckBox.Checked = false;
                        digitCheckBox.Checked = false;
                    }
                    else
                    {
                        // 当启用时，如果两个子选项都未选中，则默认选中第一个
                        if (!alphaCheckBox.Checked && !digitCheckBox.Checked)
                        {
                            alphaCheckBox.Checked = true;
                            rule.AddSpaceBetweenFarEastAndAlpha = true;
                        }
                    }
                };

                alphaCheckBox.CheckedChanged += (s, e) => {
                    rule.AddSpaceBetweenFarEastAndAlpha = alphaCheckBox.Checked;
                };

                digitCheckBox.CheckedChanged += (s, e) => {
                    rule.AddSpaceBetweenFarEastAndDigit = digitCheckBox.Checked;
                };

                container.Controls.Add(enableCheckBox, 0, 0);
                container.Controls.Add(alphaCheckBox, 1, 0);
                container.Controls.Add(digitCheckBox, 2, 0);

                return container;
            });
            layout.Controls.Add(spacingPanel, 0, 0);

            // 2. 标点压缩
            var punctuationPanel = CreateLabeledControl("标点压缩：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 3,
                    RowCount = 1,
                    AutoSize = true
                };

                var enableCheckBox = new CheckBox
                {
                    Text = "启用",
                    AutoSize = true,
                    Checked = rule.EnablePunctuationCompression
                };

                var typeLabel = new Label
                {
                    Text = "类型:",
                    AutoSize = true,
                    Enabled = rule.EnablePunctuationCompression
                };

                var typeComboBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Width = 200,
                    Enabled = rule.EnablePunctuationCompression,
                    DrawMode = DrawMode.OwnerDrawFixed
                };
                typeComboBox.Items.AddRange(new string[] { "无", "简易（仅压缩标点）", "完全（压缩标点和部分字符）" });
                typeComboBox.SelectedIndex = rule.PunctuationCompressionLevel;
                typeComboBox.DrawItem += ComboBox_DrawItem;

                enableCheckBox.CheckedChanged += (s, e) => {
                    bool enabled = enableCheckBox.Checked;
                    typeLabel.Enabled = enabled;
                    typeComboBox.Enabled = enabled;
                    rule.EnablePunctuationCompression = enabled;

                    if (!enabled)
                    {
                        rule.PunctuationCompressionLevel = 0;
                        typeComboBox.SelectedIndex = 0;
                    }
                };

                typeComboBox.SelectedIndexChanged += (s, e) => {
                    rule.PunctuationCompressionLevel = typeComboBox.SelectedIndex;
                };

                container.Controls.Add(enableCheckBox, 0, 0);
                container.Controls.Add(typeLabel, 1, 0);
                container.Controls.Add(typeComboBox, 2, 0);

                return container;
            });
            layout.Controls.Add(punctuationPanel, 0, 1);

            // 3. 文字方向
            var directionPanel = CreateLabeledControl("文字方向：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 3,
                    RowCount = 1,
                    AutoSize = true
                };

                // 文字方向有独立的启用控制
                bool directionEnabled = rule.EnableTextDirection;

                var enableCheckBox = new CheckBox
                {
                    Text = "启用",
                    AutoSize = true,
                    Checked = directionEnabled
                };

                var horizontalRadio = new RadioButton
                {
                    Text = "水平",
                    AutoSize = true,
                    Checked = !rule.VerticalText,
                    Enabled = directionEnabled
                };

                var verticalRadio = new RadioButton
                {
                    Text = "垂直",
                    AutoSize = true,
                    Checked = rule.VerticalText,
                    Enabled = directionEnabled
                };

                enableCheckBox.CheckedChanged += (s, e) => {
                    bool enabled = enableCheckBox.Checked;
                    horizontalRadio.Enabled = enabled;
                    verticalRadio.Enabled = enabled;
                    rule.EnableTextDirection = enabled;

                    if (!enabled)
                    {
                        rule.VerticalText = false;
                        horizontalRadio.Checked = true;
                        verticalRadio.Checked = false;
                    }
                };

                horizontalRadio.CheckedChanged += (s, e) => {
                    if (horizontalRadio.Checked)
                    {
                        rule.VerticalText = false;
                    }
                };

                verticalRadio.CheckedChanged += (s, e) => {
                    if (verticalRadio.Checked)
                    {
                        rule.VerticalText = true;
                    }
                };

                container.Controls.Add(enableCheckBox, 0, 0);
                container.Controls.Add(horizontalRadio, 1, 0);
                container.Controls.Add(verticalRadio, 2, 0);

                return container;
            });
            layout.Controls.Add(directionPanel, 0, 2);

            // 4. 悬挂标点
            var hangingPunctuationPanel = CreateLabeledControl("悬挂标点：", () => {
                var checkBox = new CheckBox
                {
                    Text = "允许标点悬挂",
                    AutoSize = true,
                    Checked = rule.HangingPunctuation
                };

                checkBox.CheckedChanged += (s, e) => {
                    rule.HangingPunctuation = checkBox.Checked;
                };

                return checkBox;
            });
            layout.Controls.Add(hangingPunctuationPanel, 0, 3);

            panel.Controls.Add(layout);
            return panel;
        }

        // 创建字符单位面板
        private Panel CreateCharacterUnitPanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill, AutoScroll = true };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(10),
                AutoSize = true
            };

            // 设置行高
            for (int i = 0; i < 3; i++)
            {
                layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 1. 字符单位缩进
            var indentPanel = CreateLabeledControl("字符单位缩进：", () => {
                var mainContainer = new TableLayoutPanel
                {
                    ColumnCount = 1,
                    RowCount = 2,
                    AutoSize = true
                };

                var enableCheckBox = new CheckBox
                {
                    Text = "启用字符单位缩进",
                    AutoSize = true,
                    Checked = rule.EnableCharacterUnitIndent
                };

                var container = new TableLayoutPanel
                {
                    ColumnCount = 6,
                    RowCount = 2,
                    AutoSize = true,
                    Enabled = rule.EnableCharacterUnitIndent
                };

                // 第一行：首行缩进
                var firstLineLabel = new Label { Text = "首行:", AutoSize = true };
                var firstLineNumeric = new NumericUpDown
                {
                    Minimum = -100,
                    Maximum = 100,
                    DecimalPlaces = 1,
                    Increment = 0.5m,
                    Value = (decimal)rule.CharacterUnitFirstLineIndent,
                    Width = 80,
                    TextAlign = HorizontalAlignment.Center
                };
                var firstLineUnitLabel = new Label { Text = "字符", AutoSize = true };

                // 左缩进
                var leftLabel = new Label { Text = "左:", AutoSize = true };
                var leftNumeric = new NumericUpDown
                {
                    Minimum = 0,
                    Maximum = 100,
                    DecimalPlaces = 1,
                    Increment = 0.5m,
                    Value = (decimal)rule.CharacterUnitLeftIndent,
                    Width = 80,
                    TextAlign = HorizontalAlignment.Center
                };
                var leftUnitLabel = new Label { Text = "字符", AutoSize = true };

                container.Controls.Add(firstLineLabel, 0, 0);
                container.Controls.Add(firstLineNumeric, 1, 0);
                container.Controls.Add(firstLineUnitLabel, 2, 0);
                container.Controls.Add(leftLabel, 3, 0);
                container.Controls.Add(leftNumeric, 4, 0);
                container.Controls.Add(leftUnitLabel, 5, 0);

                // 第二行：右缩进和悬挂缩进
                var rightLabel = new Label { Text = "右:", AutoSize = true };
                var rightNumeric = new NumericUpDown
                {
                    Minimum = 0,
                    Maximum = 100,
                    DecimalPlaces = 1,
                    Increment = 0.5m,
                    Value = (decimal)rule.CharacterUnitRightIndent,
                    Width = 80,
                    TextAlign = HorizontalAlignment.Center
                };
                var rightUnitLabel = new Label { Text = "字符", AutoSize = true };

                // 注意：悬挂缩进在字符单位中通过首行缩进的负值实现，不需要单独的控件
                // 这里我们添加一个说明标签
                var noteLabel = new Label
                {
                    Text = "注：悬挂缩进通过首行缩进负值实现",
                    AutoSize = true,
                    ForeColor = Color.Gray,
                    Font = new Font(Font.FontFamily, Font.Size - 1)
                };

                container.Controls.Add(rightLabel, 0, 1);
                container.Controls.Add(rightNumeric, 1, 1);
                container.Controls.Add(rightUnitLabel, 2, 1);
                container.Controls.Add(noteLabel, 3, 1);
                container.SetColumnSpan(noteLabel, 3); // 跨越3列显示说明

                enableCheckBox.CheckedChanged += (s, e) => {
                    container.Enabled = enableCheckBox.Checked;
                    rule.EnableCharacterUnitIndent = enableCheckBox.Checked;

                    if (!enableCheckBox.Checked)
                    {
                        rule.CharacterUnitFirstLineIndent = 0;
                        rule.CharacterUnitLeftIndent = 0;
                        rule.CharacterUnitRightIndent = 0;
                        firstLineNumeric.Value = 0;
                        leftNumeric.Value = 0;
                        rightNumeric.Value = 0;
                    }
                };

                firstLineNumeric.ValueChanged += (s, e) => {
                    rule.CharacterUnitFirstLineIndent = (double)firstLineNumeric.Value;
                };

                leftNumeric.ValueChanged += (s, e) => {
                    rule.CharacterUnitLeftIndent = (double)leftNumeric.Value;
                };

                rightNumeric.ValueChanged += (s, e) => {
                    rule.CharacterUnitRightIndent = (double)rightNumeric.Value;
                };

                mainContainer.Controls.Add(enableCheckBox, 0, 0);
                mainContainer.Controls.Add(container, 0, 1);

                return mainContainer;
            });
            layout.Controls.Add(indentPanel, 0, 0);

            // 2. 字符单位间距
            var spacingPanel = CreateLabeledControl("字符单位间距：", () => {
                var mainContainer = new TableLayoutPanel
                {
                    ColumnCount = 1,
                    RowCount = 2,
                    AutoSize = true
                };

                var enableCheckBox = new CheckBox
                {
                    Text = "启用字符单位间距",
                    AutoSize = true,
                    Checked = rule.EnableCharacterUnitSpacing
                };

                var container = new TableLayoutPanel
                {
                    ColumnCount = 6,
                    RowCount = 1,
                    AutoSize = true,
                    Enabled = rule.EnableCharacterUnitSpacing
                };

                var beforeLabel = new Label { Text = "段前:", AutoSize = true };
                var beforeNumeric = new NumericUpDown
                {
                    Minimum = 0,
                    Maximum = 100,
                    DecimalPlaces = 1,
                    Increment = 0.5m,
                    Value = (decimal)rule.CharacterUnitBefore,
                    Width = 80,
                    TextAlign = HorizontalAlignment.Center
                };
                var beforeUnitLabel = new Label { Text = "字符", AutoSize = true };

                var afterLabel = new Label { Text = "段后:", AutoSize = true };
                var afterNumeric = new NumericUpDown
                {
                    Minimum = 0,
                    Maximum = 100,
                    DecimalPlaces = 1,
                    Increment = 0.5m,
                    Value = (decimal)rule.CharacterUnitAfter,
                    Width = 80,
                    TextAlign = HorizontalAlignment.Center
                };
                var afterUnitLabel = new Label { Text = "字符", AutoSize = true };

                container.Controls.Add(beforeLabel, 0, 0);
                container.Controls.Add(beforeNumeric, 1, 0);
                container.Controls.Add(beforeUnitLabel, 2, 0);
                container.Controls.Add(afterLabel, 3, 0);
                container.Controls.Add(afterNumeric, 4, 0);
                container.Controls.Add(afterUnitLabel, 5, 0);

                enableCheckBox.CheckedChanged += (s, e) => {
                    container.Enabled = enableCheckBox.Checked;
                    rule.EnableCharacterUnitSpacing = enableCheckBox.Checked;

                    if (!enableCheckBox.Checked)
                    {
                        rule.CharacterUnitBefore = 0;
                        rule.CharacterUnitAfter = 0;
                        beforeNumeric.Value = 0;
                        afterNumeric.Value = 0;
                    }
                };

                beforeNumeric.ValueChanged += (s, e) => {
                    rule.CharacterUnitBefore = (double)beforeNumeric.Value;
                };

                afterNumeric.ValueChanged += (s, e) => {
                    rule.CharacterUnitAfter = (double)afterNumeric.Value;
                };

                mainContainer.Controls.Add(enableCheckBox, 0, 0);
                mainContainer.Controls.Add(container, 0, 1);

                return mainContainer;
            });
            layout.Controls.Add(spacingPanel, 0, 1);

            // 3. 字符单位行距设置
            var lineSpacingPanel = CreateLabeledControl("字符单位行距：", () => {
                var container = new TableLayoutPanel
                {
                    ColumnCount = 1,
                    RowCount = 2,
                    AutoSize = true
                };

                var enableLineSpacingCheckBox = new CheckBox
                {
                    Text = "启用字符单位行距",
                    AutoSize = true,
                    Checked = rule.EnableCharacterUnitLineSpacing
                };

                var lineSpacingContainer = new TableLayoutPanel
                {
                    ColumnCount = 4,
                    RowCount = 1,
                    AutoSize = true,
                    Enabled = rule.EnableCharacterUnitLineSpacing
                };

                var lineSpacingLabel = new Label { Text = "行距:", AutoSize = true };
                var lineSpacingNumeric = new NumericUpDown
                {
                    Minimum = 0.5m,
                    Maximum = 10,
                    DecimalPlaces = 1,
                    Increment = 0.1m,
                    Value = (decimal)rule.CharacterUnitLineSpacing,
                    Width = 80,
                    TextAlign = HorizontalAlignment.Center
                };
                var lineSpacingUnitLabel = new Label { Text = "字符", AutoSize = true };

                lineSpacingContainer.Controls.Add(lineSpacingLabel, 0, 0);
                lineSpacingContainer.Controls.Add(lineSpacingNumeric, 1, 0);
                lineSpacingContainer.Controls.Add(lineSpacingUnitLabel, 2, 0);

                enableLineSpacingCheckBox.CheckedChanged += (s, e) => {
                    lineSpacingContainer.Enabled = enableLineSpacingCheckBox.Checked;
                    rule.EnableCharacterUnitLineSpacing = enableLineSpacingCheckBox.Checked;

                    if (!enableLineSpacingCheckBox.Checked)
                    {
                        // 禁用时重置为默认值，但不影响现有的行距设置
                        rule.CharacterUnitLineSpacing = 1.0;
                        lineSpacingNumeric.Value = 1.0m;
                        // 注意：这里不重置普通行距设置，让用户可以在两种模式间切换
                    }
                };

                lineSpacingNumeric.ValueChanged += (s, e) => {
                    rule.CharacterUnitLineSpacing = (double)lineSpacingNumeric.Value;
                };

                container.Controls.Add(enableLineSpacingCheckBox, 0, 0);
                container.Controls.Add(lineSpacingContainer, 0, 1);

                return container;
            });
            layout.Controls.Add(lineSpacingPanel, 0, 2);

            // 添加说明面板
            var infoPanel = CreateLabeledControl("说明：", () => {
                var infoLabel = new Label
                {
                    Text = "字符单位设置以字符宽度/高度为单位进行排版控制，适用于中文文档的精确排版。\n" +
                           "• 字符单位缩进：以字符宽度为单位设置缩进\n" +
                           "• 字符单位间距：以字符宽度为单位设置段前段后间距\n" +
                           "• 字符单位行距：以字符高度为单位设置行距\n" +
                           "注意：启用字符单位设置会覆盖对应的普通设置。",
                    AutoSize = true,
                    ForeColor = Color.DarkBlue,
                    Font = new Font(Font.FontFamily, Font.Size - 1),
                    MaximumSize = new Size(500, 0) // 限制宽度以便自动换行
                };
                return infoLabel;
            });

            // 增加行数以容纳说明面板
            layout.RowCount = 4;
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            layout.Controls.Add(infoPanel, 0, 3);

            panel.Controls.Add(layout);
            return panel;
        }
    }
}
