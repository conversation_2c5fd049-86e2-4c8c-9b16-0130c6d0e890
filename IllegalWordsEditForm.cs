/*
 * ========================================
 * 文件名: IllegalWordsEditForm.cs
 * 功能描述: 非法词编辑窗体
 * ========================================
 *
 * 主要功能:
 * 1. 编辑文件名非法词列表
 * 2. 编辑内容非法词列表
 * 3. 支持添加、删除、修改非法词
 * 4. 支持特殊字符的非法词
 * 5. 实时保存到配置文件
 *
 * 界面特性:
 * - 多行文本框编辑（一行一个非法词）
 * - 支持复制粘贴
 * - 自动去除空行和重复项
 * - 提供常用非法词模板
 */

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace AsposeWordFormatter
{
    public partial class IllegalWordsEditForm : Form
    {
        public List<string> IllegalWords { get; private set; }
        private TextBox illegalWordsTextBox = null!;
        private Button okButton = null!;
        private Button cancelButton = null!;
        private Button addTemplateButton = null!;

        public IllegalWordsEditForm(List<string> illegalWords, string title)
        {
            IllegalWords = new List<string>(illegalWords ?? new List<string>());
            InitializeComponent();
            this.Text = $"编辑{title}";
            LoadIllegalWords();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4,
                Padding = new Padding(10)
            };
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            // 说明标签
            var descriptionLabel = new Label
            {
                Text = "请输入非法词，每行一个。支持特殊字符。",
                AutoSize = true,
                Dock = DockStyle.Top
            };
            mainLayout.Controls.Add(descriptionLabel, 0, 0);

            // 非法词文本框
            illegalWordsTextBox = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Dock = DockStyle.Fill,
                AcceptsReturn = true,
                AcceptsTab = false,
                WordWrap = true,
                Font = new Font("Consolas", 10)
            };
            mainLayout.Controls.Add(illegalWordsTextBox, 0, 1);

            // 模板按钮
            addTemplateButton = new Button
            {
                Text = "添加常用模板",
                AutoSize = true,
                Dock = DockStyle.Top
            };
            addTemplateButton.Click += AddTemplateButton_Click;
            mainLayout.Controls.Add(addTemplateButton, 0, 2);

            // 按钮面板
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                AutoSize = true,
                Padding = new Padding(0, 10, 0, 0)
            };

            cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                AutoSize = true
            };

            okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK,
                AutoSize = true
            };
            okButton.Click += OkButton_Click;

            buttonPanel.Controls.Add(cancelButton);
            buttonPanel.Controls.Add(okButton);
            mainLayout.Controls.Add(buttonPanel, 0, 3);

            this.Controls.Add(mainLayout);
            this.AcceptButton = okButton;
            this.CancelButton = cancelButton;
        }

        private void LoadIllegalWords()
        {
            illegalWordsTextBox.Text = string.Join(Environment.NewLine, IllegalWords);
        }

        private void OkButton_Click(object? sender, EventArgs e)
        {
            // 解析文本框内容
            IllegalWords = illegalWordsTextBox.Text
                .Split(new[] { Environment.NewLine, "\n", "\r" }, StringSplitOptions.RemoveEmptyEntries)
                .Select(s => s.Trim())
                .Where(s => !string.IsNullOrEmpty(s))
                .Distinct()
                .ToList();
        }

        private void AddTemplateButton_Click(object? sender, EventArgs e)
        {
            var templateWords = new List<string>
            {
                "测试", "临时", "temp", "test", "副本", "copy", "备份", "backup",
                "草稿", "draft", "机密", "confidential", "内部", "internal",
                "删除", "delete", "废弃", "废除", "作废", "无效"
            };

            var currentWords = illegalWordsTextBox.Text
                .Split(new[] { Environment.NewLine, "\n", "\r" }, StringSplitOptions.RemoveEmptyEntries)
                .Select(s => s.Trim())
                .Where(s => !string.IsNullOrEmpty(s))
                .ToList();

            // 添加模板中不存在的词
            var newWords = templateWords.Where(w => !currentWords.Contains(w)).ToList();
            
            if (newWords.Count > 0)
            {
                if (!string.IsNullOrEmpty(illegalWordsTextBox.Text) && !illegalWordsTextBox.Text.EndsWith(Environment.NewLine))
                {
                    illegalWordsTextBox.Text += Environment.NewLine;
                }
                illegalWordsTextBox.Text += string.Join(Environment.NewLine, newWords);
                MessageBox.Show($"已添加 {newWords.Count} 个常用非法词。", "添加完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("所有常用非法词都已存在。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
    }
}
