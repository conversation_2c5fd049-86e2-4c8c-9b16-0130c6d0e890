// 页眉页脚设置
// 此文件控制文档的页眉页脚内容和格式
{
  // 基本选项
  "EnableHeaderFooter": true, // 是否启用页眉页脚
  "DifferentFirstPage": true, // 首页是否使用不同的页眉页脚
  "DifferentOddEven": false, // 奇偶页是否使用不同的页眉页脚
  "LinkToPrevious": false, // 是否与前一节的页眉页脚相同
  
  // 页眉内容设置
  "PrimaryHeaderContent": "文档标题", // 主要页眉内容（普通页或奇数页）
  "EvenPageHeaderContent": "", // 偶数页页眉内容
  "FirstPageHeaderContent": "", // 首页页眉内容
  
  // 页脚内容设置
  "PrimaryFooterContent": "第 {0} 页，共 {1} 页", // 主要页脚内容（普通页或奇数页）
  "EvenPageFooterContent": "", // 偶数页页脚内容
  "FirstPageFooterContent": "", // 首页页脚内容
  
  // 页眉格式设置
  "PrimaryHeaderFontName": "宋体", // 主要页眉字体
  "PrimaryHeaderFontSize": 10.5, // 主要页眉字体大小（磅）
  "PrimaryHeaderBold": false, // 主要页眉是否加粗
  "PrimaryHeaderItalic": false, // 主要页眉是否斜体
  "PrimaryHeaderAlignment": 1, // 主要页眉对齐方式：0=左对齐，1=居中，2=右对齐，3=两端对齐
  "PrimaryHeaderBottomBorder": true, // 主要页眉是否有下边框
  "PrimaryHeaderBorderWidth": 0.5, // 主要页眉边框宽度（磅）
  "PrimaryHeaderBorderStyle": 1, // 主要页眉边框样式：1=单线，2=双线
  "PrimaryHeaderBorderColor": "#000000", // 主要页眉边框颜色
  
  // 页脚格式设置
  "PrimaryFooterFontName": "宋体", // 主要页脚字体
  "PrimaryFooterFontSize": 10.5, // 主要页脚字体大小（磅）
  "PrimaryFooterBold": false, // 主要页脚是否加粗
  "PrimaryFooterItalic": false, // 主要页脚是否斜体
  "PrimaryFooterAlignment": 1, // 主要页脚对齐方式：0=左对齐，1=居中，2=右对齐，3=两端对齐
  "PrimaryFooterTopBorder": true, // 主要页脚是否有上边框
  "PrimaryFooterBorderWidth": 0.5, // 主要页脚边框宽度（磅）
  "PrimaryFooterBorderStyle": 1, // 主要页脚边框样式：1=单线，2=双线
  "PrimaryFooterBorderColor": "#000000", // 主要页脚边框颜色
  
  // 首页页眉格式设置
  "FirstPageHeaderFontName": "宋体", // 首页页眉字体
  "FirstPageHeaderFontSize": 10.5, // 首页页眉字体大小（磅）
  "FirstPageHeaderBold": false, // 首页页眉是否加粗
  "FirstPageHeaderItalic": false, // 首页页眉是否斜体
  "FirstPageHeaderAlignment": 1, // 首页页眉对齐方式：0=左对齐，1=居中，2=右对齐，3=两端对齐
  "FirstPageHeaderBottomBorder": true, // 首页页眉是否有下边框
  "FirstPageHeaderBorderWidth": 0.5, // 首页页眉边框宽度（磅）
  "FirstPageHeaderBorderStyle": 1, // 首页页眉边框样式：1=单线，2=双线
  "FirstPageHeaderBorderColor": "#000000", // 首页页眉边框颜色
  
  // 首页页脚格式设置
  "FirstPageFooterFontName": "宋体", // 首页页脚字体
  "FirstPageFooterFontSize": 10.5, // 首页页脚字体大小（磅）
  "FirstPageFooterBold": false, // 首页页脚是否加粗
  "FirstPageFooterItalic": false, // 首页页脚是否斜体
  "FirstPageFooterAlignment": 1, // 首页页脚对齐方式：0=左对齐，1=居中，2=右对齐，3=两端对齐
  "FirstPageFooterTopBorder": true, // 首页页脚是否有上边框
  "FirstPageFooterBorderWidth": 0.5, // 首页页脚边框宽度（磅）
  "FirstPageFooterBorderStyle": 1, // 首页页脚边框样式：1=单线，2=双线
  "FirstPageFooterBorderColor": "#000000", // 首页页脚边框颜色
  
  // 自动内容设置
  "IncludePageNumbers": true, // 是否包含页码
  "PageNumberFormat": "第 {0} 页，共 {1} 页", // 页码格式，{0}表示当前页码，{1}表示总页数
  "IncludeCurrentDate": false, // 是否包含当前日期
  "DateFormat": "yyyy年MM月dd日", // 日期格式
  "IncludeDocumentTitle": true, // 是否包含文档标题
  "IncludeFileName": false, // 是否包含文件名
  "IncludeAuthor": false, // 是否包含作者
  
  // 高级设置
  "HeaderDistance": 36.0, // 页眉与页边距的距离（磅）
  "FooterDistance": 36.0, // 页脚与页边距的距离（磅）
  "UseFirstPageSettings": true, // 是否使用首页设置
  "UseEvenPageSettings": false, // 是否使用偶数页设置
  
  // 页眉页脚位置
  "HeaderVerticalPosition": 0, // 页眉垂直位置，0表示默认
  "FooterVerticalPosition": 0, // 页脚垂直位置，0表示默认
  
  // 页眉页脚内容格式化选项
  "RemoveDuplicateSpaces": true, // 是否删除重复空格
  "TrimContent": true, // 是否修剪内容前后空白
  "UseKerning": true // 是否使用字偶距调整
} 